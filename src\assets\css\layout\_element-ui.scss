// el-menu
.el-menu {
  border-right: none;

  &.sty1-menu {

    .el-menu-item {
      font-size: 16px;
      font-weight: 500;
      color: #738C8F;
      height: 44px;
      line-height: 44px;
      margin-bottom: 8px;
      margin-top: 8px;

      i {
        font-size: 20px;
        margin-right: 12px;
        margin-left: 20px;
      }

      &:hover {
        background-color: #CEDBDB !important;
      }

      &.is-active {
        color: #002E33;
        font-weight: 700;
      }
    }
  }
}
// el-progress
.el-progress {
  &.sty1-progress {
    .el-progress__text {
      color: #002E33 !important;
      font-size: 12px !important;
      font-weight: 400;
    }
  }
}

// el-submenu
.el-submenu {
  &.sty1-submenu {
    .el-submenu__title {
      font-size: 16px;
      color: #738C8F;
      height: 44px;
      line-height: 44px;

      i {
        font-size: 20px;
        margin-right: 12px;
        margin-left: 20px;
      }

      .el-submenu__icon-arrow {
        font-size: 12px;
        margin-top: -4px;
        &::before {
          color: #738C8F;
          font-family: 'iconfont';
          content: "\e724";
        }
      }
    }
    .el-submenu__title:hover {
      background-color: #CEDBDB !important;
    }
  }
}

// el-form
.el-form {
  &.sty1-form {
    .mgb-20 {
      margin-bottom: 20px;
    }
    .mgb-40 {
      margin-bottom: 40px;
    }
    .mgb-24 {
      margin-bottom: 24px;
    }
    .mgb-26 {
      margin-bottom: 26px;
    }
    .mgb-8 {
      margin-bottom: 8px;
    }
    .mgb-6 {
      margin-bottom: 6px;
    }
    .mgb-0 {
      margin-bottom: 0px;
    }

    .el-form-item__label {
      font-weight: 400;
      line-height: 16px;
      font-size: 12px;
      color: #002E33;
      padding: 0 0 8px 0;
    }

    .el-form-item__content {
      line-height: 42px;
    }
    .el-form-item.is-error .el-input__inner {
      border: 1px solid #EE6969;
    }
    .el-form-item__error {
      width: 100%;
      // text-align: center;
      padding-top: 6px;
      // position: relative;
    }
    .closeeye {
      .el-input__suffix .el-icon-view::before {
        font-family: "color-iconfont"; 
        content: "\e603";
      }
    }
    .openeye {
      .el-input__suffix .el-icon-view::before {
        font-family: "color-iconfont"; 
        content: "\e604";
      }
    }
    .el-input__suffix {
      .el-input__suffix-inner {
        display: flex;
        flex-direction: row;
      }
      .el-input__icon {
        width: 20px;
        line-height: 42px;
      }
      .el-icon-circle-close {
        order: 2;
      }
      .el-icon-view {
        order: 1;
      }
      i {
        margin-left: 10px;
        font-size: 20px;
      }
      .el-icon-circle-close::before {
        font-family: "color-iconfont"; 
        content: "\e605";
      }
      right: 16px;
    }
    .password {
      position: absolute;
      bottom: 0;
      right: 40px;
      font-size: 20px;
      width: 20px;
      line-height: 42px;
      cursor: pointer;
    }
    .el-input__inner {
      font-family: "PingFang SC", "Source Han Sans CN", BlinkMacSystemFont, "Microsoft YaHei UI", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
      padding: 0px 65px 0 18px;
      color: #002E33;
      // border: 2px solid #FFFFFF08;
      border: none;
      height: 43px;
      border-radius: 26px;
      background-color: rgba(255, 255, 255, 0.8);

      &::placeholder {
        color: #B3C0C2;
        font-size: 400;
        font-size: 12px;
        line-height: 16px;
      }
      &.creat-input {
        height: 36px;
      }
    }

    .retrieve {
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      color: #002E33;
      cursor: pointer;
    }
    .partner {
      margin-left: 4px;
      color: #B3C0C2;
    }

    .el-button {
      padding: 13px 20px;
      background-color: #005563;
      width: 100%;
      border-radius: 28px;
      color: #FFFFFF;
      font-weight: 500;
      border: none;
      &:hover{
        border-color: #002E33;;
      }
      &.is-disabled {
        opacity: 0.5;
      }
    }

  }
  &.sty2-form {
    .el-form-item.is-error .el-input__inner {
      border: 1px solid #EE6969;
    }
    .el-form-item__error {
      margin-left: 46px;
      padding-top: 6px;
      position: relative;
    }
    .el-form-item {
      margin-bottom: 0;
    }
  }
  &.sty3-form {
    .el-input__inner {
      height: 36px;
      padding: 12px 65px 12px 18px;
      border: 1px solid#CEDBDB;
    }
    .pdl35 {
      .el-input__inner {
        padding: 12px 35px 12px 18px;
      }
    }
    .uncheck .el-form-item__error {
      padding-top: 0px;
    }
  }
  &.sty4-form {
    position: relative;
    .el-form-item__content {
      line-height: 34px;
    }
    .el-input__inner {
      height: 34px;
      padding: 12px 65px 12px 18px;
      border: 1px solid#CEDBDB;
    }
    .pdl35 {
      .el-input__inner {
        padding: 12px 35px 12px 18px;
      }
    }
    .creatInput {
      .el-input__inner {
        &::placeholder{
          font-size: 400;
          font-size: 11px;
          line-height: 16px;
        }
      }
    }
    .el-button {
      padding: 10px 20px;
    }
    .el-input__suffix {
      .el-input__icon {
        line-height: 34px;
      }
    }
    .errorword {
      .el-form-item__error {
        text-align: left;
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        text-align: center;
        position: relative;
      }
    }
  }
  &.sty5-form {
    display: grid;
    grid-gap: 20px;
    width: 60%;
    &.sty5-form-width {
      width: 100%;
    }
    .el-form-item.is-error {
      .el-input__inner {
        border: 1px solid #EE6969;
      }
      .el-form-item__content {
        .el-form-item__error {
          color: #EE6969;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          margin-top: 4px;
        }
        margin-bottom: 24px;
      }
    } 
    .warnInput .el-form-item__content {
      .el-input__inner  {
        border-color: #FFBA53 !important;
      }
      .el-form-item__error {
        color: #FFBA53 !important;
      }
    }
    .el-form-item__content {
      color: #33585C;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
    }
    .input1 {
      width: 80%;
      margin-right: 4px;
    }
    .input2 {
      width: 20%;
      position: relative;
      .el-input__inner {
        padding-right: 35px;
        text-align: center;
      }
      &::after {
        content: "%";
        position: absolute;
        top: calc(50% + 4px);
        transform: translateY(-50%);
        right: 24px;
      }
    }
    
    .el-form-item {
      margin-bottom: 0;
      input {
        width: 100%;
        border-radius: 65px;
        border: 1px solid rgba(0, 0, 0, 0.05);
        background: #FFFFFF;
        margin-top: 4px;
        color: #002E33;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        // padding: 14px 24px;
        height: 48px;
      }
      input::placeholder {
        color: #B3C0C2;
      }
    }
  }
  &.cen-align-err{
    .el-form-item__error {
      text-align: center;
    }
  }
}

.el-upload {
  .el-upload-dragger {
    width: 100%;
    border-color: rgba(0, 46, 51, 0.10);
    background-color: #F7F7F7;
  }
  .el-upload__text {
    width: 63%;
    margin: 0 auto;
  }
}

.el-textarea {
  &.sty1-input-textarea {
    margin-bottom: 24px;
    height: 120px;
    border: 1px solid #DCDFE6;
    border-radius: 8px;
    padding-top: 8px;
    .el-textarea__inner {
      border-radius: 8px;
      padding: 0 12px;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;  
      color: #002E33;
      height: auto;
      border: none;
      min-height: 90px !important;
    }
    .el-input__count {
      padding: 8px 12px;
      right: 2px;
      bottom: 2px;
    }
    .el-textarea__inner:focus {
      border-color: #ADADAD;
    }
  }
}

// el-input
.el-input {

  // 输入框/面性
  &.sty1-input {
    .el-input__inner {
      font-weight: normal;
      padding: 0 12px;
      color: #002E33;
      border-color: #F7F7F7;
      height: 40px;
      line-height: 40px;
      background: #F7F7F7;
      border-radius: 10px;

      &::placeholder {
        color: #B3C0C2;
      }
    }

    .el-input__prefix {}

    .el-input__suffix {
      i {
        font-size: 20px;
        // color: #F2F7F7;
        background-color: #FFFFFF;
        color: #B3C0C2;
        position: relative;

        &::before {
          font-family: "iconfont";
          content: "\e60a";
        }
      }
    }

    .el-form-item__error {}
  }

  &.sty2-input {
    .el-input__inner {
      padding: 0 12px;
      color: #002E33;
      border: 1px solid #F2F7F7;
      // border: 1px solid red;
      background: #FFFFFF;
      border-radius: 10px;

      &::placeholder {
        color: #B3C0C2;
      }
    }

    .el-input__suffix {
      i {
        color: #B3C0C2;
        font-size: 20px;
      }
    }
  }

  &.sty1-input-search {
    width: 240px;

    .el-input__inner {
      border: none;
      background: #F7F7F7;
      border-radius: 12px;
      color: #002E33;

      &::placeholder {
        color: #B3C0C2;
      }
    }

    .el-input__icon {
      width: 16px;
      font-size: 16px;
      line-height: 40px;
    }

    .el-input__prefix {
      left: 12px;
    }

    .el-input__suffix {
      .el-input__icon {
        width: 20px;
      }
      i {
        font-size: 20px;
      }
      i::before {
        font-family: "color-iconfont"; 
        content: "\e731";
      }
      right: 12px;
    }

    &.el-input--prefix .el-input__inner {
      padding-left: 32px;
    }
  }

  &.width-1 {
    width: 100%;
  }

  &.width-265{
    width: 265px;
  }

  &.sty2-input-search {
    width: 188px;
    .el-input__inner {
      height: 28px;
      border-radius: 6px;
    }
    .el-input__icon {
      line-height: 28px;
    }
  }
  &.sty3-input {
    border-radius: 26px;
    background-color: #F7F7F7;
    border: none;
    color: #002E33;
    .el-input__suffix .el-input__suffix-inner {
      font-size: 20px;
    }
    .el-input__inner {
      border-radius: 26px;
    }
    .mcico-celar {
      margin-right: 8px;
      cursor: pointer;
    
      &::before {
        background-color: #F7F7F7;
        font-size: 20px;
      }
    }
  }
  &.sty4-input {
    .el-input__inner {
      background: rgba(132, 182, 184, 0.02);
      padding: 0;
      height: 24px;
      border: none;
      color: #33585C;
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      &::placeholder {
        color: #B3C0C2;
      }
    }
    &.sty4-input-width {
      .el-input__inner {
        width: 380px;
        margin-right: 16px;
      }
    }
    &.sty4-input-sty1 {
      .el-input__inner {
        padding: 9px 16px;
        height: 40px;
        border-radius: 20px;
        border: 1px solid rgba(0, 0, 0, 0.05);
      }
    }
    &.sty4-input-sty1-error {
      .el-input__inner {
        border: 1px solid #EE6969;
      }
    }
    &.sty4-input-sty2 {
      .el-input__inner {
        border: 1px solid rgba(0, 0, 0, 0.05);
        background: rgba(132, 182, 184, 0.04);
        color: #809799;
      }
    }
  }
}

// el-select
.el-select {

  // 请参考 dashboard 页面 select
  &.sty1-select {
    .el-input {
      width: 180px;
    }

    .el-input__prefix {
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    .el-input__inner {
      padding: 0 16px 0 40px;
      background: #F7F7F7;
      border-radius: 12px;
      border: none;
      color: #002E33;

      &::placeholder {
        color: #B3C0C2;
      }
    }

    .el-select__caret {
      font-size: 16px;
      color: #99ABAD;
    }
  }

  &.padding-1 {
    .el-input__inner {
      padding: 0 16px 0 16px;
    }
  }

  &.width-265 {
    .el-input {
      width: 265px;
    }
  }

  &.sty2-select {
    .el-input {
      width: 130px;
    }

    .el-input__inner {
      border-radius: 5px;
      border: 1px solid #F0F0F0;
      background: #F2F7F7;
      padding: 0 12px;
      font-size: 14px;
      line-height: 18px;
      color: #002E33;
    }

    .el-input.is-focus .el-input__inner{
      border-color: #F0F0F0;
    }

    .el-select__caret {
      color: #002E33;
      font-weight: bolder;
    }

    .el-select-dropdown {
      box-shadow: 0px 2px 4px -1px #0000001F;
    }

    .el-select-dropdown__item {
      margin: auto 6px;
    }

    .el-select-dropdown__item.selected {
      color: #005563;
      background-color: rgba(217, 246, 239, 0.2);
    }
    .el-select-dropdown__item.hover {
      background-color: #F7F7F7;
    }
    .el-popper[x-placement^="bottom"] .popper__arrow {
      border-bottom-color: transparent;
      &::after {
        border-bottom-color: transparent;
      }
    }
  }
  &.sty3-select {
    .el-input {
      width: 188px;
    }

    .el-input__inner {
      padding: 0 16px;
      background: #F7F7F7;
      border-radius: 6px;
      border: none;
      color: #002E33;
      height: 28px !important;
      line-height: 28px;

      &::placeholder {
        color: #B3C0C2;
      }
    }

  }
  &.sty4-select {
    display: flex;
    margin-bottom: 24px;
    .el-input {
      width: 100%;
    }
    .el-input__inner {
      font-size: 14px;
      padding: 12px 16px;
      font-weight: 400;
      line-height: 22px;
    }
  }
}


// el-popper
.el-popper {

  // 请参考 dashboard 页面 select 弹出框
  &.sty1-popper {
    &.el-popover {
      padding: 0;
      border-radius: 12px;
      border: none;
      box-shadow: none;
    }
    .popper__arrow {
      display: none;
    }
    &.el-select-dropdown {
      background: #FFFFFF;
      border: 1px solid #CEDBDB;
      box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.12);
      border-radius: 12px;
    }

    .el-select-dropdown__list {
      padding: 6px;
    }

    .el-select-dropdown__item {
      color: #002E33;
      padding: 0 8px;
      height: 32px;
      line-height: 32px;
      border-radius: 3px;

      &.hover {
        background-color: #F7F7F7;
      }

      &.selected {
        background: linear-gradient(0deg, rgba(217, 246, 239, 0.2), rgba(217, 246, 239, 0.2)), rgba(255, 255, 255, 0.2);
        font-weight: normal;
        color: #005563;
      }
    } 
  }

  &.sty2-popper {
    &.el-popper[x-placement^=bottom] {
      margin-top: 0;
    }
  }

  &.sty3-popper {
    margin-top: 10px;
    border-radius: 12px;
    padding: 6px;
    border: 1px solid #CEDBDB;
    background: #FFF;
    box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.12);
    .popper__arrow {
      display: none;
    }
  }
  &.sty3-popper-fail {
    display: none;
  }
  &.sty4-popper {
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 16px;
    color: #FFFFFF;
    .popper__arrow::after {
      border-bottom-color: rgba(0, 0, 0, 0.8);
    }
    .copy {
      cursor: pointer;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
    }
    .mico-copy {
      margin-top: 5px;
      margin-left: 2px;
      font-size: 12px;
    }
  }
  &.sty5-popper {
    padding: 6px;
    border-radius: 12px;
    border: 1px solid #CEDBDB;
    box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.12);
  }
  &.sty6-popper {
    min-width: 0px !important;
    width: 202px;
    margin-left: 368px;
    border-radius: 12px;
    .popper__arrow {
      display: none;
    }
    .el-popper {
      margin-left: 194px;
    }
    .el-select-dropdown__list {
      padding: 5px;
    }
    .el-select-dropdown__item {
      padding: 0 7px;
      border-radius: 6px;
    }
    .el-select-dropdown__item:not(:hover) {
      background-color: #FFFFFF;
    }
  }
  &.sty7-popper {
    min-width: 527px;
    padding: 8px;
    display: grid;
    grid-gap: 12px;
    border-radius: 6px;
    border: 1px solid #CEDBDB;
    background: #FFF;
    margin-bottom: 30px;
    font-size: 12px;
    color: #002E33;
    line-height: 18px;
    i {
      font-size: 14px;
    }
    .textblod {
      font-weight: 700;
      div {
        margin-top: 4px;
        font-weight: 400;
      }
    }
  }
  &.sty8-popper {
    margin-top: 10px;
    border-radius: 12px;
    height: 46px;
    padding: 0 19px;
    border: 1px solid #CEDBDB;
    background: #FFF;
    box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.12);
    .disconnect {
      width: 100%;
      height: 100%;
      position: relative;
      color: #EE6969;
      font-size: 14px;
      font-weight: 500;
    }
    .popper__arrow {
      display: none;
    }
  }
  // &.sty9-popper {
  //   .dropdown-link {
  //     border-radius: 58px;
  //     background: rgba(132, 182, 184, 0.12);
  //   }
  // }
}
.connectWallet {
  width: 128px;
  height: 36px;
  padding: 8px 12px;
  margin: 0 24px;
  border-radius: 58px;
  color: #002E33;
  font-size: 14px;
  font-weight: 500;
  line-height: 140%;
  background: rgba(132, 182, 184, 0.12);
  border: none;
  &:focus {
    color: #002E33;
    border: none;
    background: rgba(132, 182, 184, 0.12);
  }
  &:active {
    color: #002E33;
    border: none;
    background: rgba(132, 182, 184, 0.12);
  }
  &:hover {
    color: #002E33;
    border: none;
    background: rgba(132, 182, 184, 0.12);
  }
}
.el-color-picker__panel {
  &.sty1-ColorPicker, &.sty2-ColorPicker {
    width: 244px;
    height: 315px;
    margin-left: 540px;
    margin-top: -333px;
    position: relative;
    padding: 44px 20px 0;
    border-radius: 12px;
    border: none;
    .el-color-dropdown__btns {
      margin-top: 20px;
    }
    .el-color-predefine {
      position: absolute;
      bottom: 16px;
    }
    .el-color-svpanel {
      width: 204px;
      height: 204px;
    }
    .el-color-svpanel__cursor div {
      width: 8px;
      height: 8px;
      box-shadow: 0 0 0 1.5px #fff, inset 0 0 0px 0px rgba(0, 0, 0, 0.3), 0 0 0px 0px rgba(0, 0, 0, 0.4);
    }
    .el-input__inner {
      text-align: center;
      border-color: #F0F0F0;
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      color:#002E33;
    }
    .el-color-dropdown__value {
      position: relative;
      padding-left: 32px;
      &::after {
        font-weight: 500;
        font-size: 12px;
        color: #002E33;
        position: absolute;
        left: 0;
        content: 'Hex';
      }
    }
    button {
      display: none;
    }
    .el-color-hue-slider, .is-vertical {
      height: 204px;
      position: relative;
      // border-radius: 25px;
      overflow: hidden;
    }
    &::after{
      content: 'Color';
      position: absolute;
      top: 16px;
      left: 20px;
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
    }
  }
  &.sty2-ColorPicker {
    background-color: #141414;
    &::after{
      color: #FFFFFF;
    }
    .el-input__inner {
      border-color: #3C3C3C;
      background-color: #282828;
      color: #FFFFFF;
    }
    .el-color-dropdown__value {
      &::after {
        color: #FFFFFF;
      }
    }
  }
}

// el-date-editor
.el-date-editor {
  &.sty1-date-editor {
    background: #F7F7F7;
    border-radius: 30px;
    border: none;
    position: relative;
    cursor: pointer;
    &::before {
      font-family: "iconfont";
      content: "\e724";
      position: absolute;
      right: 23px;
      z-index: 1;
      transition: transform .3s;
    }
    &.is-active {
      &::before {
        transform: rotate(-180deg);
        transition: transform .3s;
      }
    }

    &.el-input__inner {
      padding: 0px 52px 0px 20px;
      // width: 260px;
      width: 275px;
      box-sizing: border-box;
      height: 44px;
      line-height: 44px;
    }

    .el-range-input {
      cursor: pointer;
      background: #F7F7F7;
    }

    .el-range__icon {
      font-size: 20px;
      margin-left: 0;
      margin-right: 8px;
      color: #738C8F;
      line-height: 44px;
    }

    .el-range-input,
    .el-range-separator {
      font-weight: 700;
      font-size: 14px;
      color: #002E33;
    }

    .el-range-separator {
      padding: 0;
      width: 13px;
      line-height: 44px;
    }

    .el-range__close-icon {
      display: none;
    }
  }

  &.sty2-date-editor {
    background: #F7F7F7;
    border-radius: 30px;
    border: none;
    position: relative;
    cursor: pointer;
    &::before {
      font-family: "iconfont";
      content: "\e724";
      position: absolute;
      right: 23px;
      z-index: 1;
      transition: transform .3s;
    }
    &.is-active {
      &::before {
        transform: rotate(-180deg);
        transition: transform .3s;
      }
    }

    &.el-input__inner {
      padding: 0px 52px 0px 20px;
      // width: 260px;
      width: 100%;
      box-sizing: border-box;
      height: 40px;
      line-height: 40px;
    }

    .el-range-input {
      cursor: pointer;
      background: #F7F7F7;
      width: 20%;
    }

    .el-range__icon {
      font-size: 20px;
      margin-left: 0;
      margin-right: 8px;
      color: #738C8F;
      line-height: 44px;
    }

    .el-range-input,
    .el-range-separator {
      font-weight: 700;
      font-size: 14px;
      color: #002E33;
    }

    .el-range-separator {
      padding: 0;
      width: 13px;
      line-height: 44px;
    }

    .el-range__close-icon {
      display: none;
    }
  }

  &.sty3-date-editor {
    background: transparent;
    .el-range-input {
      background: transparent;
    }
  }

  &.sty4-date-editor {
    width: 100%;
    &.sty4-date-editor-error {
      .el-input__inner {
        border: 1px solid #EE6969;
      }
    }
    .el-input__inner {
      width: 100%;
      border-radius: 65px;
      border: 1px solid #CEDBDB;
      background: #FFF;
      color: #002E33;
      font-size: 14px;
      font-weight: 450;
      line-height: 20px;
      padding: 14px 24px 14px 50px;
      &::placeholder {
        font-weight: 400;
        color: #809799;
      }
    }
    .el-input__prefix {
      left: 24px;
      top: 2px;
      .mico-date {
        font-size: 16px;
        color: #002E33;
      }
    }
    &::before {
      font-family: "iconfont";
      content: "\e601";
      position: absolute;
      top: 14px;
      right: 24px;
      font-size: 14px;
      color: #99ABAD;
      transition: transform .3s;
    }
    &.sty4-date-editor-active::before {
      transform: rotate(-180deg);
    }
  }
}

.el-picker-panel {
  &.sty1-date-popper {
    border: 1px solid #CEDBDB;
    border-radius: 6px;
    color: #002E33;
    &.el-popper[x-placement^="bottom"] .popper__arrow {
      display: none;
    }
    .el-date-table {
      font-size: 14px;
    }
    .el-date-table td.next-month, .el-date-table td.prev-month {
      color: #CEDBDB;
    }
    .el-date-table td.in-range div {
      background-color: #E6EDED;
    }
    .el-date-table td.start-date span, .el-date-table td.end-date span {
      background-color: #005563;
    }
    .el-date-table th {
      color: #CEDBDB;
    }
    .el-picker-panel__icon-btn {
      color: #002E33;
      font-size: 14px;
    }
  }
  &.sty2-date-popper {
    border-radius: 16px;
    border: 1px solid #CEDBDB;
    background: #FFF;
    .el-picker-panel__footer {
      display: none;
    }
    &.el-popper[x-placement^="bottom"] .popper__arrow {
      display: none;
    }
    .el-date-picker__time-header {
      border: none;
    }
    .el-time-panel {
      border-color: #CEDBDB;
      border-radius: 16px;
      box-shadow: none;
    }
    .el-input--small {
      input {
        border-radius: 65px;
        &:focus {
          border-color: #8EDEE3;
        }
      }
    }
    .el-time-spinner {
      border-radius: 16px;
      padding: 12px 0;
    }
    .el-scrollbar__view {
      // padding: 12 0;
      border-radius: 16px;
      &::before {
        // height: 0;
      }
      .el-time-spinner__item {
        color: #CEDBDB;
        &.active {
          color: #002E33;
        }
      }
    }
    .el-time-panel__footer {
      display: none;
    }
    .el-date-picker__header {
      color: #002E33;
      font-size: 14px;
      font-weight: 450;
      line-height: 22px;
      button {
        margin-top: 5px;
        &::before {
          font-size: 14px;
          color: #002E33;
          font-weight: 800;
        }
      }
      .el-icon-d-arrow-right {
        &::before {
          margin-left: 12px;
        }
      }
      .el-icon-d-arrow-left {
        &::before {
          margin-right: 12px;
        }
      }
    }
    .prev-month, .next-month {
      color: #CEDBDB;
      font-size: 14px;
      font-weight: 450;
      line-height: 22px;
    }
    .el-date-table th {
      border: none;
      color: #CEDBDB;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
    }
    .el-date-table__row .today span {
      background: #E6EDED;
      color: #005563;
    }
    .el-date-table__row .current span {
      background-color: #005563;
      color: #FFFFFF;
    }
    .el-date-table {
      color: #002E33;
      font-size: 14px;
      font-weight: 450;
      line-height: 22px;
    }
  }

  &.width-1 {
    max-width: 546px !important;
  }
}

.el-popover {
  &.NotRecord_popover {
    min-width: 0px;
    padding: 6px 10px;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    // margin: 0 0 6px 10px;
    border-radius: 8px;
    border: none;
    max-width: 200px;
    word-wrap: break-word;
    color: #33585c;
  }
  &.Kyc_from_tip {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: normal;
    hyphens: auto;
    text-align: left !important;
    max-width: 298px;
  }
  &.Types_popover {
    // color: #33585c;
    .popper__arrow::after {
      margin-left: -14px;
    }
  }
  &.WhiteList {
    // color: #33585c;
    margin-bottom: 15px;
    text-align: center;
    .popper__arrow::after {
      transform: scale(2);
      border-bottom: 5px;
    }
  }
  &.jsonTip {
    max-width: 288px;
  }
  &.kyt_popover {
    max-width: 468px;
    background: rgba(0, 0, 0, 0.9);
    color: #FFFFFF;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    .popper__arrow {
      border-top-color: rgba(0, 0, 0, 0.9);
      &::after {
        border-top-color: rgba(0, 0, 0, 0.9);
        border-bottom-color: rgba(0, 0, 0, 0.9);
      }
    }
  }
}


.el-popper {
  &.extendlist {
    max-height: 232px;
    overflow-y: auto;
    margin-top: -20px;
    min-width: 130px;
    padding: 8px 0;
    border-radius: 4px;
    background-color: #FFFFFF !important;
    &::-webkit-scrollbar-track-piece {
      margin: 8px 0;
    }
    &::-webkit-scrollbar-thumb {
      width: 3px;
      height: 74px;
      background-color: #F0F0F0;
      border-radius: 23px;
    }
    .el-dropdown-menu__item {
      color: #002e33;
    }
    .dropdownindex {
      font-weight: 500;
      color: #33585c;
      background-color: rgba(217, 246, 239, 0.2);
    }
    li:hover {
      background-color: #F7F7F7;
    }
    .dropdownindex:hover {
      background-color: #f3f8f7;
    }
    .popper__arrow {
      display: none;
    }
  }
}
.el-dropdown {
  .dropdownbut {
    color: #002E33;
    padding: 11px 14px 11px 12px;
    border: 1px solid #F2F7F7;
    min-width: 130px;
    height: 40px;
    border-radius: 5px;
    margin: 0 0 24px 12px;
    background-color: rgba(217, 246, 239, 0.2);
    span {
      display: flex;
      justify-content: space-between;
      i {
        font-size: 12px;
        color: #002E33;
      }
    }
  }
}

.el-tag {
  &.recorded_tag {
    border: 1px solid #E6EDED;
    padding: 0 12px;
    width: 78px;
    height: 32px;
    background-color: #F2F7F7;
    color: #002E33;
    font-weight: 400;
    font-size: 14px;
    cursor: pointer;
    .mico-close {
      font-size: 12px;
      margin-left: 12px;
      color: #005563;
      // cursor: pointer;
    }
  }
}
.el-table {
  &.table_topping {
    .el-table__body tr.Apply {
      background-color: rgba(217, 246, 239, 0.2);
      .table_Copy {
        background-color: #F3F8F7;
      }
    }
    .el-table__body tr:hover.Apply>td{
      cursor: pointer;
      background: #F3F8F7;
    }
  }
  &.recorded_table {
    // .el-table__body tr:nth-of-type(1) {
    //   background-color: rgba(217, 246, 239, 0.2);
    // }
    // .el-table__body tr:hover:nth-of-type(1)>td{
    //   cursor: pointer;
    //   background: #F3F8F7;
    // }
    .caret-wrapper {
      // height: 20px;
      // display: block;
      transform: scale(0.3);
      margin-bottom: 4px;
      .ascending,.descending {
        display: none;
      }
      .mico-sort-up,.mico-sort-dow {
        display: block;
        color: #B3C0C2;
        // transform: scale(0.3);
        margin-bottom: 9px;
      }
      .mico-sort-up {
        margin-bottom: 9px;
      }
    }
    .ascending .mico-sort-up, .descending .mico-sort-dow{
      color: #738C8F;
    }
    .mico-a-mico-screen-upsvg,.mico-a-mico-screen-dowsvg {
      color: #B3C0C2;
      font-size: 1px;
    }
    .el-table__body tr:hover>td{
      cursor: pointer;
      background: #F7F7F7;
    }
    // .Apply {
    //   background-color: rgba(217, 246, 239, 0.2);
    // }
    th {
      &.el-table__cell > .cell{
        font-weight: 400;
        padding: 0px !important;
      }
    }
      
    .el-table__cell > .cell {
      padding: 0px;
    }
    &::before {
      display: none;
    }
    .has-gutter {
      color: #738C8F;
      th {
        padding: 0 0 8px 0;
        border: none;
      }
    }
    .el-table__row {  
      color: #002E33;
      font-weight: 400;
      td {
        padding: 10px 0;
        border: none;
        .cell {
          line-height: 22px;
          font-weight: 450;
        }
      }
    }
    .cellClassname {
      .cell {
        padding-right: 50px;
        .el-popover__reference {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .el-table__empty-block {
      display: none;
    }
  }
  &.dialog_table {
    border-radius: 11px;
    .has-gutter {
      th {
        &:first-child {
          background-color: #F7F7F7;
        }
        &:nth-child(2) {
          background-color: rgba(217, 246, 239, 0.2);
        }
        text-align: center;
        color: #002E33;
      }
    }
    .el-table__row {
      &:last-child {
        td + td {
          border-radius: 0px 0px 11px 0px;
        }
      }
      td {
        text-align: center;
        color: #002E33;
      }
    }
  }
}
// .el-pagination {
//   &.pagination {
//     padding: 0;
//     .btn-prev, .btn-next, .btn-quicknext {
//       height: 32px;
//     }
//     .active, .number{
//       height: 32px;
//       line-height: 32px;
//       padding: 0;
//       font-weight: 400;
//       font-size: 14px;
//       min-width: 32px;
//     }
//     .btn-quicknext {
//       color: #B3C0C2;
//     }
//     .number {
//       color: #B3C0C2;
//     }
//     .active {
//       border-radius: 50%;
//       background-color: #E6EDED;
//       color: #002E33;
//     }
//   }
// }
.el-drawer__wrapper {
  &.recorded_drawer {
    .el-drawer {
      width: auto !important;
      box-shadow: -2px 0px 8px rgba(0, 0, 0, 0.08);
    }
    .el-drawer__header {
      margin: 0;
      padding: 60px 24px 8px;
      color: #002E33;
      font-weight: 700;
      font-size: 20px;
      line-height: 24px;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      span {
        max-width: 400px;
        word-wrap: break-word;
      }
    }
    .el-dialog__close:before {
      font-size: 24px;
      color: #002E33;
      font-family: "iconfont";
      content: "\e614";
    }
    .el-drawer__body {
      padding: 0 24px;
    }
  }
}
.el-divider {
  &.Sybil {
    margin: 28px 0 16px;
    background-color: #F0F0F0;
    .el-divider__text {
      left: 18px;
      padding: 0 6px;
    }
  }
  &.sty1-divider {
    margin: 16px 0 32px;
    background-color: #F0F0F0;
    .el-divider__text {
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      color: #738C8F;
    }
  }
}
.el-dropdown {
  &.sty1-dropdown {
    width: 100%;
    margin-top: 4px;
    span {
      border-radius: 65px;
      border: 1px solid rgba(0, 0, 0, 0.05);
      display: flex;
      padding: 14px 24px;
      justify-content: space-between;
      width: calc(100% - 48px);
      .el-icon-arrow-down {
        font-size: 14px;
        color: #002E33;
        display: flex;
        align-items: center;
      }
    }
  }
}
.el-dropdown-menu {
  &.sty1-dropdown-menu {
    padding: 12px;
    width: 44vw;
    border-radius: 24px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    background: #FFF;
    box-shadow: none;
    &.sty1-dropdown-menu-step {
      height: 50%;
      overflow: scroll;
    }
    .el-dropdown-menu__item {
      border-radius: 12px;
      &:hover {
        background-color: rgba(132, 182, 184, 0.08);
      }
    }
    .popper__arrow {
      display: none;
    }
  }
}

.extendlist {
  background: #F2F7F7 !important;
  width: 40px;

  .popper__arrow {
    border-bottom-color: #0000 !important;

    ::after {
      border-bottom-color: #F2F7F7 !important;
    }
  }
}

// 侧边导航栏收起时候的样式修改，由于没有对应的属性，全局修改了UI
.el-tooltip__popper {
  border-radius: 6px;
  font-size: 14px;
  line-height: 22px;
  padding: 5px 8px;

  &.is-dark {
    box-shadow: 0px 3px 14px 2px rgb(0 0 0 / 5%), 0px 8px 10px 1px rgb(0 0 0 / 6%), 0px 5px 5px -3px rgb(0 0 0 / 10%);
    background-color: rgb(102, 102, 102);
  }

  &[x-placement^="right"] {
    margin-left: 18px;

    .popper__arrow,
    .popper__arrow::after {
      border-right-color: rgb(102, 102, 102);
    }
  }
  &.sty1-tooltip {
    border: none;
    max-width: 310px;
    padding: 6px 10px;
    font-weight: 450px;
    font-size: 12px;
    line-height: 16px;
    border-color: transparent;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
    .popper__arrow {
      border-top-color: #FFFFFF !important;
    }
  }
  &.sty2-tooltip {
    border: none;
    max-width: 310px;
    padding: 6px 10px;
    font-weight: 450px;
    font-size: 12px;
    line-height: 16px;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
    background-color: #343434;
    color: #FFFFFF;
    .popper__arrow {
      border-top-color: #343434 !important;
      &::after {
        border-top-color: #343434 !important;
      }
    }
  }
  &.sty3-tooltip {
    border: none !important;
    max-width: 112px;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #FFFFFF;
    .popper__arrow {
      border-top-color: rgba(0, 0, 0, 0.8) !important;
      &::after {
        border-top-color: rgba(0, 0, 0, 0.8) !important;
      }
    }
  }
}

.el-menu--popup {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.12);
  border: 1px solid #CEDBDB;
  border-radius: 6px;
  background-color: #FFFFFF !important;
  min-width: 147px;
  width: 147px;
  padding: 6px;

  >.el-menu-item {
    background-color: #FFFFFF !important;
    padding: 0 8px !important;
    height: 32px;
    line-height: 32px;
    border-radius: 3px;

    &.is-active {
      background: linear-gradient(0deg, rgba(217, 246, 239, 0.2), rgba(217, 246, 239, 0.2)), rgba(255, 255, 255, 0.2) !important;
    }
  }

  >.el-menu-item:hover,
  >.el-menu-item:focus {
    background-color: #F7F7F7 !important;
  }

  &.el-menu--popup-right-start {
    margin-left: 12px;
  }
}

// el-button 
.el-button {
  &.gray {
    background-color: #F7F7F7;
  }

  &.green {
    background-color: #A9E1D3;
    &.is-disabled {
      background-color: #A9E1D3;
      color: rgba(0, 46, 51, 0.4);
    }
  }

  &.sty1-button {
    width: 90px;
    color: #002E33;
    border: none;
    height: 36px;
    font-weight: normal;
    line-height: 18px;
    padding: 9px;
  }
  &.sty2-button {
    width: 148px;
    height: 48px;
    background: #005563;
    color: #FFFFFF;
    font-weight: 700;
    font-size: 16px;
    border-radius: 26px;
    &.el-button.is-disabled, &.el-button.is-disabled:hover, &.el-button.is-disabled:focus {
      opacity: .5;
      background: #005563;
      color: #FFFFFF;
    }
  }
  &.sty3-button {
    width: 92px;
    height: 48px;
    border-radius: 26px;
    background: #EE69690F;
    color: #EE6969;
    border: none;
    font-weight: 700;
    // font-size: 14px;
  }
  &.sty4-button {
    width: 158px;
    height: 48px;
    border-radius: 26px;
    background: #F7F7F7;
    color: #33585C;
    border: none;
    font-weight: 700;
    // font-size: 14px;
  }
  &.fs-14 {
    font-size: 14px;
  }
  &.width-1 {
    width: 148px;
  }
  &.width-2 {
    width: 120px;
  }
  &.width-3 {
    width: 180px;
    height: 36px;
    font-weight: 500;
    font-size: 14px;
    line-height: 10px;
  }
  &.sty5-button {
    color: #002E33;
    border: none;
    background-color: #A9E1D3;
    border-radius: 26px;
    padding: 8px 20px;
    height: 36px;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #002E33;
    span {
      font-weight: 500;
    }
    &.is-disabled {
      opacity: 0.5;
    }
    &:hover {
      background-color: #A9E1D3;
      color: #002E33;
    }
  }
  &.sty6-button {
    width: 120px;
    height: 36px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    padding: 0 20px;
    margin-top: 22px;
  }
  &.sty7-button {
    width: fit-content;
    height: 36px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    padding: 0 20px;
    margin-top: 22px;
    margin: 0;
  }
  &.sty8-button {
    background: #8EDEE3;
    margin-right: 12px;
    border: none;
    color: #002E33;
  }
  &.sty9-button {
    background: #EE6969;
    border: none;
  }
  &.sty10-button {
    border: none;
    width: 160px;
    color: #005563;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
  }
  &.sty11-button {
    border: none;
    width: 120px;
    color: #005563;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
  }
  &.sty12-button {
    width: 100%;
    background: #F6F6F6;
    color: #002E33;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    border: none;
  }
  &.sty13-button {
    padding: 13px 16px;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }
}

    
.el-tooltip__popper.is-dark {
  background: #FFFFFF;
  color: #33585C;
  border-radius: 8px;
  padding: 6px 10px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin-bottom: 0px;
  &.account-detail-tootip {
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #FFFFFF;
    border-radius: 16px;
    &.el-tooltip__popper[x-placement^="bottom"] .popper__arrow {
      top: -5px;
    }
    .row + .row {
      margin-top: 10px;
    }
    &.authDocument {
      display: none;
    }
    &.showAuthDocument {
      width: 316px;
    }
  }
  &.account-detail-tootip2 {
    background: #FFFFFF;
    color: #33585C;
    box-shadow: 2px -2px 6px 2px rgba(0, 0, 0, 0.12);
    &.el-tooltip__popper[x-placement^="bottom"] .popper__arrow {
      border-bottom-color: #FFFFFF;
      &::after{
        border-bottom-color: #FFFFFF;
      }
    }
  }
}
.el-tooltip__popper[x-placement^="top"] .popper__arrow {
  border-top-color: #FFFFFF;
  &::after{
    border-top-color: #FFFFFF;
  }
}
.el-tooltip__popper {
  &.sty1-tooltip {
    // width: 165px;
    text-align: center;
    color: #33585C;
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    border-radius: 12px;
    background: #F0F6F6;
    padding: 8px;
    margin-bottom: 8px;
    box-shadow: -2px 2px 12px 0px rgba(0, 85, 99, 0.08);
    &[x-placement^="top"] .popper__arrow {
      border-top-color: #F0F6F6;
      &::after{
        border-top-color: #F0F6F6;
      }
    }
  }
}

// el-table
.el-table {
  &.sty1-table {
    font-weight: 500;
    .el-table__header {
      .el-table__cell {
        padding: 0 0 8px 0 !important;
        cursor: default !important;
      }
      .cell {
        font-size: 14px;
        line-height: 34px;
        color: #738C8F !important;
        font-weight: normal !important;
      }
    }
    .cell {
      font-size: 14px;
      line-height: 18px;
      color: #002E33;
      padding-left: 4px;
      padding-right: 4px;
    }
    &.el-table--border .el-table__cell:first-child .cell {
      padding-left: 4px;
    }
    th.el-table__cell.is-leaf, td.el-table__cell {
      border: none;
      padding: 12px 0;
      // cursor: pointer;
    }
    &.el-table--group, &.el-table--border {
      border: none;
    }
    &.el-table--group::after, &.el-table--border::after, &.el-table::before {
      background-color: transparent;
    }
    &.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background: linear-gradient(0deg, rgba(217, 246, 239, 0.2), rgba(217, 246, 239, 0.2)), rgba(255, 255, 255, 0.2);
    }
    .el-table__column-filter-trigger i {
      font-size: 16px;
      color: #99ABAD;
      font-weight: 600;
      margin-left: 4px;
    }
    .el-table__empty-block {
      display: none;
    }
    .frist-table-name {
      padding-left: 30px !important;
      position: relative;
    }
    .frist-table-name ::after {
      content: 'All';
      position: absolute;
      left: 6px;
    }
    .nation-sprites {
      width: 24px;
      height: 24px;
      background: url('~@/assets/img/nation_sprites2.png') no-repeat;
      background-size: 8466px;
      transform-origin: 0 40%;
      margin-right: 8px;
    }
    .citizenship {
      overflow: hidden; 
      text-overflow: ellipsis; 
      white-space: nowrap;
    }
    .el-checkbox__input.is-focus .el-checkbox__inner {
      border-color: #DCDFE6;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner {
      border-color: #002E33;
    }
  }
  &.sty1-table-loading {
    min-height: 462px;
  }
  &.sty2-table {
    font-weight: 500;
    .el-table__header {
      .el-table__cell {
        padding: 0 0 8px 0 !important;
        cursor: default !important;
      }
      .cell {
        font-size: 14px;
        line-height: 18px;
        color: #738C8F !important;
        font-weight: normal !important;
      }
    }
    .cell {
      font-size: 14px;
      line-height: 18px;
      color: #002E33;
      padding-left: 4px;
      padding-right: 4px;
    }
    &.el-table--border .el-table__cell:first-child .cell {
      padding-left: 4px;
    }
    th.el-table__cell.is-leaf, td.el-table__cell {
      border: none;
      padding: 7px 0;
      cursor: pointer;
    }
    &.el-table--group, &.el-table--border {
      border: none;
    }
    &.el-table--group::after, &.el-table--border::after, &.el-table::before {
      background-color: transparent;
    }
    &.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
      background: linear-gradient(0deg, rgba(217, 246, 239, 0.2), rgba(217, 246, 239, 0.2)), rgba(255, 255, 255, 0.2);
    }
    .el-table__column-filter-trigger i {
      font-size: 16px;
      color: #99ABAD;
      font-weight: 600;
      margin-left: 4px;
    }
    .el-table__empty-block {
      display: none;
    }
  }
  &.sty3-table {
    th.el-table__cell.is-leaf, td.el-table__cell {
      padding: 12px 0;
    }
    .el-table__header .el-table__cell {
      padding: 8px 0 14px 0 !important;
      cursor: default !important;
    }
  }
  &.sty4-table {
    .el-table__empty-block {
      display: flex;
      margin: 96px 0 146px;
      .el-table__empty-text {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.3);
        img {
          width: 140px;
          height: 140px;
        }
      }
    }
  }
  &.el-table th.el-table__cell > .cell {
    padding-left: 4px;
    padding-right: 4px;
  }
  &.sty5-table {
    border-color: #DFEBED;
    &::before, &::after {
      display: none;
    }
    .el-table__header {
      border-radius: 50% 0 0 0;
      th {
        padding: 0;
        border-color: #DFEBED;
      }
      .cell {
        color: #809799;
        font-size: 12px;
        font-weight: 500;
        padding: 8px 12px !important;
      }
    }
    .cell {
      background: #F7F7F7;
    }
    .el-table__row {
      td {
        border-color: #DFEBED;
        padding: 0;
      }
      .cell {
        padding: 15px 12px !important;
      }
    }
  }
  &.sty6-table {
    .el-table__header .el-table__cell{
      padding: 0 !important;
    }
    .el-table__header {
      .cell {
        color: #002E33 !important;
        font-size: 16px;
        font-weight: 500 !important;
        line-height: 24px;
      }
    }
    th.el-table__cell.is-leaf, td.el-table__cell {
      border: none;
      padding: 0;
    }
    .cell {
      word-break: normal;
      background: #F7F7F7;
      color: #33585C;
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      padding: 12px 0 12px 48px  !important;
      &:hover {
      }
    }
    .el-table__row {
      background: #F7F7F7;
      td + td {
        width: 30% !important;
        margin-left: 50px;
        .cell {
          padding: 12px 12px 12px 48px !important;
          // width: fit-content;
        }
      }
    }
  }
}

.el-pagination {
  &.sty1-pagination {
    padding: 0 5px;
    .btn-prev, .btn-next {
      margin: 0;
      min-width: 32px;
      background-color: transparent;
    }
    .el-pager li {
      margin: 0 8px;
      min-width: 32px;
      border-radius: 50%;
      background-color: transparent;
      height: 32px;
      line-height: 32px;
      &:not(.disabled).active {
        background-color: #E6EDED;
        color: #002E33;
      }
    }
  }
}

/* el-message */
.el-message {
  min-width: 200px;
  &.sty1-message {
    background-color: #738C8F;
    border: none;
    padding: 10px 16px;
    border-radius: 32px;
    .el-message__content {
      font-size: 14px;
      line-height: 16px;
      color: #FFFFFF;
    }
    i {
      color: #FFFFFF;
      font-size: 16px;
      margin-right: 4px;
    }
  }
  &.sty2-message {
    top: 80px !important;
    text-wrap: nowrap;
    background-color: #eaf8f8;
    .el-message__content {
      color: #002E33;
    }
    padding: 10px 48px;
  }
  &.sty3-message {
    top: 80px !important;
    text-wrap: nowrap;
    white-space:nowrap;
    background-color: rgba(238, 105, 105, 0.20);
    padding: 10px 48px;
    .el-message__content {
      color: #0B4B52;
    }
    i {
      color: #0B4B52;
    }
    &.white {
      background-color: #fff;
    }
  }
  &.sty4-message {
    min-width: auto;
    background-color: #F2F7F7;
    padding: 6px 12px;
    border-radius: 6px;
    &.supportDocuments i {
      font-size: 20px;
    }
    i {
      color: #005563;
      margin-right: 8px;
    }
    .el-message__content {
      font-weight: 500;
      font-size: 14px;
      line-height: 18px;
      color: #002E33;
    }
  }
  &.sty5-message {
    background-color: #F2F7F7;
    border-radius: 6px;
    &.is-center {
      justify-content: start;
    }
    .jumpLink {
      text-decoration: underline;
      cursor: pointer;
      color:#002E33;
    }
    .el-message__content {
      color: #002E33;
    }
    i {
      margin-right: 6px;
      color: #EE6969;
    }
    .el-message__content {
      text-wrap: nowrap;
      padding-right: 72px;
    }
    .el-icon-close {
      font-size: 24px;
      cursor: pointer;
      color: #002E33;
      margin: 0 0 0 72px;
      &::before {
        content: "\e614";
        font-family: 'iconfont';
      }
    }
    padding: 6px 12px;
  }
  &.sty6-message {
    padding: 12px 20px;
    .title {
      color: #EE6969;
      font-size: 16px;
      font-weight: 700;
      line-height: 20px;
      margin-bottom: 8px;
    }
    .content {
      color: #EE6969;
      font-size: 14px;
      font-weight: 400;
      line-height: 140%;
      display: none;
    }
    .el-icon-close {
      position: absolute;
      right: 12px;
      top: 24px;
      color: #EE6969;
      font-size: 20px;
    }
    .block {
      display: block;
    }
  }
}


.el-checkbox {
  &.sty1-checkbox {
    display: inline-table;
    align-items: center;
    color: #002e33;
    font-weight: 400;
    .el-checkbox__input {
      height: 15px;
      width: 15px;
    }
    .el-checkbox__input.is-focus .el-checkbox__inner {
      border-color: #DCDFE6;
      // box-sizing: border-box;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner {
      border-color: #005563;
      background-color: #005563;
      border: none;
    }

    .el-checkbox__inner::after {
      content: "\e608";
      font-family: 'color-iconfont';
      transform: scaleY(1);
      top: 0;
      left: 0;
      border: none;
      width: 14px;
      height: 14px;
      font-size: 14px;
      visibility: hidden;
    }

    .el-checkbox__input.is-checked .el-checkbox__inner::after {
      content: "\e608";
      font-family: 'color-iconfont';
      top: 0;
      left: 0;
      border: none;
      width: 14px;
      height: 14px;
      font-size: 14px;
      visibility: visible;
    }
    .el-checkbox__inner {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      // &::after {
      //   left: 5px;
      //   top: 2px;
      // }
    }
    .el-checkbox__label {
      padding-left: 6px;
    }

    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      border-color: #005563;
      background-color: #005563;
    }
    .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
  &.sty2-checkbox {
    margin-right: 20px;
    .el-checkbox__inner {
      border-color: #B3C0C2;
    }
    .is-checked > .el-checkbox__inner::after{
      display: none;
    }
    .is-checked > .el-checkbox__inner::before{
      color: #FFFFFF;
      font-family: "iconfont";
      content: "\e60d";
      transform: scale(0.4);
      display: block;
      height: 11px;
      margin-right: 8px;
    }
    .is-focus>.el-checkbox__inner{
      border-color: #B3C0C2;
    }
    .is-checked>.el-checkbox__inner{
      background-color: #005563;
      border-color: #005563;
    }
    .el-checkbox__label {
      padding-left: 8px;
  }
  }
}

.el-dialog {
  &.sty1-dialog {
    border-radius: 16px;
    .el-dialog__header {
      padding: 24px;
    }
    .el-dialog__body {
      padding: 0 24px 24px 24px;
    }
    .el-dialog__title {
      color: #002E33;
      font-size: 18px;
      font-weight: 700;
      line-height: 25px;
    }
    .el-dialog__headerbtn {
      top: 24px;
      right: 24px;
    }
    .el-dialog__close:before {
      font-family: "iconfont";
      content: "\e614";
      color: #738C8F;
      font-size: 20px;
    }
    .diaIcon {
      margin: 0 8px 0 0;
    }
  }
  &.sty3-dialog {
    width: 610px;
    height: calc(100% - 30vh);
    overflow-y: hidden;
    .el-dialog__body {
      height: calc(100% - 73px);
      padding-right: 14px;
      box-sizing: border-box;
    }
  }
  &.sty4-dialog {
    top:50%;
    left: 50%;
    margin: 0;
    margin-top: 0 !important;
    transform: translate(-50%,-50%);
  }
  &.sty5-dialog {
    div {
      color: #002E33;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
    }
    .datailsTitle {
      color: #738C8F;
      font-size: 14px;
      font-weight: 450;
      line-height: 18px;
      margin-bottom: 24px;
    }
    .kycDetailsList {
      margin: 24px 0;
    }
    .title {
      color: #738C8F;
      font-size: 14px;
      font-weight: 450;
      line-height: 18px;
    }
    .sty1 {
      min-width: 123px;
    }
    .sty2 {
      min-width: 83px;
    }
  }
  &.sty6-dialog {
    top:50%;
    left: 50%;
    margin: 0;
    margin-top: 0 !important;
    transform: translate(-50%,-50%);
    border-radius: 20px;
    .el-dialog__header {
      padding: 16px 24px;
    }
    .contractImg {
      width: 255px;
      height: 190px;
      object-fit: cover;
    }
    .el-dialog__body {
      padding: 20px 40px;
      .t1 {
        color: #000;
        font-size: 20px;
        font-weight: 700;
        line-height: 140%;
        text-align: center;
        margin: 20px 0;
      }
      .t2 {
        color: #33585C;
        font-size: 18px;
        font-weight: 400;
        text-align: center;
        margin-bottom: 12px;
      }
      .t3 {
        color: #809799;
        font-size: 18px;
        font-weight: 400;
        text-align: center;
      }
    }
    .el-dialog__footer {
      padding: 20px 40px 40px 40px;
    }
  }
}
.sty2-dialog {
  .el-dialog {
    top:50%;
    left: 50%;
    margin: 0;
    margin-top: 0 !important;
    transform: translate(-50%,-50%);
    width: 400px;
  }
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    padding: 24px 24px 0 24px !important;
  }
  .el-dialog__headerbtn:focus .el-dialog__close, .el-dialog__headerbtn:hover .el-dialog__close {
    color: #738C8F !important;
  }
}
.sty3-dialog {
  .el-dialog {
    border-radius: 20px; 
  }
  .el-dialog__header {
    padding-top: 68px;
    color: #000;
    text-align: center;
    font-size: 24px;
    font-weight: 700;
    line-height: 140%;
    padding-bottom: 0px;
    .mico-drawer-close {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.5);
      position: absolute;
      top: 16px;
      right: 24px;
      cursor: pointer;
    }
    .mico-Fallback {
      font-size: 24px;
      color: rgba(0, 0, 0, 0.5);
      position: absolute;
      top: 16px;
      left: 24px;
      cursor: pointer;
    }
  }
  .el-dialog__body {
    padding: 80px 0 48px;
  }
}
.sty4-dialog {
  .el-dialog__body {
    padding-top: 0px;
    padding-bottom: 140px;
  }
}
.sty5-dialog {
  .el-dialog__body {
    padding-bottom: 85px;
  }
}

@media screen and (max-width: 1200px) {
  .el-menu {
    &.sty1-menu {
      width: 100%;

      .el-menu-item {
        position: relative;
        height: 44px;
        line-height: 44px;
        i {
          font-size: 28px;
          line-height: 44px;
          margin: auto;
        }
        span {
          display: none;
        }
      }
      .el-submenu__title {
        span {
          display: none;
        }
      }

      .el-tooltip {
        text-align: center;
      }
    }
  }

  .el-submenu {
    &.sty1-submenu {
      .el-submenu__title {
        i {
          font-size: 32px;
          line-height: 44px;
          margin: auto;
        }
      }
    }
  }
}

.el-tabs {
  &.sty1-tabs {
    .el-tabs__header {
      background-color: rgba(132, 182, 184, 0.12);
      border-radius: 33px;
      height: 25px;
      margin: 0;
    }
    .el-tabs__item {
      padding: 4px 20px !important;
      height: 25px;
      color: #738C8F;
      font-size: 12px;
      font-weight: 500;
      line-height: 140%;
      border-radius: 33px;
    }
    .el-tabs__item + .el-tabs__item {
      margin-left: 0px;
    }
    .el-tabs__item.is-active {
      color: #002E33;
      position: relative;
      background: #A9E1D3;
    }
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__active-bar {
      display: none;
    }
  }
}

.el-cascader-panel {
  &.sty1-cascader {
    border: none;
    .el-scrollbar__wrap {
      margin-bottom: 0 !important;
      margin-right: 0 !important;
    }
    .el-cascader-menu + .el-cascader-menu {
      margin-left: 8px;
    }
    .el-cascader-menu {
      border: 1px solid #CEDBDB;
      border-radius: 12px;
      box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.12);
    }
    .el-cascader-menu:nth-child(1) {
      // height: 118px;
    }
    .el-cascader-menu:nth-child(2) {
      width: 220px;
    }
    .el-cascader-node__prefix {
      display: none;
    }
    .el-cascader-menu__list {
      padding: 6px 0 0 6px;
    }
    
    .el-cascader-node {
      padding: 0 0 0 8px;
      height: 32px;
      line-height: 32px;
      border-radius: 3px;
      color: #002E33;
      font-weight: 400;
    }
    .el-cascader-menu__list :nth-child(2) {
      // padding: 0 8px;
      // background: red;
    }
    .el-cascader-node__label {
      padding: 0;
    }
    .el-cascader-node:not(.is-disabled):hover, .el-cascader-node:not(.is-disabled):focus {
      background-color: #F7F7F7;
    }
    .el-cascader-node.in-active-path, .el-cascader-node.is-selectable.in-checked-path, .el-cascader-node.is-active {
      background: var(--6, linear-gradient(0deg, rgba(217, 246, 239, 0.20) 0%, rgba(217, 246, 239, 0.20) 100%), rgba(255, 255, 255, 0.20));
      color: #005563;
    }
  }
  &.sty2-cascader {
    
  }
}
.el-collapse {
  &.igCollapse {
    border-top: none;
    border-bottom: none;
    margin-top: 32px;
    padding-bottom: 32px;
    .el-collapse-item__header {
      display: block;
      height: auto;
      border: none;
    }
    .el-collapse-item__arrow {
      display: none;
    }
    .el-collapse-item__wrap {
      border: none;
    }
    .el-collapse-item__content {
      padding-bottom: 0px;
    }
  }
  &.sty1-collapse {
    border: none;
    .el-collapse-item__header {
      height: 22px;
      line-height: 22px;
      border: none;
    }
    .el-collapse-item__content {
      padding: 0;
      line-height: 0;
    }
    .el-collapse-item__wrap {
      border: none;
    }
  }
  &.sty2-collapse {
    .el-collapse-item__wrap {
      background-color: #141414;
    }
    .el-collapse-item__header {
      background-color: #141414;
      color: #FFFFFF;
    }
  }
  &.sty3-collapse {
    border: none;
    margin-top: 24px;
    .el-collapse-item {
    }
    .el-collapse-item__header {
      line-height: 22px;
      height: 22px;
    }
    .compress {
      margin-top: 20px;
    }
    .collapseReport {
      // &:hover {
      //   background-color: skyblue;
      // }
      i {
        font-size: 24px;
        margin-right: 6px;
      }
      div {
        min-width: 33%;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #33585C;
        margin-right: 88px;
        text-wrap: nowrap;
        span {
          font-size: 16px;
          font-weight: 700;
          line-height: 22px;
          text-wrap: nowrap;
        }
      }
      .view {
        color: #33585C;
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
      }
      .toKYT {
        cursor: pointer;
      }
      .more {
        color: #33585C;
        margin-left: 4px;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        text-decoration: underline;
      }
    }
    .el-collapse-item__wrap {
      background-color: transparent;
      border: none;
    }
    .el-collapse-item__content {
      padding: 0;
    }
    .el-collapse-item__header {
      background-color: transparent;
      border: none;
    }
  }
  &.sty4-collapse {
    .error {
      color: #002E33;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
    }
    i {
      margin-right: 4px;
      color: #99ABAD;
      font-size: 12px;
      transform: rotate(270deg);
    }
  }
  &.isDisabled {
    .el-collapse-item__header{
      cursor: default;
    }
    .el-collapse-item__arrow {
      display: none;
    }
  }
}
