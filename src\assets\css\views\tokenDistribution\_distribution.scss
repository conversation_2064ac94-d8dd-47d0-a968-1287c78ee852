.dropdown {
  width: 200px;
  padding: 8px 12px;
  border-radius: 58px;
  background: rgba(132, 182, 184, 0.12);
  color: #002E33;
  font-size: 14px;
  font-weight: 500;
  line-height: 140%;
  cursor: pointer;
  .mico-arrow-bottom {
    margin-left: 8px;
  }
  position: relative;
  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 58px;
    background: transparent;
    top: 0;
    left: 0;
    z-index: 1;
  }
}
.chainText {
  margin-left: 8px;
}
.guideline {
  border-radius: 48px;
  background: #8EDEE3;
  color: #002E33;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  padding: 4px 12px;
  cursor: pointer;
  margin-left: 8px;
  svg {
    margin-left: 4px;
  }
}
.t1 {
  color: #002E33;
  font-size: 16px;
  font-weight: 700;
  line-height: 140%;
  padding: 12px 16px;
}
.selectNetwork {
  width: 288px;
  position: relative;
  bottom: -12px;
  border-radius: 16px;
  border: 1px solid rgba(132, 182, 184, 0.12);
  background: #FFF;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);
  li {
    cursor: pointer;
    padding: 12px 16px;
    color: #002E33;
    font-size: 14px;
    font-weight: 500;
    line-height: 140%;
    img {
      margin-right: 8px;
    }
  }
}
