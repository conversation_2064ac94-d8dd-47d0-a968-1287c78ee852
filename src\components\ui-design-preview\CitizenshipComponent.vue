<template>
    <div>
    <div class="bg" @touchmove.prevent>
      <div ref="ndContain" id="ndContain" class="ndContain" @touchmove.prevent>
        <header class="mui-fl-central sty1-header">
          <i class="popico-back header-back" />
          Proof-of-Citizenship (PoC)
        </header>
        <div class="mui-fl-vert step-box">
          <i class="popico-globe-alt mui-fl-central icon" />
          <p class="c1">Nationality (1/3)</p>
        </div>
        <h2 class="t1">
          Select Citizenship
        </h2>
        <p class="c2">
          Please ensure your Citizenship matches your valid ID. Your privileges could change based on the selection.
        </p>
        <div class="country-box">
          <div class="mui-fl-vert select-box popup-select">
            <div class="mui-fl-vert mui-fl-1">
              <UsaCircularSvg class="counrty" :themeColor1="themeColor1" />
              <span class="placeholder">Select Country</span>
            </div>
            <CaretDownSvg :themeColor1="themeColor1" />
          </div>
        </div>
        <div class="mui-fl-central sty1-footer">
          <m-button class="width-3" disabled type="primary" round size="large">Continue</m-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { CaretDownSvg, UsaCircularSvg } from '@/components/ui-design-preview'
export default {
  name: 'CitizenshipComponent',
  components: { UsaCircularSvg, CaretDownSvg },
  props: {
    themeColor1: {
      type: String,
      required: true,
      default: () => {
        return '#005563'
      }
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/ui-design-preview/_citizenship.scss" scoped></style>
