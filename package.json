{"name": "zkme-web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "serve-dev": "vue-cli-service serve --mode serve.dev", "serve-test": "vue-cli-service serve --mode serve.test", "build": "vue-cli-service build --modern", "build-dev": "vue-cli-service build --modern --mode build.dev", "build-test": "vue-cli-service build --modern --mode build.test", "lint": "vue-cli-service lint"}, "dependencies": {"@ant-design/colors": "^7.0.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@cosmjs/proto-signing": "^0.31.1", "@ton/core": "^0.59.0", "@ton/crypto": "^3.3.0", "@ton/ton": "^15.1.0", "aptos": "^1.20.0", "axios": "^1.1.3", "babel-plugin-component": "^1.1.1", "bignumber.js": "^9.1.1", "buffer": "^6.0.3", "core-js": "^3.8.3", "crypto-browserify": "^3.12.1", "crypto-js": "^4.1.1", "echarts": "^5.4.0", "element-ui": "^2.15.12", "ethers": "^6.14.1", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "mipd": "^0.0.7", "postcss": "^8.4.31", "postcss-nested": "^6.0.1", "sjcl": "^1.0.8", "stream-browserify": "^3.0.0", "vue": "^2.6.14", "vue-router": "^3.2.0", "vuex": "^3.4.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~4.5.13", "@vue/cli-plugin-vuex": "~4.5.13", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}