.noAccess {
  padding: 0 120px;
  .n {
    font-weight: 700;
    font-size: 24px;
    line-height: 32px;
    color: #002E33;
  }
  .e {
    margin: 16px 0 32px;
  }
  .b {
    border-radius: 22px;
    background-color: #F7F7F7;
    padding: 20px 40px;
    width: 100%;
    .apis {
      font-weight: 700;
      font-size: 20px;
      line-height: 28px;
    }
    .zkKyc {
      font-size: 16px;
      font-weight: 400;
      line-height: 22px;
      color: #002E33;
    }
    .p {
      margin: 20px 0 12px;
    }
  }
}
.hand{
  height: 64px;
  margin-bottom: 40px;
  font-weight: 700;
  font-size: 22px;
  line-height: 32px;
  :first-child {
    margin-right: 40px;
  }
  .text {
    cursor: pointer;
    position: relative;
  }
  .hf {
    cursor: not-allowed;
    color: #B3C0C2;
    &::after {
      opacity: 0;
    }
  }
  .text::after {
    content: '';
    position: absolute;
    height: 2px;
    width: 88px;
    background-color: #002E33;
    bottom: -4px;
    left: 15px;
  }
}
.title {
  color: #002E33;
  font-weight: 700;
  font-size: 24px;
  line-height: 32px;
}
.content {
  padding: 16px 20px;
  border-radius: 12px;
  background-color: #F7F7F7;
  i {
    font-size: 24px;
    color: #738C8F;
    margin-right: 6px;
  }
  .mico-copy {
    padding-top: 2px;
    font-size: 20px;
    color: #33585C;
  }
  .apd, .api {
    white-space: nowrap;
    color: #738C8F;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    margin-right: 25px;
  }
  .apn {
    color: #33585C;
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    margin-right: 30px;
  }
  .apb {
    border-radius: 45px;
    background-color: #005563;
    border: none;
    color: #FFFFFF;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    padding: 7px 12px;
  }
  .generated {
    margin-right: 24px;
  }
  .reset {
    cursor: pointer;
    background-color: #82F5E7;
    border-radius: 43px;
    padding: 4px 8px;
    i {
      font-size: 16px;
    }
    .apt {
      color: #005563;
      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
    }
  }
}
.tip {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #738C8F;
  margin: 8px 0 28px;
  .mico-a-apitip {
    font-size: 24px;
    margin-right: 8px;
  }
}
.authority {
  border-radius: 12px;
  // padding: 20px 24px;
  // border: 1px solid #F0F0F0;
  margin-top: 32px;
  div + div {
    margin-top: 13px;
  }
  .radio {
    border-radius: 50%;
    border: 1px #B3C0C2 solid;
    width: 18px;
    height: 18px;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: #B3C0C2;
      border-radius: 50%;
      width: 12px;
      height: 12px;
    }
  }
  .meName {
    color: #002E33;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    margin-right: 16px;
  }
  .meState {
    color:#738C8F;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    background-color: #CEDBDB;
    border-radius: 43px;
    padding: 2px 16px;
  }
  .kmVerify {
    .radio {
      border-color: #6CCC81;
      &::after {
        background-color: #6CCC81;
      }
    }
    .meState {
      color: #6CCC81;
      background-color: rgba(119, 229, 143, 0.2);
    }
  }
}
.zkmeApi {
  border-bottom: 2px solid rgba(0, 46, 51, 0.1);
}
.contact {
  font-weight: 500;
  font-size: 18px;
  line-height: 25px;
  color: #738C8F;
  margin-top: 32px;
  .mico-a-apiemail {
    font-size: 24px;
    margin-right: 4px;
  }
  .email {
    margin-left: 4px;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    color: #33585C;
  }
}
.igcon {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  .mico-a-apiemail {
    font-size: 16px;
  }
  .email {
    font-size: 16px;
    -ms-text-kashida-space: 22px;
  }
  
}
.ct {
  font-weight: 700;
  font-size: 24px;
  line-height: 32px;
  .mico-fold {
    margin-left: 8px;
  }
  .rt {
    transform: rotate(180deg);
  }
}
.apiKeycla {
  max-width: 820px;
  margin: 0 auto;
  padding: 24px;
  border-radius: 16px;
  border: 1px solid rgba(0, 46, 51, 0.1);
}
.tipWarning {
  color: #EE6969;
  font-weight: normal;
  border-radius: 12px;
  border: 1px solid rgba(238, 105, 105, 0.60);
  background: rgba(238, 105, 105, 0.10);
  padding: 16px;
  margin-top: 24px;
  .mico-warning {
    margin-right: 8px;
    font-size: 16px;
    color: #EE6969;
    line-height: 22px;
  }
  .tipText {
    .tipHand {
      font-weight: 700;
    }
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
  }
}
.watitdb {
  opacity: 0.2;
  cursor: not-allowed;
}
.mico-copy  {
  margin-left: 4px;
  cursor: pointer;
}
