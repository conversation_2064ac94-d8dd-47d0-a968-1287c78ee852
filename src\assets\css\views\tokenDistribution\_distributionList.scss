.title1 {
  color: #002E33;
  font-size: 28px;
  font-weight: 700;
  margin: 16px 0 8px;
  text-align: center;
}
.title2 {
  color: #33585C;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  margin-bottom: 40px;
}
.title3 {
  color: #002E33;
  font-size: 16px;
  font-weight: 500;
}
.title4 {
  color: #ABB8BA;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin: 8px 0 24px;
}
.title {
  color: #002E33;
  font-size: 16px;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 12px;
  .t1 {
    color: #809799;
  }
}
.listBox {
  display: grid;
  grid-gap: 20px;
  .list {
    grid-gap: 20px;
    cursor: pointer;
    // height: 96px;
    border-radius: 12px;
    border: 1px solid rgba(169, 225, 211, 0.60);
    background: rgba(132, 182, 184, 0.12);
    padding: 20px 32px;
    .startTime {
      margin-right: 12px;
    }
    .link {
      text-decoration: underline;
    }
    .name {
      color: #33585C;
      font-size: 20px;
      font-weight: 700;
      span {
        margin-right: 8px;
      }
    }
    .t2 {
      color: #809799;
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      margin-bottom: 3px;
      white-space: nowrap;
    }
    .t3 {
      color: #002E33;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      white-space: nowrap;
    }
    .t4 {
      color: #002E33;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
.noToken {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  img {
    width: 192px;
    height: 120px;
    object-fit: cover;
    margin-bottom: 24px;
  }
}
.Draft, .Upcoming, .Active, .Completed {
  height: 14px;
  background: #C7EFF1;
  padding: 4px 8px;
  border-radius: 8px;
  color: #33585C;
  font-size: 12px;
  font-weight: 400;
  svg {
    margin-right: 4px;
  }
}
.Upcoming {
  background: #88F0DA;
  color: #002E33;
}
.Active {
  background: #005563;
  color: #FFFFFF;
}
.Completed {
  background: #C7EFF1;
  color: #002E33;
  opacity: 0.5;
}
