<template>
  <div
    :class="[
      (mode === 1 || (mode !== 0 && themeMode === 'dark')) && 'modeDark',
      'previewMode',
    ]"
  >
    <div class="sty2-cell ui_bg mui-fl-btw" style="gap: 80px;">
      <div class="mui-fl-col">
        <div class="t1 mgt16">Create Token Distribution</div>
        <div class="ui_selector ui_mgt_20">
          <div class="mode_choose mui-fl-hori mode_choose1">
            <div
              :class="['mui-fl-central', !changemode && 'select']"
              @click="selectMode(0)"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="17"
                height="16"
                viewBox="0 0 17 16"
                fill="none"
              >
                <path
                  d="M8.5 2V2.66667M8.5 13.3333V14M3.16667 8H2.5M4.70941 4.20941L4.16667 3.66667M12.2906
                4.20941L12.8333 3.66667M4.70941 11.7933L4.16667 12.3334M12.2906 11.7933L12.8333
                12.3334M14.5 8H13.8333M11.1667 8C11.1667 9.47276 9.97276 10.6667 8.5
                10.6667C7.02724 10.6667 5.83333 9.47276 5.83333 8C5.83333 6.52724
                7.02724 5.33333 8.5 5.33333C9.97276 5.33333 11.1667 6.52724 11.1667 8Z"
                  stroke="white"
                  stroke-width="1.33333"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              Light mode
            </div>
            <div
              :class="['mui-fl-central', changemode === 1 && 'select']"
              @click="selectMode(1)"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="17"
                height="16"
                viewBox="0 0 17 16"
                fill="none"
              >
                <path
                  d="M9.16667 4V2M12.8333 8V4.66667M10.1667 3H8.16667M14.5 6.33333H11.1667M10.8698
                11.2101C11.6886 11.2101 12.4662 11.0337 13.1667 10.7169C12.2911 12.6529 10.3428 14
                8.0799 14C4.99821 14 2.5 11.5018 2.5 8.4201C2.5 6.15717 3.84707 4.20887 5.78308
                3.33333C5.4663 4.03381 5.28995 4.81139 5.28995 5.63015C5.28995 8.71185 7.78815
                11.2101 10.8698 11.2101Z"
                  :stroke="changemode === 1 ? '#FFFFFF' : '#002E33'"
                  stroke-width="1.33333"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              Dark mode
            </div>
          </div>
        </div>
        <div class="ui_selector ui_mgt_14">
          <div class="ui_text ui_mgb_16">Set your theme color</div>
          <div class="mui-fl-wrap mui-fl-start" style="max-width: 396px">
            <div
              v-for="(color, index) of mode === 0 || themeMode !== 'dark'
                ? colorsOp
                : darkColorsOp"
              :key="index"
              class="ui_color"
              @click="selectColor(color, index)"
            >
              <div
                class="ui_color_hand"
                :style="{ background: color[0] }"
              ></div>
              <div class="mui-flex">
                <div
                  class="ui_color_Lfoot"
                  :style="{ background: color[1] }"
                ></div>
                <div
                  class="ui_color_Rfoot"
                  :style="{ background: color[2] }"
                ></div>
              </div>
              <i v-show="indexColor === index" class="mcico-ui-success"></i>
            </div>
            <div class="ui_color ui_color_design mui-fl-central">
              <i v-show="indexColor === 'custom'" class="mcico-ui-success"></i>
              <i class="mico-drawing"></i>
              <div class="color_picker">
                <m-color-picker
                  ref="colorPicker"
                  v-model="colorPicker"
                  @active-change="colorChange"
                  :popper-class="
                    mode === 1 || (mode !== 0 && themeMode === 'dark')
                      ? 'sty2-ColorPicker'
                      : 'sty1-ColorPicker'
                  "
                  :show-alpha="false"
                  :predefine="predefineColors"
                >
                </m-color-picker>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        :class="[
          (mode === 1 || (mode !== 0 && themeMode === 'dark')) && 'dark',
          'preview_con',
          'mui-fl-col',
          'mui-fl-central',
          'mui-fl-1',
        ]"
      >
        <div class="mui-fl-vert ui_mode">
          <div class="mode_choose mui-fl-btw mui-fl-vert">
            <div
              :class="[
                'mui-fl-1',
                'mui-fl-central',
                previewMode === 'web' && 'select',
              ]"
              @click="selectPreviewMode('web')"
            >
              <i class="mico-monitor-alt"></i>PC
            </div>
            <div
              :class="[
                'mui-fl-1',
                'mui-fl-central',
                previewMode === 'mobile' && 'select',
              ]"
              @click="selectPreviewMode('mobile')"
            >
              <i class="mico-mobile-alt"></i>Mobile
            </div>
          </div>
        </div>
        <DistributionTocPage :changemode="changemode" :selectColorData="selectColorDataList" :previewMode="previewMode"></DistributionTocPage>
      </div>
    </div>
  </div>
</template>
<script>
import { generate } from '@ant-design/colors'
import DistributionTocPage from './distributionTocPage.vue'
export default {
  components: { DistributionTocPage },
  data () {
    return {
      colors: [
        '#005563',
        '#0057FF',
        '#0082FF',
        '#00A8EF',
        '#14BCD4',
        '#00D29F',
        '#00CB49',
        '#C0E725',
        '#E6C832',
        '#FFB800',
        '#df9143',
        '#FF6800',
        '#A65529',
        '#FF339B',
        '#EB35F7',
        '#A14FFF',
        '#5A46F9'
      ],
      colorsOp: [],
      darkColorsOp: [],
      themeMode: 'light',
      mode: 0,
      changemode: 0,
      preivewStep: 1,
      previewMode: 'web',
      colorPicker: null,
      predefineColors: [
        '#005563',
        '#0057FF',
        '#0082FF',
        '#00A8EF',
        '#14BCD4',
        '#00D29F',
        '#00CB49',
        '#C0E725',
        '#E6C832',
        '#FFB800',
        '#DF9E63',
        '#FF6800',
        '#A65529',
        '#FF339B',
        '#EB35F7',
        '#A14FFF',
        '#5A46F9'
      ],

      hexColor: '',
      indexColor: '',
      selectColorData: [[], []],
      selectColorDataList: '',
      tempColor: '',
      keepColor: false,
      warningtip: false,
      confirmReset: false,
      localStorageColor: JSON.parse(localStorage.getItem('localStorageColor')),
      cssTextarea: ''
    }
  },
  computed: {
    userName () {
      return this.$store.state.auth.user.name
    },
    detail () {
      return this.$store.state.distribution.detail
    }
  },
  async created () {
    this.classification()
  },
  mounted () {
    this.selectColor(this.colors[0], 0)
  },
  methods: {
    async checkForm () {
      const rp = await this.$api.request(
        'disctrbution.putTdClaimPageUiConfig',
        {
          id: this.detail.onlyConfigId,
          configId: this.detail.configId,
          // planId: this.detail.planId,
          darkColor: this.selectColorData[0],
          lightColor: this.selectColorData[1]
          // secondaryColor: '#ffffff',
          // themeColor: '#000000',
          // buttonColor: '#000000',
          // fontColor: '#000000'
        },
        {},
        'distribution'
      )
      console.log(rp)
      return await this.$store.dispatch('putDistributionInfo', { step: 6 })
      // return true
    },
    classification () {
      this.colors.forEach((v) => {
        const color = this.colorSelect(v)
        this.colorsOp.push([color[5], color[2], color[0]])
        const darkcolor = this.colorSelect(v, 'dark')
        this.darkColorsOp.push([darkcolor[5], darkcolor[2], darkcolor[1]])
      })
    },
    colorSelect (color, mode = 'default') {
      return generate(color, {
        theme: mode
      })
    },
    selectMode (mode) {
      this.changemode = mode
      this.mode = mode
    },
    selectPreviewMode (mode) {
      this.previewMode = mode
    },
    componentToHex (val) {
      var hex = val.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    },
    colorChange (val) {
      var matchColors = /rgb\((\d{1,3}), (\d{1,3}), (\d{1,3})\)/
      var match = matchColors.exec(val)
      if (match !== null) {
        const hex =
          '#' +
          this.componentToHex(Number(match[1])) +
          this.componentToHex(Number(match[2])) +
          this.componentToHex(Number(match[3]))
        const color = this.colorSelect(hex)
        const darkcolor = this.colorSelect(hex, 'dark')
        const selectColorData = [
          [
            color[5],
            color[2],
            color[0]
          ],
          [
            darkcolor[5],
            darkcolor[3],
            darkcolor[1]
          ]
        ]
        this.indexColor = 'custom'
        this.selectColorDataList = selectColorData.toString()
      }
    },
    selectColor (val, index) {
      this.indexColor = index
      if (typeof index === 'number') {
        this.selectColorData = [
          [
            this.colorsOp[index][0],
            this.colorsOp[index][1],
            this.colorsOp[index][2]
          ],
          [
            this.darkColorsOp[index][0],
            this.darkColorsOp[index][1],
            this.darkColorsOp[index][2]
          ]
        ]
        this.selectColorDataList = this.colorsOp[index].concat(this.darkColorsOp[index]).toString()
      }
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/tokenDistribution/_distributionUIDesignStep5.scss" scoped />
