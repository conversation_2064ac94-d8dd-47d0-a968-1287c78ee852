
.web {
  width: 100%;
  min-width: 920px;
  // padding: 12px 34px;
  background: #FFFF;
  border-radius: 16px;
}
.block1 {
  border-radius: 40px;
  background: rgba(132, 182, 184, 0.12);
  padding: 6px 14px;
}
.block2 {
  border-radius: 14px;
  background: #F0F6F6;
  padding: 18px;
}
.symbolUrl {
  width: 385px;
  // height: 171px;
  border-radius: 14px;
  object-fit: contain;
}
.default {
  width: 26px;
  height: 26px;
}
.kaiaImg {
  width: 22px;
  height: 22px;
  margin-right: 4px;
}
.kaiaImg1 {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}
.logo {
  gap: 4px;
  i {
    font-size: 22px;
    color: rgba(0, 85, 99, 0.4);
  }
}
.progressBar {
  width: 100%;
  height: 6px;
  border-radius: 2.843px;
  background: linear-gradient(90deg, #00ADC9 0%, #005563 100%);
  background: rgba(0, 85, 99, 0.08);
  position: relative;
  margin: 6px 0;
  .progress {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: calc(70% + 9px);
    height: 100%;
    border-radius: 2.843px;
    background: linear-gradient(90deg, #00ADC9 0%, #005563 100%);
  }
  img {
    z-index: 2;
    width: 18px;
    height: 18px;
    position: absolute;
    top: 50%;
    left: 70%;
    transform: translateY(-50%);
  }
}
.countdownBox {
  // position: relative;
  height: 71px;
  background: rgba(0, 46, 51, 0.12);
  width: calc(100% - 9px);
  border-radius: 10px;
  box-shadow: 0px 1.421px 5.685px 0px rgba(0, 0, 0, 0.12);
}
.countdown {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 8px;
  background: linear-gradient(179deg, #E3FFFC 1.24%, #A9E9ED 98.77%);
  height: 71px;
  width: calc(100% - 12px);
  height: calc(100% - 4px);
  .countdownAfter {
    content: '';
    position: absolute;
    width: 100%;
    height: 50%;
    border-radius:0 0 8px 8px;
    // background: red;
    background: linear-gradient(0deg, #C7F7F4 0%, rgba(255, 255, 255, 0.6) 100%);
    // top: 0;
    left: 0;
    bottom: 0;
    z-index: 2;
  }
  .time {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    color: #002E33;
    font-size: 34px;
    font-weight: 700;
    letter-spacing: 2.729px;
  }
  
  .active {
    position: absolute;
    top: 12px;
    left: 12px;
    z-index: 2;
    color: #002E33;
    font-size: 7px;
    font-weight: 500;
    border-radius: 5.685px;
    background: #8EDEE3;
    padding: 3px 6px;
    .point {
      width: 5px;
      height: 5px;
      margin-right: 3px;
      border-radius: 50%;
      background: #005563;
    }
  }
}
.countdownBox1, .countdownBox2 {
  position: absolute;
  width: 3px;
  height: 12px;
  border-radius: 22px;
  opacity: 0.3;
  background: rgba(0, 0, 0, 0.3);
  top: 50%;
  transform: translateY(-50%);
}
.countdownBox1 {
  left: 0;
}
.countdownBox2 {
  right: 0;
}
.t1 {
  color: #002E33;
  font-size: 18px;
  font-weight: 700;
  line-height: 22px;
}
.t2 {
  color: #002E33;
  font-size: 10px;
  font-weight: 500;
  line-height: 140%;
}
.t3 {
  color: #002E33;
  font-size: 12px;
  font-weight: 500;
}
.t4 {
  color: #FFF;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
}
.t5 {
  color: #002E33;
  font-size: 34px;
  font-weight: 700;
  letter-spacing: 2.729px;
}
.t6 {
  color: #002E33;
  font-size: 12px;
  font-weight: 700;
}
.t7 {
  color: #002E33;
  font-size: 22px;
  font-weight: 700;
}
.t8 {
  color: #809799;
  font-size: 18px;
  font-weight: 700;
}
.t9 {
  color: #33585C;
  font-size: 10px;
  font-weight: 400;
}
.t10 {
  color: #002E33;
  font-size: 12px;
  font-weight: 700;
}
.t11 {
  color: #809799;
  font-size: 10px;
  font-weight: 400;
}
.t12 {
  color: #000;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.t13 {
  color: #000;
  font-size: 28px;
  font-weight: 700;
  line-height: 32px;
}
.mgt-6 {
  margin-top: 6px;
}
.mgt-12 {
  margin-top: 12px;
}
.mgt-14 {
  margin-top: 14px;
}
.mgt-20 {
  margin-top: 20px;
}
.mgt-24 {
  margin-top: 24px;
}
.mgt-28 {
  margin-top: 28px;
}
.mgt-32 {
  margin-top: 32px;
}
.mgt-40 {
  margin-top: 40px;
}
.mgt-48 {
  margin-top: 48px;
}
.mgb-12 {
  margin-bottom: 12px;
}
.mgb-20 {
  margin-bottom: 20px;
}
.mgb-24 {
  margin-bottom: 24px;
}
.mgb-32 {
  margin-bottom: 32px;
}
.mgb-40 {
  margin-bottom: 40px;
}
.mgb-48 {
  margin-bottom: 48px;
}
@media screen and (max-width: 920px) {
}
