<template>
  <div class="setting">
    <div class="t1">Create Token Distribution</div>
    <div class="t2">Select your zkKYC Configuration</div>
    <div class="t3">Reminder: Please ensure that the block chain selected for zkKYC is the same as the block chain on
      which the selected token resides.</div>
    <ul class="list">
      <li v-for="(data, index) of list" :key="index" @click="expandDetails(data, index)" :class="[data.flag && 'avtive']">
        <div class="mui-fl-btw">
          <div>{{ data.name }}</div>
          <div class="mui-fl-hori">
            <img style="width: 24px; height: 24px;" src="@/assets/img/networkicon/moca.svg" alt="">
            <span class="coin">{{ data.coin }}</span>
            <m-checkbox v-model="data.flag" style="pointer-events: none;" class="sty1-checkbox"></m-checkbox>
          </div>
        </div>
      </li>
    </ul>
    <div :class="[!list.length ? 'mui-fl-hori' : 'mui-fl-end']">
      <m-button class="sty2-button sty7-button" @click=createKyc>Go to Create a new zkKYC Configuration</m-button>
    </div>
    <drawer-component v-if="drawerVisible" ref="drawerComponent" :drawerVisible="drawerVisible" :id="drawerDetails.id" :createTime="drawerDetails.createTime"
      :has-applied-program="true" type="distribution" @close="close" @update="updateList">
    </drawer-component>
  </div>
</template>

<script>
import DrawerComponent from '@/views/zkKyc/components/DrawerComponent.vue'

export default {
  components: { DrawerComponent },
  data () {
    return {
      drawerVisible: false,
      drawerDetails: {
        id: 0,
        createTime: 0,
        hasAppliedProgram: true
      },
      list: []
    }
  },
  computed: {
    details () {
      return this.$store.state.distribution.detail
    }
  },
  watch: {
  },
  created () {
    this.queryUserKycList()
  },
  methods: {
    async checkForm () {
      if (!this.drawerDetails.id) {
        return false
      }
      const kycProgramId = this.details.kycProgramId === this.list.filter(x => x.flag)[0].kycProgramId
      if (this.details.step >= 4 && kycProgramId) {
        return true
      }
      if (this.drawerDetails.id && !kycProgramId) {
        return await this.$store.dispatch('putDistributionInfo', { kycProgramId: this.list.filter(x => x.flag)[0].kycProgramId, step: 5 })
      } else {
        return true
      }
    },
    async queryUserKycList () {
      const data = {
        auditedUser: '',
        filterHighestLevel: '',
        hideUnapplied: 0,
        highestLevel: '',
        items: '',
        pageReq: {
          page: 1,
          size: 100
        },
        pinCurrentApply: 1,
        programName: '',
        time: 0
      }
      const rp = await this.$api.request('kyc.queryUserKycList', data)

      const kycProgramIds = {
        kycProgramIds: rp.data.list.map(x => x.kycProgramId)
      }
      const chainRp = await this.$api.request('kyc.querySupportChainByProgramId', kycProgramIds)

      const filterList = chainRp.data.filter(x => x.chainName === 'Moca Chain Testnet' && x.status === 1).map(x => x.kycProgramId)

      const list = rp.data.list.filter((item, index) => filterList.includes(item.kycProgramId.toString())).map((item, index) => {
        return {
          ...item,
          name: item.programName,
          coin: 'KAIA',
          createTime: new Date(item.createTime).getTime(),
          flag: false
        }
      })
      this.list = list
      if (!list.length) return
      this.expandDetails({
        id: list[0].id,
        createTime: list[0].createTime,
        hasAppliedProgram: true
      }, 0)
    },
    createKyc () {
      this.$router.push('/zk-kyc')
    },
    checkDetail (dataIndex) {
      this.list.forEach((item, index) => {
        item.flag = false
        if (index === dataIndex) {
          item.flag = true
        }
      })
    },
    close () {
      this.drawerVisible = false
    },
    updateList () {
    },
    async expandDetails (data, index) {
      this.checkDetail(index)
      this.drawerVisible = true
      this.drawerDetails = data
      this.$store.commit('SET_CATEGORY', 1)
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/tokenDistribution/_distributionKycSettingStep4.scss" scoped></style>
