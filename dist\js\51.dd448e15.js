"use strict";(self["webpackChunkzkme_web"]=self["webpackChunkzkme_web"]||[]).push([[51],{36652:function(t,e,s){s.r(e),s.d(e,{default:function(){return p}});var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"pg-account-detail",attrs:{id:"pg-account-detail"}},[e("div",{staticClass:"sty1-bread-crumb mui-fl-vert mui-shr-0"},[e("i",{staticClass:"mcico-dashboard taplight2",on:{click:function(e){return t.$router.back()}}}),e("span",[t._v("/")]),t._v(" Applicant Data ")]),e("a",{ref:"aLink",staticStyle:{display:"none"}}),e("ul",{staticClass:"account"},[e("div",{staticClass:"mui-fl-btw"},[e("li",{staticClass:"mui-fl-vert"},[e("p",{staticClass:"t1"},[t._v(t._s(2===t.kycText?"zkMe account":"Account"))]),e("m-popover",{attrs:{placement:"bottom",trigger:"hover","popper-class":t.accountWidth?"sty4-popper":"sty4-popper sty3-popper-fail"}},[e("div",[t._v(t._s(t.RP&&t.RP.email))]),e("span",{attrs:{slot:"reference"},slot:"reference"},[e("p",{ref:"account",staticClass:"t2"},[t._v(t._s(t.RP&&t.RP.email))])])])],1),2===t.level&&t.RP?.boundingDetails[0]?.authorizationInfo&&!t.isNoMint?e("li",{staticClass:"mui-fl-vert"},[t._m(0),e("p",{staticClass:"t4"},[e("m-popover",{attrs:{placement:"bottom",width:540,trigger:"click","popper-class":"sty4-popper"}},[e("div",{staticClass:"mui-fl-end copy mui-fl-vert",on:{click:function(e){return e.stopPropagation(),t.copyTxt(t.RP?.boundingDetails[0]?.authorizationInfo)}}},[e("div",[t._v("Copy")]),e("i",{staticClass:"mico-copy taplight"})]),e("div",{staticClass:"mui-fl-vert authorization-info-view-content"},[e("ul",[t._v(" { "),t._l(JSON.parse(t.RP?.boundingDetails[0]?.authorizationInfo),(function(s,i){return e("li",{key:i,staticStyle:{"margin-left":"6px"}},["string"===typeof s?[e("p",[t._v('"'+t._s(i)+'": "'+t._s(s)+'"')])]:t._e(),"object"===typeof s?[e("div",[e("p",[t._v('"'+t._s(i)+'": {')]),t._l(s,(function(s,i){return e("div",{key:i},[e("p",{staticStyle:{"margin-left":"6px"}},[t._v('"'+t._s(i)+'": "'+t._s(s)+'"')])])})),t._v("} ")],2)]:t._e()],2)})),t._v(" } ")],2)]),e("span",{staticStyle:{cursor:"pointer"},attrs:{slot:"reference"},slot:"reference"},[t._v("view")])])],1)]):t._e(),2===t.level&&t.RP?.boundingDetails[0]?.userIds?.length?e("li",{staticClass:"mui-fl-vert"},[t._m(1),e("p",{staticClass:"t4 mui-fl-central"},[t._v(" "+t._s(t.RP?.boundingDetails[0]?.userIds.length?t.formatPubKey(t.RP?.boundingDetails[0]?.userIds[0]):"-")+" "),e("i",{staticClass:"mico-copy boundCopy",staticStyle:{margin:"0 4px"},on:{click:function(e){return e.stopPropagation(),t.copyTxt(t.userId)}}}),2===t.kycText&&t.RP?.boundingDetails[0]?.userIds.length>1?e("m-tooltip",{staticClass:"sty3-tooltip",attrs:{placement:"bottom","popper-class":"account-detail-tootip"}},[e("template",{slot:"content"},t._l(t.RP?.boundingDetails[0]?.userIds.slice(1),(function(s,i){return e("div",{key:i,staticClass:"row"},[t._v(" "+t._s(t._f("formatPubKey")(s))+" "),e("i",{staticClass:"mico-copy moreCopy",on:{click:function(e){return e.stopPropagation(),t.copyTxt(s)}}})])})),0),e("div",{staticClass:"more mui-fl-vert"},[e("span",[t._v("More")])])],2):t._e()],1)]):t._e(),e("div",{staticClass:"mui-flex"},[e("li",{staticClass:"download mui-fl-central taplight",on:{click:t.downloadPdf}},[e("i",{staticClass:"mico-download"})]),e("li",{staticClass:"download mui-fl-central taplight marg-l10",style:{cursor:6!==t.submitCheckFail?"no-drop":"pointer"}},[e("m-popover",{attrs:{"popper-class":6!==t.submitCheckFail?"sty3-popper sty3-popper-fail":" sty3-popper",placement:"bottom-end",width:"180",trigger:"hover"}},[e("template",{slot:"reference"},[e("i",{staticClass:"icon-download mico-information"})]),6!==!t.submitCheckFail?e("div",{staticClass:"information_select"},[e("div",{on:{click:function(e){t.reqState="form"}}},[t._v("Submit data recovery")]),e("div",{on:{click:function(e){t.reqState="list"}}},[t._v("Submission records")])]):t._e()],2)],1)])]),2!==t.kycText&&t.RP?.boundingDetails.length?e("m-collapse",{staticClass:"sty3-collapse",class:[(t.RP?.boundingDetails.length<=1||2===t.kycText)&&"isDisabled"]},[e("m-collapse-item",[e("template",{slot:"title"},[e("div",{staticClass:"mui-fl-btw collapseReport",staticStyle:{width:"80%"}},[2!==t.kycText?e("div",{staticClass:"mui-fl-1 mui-fl-vert"},[e("i",{staticClass:"mcico-network"}),t._v("Network:  "),e("span",[t._v(t._s(t.RP?.boundingDetails[0]?.network||"-"))])]):t._e(),t.kycText?t._e():e("div",{staticClass:"mui-fl-1 mui-fl-vert"},[e("i",{staticClass:"mico-AppID"}),t._v("SBT ID:  "),e("span",[t._v(t._s(t.RP?.boundingDetails[0]?.tokenId||"-"))])]),e("div",{staticClass:"mui-fl-1 mui-fl-vert",staticStyle:{"text-wrap":"nowrap"}},[e("i",{staticClass:"mico-APIKey"}),t._v(" Bound wallet:  "),e("span",{style:{cursor:t.checkChain(t.RP?.boundingDetails[0])?"default":"pointer"},on:{click:function(e){return e.stopPropagation(),t.toKYT(t.RP?.boundingDetails[0])}}},[t._v(" "+t._s(t.RP?.boundingDetails[0]?.walletAddress?t.formatPubKey(t.RP?.boundingDetails[0]?.walletAddress):"-")+" ")]),e("i",{staticClass:"mico-copy boundCopy",on:{click:function(e){return e.stopPropagation(),t.copyTxt(t.RP?.boundingDetails[0]?.walletAddress)}}})])])]),2!==t.kycText?t._l(t.RP?.boundingDetails,(function(s,i){return e("div",{key:i,staticStyle:{width:"80%"}},[i?e("div",{staticClass:"mui-fl-btw collapseReport compress"},[e("div",{staticClass:"mui-fl-1 mui-fl-vert"},[e("i",{staticClass:"mcico-network"}),t._v("Network:  "),e("span",[t._v(t._s(s.network))])]),t.kycText?t._e():e("div",{staticClass:"mui-fl-1 mui-fl-vert"},[e("i",{staticClass:"mico-AppID"}),t._v("ZIS ID:  "),e("span",[t._v(t._s(s.tokenId||"-"))])]),e("div",{staticClass:"mui-fl-1 mui-fl-vert"},[e("i",{staticClass:"mico-APIKey"}),t._v("Bound wallet:  "),e("span",{staticClass:"toKYT",style:{cursor:t.checkChain(s)?"default":"pointer"},on:{click:function(e){return e.stopPropagation(),t.toKYT(s)}}},[t._v(t._s(t._f("formatPubKey")(s.walletAddress))+" "),e("i",{staticClass:"mico-copy boundCopy",on:{click:function(e){return e.stopPropagation(),t.copyTxt(s.walletAddress)}}})])])]):t._e()])})):t._e()],2)],1):t._e()],1),e("RequestInformation",{attrs:{state:t.reqState,selectAccount:{1:[{zkmeId:t.zkmeId,kycProgramId:t.kycProgramId}]}},on:{restState:function(e){t.reqState=""}}}),e("div",{staticClass:"setting-items"},[e("p",{staticClass:"title"},[t._v("Client settings")]),e("ul",{staticClass:"client-settings sty1 mui-fl-vert"},[e("li",{staticClass:"mui-fl-vert"},[e("i",{staticClass:"a-icon a-icon-checked"}),e("p",[t._v("Minimum age: "+t._s(t.RP&&t.RP.minimumAge))])]),t.RP?.availableCitizenship?e("li",{staticClass:"mui-fl-vert"},[e("i",{staticClass:"a-icon a-icon-checked"}),e("p",[t._v("Available citizenship")])]):t._e(),t.RP?.availableCountries?e("li",{staticClass:"mui-fl-vert"},[e("i",{staticClass:"a-icon a-icon-checked"}),e("p",[t._v("Available countries/regions")])]):t._e(),t.RP?.amlChecking?e("li",{staticClass:"mui-fl-vert"},[e("i",{staticClass:"a-icon a-icon-checked"}),e("p",[t._v("AML Checking")])]):t._e(),t.RP?.availableUniqueness?e("li",{staticClass:"mui-fl-vert"},[e("i",{staticClass:"a-icon a-icon-checked"}),e("p",[t._v("Uniqueness check")])]):t._e()])]),e("div",{staticClass:"setting-items"},[e("p",{staticClass:"title"},[t._v("Regulations")]),e("ul",{staticClass:"regulations sty1 mui-fl-vert mui-fl-btw"},t._l(t.regulations,(function(s,i){return e("li",{key:i},[s.checking?t._e():e("p",{staticClass:"mui-fl-vert regulationsIcon"},[e("m-tooltip",{attrs:{placement:"bottom","popper-class":"false"===s.status?.toString()&&"Supported document"===s.name?"showAuthDocument account-detail-tootip":"account-detail-tootip authDocument"}},[e("template",{slot:"content"},[e("div",{staticClass:"row"},[t._v(" The current document submitted by the user does not meet the required criteria, please be patient while waiting for the user to re-upload the document. ")])]),s.checking?t._e():e("p",{staticClass:"mui-fl-vert regulationsIcon"},["true"===s.status?.toString()?e("i",{staticClass:"a-icon mcico-success2"}):"false"===s.status?.toString()&&"Supported document"===s.name?e("i",{staticClass:"a-icon mcico-supportedTip"}):"false"===s.status?.toString()?e("i",{staticClass:"a-icon a-icon-error"}):"null"===s.status?.toString()?e("i",{staticClass:"a-icon mcico-wait"}):t._e(),t._v(" "+t._s(s.name)+" ")])],2)],1)])})),0)]),e("div",{staticClass:"setting-items"},[e("p",{staticClass:"title"},[t._v("Key findings")]),e("ul",{staticClass:"key-findings sty2 mui-fl-wrap"},t._l(t.keyfindings,(function(s,i){return e("li",{key:i,staticClass:"mui-shr-0"},[s.status?t._e():e("p",{staticClass:"t1"},[t._v(t._s(s.title))]),s.status?t._e():e("div",{staticClass:"mui-fl-wrap key-findings-items",class:[s?.style&&"mui-fl-col key-findings-items-error"]},t._l(s.list,(function(s,i){return e("div",{key:i},[e("div",{staticClass:"mui-fl-vert"},["boolean"!==typeof s.status?e("i",{staticClass:"a-icon mcico-wait"}):e("i",{staticClass:"a-icon",class:[s.status?"a-icon-success":"a-icon-error"]}),e("span",[t._v(t._s(s.name))])]),s.data?.length&&!s.status?e("ul",{staticClass:"mui-fl-col exceptionData"},t._l(s.data,(function(s,i){return e("li",{key:i},[e("div",{staticClass:"title"},[t._v(" "+t._s(s.title)+" ")]),e("div",{staticClass:"detail"},[t._v(" "+t._s(s.detail)+" ")])])})),0):t._e()])})),0)])})),0)]),e("div",{staticClass:"setting-items",attrs:{id:"events"}},[e("p",{staticClass:"title"},[t._v("Events")]),e("ul",{staticClass:"sty1 events-items"},t._l(t.RP&&t.eventList,(function(s,i){return e("li",{key:i,class:{"eventTime-bg":!s.eventTime&&i<=t.lastNoTime}},[e("div",{staticClass:"mui-fl-vert mui-fl-btw"},[e("div",[e("p",{staticClass:"event-name"},[t._v(t._s(s.eventCode))])]),s.eventTime?e("div",{staticClass:"event-applicant mui-fl-vert"},[e("p",[t._v("Last Updated:  ")]),e("p",{staticClass:"value"},[t._v(t._s(t._f("formatGMTDate")(s.eventTime))+" (GMT+8)")])]):t._e()])])})),0)])],1)},a=[function(){var t=this,e=t._self._c;return e("p",{staticClass:"t3 mui-fl-vert"},[e("i",{staticClass:"mico-APIKey"}),t._v(" Authorization Info: ")])},function(){var t=this,e=t._self._c;return e("p",{staticClass:"t3 mui-fl-vert"},[e("i",{staticClass:"mico-AppID"}),t._v(" User ID: ")])}],n=(s(44114),s(98992),s(54520),s(72577),s(3949),s(14603),s(47566),s(98721),s(93518)),o=s(5626),l=s(94373),c=s(34981),r={components:{RequestInformation:c.A},data(){return{lastNoTime:-1,formatPubKey:o.Qv,regulations:[],keyfindings:[],eventList:[],blockchainId:"",userWalletAddress:"",popBindingId:"",zkmeId:"",userId:"",kycProgramId:"",RP:null,reqState:"",keyComArr:["Sanctions","Adverse media","Politically exposed persons (PEP)"],downloadLink:"",submitCheckFail:1,accountWidth:!1,userStep:["KYC Passed","OnChain Minted","SBT Minted","ZKP Generated","Liveness Checked","OCR Passed","Verification Started"]}},computed:{...(0,n.aH)({level:({auth:t})=>t.user&&t.user.level,isNoMint:({auth:t})=>t.user&&t.user.isNoMint}),kycText(){return localStorage.getItem("zkmeAdminUser")&&JSON.parse(localStorage.getItem("zkmeAdminUser")).level||this.$store.state.auth.user.level}},created(){this.blockchainId=this.$route.query.blockchainId,this.userWalletAddress=this.$route.query.userWalletAddress,this.zkmeId=this.$route.query.zkmeId,this.userId=this.$route.query.userId,this.kycProgramId=this.$route.query.kycProgramId,this.account=this.$route.query.account,this.popBindingId=this.$route.query.popBindingId,this.getDetail()},methods:{checkChain(t){return!!(t.network.startsWith("sei")||t.network.startsWith("neutron")||t.network.startsWith("Aptos"))},findLastNoTimeIndex(){for(let t=0;t<this.eventList.length;t++)if(this.eventList[t].eventTime)return t;return-1},async getDetail(){const t=await this.$api.request("dashboard.getDetail",{kycProgramId:this.kycProgramId,zkmeId:this.zkmeId});8e7===t.code&&(this.RP=t.data,this.submitCheckFail=t.data.dashBoardKycStatus,0!==this.level&&(this.userStep=this.userStep.filter((t=>"OnChain Minted"!==t))),this.userStep.forEach((e=>{this.eventList.push({eventCode:e,eventTime:t.data.eventList.find((t=>t.eventTitle===e))?.eventTime})})),this.isNoMint&&(this.eventList=this.eventList.filter((t=>"OnChain Minted"!==t.eventCode&&"SBT Minted"!==t.eventCode))),this.lastNoTime=this.findLastNoTimeIndex(),this.regulations.push({name:"Supported document",status:"boolean"!==typeof t.data.supportedDocument?"null":t.data.supportedDocument},{name:"Facial check",status:"boolean"!==typeof t.data.facialCheck?"null":t.data.facialCheck},{name:"Citizenship",status:"boolean"!==typeof t.data.regulationsCitizenship?"null":t.data.regulationsCitizenship},{name:"Age",status:"boolean"!==typeof t.data.regulationsAge?"null":t.data.regulationsAge},{name:"Uniqueness check",status:"boolean"!==typeof t.data.isDocumentUniqueness?"null":t.data.isDocumentUniqueness,checking:!this.RP?.availableUniqueness},{name:"Risk level: unknown",status:!![6,-1].includes(this.submitCheckFail)||"null",checking:!this.RP?.amlChecking},{name:"Location",status:"boolean"!==typeof t.data.location?"null":t.data.location,checking:!this.RP?.availableCountries}),this.keyfindings.push({title:"Document review check",list:[{name:"Images quality",status:t.data.imagesQuality},{name:"Security features",status:t.data.securityFeatures},{name:"Template",status:t.data.template},{name:"Fonts",status:t.data.fonts}]},{title:"Data validation",list:[{name:"Document has not been recorded as expired, lost, stolen or compromised",status:t.data.docEffective},{name:"Document has not been found on our blocklist",status:t.data.docAllowed},{name:"Data comparison",status:t.data.docComparison}]},{title:"Facial check",list:[{name:"Face detection",status:t.data.faceDetection},{name:"Liveness check",status:t.data.livenessCheck}]},{title:"Compromised persons/organizations",style:"grid",status:!this.RP?.amlChecking,list:[{name:"Sanctions, Warnings, and Fitness & Probity",status:t.data.sanctions,data:t.data.sanctionsDetail},{name:"Politically exposed persons (PEP)",status:t.data.pep,data:t.data.pepDetail},{name:"Adverse media",status:t.data.adverseMedia,data:t.data.adverseMediaDetail}]}),this.$nextTick((()=>{this.accountWidth=250===this.$refs.account.clientWidth})))},async copyTxt(t){if(this.isCopyTxt=!0,navigator.clipboard&&window.isSecureContext)try{return await navigator.clipboard.writeText(t),void this.$message({message:"Copied",iconClass:"mico-lightTip",customClass:"sty1-message",duration:3e3,offset:32,center:!0})}catch(e){return console.error(e),!1}else{const e=document.createElement("textarea");e.textContent=t,e.style.position="fixed",document.body.appendChild(e),e.select();try{return document.execCommand("copy"),void this.$message({message:"Copied",iconClass:"mico-lightTip",customClass:"sty1-message",duration:3e3,offset:32,center:!0})}catch(s){return this.$message({message:"Copy to clipboard failed.",iconClass:"mico-lightTip",customClass:"sty1-message",duration:3e3,offset:32,center:!0}),!1}finally{document.body.removeChild(e)}}},async downloadPdf(){if(![-1,6].includes(this.submitCheckFail))return void this.$message({message:"The current user has not completed the KYC process.",iconClass:"mcico-warn2",customClass:"sty4-message supportDocuments",duration:3e3,offset:32,center:!0});const t={kycProgramId:this.kycProgramId,blockchainId:this.RP.boundingDetails[0]?.blockchainId,userWalletAddress:this.userWalletAddress,zkmeId:this.zkmeId,account:this.account,popBindingId:this.popBindingId};let e;2===this.kycText&&(delete t.userWalletAddress,e=Object.assign(t,{account:this.RP.boundingDetails[0]?.userIds[0]}));const s=await this.$api.request("dashboard.downloadKycReport",e||t);if(8e7===s.code){const t=await l.A.get(s.data.reportUrl,{responseType:"blob"}),e="application/pdf",i=new Blob([t.data],{type:e}),a=URL.createObjectURL(i);this.$refs.aLink.href=a,this.$refs.aLink.download=this.kycProgramId+"_KYCreport_"+this.RP.email+".pdf",this.$refs.aLink.click()}},toKYT(t){this.checkChain(t)||this.$router.push({path:"/dashboard/account-kyt",query:{blockchainId:t.blockchainId,userWalletAddress:t.walletAddress,zkmeId:this.zkmeId,kycProgramId:this.kycProgramId}})}}},u=r,d=s(81656),m=(0,d.A)(u,i,a,!1,null,"346b8651",null),p=m.exports}}]);