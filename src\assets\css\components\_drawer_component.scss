.drawer-title {
  word-break: break-word;
}
.icon-edit {
  font-size: 20px;
  margin-left: 8px;
  cursor: pointer;
}

.program {
  .m {
    border-left: 1px solid #CEDBDB;
    margin: 0 8px;
  }
  .l {
    // font-weight: 450;
    line-height: 18px;
    color: #B3C0C2;
    cursor: pointer;
  }
  .cop {
    opacity: 0.5;
  }
}
.steps-box {
  margin-top: 36px;
  margin-bottom: 100px;
}
.drawer_records {
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  margin-bottom: 20px;
  color: #002E33;
}
.edAddress {
  background-color: #F7F7F7;
  border-radius: 12px;
  padding: 12px;
  .addressWl .addresscor {
    word-break: break-all;
  }
}
.addressHand {
  width: 90px;
  min-width: 90px;
  margin-right: 8px;
}
.con-text {
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
  color: #002E33;
  margin: 20px 0 8px;
}
.igTexts {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #738C8F;
}
.addresscor {
  color: #002E33;
}
.mbs {
  margin-bottom: 16px;
}
.igcur {
  cursor: pointer;
}

.drawer_step {
  margin-bottom: 36px;
  .boolball {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #6CCC81;
    margin-right: 8px;
  }
  .drawer_division {
    padding: 10px 0;
    min-width: 1px;
    margin-left: 4px;
    border-left: 2px solid #F2F7F7;
    &.textma{
      padding: 20px 0;
    }
    .text {
      cursor: pointer;
      margin-left: 12px;
      color: #6684BF;
      .mico-fold-rotate {
        transform: rotate(-180deg);
      }
    }
    .foldtext {
      line-height: 18px;
      margin-top: 10px;
      color: #B3C0C2;
    }
  }
  .drawer_text_hand {
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    margin-right: 4px;
  }
  .drawer_text {
    line-height: 20px;
  }
  .applied {
    .boolball {
      background-color: #6CCC81;
    }
    color: #6CCC81;
  }
  .created {
    .boolball {
      background-color: #6684BF;
    }
    color: #6684BF;
  }
  .expired {
    .boolball {
      background-color: #B3C0C2;
    }
    color: #B3C0C2;
  }
  .pending {
    .boolball {
      background-color: #FFD14B;
    }
    color: #FFD14B;
  }
}

.footer-box {
  width: -webkit-fill-available;
  background-color: #fff;
  padding: 16px 36px ;
  position: fixed;
  bottom: 0;
  right: 0;
  width: 670px;
}

.program-name {
  margin-bottom: 8px;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #738C8F;
}

// .leavetip {
  .leavetip_text {
    width: 340px;
    line-height: 18px;
  }
  .leavetip_title {
    font-weight: 500;
    line-height: 22px;
    font-size: 18px;
    margin-bottom: 24px;
    color: #002E33;
  }

  .dialog_title {
    color: #002E33;
    line-height: 18px;
  }
  .mico-warning {
    font-size: 20px;
    color: #EE6969;
    margin-right: 8px;
  }
  .dialog_button {
    .kycSave, .kycCancel {
      width: 109px;
      height: 36px;
      background-color: #F7F7F7;
      color: #33585C;
      border-radius: 26px;
      border: none;
      margin-top: 24px;
    }
    .kycCancel {
      background-color: #A9E1D3;
      color: #002E33;
      margin-left: 12px;
      margin: 24px 0 24px 12px;
    }
  }
  .warin_button {
    .kycSave, .kycCancel {
      width: auto !important;
    }
    .kycSave {
      padding: 0 36px !important;
    }
    .kycCancel {
      margin: 24px 0 12px 12px !important;
    }
    .delete {
      padding: 0 30px;
    }
    .applied, .Stay {
      padding: 0 36px;
    }
  }
  .dialog_recorded_button {
    .kycCancel {
      background: rgba(238, 105, 105, 0.06) !important;
      color: #EE6969  !important;
    }
  }

  .dialog_warning {
    margin-top: 6px;
    font-weight: 400;
    font-size: 12px;
    line-height: 15px;
    color: #EE6969;
  }
// }