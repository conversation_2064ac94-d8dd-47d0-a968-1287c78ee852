<template>
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style="margin-right: 6px;"
  >
    <g clip-path="url(#clip0_6328_11973)">
      <g filter="url(#filter0_d_6328_11973)">
        <path
          d="M1.80615 3.95552C1.77363 3.17522 2.28732 2.47698 3.04193 2.27575L5.20916 1.69782C7.0381 1.2101 8.96291 1.2101 10.7919 1.69782L12.9591 2.27575C13.7137 2.47698 14.2274 3.17522 14.1949 3.95552L14.0209 8.13066C13.9078 10.8442 12.16 13.2179 9.60226 14.1314L9.12163 14.3031C8.39664 14.562 7.60437 14.562 6.87938 14.3031L6.39876 14.1314C3.84106 13.2179 2.09318 10.8442 1.98011 8.13066L1.80615 3.95552Z"
          fill="url(#paint0_linear_6328_11973)"
        />
        <!-- 底色修改 141414  000000 -->
        <path
          d="M14.4206 8.14732L14.5945 3.97218C14.6348 3.0046 13.9979 2.13878 13.0621 1.88925L10.8949 1.31133C8.99845 0.8056 7.00256 0.8056 5.10609 1.31133L2.93887 1.88925C2.00315 2.13878 1.36618 3.0046 1.40649 3.97218L1.58046 8.14732C1.70031 11.0237 3.55307 13.5398 6.26422 14.5081L6.74485 14.6798C7.55684 14.9698 8.44417 14.9698 9.25616 14.6798L9.73679 14.5081C12.4479 13.5398 14.3007 11.0237 14.4206 8.14732Z"
          :stroke="themeMode === 'light' ? '#ffffff' : '#141414'"
          stroke-width="0.8"
        />
      </g>
      <mask
        id="mask0_6328_11973"
        style="mask-type: alpha"
        maskUnits="userSpaceOnUse"
        x="1"
        y="1"
        width="14"
        height="14"
      >
        <path
          d="M1.80615 3.95552C1.77363 3.17522 2.28732 2.47698 3.04193 2.27575L5.20916 1.69782C7.0381 1.2101 8.96291 1.2101 10.7919 1.69782L12.9591 2.27575C13.7137 2.47698 14.2274 3.17522 14.1949 3.95552L14.0209 8.13066C13.9078 10.8442 12.16 13.2179 9.60226 14.1314L9.12163 14.3031C8.39664 14.562 7.60437 14.562 6.87938 14.3031L6.39876 14.1314C3.84106 13.2179 2.09318 10.8442 1.98011 8.13066L1.80615 3.95552Z"
          fill="url(#paint1_linear_6328_11973)"
        />
      </mask>
      <g mask="url(#mask0_6328_11973)">
        <rect
          x="11.5"
          y="-0.46875"
          width="2.86948"
          height="17"
          transform="rotate(30 11.5 -0.46875)"
          fill="url(#paint2_linear_6328_11973)"
          fill-opacity="0.43"
        />
      </g>
      <path
        d="M3.00118 4.72259C2.97494 4.09289 3.38948 3.52942 3.99844 3.36703L5.74736 2.90065C7.22329 2.50707 8.77658 2.50707 10.2525 2.90065L12.0014 3.36703C12.6104 3.52942 13.0249 4.09289 12.9987 4.72259L12.8583 8.09187C12.7671 10.2817 11.3565 12.1972 9.29253 12.9344L8.90467 13.0729C8.31961 13.2818 7.68026 13.2818 7.09521 13.0729L6.70735 12.9344C4.64332 12.1972 3.23281 10.2817 3.14156 8.09187L3.00118 4.72259Z"
        :fill="themeColor2"
      />
      <mask
        id="mask1_6328_11973"
        style="mask-type: alpha"
        maskUnits="userSpaceOnUse"
        x="3"
        y="2"
        width="10"
        height="12"
      >
        <path
          d="M3.00118 4.72259C2.97494 4.09289 3.38948 3.52942 3.99844 3.36703L5.74736 2.90065C7.22329 2.50707 8.77658 2.50707 10.2525 2.90065L12.0014 3.36703C12.6104 3.52942 13.0249 4.09289 12.9987 4.72259L12.8583 8.09187C12.7671 10.2817 11.3565 12.1972 9.29253 12.9344L8.90467 13.0729C8.31961 13.2818 7.68026 13.2818 7.09521 13.0729L6.70735 12.9344C4.64332 12.1972 3.23281 10.2817 3.14156 8.09187L3.00118 4.72259Z"
          fill="#956B2D"
        />
      </mask>
      <g mask="url(#mask1_6328_11973)">
        <rect
          x="1.33594"
          y="1.78906"
          width="6.66667"
          height="12.5"
          :fill="themeColor1"
        />
      </g>
      <path
        d="M5.60156 8L7.60156 10L10.8016 6"
        stroke="white"
        stroke-width="1.6"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <mask
        id="mask2_6328_11973"
        style="mask-type: alpha"
        maskUnits="userSpaceOnUse"
        x="3"
        y="2"
        width="10"
        height="12"
      >
        <path
          d="M3.00118 4.72259C2.97494 4.09289 3.38948 3.52942 3.99844 3.36703L5.74736 2.90065C7.22329 2.50707 8.77658 2.50707 10.2525 2.90065L12.0014 3.36703C12.6104 3.52942 13.0249 4.09289 12.9987 4.72259L12.8583 8.09187C12.7671 10.2817 11.3565 12.1972 9.29252 12.9344L8.90467 13.0729C8.31961 13.2818 7.68026 13.2818 7.09521 13.0729L6.70735 12.9344C4.64332 12.1972 3.23281 10.2817 3.14156 8.09187L3.00118 4.72259Z"
          fill="#956B2D"
        />
      </mask>
      <g mask="url(#mask2_6328_11973)">
        <rect
          x="11.5"
          y="-0.46875"
          width="2.86948"
          height="17"
          transform="rotate(30 11.5 -0.46875)"
          fill="url(#paint3_linear_6328_11973)"
          fill-opacity="0.43"
        />
      </g>
    </g>
    <defs>
      <filter
        id="filter0_d_6328_11973"
        x="0.204639"
        y="-0.267773"
        width="15.5917"
        height="16.3651"
        filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB"
      >
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset />
        <feGaussianBlur stdDeviation="0.4" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_6328_11973"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_6328_11973"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_6328_11973"
        x1="8.00051"
        y1="1.33203"
        x2="8.00051"
        y2="14.4973"
        gradientUnits="userSpaceOnUse"
      >
        <stop :stop-color="themeColor2" />
        <stop offset="1" :stop-color="themeColor1" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_6328_11973"
        x1="8.00051"
        y1="1.33203"
        x2="8.00051"
        y2="14.4973"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#B07E33" />
        <stop offset="1" stop-color="#D08E2C" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_6328_11973"
        x1="12.9347"
        y1="-0.46875"
        x2="12.9347"
        y2="16.5312"
        gradientUnits="userSpaceOnUse"
      >
        <stop :stop-color="themeColor1" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_6328_11973"
        x1="12.9347"
        y1="-0.46875"
        x2="12.9347"
        y2="16.5312"
        gradientUnits="userSpaceOnUse"
      >
        <stop :stop-color="themeColor1" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <clipPath id="clip0_6328_11973">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
</template>
<script>
export default {
  name: 'WarnTipSvg',
  props: {
    themeColor1: {
      type: String,
      required: true,
      default: () => {
        return '#005563'
      }
    },
    themeColor2: {
      type: String,
      required: true,
      default: () => {
        return '#CCDDE0'
      }
    },
    themeMode: {
      type: String,
      required: true,
      default: () => {
        return 'light'
      }
    }
  }
}
</script>
