<template>
  <div style="height: calc(100% - 32px); flex-direction: column; position: static;">
    <DistributionStep1 v-if="step === 1" ref="step1"></DistributionStep1>
    <DistributionStep2 v-else-if="step === 2" ref="step2"></DistributionStep2>
    <DistributionStep3 v-else-if="step === 3" ref="step3"></DistributionStep3>
    <DistributionStep4 v-else-if="step === 4" ref="step4"></DistributionStep4>
    <!-- <DistributionStep5 v-else-if="step === 5" ref="step5"></DistributionStep5> -->
    <DistributionStep6 v-else-if="step === 5"></DistributionStep6>
    <div :class="['mui-fl-btw', 'stepEnd', step === 1 && 'stepEnd2']">
      <m-button class="sty2-button sty10-button" style="background: #8EDEE3;" @click="back">
        <i class="mico-Fallback"></i>
        Back
      </m-button>
      <ul class="mui-fl-vert">
        <li v-for="(data, index) of stepText" :key="index"
          :class="['mui-fl-vert', 'stepBox',
          index >= step && 'unfinished',
          ((index + 1 !== step) && (index < distributionStep)) && 'finished'
        ]">
          <div class="step mui-fl-central" @click="stepChange(index + 1)">
            {{ index + 1 }}
            <div class="stepName"> {{ data.name }}</div>
          </div>
          <div v-if="Number(index) < 4" class="segmentation"></div>
        </li>
      </ul>
      <m-button v-if="step !== 5" class="sty2-button sty10-button mui-fl-central" style="color: #FFFFFF;" @click="next">
        <div v-if="!loading">
          Next
          <i class="mico-Fallback next"></i>
        </div>
        <div v-else class="loader loader2"></div>
      </m-button>
      <div class="mui-fl-btw" v-else>
        <m-button class="sty2-button sty11-button mui-fl-central" style="color: #FFFFFF;" @click="backTo = true">
          <div v-if="!loading">
            Save
          </div>
          <div v-else class="loader loader2"></div>
        </m-button>
        <m-button class="sty2-button sty11-button mui-fl-central" style="background: #A9E1D3;" @click="dialogDeploy = true">
          <div v-if="!loading">
            Deploy
          </div>
          <div v-else class="loader loader2"></div>
        </m-button>
      </div>
    </div>
    <m-dialog
      :visible.sync="backTo"
      custom-class="sty6-dialog"
      width="510px"
    >
      <div class="mui-fl-central mui-fl-col">
        <img class="contractImg" src="../../assets/img/contractImg.png" alt="">
        <div class="t1">Distribution Plan Saved Success</div>
      </div>
      <span slot="footer" class="mui-fl-btw">
        <m-button class="sty2-button sty12-button" style="background: #005563; color: #FFFFFF;" @click="$emit('onjumpStep')">Back to Dashboard</m-button>
      </span>
    </m-dialog>
    <m-dialog
      :visible.sync="dialogDeploy"
      @close="dialogClose"
      custom-class="sty6-dialog"
      width="510px">
      <div v-if="!deployContracts">
        <div class="t1">Reminder</div>
        <div class="t2">
          Once the token distribution plan is deployed, it will<br/>
          be recorded on-chain. You can still modify the<br/>
          distribution data before the start date.<br/>
          Your admin address will be:
        </div>
        <div class="t3">{{ $store.state.wallet.connectedAddress }}</div>
      </div>
      <div v-else class="mui-fl-central mui-fl-col">
        <img class="contractImg" src="../../assets/img/contractImg.png" alt="">
        <div class="t1">Transaction Success</div>
      </div>
      <span slot="footer" class="mui-fl-btw">
        <m-button v-if="!deployContracts" class="sty2-button sty12-button" @click="dialogDeploy = false, deployFlag = false">Cancel</m-button>
        <m-button v-if="!deployContracts" class="sty2-button sty12-button" style="background: #005563; color: #FFFFFF; margin-left: 24px;" type="primary" @click="deploy">
          <div v-if="deployFlag" class="mui-fl-central">
            <div class="loader loader2"></div>
          </div>
          <div v-else>Deploy</div>
        </m-button>
        <m-button v-if="deployContracts" class="sty2-button sty12-button">
          <a :href=txHash target="_blank" style="color: #002E33;">View Transaction</a>
        </m-button>
        <m-button v-if="deployContracts" class="sty2-button sty12-button" style="background: #005563; color: #FFFFFF; margin-left: 24px;" type="primary" @click="dialogClose">
          Got it
        </m-button>
      </span>
    </m-dialog>
  </div>
</template>

<script>
import DistributionStep1 from './components/distributionDetailStep1.vue'
import DistributionStep2 from './components/distributionWhitelistStep2.vue'
import DistributionStep3 from './components/distributionPageSettingStep3.vue'
import DistributionStep4 from './components/distributionKycSettingStep4.vue'
// import DistributionStep5 from './components/distributionUIDesignStep5.vue'
import DistributionStep6 from './components/distributionConfirmStep6.vue'

export default {
  components: {
    DistributionStep1,
    DistributionStep2,
    DistributionStep3,
    DistributionStep4,
    // DistributionStep5,
    DistributionStep6
  },
  data () {
    return {
      backTo: false,
      deployFlag: false,
      deployContracts: false,
      dialogDeploy: false,
      loading: false,
      step: this.$store.state.distribution.detail.step || 1,
      indexStep: 2,
      txHash: '#',
      stepText: [
        {
          name: 'Distribution Details',
          type: true
        },
        {
          name: 'Upload Address',
          type: false
        },
        {
          name: 'Page Settings',
          type: false
        },
        {
          name: 'KYC Settingss',
          type: false
        },
        // {
        //   name: 'UI Design',
        //   type: false
        // },
        {
          name: 'Confirm Information',
          type: false
        }
      ]
    }
  },
  computed: {
    address () {
      return this.$store.state.wallet.connectedAddress || ''
    },
    distributionStep () {
      return this.$store.state.distribution.detail.step
    },
    detail () {
      return this.$store.state.distribution.detail
    }
  },
  watch: {
  },
  created () {
  },
  methods: {
    viewTransaction () {
    },
    dialogClose () {
      if (this.deployContracts) {
        this.$emit('onBack')
      } else {
        this.dialogDeploy = false
      }
    },
    async deploy () {
      // const { balanceOf } = await this.$store.dispatch('checkContract', {
      //   tokenAddress: this.detail.tokenContractAddress,
      //   chainId: this.detail.blockchainId
      // })
      // const permission = `
      //   <div class="mui-fl-vert title">
      //     <i class="mico-warning"></i>
      //     <div>Notice</div>
      //   </div>
      //   <div class="content ${new Date().getTime() > (new Date(this.detail.startDate).getTime() + 7200000) && 'block'}">The plan’s start time must be set two hours after the current time.</div>
      //   <div class="content ${Number(this.detail.amount) > Number(balanceOf) && 'block'}">The amount you want to distribute exceeds the available balance.</div>
      // `
      // if (new Date().getTime() > (new Date(this.detail.startDate).getTime() + 7200000) || this.detail.amount > Number(balanceOf)) {
      //   this.$message({
      //     dangerouslyUseHTMLString: true,
      //     duration: 0,
      //     offset: 32,
      //     center: true,
      //     customClass: 'sty1-message sty5-message sty6-message',
      //     type: 'success',
      //     iconClass: 'mico',
      //     showClose: true,
      //     message: permission
      //   })
      //   return
      // }
      if (!this.address) {
        this.$emit('connectWallet')
        return
      }
      if (this.$store.state.wallet.walletChainId !== Number(this.detail.blockchainId)) {
        this.$message.error('Please switch to the correct network.')
        return
      }
      if (this.deployFlag) return
      this.deployFlag = true
      const checkContract = await this.$store.dispatch('checkContract', {
        tokenAddress: this.detail.tokenContractAddress,
        chainId: '0x141f'
      })
      if (Number(checkContract.balanceOf) >= this.detail.amount) {
        const tx = await this.$store.dispatch('callContract', this.detail)
        if (tx) {
          this.txHash = 'https://testnet-scan.mechain.tech/tx/' + tx.hash
          this.deployContracts = true
        } else {
          this.dialogDeploy = false
          this.deployFlag = false
          this.$message.error('Deploy contract failed.')
        }
      } else {
        this.$message.error('Insufficient token balance for deployment.')
      }
      this.deployFlag = false
    },
    async next () {
      if (this.step === 5) return
      if (this.step === 1 || this.step === 2 || this.step === 3 || this.step === 4 || this.step === 5) {
        this.loading = true
        const checkForm = await this.$refs[`step${this.step}`].checkForm()
        this.loading = false
        if (!checkForm) return
      }
      this.step += 1
    },
    back () {
      if (this.step === 1) {
        this.$emit('onBack')
        return
      }
      this.step -= 1
    },
    async stepChange (step) {
      if (this.distributionStep < step) return
      if (this.step === 5) {
        this.step = step
        return
      }
      const checkForm = await this.$refs[`step${this.step}`].checkForm()
      if (!checkForm) return
      this.step = step
    }
  }
}
</script>

<style lang="scss" scoped>
  .mico-Fallback {
    margin-right: 4px;
  }
  .next {
    margin-left: 4px;
    transform: rotate(180deg);
  }
  .stepBox {
    margin-top: -16px;
  }
  .unfinished {
    .step {
      background: #B3C0C2;
      cursor: default;
      .stepName {
        color: #B3C0C2;
      }
    }
    .segmentation {
      background: #84B6B8;
    }
  }
  .finished {
    .step {
      background: rgba(0, 85, 99, .5);
      cursor: pointer;
      .stepName {
        color: rgba(0, 85, 99, .5);
      }
    }
    .segmentation {
      background: rgba(0, 85, 99, .5);
    }
  }
  .step {
    cursor: pointer;
    width: 32px;
    height: 32px;
    background: #005563;
    border-radius: 50%;
    color: #FFFFFF;
    margin-top: auto;
    position: relative;
    .stepName {
      position: absolute;
      color: #002E33;
      bottom: -20px;
      // left: 50%;
      white-space: nowrap;
      // transform: translateX(-50%);
      // margin-left: -5px;
    }
  }
  .segmentation {
    width: 5vw;
    height: 1px;
    margin: 0 16px;
    background: #005563;
  }
  .stepEnd {
    background: #FFFFFF;
    width: calc(100%);
    position: sticky;
    margin-left: -60px;
    bottom: 0;
    padding: 24px 60px;
    left: 0;
  }
  .stepEnd2 {
    // width: 100%;
    // position: sticky;
    // bottom: 0;
  }
</style>
