<template>
  <m-dialog :visible.sync="kytDetailstips" class="sty2-dialog dialog" :show-close="true" width="1036px" @close="closedia">
    <div class="title">
      Risk Score details
    </div>
    <div class="in-txns-table table-wrap mui-fl-col mui-fl-btw">
      <m-table :data="tableList" class="sty2-table sty3-table" :default-sort = "{prop: 'Volume(USD)%', order: 'descending'}">
        <m-table-column v-for="(item, index) of labelList" :key="index" :label="item" :sortable="item === 'Volume(USD)%'" prop="percent">
          <template #default="{ row }">
            <span class="sender-add" v-if="item === 'Risk Type'" style="text-transform:capitalize;">{{ row.type.replace('_', ' ') + ' Address' }}</span>
            <span class="sender-add" v-if="item === 'Address/Risk label'">{{ row.address | formatPubKey(4, 4) }} ({{ row.label }}) <i class="mico-copy" @click="copy(row.address, row.label)"></i></span>
            <span class="sender-add" v-if="item === 'Volume(USD)%'"> {{ !row.volume ? '-' : `$${row.volume}` }} ({{ row.percent }}%)</span>
          </template>
        </m-table-column>
      </m-table>
      <div class="table-pagination mui-fl-end">
        <m-pagination
          @current-change="sizeChange(paginatedData, currentPage)"
          v-show="paginatedData.length > 0"
          class="sty1-pagination"
          background
          :page-size="pageSize"
          :current-page.sync="currentPage"
          layout="prev, pager, next"
          :total="paginatedData.length"
        >
        </m-pagination>
      </div>
    </div>
  </m-dialog>
</template>

<script>
export default {
  props: {
    kytDetailstip: {
      type: Boolean,
      default: false
    },
    paginatedData: {
      type: Array,
      default: () => {
        return []
      }
    },
    labelList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      kytDetailstips: false,
      tableList: this.paginatedData.slice(0, 9),
      currentPage: 1,
      pageSize: 10
    }
  },
  watch: {
    kytDetailstip (val) {
      this.kytDetailstips = val
    }
  },
  methods: {
    sizeChange (data, currentPage) {
      const start = (currentPage - 1) * this.pageSize
      const end = currentPage * this.pageSize
      this.tableList = data.slice(start, end)
    },
    closedia () {
      this.$emit('detail')
    },
    copy (value, label) {
      const elInput = document.createElement('input')
      elInput.value = value + `(${label})`
      document.body.appendChild(elInput)
      elInput.select()
      document.execCommand('Copy')
      elInput.remove()
      this.$message({
        message: 'Copied',
        iconClass: 'mcico-success',
        customClass: 'sty4-message',
        duration: 3000,
        offset: 32,
        center: true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .title {
    font-weight: 700;
    font-size: 18px;
    line-height: 25px;
    color: #002E33;
    margin-bottom: 24px;
  }
  .mico-copy {
    font-size: 16px;
  }
  .table-pagination {
    margin: 8px 0 24px;
    border-radius: 16px;
  }
</style>
