<template>
  <div class="pg-dashboard">
    <div>
      <div class="mui-fl-vert mui-fl-btw">
        <div class="pg-title1 mui-fl-vert mui-shr-0">
          <i class="mcico-active-dashboard"></i>
          User List
        </div>
        <m-date-picker class="sty1-date-editor mui-shr-0" popper-class="sty1-date-popper" prefix-icon="mico-date"
          v-model="picker" value-format="timestamp" format="yyyy.MM.dd" type="daterange" align="right" range-separator="-"
          start-placeholder="start" end-placeholder="end" unlink-panels :clearable="false" :editable="false"
          @change="handleChangeDate" :default-time="['00:00:00', '23:59:59']">
        </m-date-picker>
      </div>
      <ul class="statistics mui-fl-vert">
        <li class="mui-fl-vert mui-fl-1">
          <img src="~@/assets/img/statistics-total.png" alt="">
          <div>
            <p class="t1">Total Initiated Accounts</p>
            <p class="t2">{{ (RP && totalAll || 0) | formatStatistics }}</p>
          </div>
        </li>
        <li class="mui-fl-vert mui-fl-1">
          <img src="~@/assets/img/statistics-zis.png" alt="">
          <div>
            <p class="t1">{{CentralizationUser.name1}}</p>
            <p class="t2">{{ (RP && RP.otherData.authorizedCount || 0) | formatStatistics }}</p>
          </div>
        </li>
        <li class="mui-fl-vert mui-fl-1">
          <img src="~@/assets/img/statistics-meid.png" alt="">
          <div>
            <p class="t1">Authorized MeID</p>
            <p class="t2">{{ (RP && RP.otherData.meIdAuthorizedCount || 0) | formatStatistics }}</p>
          </div>
        </li>
      </ul>
    </div>

    <div class="table">
      <div class="mui-fl-vert mui-fl-btw">
        <div class="mui-fl-vert">
          <p class="sty1-title">Details</p>
          <m-tabs v-model="query.tabsActive" class="sty1-tabs" @tab-click="handleTabsClk">
            <m-tab-pane :label="CentralizationUser.name2" name="zis"></m-tab-pane>
            <m-tab-pane label="MeID" name="meid"></m-tab-pane>
          </m-tabs>
        </div>
        <div class="operate-bar mui-fl-vert">
          <div class="mui-flex">
            <m-select v-model="query.type" clearable placeholder="zkMe account" class="sty1-select"
              popper-class="sty1-popper" @change="handleChangeSelect">
              <m-option v-for="item in searchOptions" :key="item.value" :label="item.label" :value="item.value">
              </m-option>
            </m-select>
            <m-input v-model="query.keyword" placeholder="Search" prefix-icon="mico-search"
              class="sty1-input-search marg-l16" @keyup.native.enter="handleClkSearch" @clear="handleClkSearch" clearable
              @input="handleInputSearch"></m-input>
          </div>
          <m-button class="sty1-button green marg-l16" type="primary" :disabled="query.keyword === ''" round
            @click="handleClkSearch">Search</m-button>
          <i :class="{ 'icon-download mico-download taplight': 1, 'disabled invalid': tableData.length === 0 }"
            @click="exportList" />

          <m-tooltip class="sty3-tooltip" placement="bottom" popper-class="account-detail-tootip account-detail-tootip2">
            <template slot="content">
              Submission records
            </template>
            <i class="icon-download mico-information" @click="reqState = 'list'" />
          </m-tooltip>
          <!-- <i class="icon-download mico-information" @click="reqState = 'list'" /> -->
          <!-- <m-popover v-if="query.tabsActive === 'zis'" popper-class="sty3-popper" placement="bottom-end" width="180"
            trigger="hover">
            <template slot="reference">
              <i class="icon-download mico-information" />
            </template>
            <div class="information_select" >
              <div @click="subRequest">Submit data recovery</div>
              <div @click="reqState = 'list'">Submission records</div>
            </div>
          </m-popover> -->
        </div>
      </div>
      <RequestInformation :endTime="query.endTime.toString()" :startTime="query.startTime.toString()" :type="query.type"
        :keyword="query.keyword" :selectall="selectall" :selectAccount="selectPageAccount" :state="reqState"
        @restState="reqState = ''" @selStateChange="selState = 'table'" @closeSelect="closeSelect"></RequestInformation>

      <div class="table-wrap mui-fl-col mui-fl-btw">
        <m-table v-loading="loading" :data="tableData" empty-text="  " @select-all="selectAll"
          :class="[loading && 'sty1-table-loading']" class="sty1-table" @cell-click="handleCellClick"
          @select="tableSelect" ref="multipleTable">
          <m-table-column v-if="query.tabsActive === 'zis'" type="selection" :selectable="selectableList" width="22">
          </m-table-column>
          <m-table-column :label-class-name="selState === 'table' ? 'frist-table-name' : ''" key="verifyTime"
            prop="verifyTime" label="Verify time" min-width="160" />
          <m-table-column key="Account" prop="account" label="zkMe account" min-width="180" />
          <template v-if="query.tabsActive === 'zis'">
            <m-table-column :show-overflow-tooltip="true" key="citizenship" prop="citizenship" label="Citizenship"
              min-width="180">
              <template #default="{ row }">
                <div class="citizenship">
                  <!-- <div class="citizenshipImg nation-sprites" :style="{backgroundPosition: `${(-5 - 34 * (row.countryId - 1))}px -5px`}" /> -->
                  {{ row.citizenship || '-' }}
                </div>
              </template>
            </m-table-column>
          </template>
          <m-table-column v-if="query.tabsActive !== 'meid'" prop="kycStatus" label="" min-width="180">
            <template slot="header" slot-scope="scope">
              <m-popover :key="'levelPopover' || scope" placement="bottom-start" popper-class="sty1-popper sty2-popper"
                trigger="click" v-model="showNetworkPopover">
                <m-cascader-panel v-model="cascaderValue" :props="{ expandTrigger: 'hover' }" :options="formNetWork"
                  class="sty1-cascader" @change="handleCascaderChange">
                  <template #default="{ data }">
                    <div :class="['network-list', 'mui-fl-vert', !data.label && 'mui-fl-btw firstPanel']">
                      <i v-if="data.label" :class="data.icon"></i>
                      <span>{{ data.value }}</span>
                      <m-popover trigger="hover" placement="top" width="160" popper-class="sty7-popper">
                        <div>
                          <span class="textblod">Current Status</span>
                          indicates which stage of the KYC process the user is currently in.<br />
                          Here’s what each status means:
                        </div>
                        <div class="textblod">
                          <i class="mcico-verification"></i>
                          Verification Started
                          <div>
                            The user has logged in and created an SSI wallet but has not yet started the KYC process.
                          </div>
                        </div>

                        <div class="textblod">
                          <i class="mcico-ocr"></i>
                          OCR Passed
                          <div>
                            The user has entered the KYC process and successfully passed OCR (Optical Character <br />
                            Recognition) verification. However, the face-matching step has not yet been completed.
                          </div>
                        </div>

                        <div class="textblod">
                          <i class="mcico-liveness"></i>
                          Liveness Checked
                          <div>
                            The user has successfully passed the liveness detection process, confirming their identity
                            <br />
                            through facial recognition.
                          </div>
                        </div>

                        <div class="textblod">
                          <i class="mcico-zkp"></i>
                          ZKP Generated
                          <div>
                            The user has successfully generated a Zero-Knowledge Proof (ZKP) during the KYC process,
                            <br />
                            ensuring data privacy and security.
                          </div>
                        </div>

                        <div v-if="!isNoMint" class="textblod">
                          <i class="mcico-sbtMinted"></i>
                          SBT Minted
                          <div>
                            A non-transferable token is issued to represent the user’s verified identity after completing
                            <br />
                            KYC.
                          </div>
                        </div>

                        <div class="textblod" v-if="!level && !isNoMint">
                          <i class="mcico-Authorized"></i>
                          OnChain Minted
                          <div>
                            The user has linked their verified identity to their wallet, completing the binding process<br/>
                            on the blockchain.
                          </div>
                        </div>

                        <div class="textblod">
                          <i class="mcico-Passed"></i>
                          KYC Passed
                          <div>
                            The user has successfully completed all KYC verification requirements including data <br />
                            collection and identity verification.
                          </div>
                        </div>

                        <div class="textblod">
                          <i class="mcico-Failed"></i>
                          Verification Failed
                          <div>
                            The user did not pass KYC due to unmet conditions such as nationality, age, AML, or Geo <br />
                            requirements.
                          </div>
                        </div>

                        <i style="color: #738C8F" slot="reference" class="mico-kycTip mui-fl-vert" v-if="!data.label"></i>
                      </m-popover>
                    </div>
                  </template>
                </m-cascader-panel>
                <m-button slot="reference" type="text" class="table-header-btn">
                  <span class="mui-fl-vert">
                    {{ tableHeaderNetworkTxt }}<i class="el-icon-arrow-down" />
                  </span>
                </m-button>
              </m-popover>
            </template>
            <template slot-scope="scopes">
              <div class="kycStatus mui-fl-vert">
                <i :class="formNetWork.find(x => x.value === scopes.row.kycStatus)?.icon"></i>
                {{ formNetWork.find(x => x.value === scopes.row.kycStatus)?.value || scopes.row.kycStatus}}
              </div>
            </template>
          </m-table-column>
          <m-table-column v-if="query.tabsActive === 'zis'" prop="programName" label="Program name"
            min-width="180"></m-table-column>
          <m-table-column v-if="query.tabsActive === 'zis'" prop="kycProgramId" label="Program No" min-width="160">
            <template #default="{ row }">
              <div class="mui-fl-vert">
                <span class="taplight2">{{ row.kycProgramId | formatPubKey }}</span>
                <i class="icon-copy-wallet mico-copy taplight" @click.stop="copyTxt(row.kycProgramId, row)"></i>
              </div>
            </template>
          </m-table-column>
          <template v-if="query.tabsActive === 'zis'">
            <m-table-column v-if="!isNoMint" key="Program-type" label="Program type" min-width="190">
              <template>
                {{ level | levelTxt }}
              </template>
            </m-table-column>
            <m-table-column key="ssiWalletAddress" prop="ssiWalletAddress" label="SSI address" min-width="160">
              <template #default="{ row }">
                <div class="mui-fl-vert">
                  <!-- <span class="taplight2" @click.stop="toKYT(row)">{{ row.ssiWalletAddress | formatPubKey }}</span> -->
                  <span class="taplight2">{{ row.ssiWalletAddress | formatPubKey }}</span>
                  <i class="icon-copy-wallet mico-copy taplight" @click.stop="copyTxt(row.ssiWalletAddress, row)"></i>
                </div>
              </template>
            </m-table-column>
          </template>
          <template v-if="query.tabsActive === 'meid'">
            <m-table-column key="userId" prop="userId" label="User ID" min-width="220">
              <template #default="{ row }">
                <div class="mui-fl-vert">
                  <span>{{ row.userId | formatPubKey }}</span>
                  <i class="icon-copy-wallet mico-copy taplight" @click.stop="copyTxt(row.userId)"></i>
                </div>
              </template>
            </m-table-column>
            <m-table-column key="meidDid" prop="walletAddress" label="DID" min-width="170">
              <template #default="{ row }">
                <div class="mui-fl-vert">
                  <span>{{ row.walletAddress | formatPubKey }}</span>
                  <i class="icon-copy-wallet mico-copy taplight" @click.stop="copyTxt(row.walletAddress)"></i>
                </div>
              </template>
            </m-table-column>
          </template>
        </m-table>
        <div class="mui-fl-end">
          <m-pagination v-show="tableData.length > 0" class="sty1-pagination sty3-cell" background hide-on-single-page
            @current-change="handleCurrentChange" :current-page.sync="query.page" :page-size="query.size"
            layout="prev, pager, next" :total="RP && RP.page.total">
          </m-pagination>
        </div>
        <div v-show="tableData.length === 0 && !loading" class="no-table-data mui-fl-col mui-fl-vert">
          <img src="~@/assets/img/no-table-data.png" alt="">
          <p>No Record</p>
        </div>
      </div>
    </div>
    <div class="endButton" v-if="query.tabsActive === 'zis'">
      <m-button v-if="selectAccountAll" class="sty2-button" @click="reqState = 'form'"
        :disabled="!selectAccountAll">Request recovery</m-button>
      <!-- <m-button class="sty2-button" @click="closeSelect">Cancel</m-button> -->
      <span v-if="selectAccountAll" class="selectedText">Selected:
        {{ new Intl.NumberFormat('en-US').format(selectAccountAll) }}
        {{ selectAccountAll ? '; Click the button to continue data recovery' : "" }}
      </span>
    </div>
  </div>
</template>

<script>
import { formatPubKey } from '@/utils/filters'
import { mapState } from 'vuex'
import RequestInformation from '@/components/request-information/RequestInformation.vue'
export default {
  components: { RequestInformation },
  data () {
    return {
      totalAll: null,
      RP: null,
      cascaderValue: '',
      query: {
        type: '',
        keyword: '',
        blockchainId: '',
        startTime: new Date().setHours(0, 0, 0, 0) - 3600 * 1000 * 24 * 30,
        endTime: new Date().setHours(23, 59, 59, 0o0),
        page: 1,
        size: 10,
        tabsActive: 'zis',
        kycStatus: ''
      },
      showNetworkPopover: false,
      tableHeaderNetworkTxt: 'Current Status',
      isCopyTxt: false,
      reqState: '',
      selState: '',
      selectPageAccount: {},
      selectall: false,
      selectAccountAll: 0,
      selectCount: 0,
      formatPubKey: formatPubKey,
      loading: false,
      selectAllFlag: false
    }
  },
  computed: {
    ...mapState({
      // blockChainList: ({ common }) => common.blockChainList,
      level: ({ auth }) => auth.user && auth.user.level,
      isNoMint: ({ auth }) => auth.user && auth.user.isNoMint
    }),
    picker: {
      get () {
        return this.query.startTime
          ? [this.query.startTime, this.query.endTime]
          : ''
      },
      set (val) {
        this.query.startTime = val ? new Date(val[0]).getTime() : ''
        this.query.endTime = val ? new Date(val[1]).getTime() : ''
      }
    },
    formNetWork () {
      let arr = [{
        value: 'All',
        label: '',
        icon: ''
      }, {
        value: 'Verification Started',
        label: '0',
        icon: 'mcico-verification'
      }, {
        value: 'OCR Passed',
        label: '1',
        icon: 'mcico-ocr'
      }, {
        value: 'Liveness Checked',
        label: '2',
        icon: 'mcico-liveness'
      }, {
        value: 'ZKP Generated',
        label: '3',
        icon: 'mcico-zkp'
      }, {
        value: 'SBT Minted',
        label: '4',
        icon: 'mcico-sbtMinted'
      }, {
        value: 'OnChain Minted',
        label: '5',
        icon: 'mcico-Authorized'
      }, {
        value: 'KYC Passed',
        label: '6',
        icon: 'mcico-Passed'
      }, {
        value: 'Verification Failed',
        label: '-1',
        icon: 'mcico-Failed'
      }
      ]
      if (this.level) {
        arr = arr.filter(x => x.label !== '5')
      }
      if (this.isNoMint) {
        arr = arr.filter(x => x.label !== '4' && x.label !== '5')
      }
      return arr
    },
    tableData () {
      return this.RP ? this.RP.list : []
    },
    searchOptions () {
      if (this.query.tabsActive === 'zis') {
        return [{
          value: '0',
          label: 'zkMe account'
        },
        {
          value: '5',
          label: 'Citizenship'
        },
        {
          value: '6',
          label: 'Program name'
        }]
      } else {
        return [{
          value: '0',
          label: 'zkMe account'
        },
        {
          value: '4',
          label: 'User ID'
        },
        {
          value: '1',
          label: 'DID'
        }]
      }
    },
    CentralizationUser () {
      const text = {
        name1: !this.isNoMint ? 'Authorized SBT' : 'KYC Passed Users',
        name2: !this.isNoMint ? 'SBT' : 'KYC'
      }
      return text
    }
  },
  filters: {
    levelTxt (val) {
      if (val === 0) {
        return 'On-chain Mint'
      } else if (val === 1) {
        return 'On-chain Transactional'
      } else if (val === 2) {
        return 'Cross-chain'
      }
    }
  },
  watch: {
    selectAccountAll (val, oldVal) {
      if (!val && oldVal) {
        this.selectall = false
        this.selectPageAccount = {}
      }
    },
    '$route.query' (n, o) {
      this.getList()
      this.filterTableList()
      if ((!Object.keys(n).length)) {
        this.tableHeaderNetworkTxt = 'Current Status'
      }
      // 浏览器后退进行数据监听
    },
    level (n, o) {
      if (n || n === 0) {
        this.getList()
        // this.level !== 2 && !this.blockChainList && this.$store.dispatch('getBlockChainList')
      }
    },
    reqState (val) {
      if (val === 'list') {
        window.gtag('event', 'Submission_records', {
          app_name: 'zkMe Dashboard'
        })
      }
    },
    selState (val) {
      if (val === 'table') {
        this.$nextTick(() => {
          const checked = document.getElementsByClassName('el-checkbox__input')[0]
          if (!this.RP.otherData.decryptCount) {
            checked.style.cursor = 'no-drop'
          } else {
            checked.style.cursor = ''
          }
        })
      }
    }
    // blockChainList (newValue) {
    //   if (newValue) {
    //     Object.keys(newValue).forEach(m => {
    //       this.blockChainList[m].forEach(n => {
    //         if (Number(this.query.blockchainId) === n.id) {
    //           this.tableHeaderNetworkTxt = n.chainName
    //         }
    //       })
    //     })
    //   }
    // }
  },
  created () {
    if (Object.keys(this.$route.query).includes('kycStatus')) {
      this.tableHeaderNetworkTxt = this.formNetWork.find(x => x.label === this.$route.query.kycStatus).value
    } else {
      this.tableHeaderNetworkTxt = 'Current Status'
    }
    if (this.level || this.level === 0) {
      this.getList()
      // this.level !== 2 && !this.blockChainList && this.$store.dispatch('getBlockChainList')
    }
    // this.totalAllCount()
  },
  methods: {
    async totalAllCount () {
      const form = {
        startTime: this.$options.filters.timestampDate(this.query.startTime),
        endTime: this.$options.filters.timestampDate(this.query.endTime)
      }
      const rp = await this.$api.request('dashboard.getList', form)
      this.totalAll = rp.data.page.total + rp.data.otherData.meIdAuthorizedCount
    },
    selectableList (row) {
      if (row.kycStatus !== 'KYC Passed') return false
      return true
    },
    subRequest () {
      window.gtag('event', 'Submit_recovery', {
        app_name: 'zkMe Dashboard'
      })
      if (this.query.tabsActive === 'zis') {
        this.selState = 'table'
      }
    },
    selectAll (val) {
      if (!this.RP.otherData.decryptCount) {
        this.$refs.multipleTable.clearSelection()
        return
      }
      this.$nextTick(() => {
        const checked = document.getElementsByClassName('el-checkbox__original')[0].checked
        if (!this.selectAllFlag) {
          this.selectPageAccount = {}
        }
        this.selectall = checked
        this.selectAllFlag = false
        if (this.tableData.length && this.selectall) {
          this.selectAccountAll = this.RP.otherData.decryptCount
        }
        this.filterTableList()
      })
    },
    tableSelect (val, row) {
      if (this.selectall) {
        const user = this.tableData.filter(x => !val.some(y => x.zkmeId === y.zkmeId && x.kycProgramId === y.kycProgramId) && x.kycStatus === 'KYC Passed')
        this.$set(this.selectPageAccount, this.query.page, user)
      } else {
        this.$set(this.selectPageAccount, this.query.page, val)
      }
      if (!val.length && this.selectAccountAll !== this.RP.otherData.decryptCount) {
        this.$nextTick(() => {
          const checked = document.getElementsByClassName('el-checkbox__input')[0]
          setTimeout(() => {
            checked.className = 'el-checkbox__input is-indeterminate'
          }, 300)
        })
      }
      this.filterTableList()
    },
    filterTableList () {
      let pageTotal = 0
      if (this.selectall) {
        pageTotal = this.RP.otherData.decryptCount
      } else {
        this.selectAccountAll = 0
      }
      for (const i in this.selectPageAccount) {
        if (this.selectall) {
          pageTotal -= this.selectPageAccount[i].filter(x => x.kycStatus === 'KYC Passed').length
          this.selectAccountAll = pageTotal
        } else {
          pageTotal += this.selectPageAccount[i].filter(x => x.kycStatus === 'KYC Passed').length
          this.selectAccountAll = pageTotal
        }
      }
    },
    closeSelect () {
      this.clearSelectList()
      this.selState = ''
    },
    async getList () {
      const qr = this.query
      const $qr = this.$route.query

      qr.kycStatus = $qr.kycStatus || ''
      qr.type = $qr.type || ''
      qr.keyword = $qr.keyword || ''
      qr.startTime = $qr.startTime || new Date().setHours(0, 0, 0, 0) - 3600 * 1000 * 24 * 30
      qr.endTime = $qr.endTime || new Date().setHours(23, 59, 59, 0o0)
      qr.page = Number($qr.page) || 1
      qr.size = Number($qr.size) || 10
      qr.tabsActive = $qr.tabsActive || 'zis'
      qr.blockchainId = $qr.blockchainId || ''

      const postData = Object.assign({}, this.query, {
        account: ((this.query.type === '' || this.query.type === '0') && this.query.keyword) || '',
        userWalletAddress: (this.query.type === '1' && this.query.keyword) || '',
        tokenId: (this.query.type === '2' && this.query.keyword) || '',
        blockchainName: (this.query.type === '3' && this.query.keyword) || '',
        userId: (this.query.type === '4' && this.query.keyword) || '',
        citizenship: (this.query.type === '5' && this.query.keyword) || '',
        programName: (this.query.type === '6' && this.query.keyword) || '',
        startTime: this.$options.filters.timestampDate(this.query.startTime),
        endTime: this.$options.filters.timestampDate(this.query.endTime),
        kycStatus: this.query.kycStatus
      })

      this.loading = true
      const rp = await this.$api.request(`${this.query.tabsActive === 'zis' ? 'dashboard.getList' : 'dashboard.getMeIDList'}`, postData)
      this.loading = false
      if (rp.code === ********) {
        this.RP = rp.data
        this.selectCount = rp.data.page.total
        if (this.$route.query.kycStatus) {
          this.totalAllCount()
        } else {
          if (this.query.tabsActive === 'meid') {
            const rp = await this.$api.request('dashboard.getList', postData)
            this.totalAll = rp.data.page.total + rp.data.otherData.meIdAuthorizedCount
          } else {
            this.totalAll = rp.data.page.total + rp.data.otherData.meIdAuthorizedCount
          }
        }
        if (this.selectall) {
          this.nextPageSelect = true
          if (!this.RP.list.filter(x => x.kycStatus === 'KYC Passed').length && this.selectAccountAll) {
            this.selectAllFlag = true
            this.$refs.multipleTable.toggleAllSelection()
          }
          this.$nextTick(() => {
            setTimeout(() => {
              if (this.selectAccountAll !== rp.data.otherData.decryptCount) {
                const checked = document.getElementsByClassName('el-checkbox__input')[0]
                checked.className = 'el-checkbox__input is-indeterminate'
              }
            }, 300)
          })
          for (const row of rp.data.list) {
            if (row.kycStatus !== 'KYC Passed') {
              continue
            }
            setTimeout(() => {
              this.$refs.multipleTable.toggleRowSelection(row)
            }, 300)
          }
        }
        if (this.selectPageAccount[this.query.page]?.length) {
          rp.data.list.forEach((row, index) => {
            if (this.level === 2) {
              if (this.selectPageAccount[this.query.page].find(x => x.userId === row.userId && x.programName === row.programName)) {
                setTimeout(() => {
                  this.$refs.multipleTable.toggleRowSelection(rp.data.list[index])
                }, 300)
              }
            } else {
              if (this.selectPageAccount[this.query.page].find(x => x.zkmeId === row.zkmeId && x.kycProgramId === row.kycProgramId)) {
                setTimeout(() => {
                  this.$refs.multipleTable.toggleRowSelection(rp.data.list[index])
                }, 300)
              }
            }
          })
        }
      }
    },

    async exportList () {
      if (this.tableData.length === 0) return
      window.gtag('event', 'Download_list', {
        app_name: 'zkMe Dashboard'
      })
      const qr = this.query
      const $qr = this.$route.query

      qr.type = $qr.type || ''
      qr.keyword = $qr.keyword || ''
      qr.startTime = $qr.startTime || (new Date().getTime() - 3600 * 1000 * 24 * 30)
      qr.endTime = $qr.endTime || new Date().getTime()
      qr.page = Number($qr.page) || 1
      qr.size = Number($qr.size) || 10

      const postData = Object.assign({}, this.query, {
        account: ((this.query.type === '' || this.query.type === '0') && this.query.keyword) || '',
        userWalletAddress: (this.query.type === '1' && this.query.keyword) || '',
        tokenId: (this.query.type === '2' && this.query.keyword) || '',
        blockchainName: (this.query.type === '3' && this.query.keyword) || '',
        citizenship: (this.query.type === '5' && this.query.keyword) || '',
        programName: (this.query.type === '6' && this.query.keyword) || '',
        startTime: this.$options.filters.timestampDate(this.query.startTime),
        endTime: this.$options.filters.timestampDate(this.query.endTime)
      })

      this.$api.request(`${this.query.tabsActive === 'zis' ? 'dashboard.exportList' : 'dashboard.exportMeIDList'}`, postData)
        .then(data => {
          if (Object.prototype.toString.call(data) === '[object Blob]') {
            this.downloadFile(data)
          }
        })
    },

    downloadFile (data) {
      window.gtag('event', 'Submit_recovery', {
        app_name: 'zkMe Dashboard'
      })
      const url = URL.createObjectURL(new Blob([data]))
      const ndLink = document.createElement('a')
      ndLink.style.display = 'none'
      ndLink.download = `${this.$options.filters.timestampDate(this.$options.filters.timestampDate((new Date()).getTime(), '', '_'))}.xlsx`
      ndLink.href = url
      document.body.appendChild(ndLink)
      ndLink.click()
      document.body.removeChild(ndLink)
    },

    handleCurrentChange () {
      this.$router.push({
        query: {
          startTime: this.query.startTime,
          endTime: this.query.endTime,
          type: this.query.type,
          keyword: this.query.keyword,
          kycStatus: this.query.kycStatus,
          // account: this.query.account,
          // tokenId: this.query.tokenId,
          // userWalletAddress: this.query.userWalletAddress,
          blockchainId: this.query.blockchainId,
          // blockchainName: this.query.blockchainName,
          page: this.query.page,
          size: this.query.size,
          tabsActive: this.query.tabsActive
        }
      })
    },

    handleChangeDate () {
      this.clearSelectList()
      this.$router.push({
        query: Object.assign({}, this.query, {
          type: this.query.type,
          keyword: this.query.keyword,
          page: 1
        })
      })
      // this.totalAllCount()
    },

    handleChangeSelect () {
      if (this.query.type !== '' && this.query.keyword === '') return
      this.handleClkSearch()
    },

    clearSelectList () {
      this.selectPageAccount = {}
      this.selectall = false
      this.selectAccountAll = 0
      this.selectCount = 0
      this.$refs.multipleTable.clearSelection()
    },

    handleClkSearch () {
      this.clearSelectList()
      this.$router.push({
        query: Object.assign({}, this.query, {
          type: this.query.type,
          keyword: this.query.keyword,
          page: 1
        })
      })
    },

    handleInputSearch (val) {
      this.query.keyword = (val.replace(/[^0-9,@,.,:,\-,_ ,a-z,A-Z\s]/g, '')).replace(/^\s+/, '')
    },

    handleCascaderChange (val) {
      this.clearSelectList()
      this.tableHeaderNetworkTxt = this.cascaderValue.toString()
      this.query.kycStatus = this.formNetWork.find(x => x.value === val[0]).label || ''
      this.query.page = 1
      this.showNetworkPopover = false
      this.handleCurrentChange()
    },

    // 复制
    async copyTxt (text) {
      this.isCopyTxt = true
      if (navigator.clipboard && window.isSecureContext) {
        try {
          await navigator.clipboard.writeText(text)
          this.$message({
            message: 'Copied',
            iconClass: 'mico-lightTip',
            customClass: 'sty1-message',
            duration: 3000,
            offset: 32,
            center: true
          })
          return
        } catch (err) {
          console.error(err)
          return false
        }
      } else {
        const textarea = document.createElement('textarea')
        textarea.textContent = text
        textarea.style.position = 'fixed'
        document.body.appendChild(textarea)
        textarea.select()
        try {
          document.execCommand('copy')
          this.$message({
            message: 'Copied',
            iconClass: 'mico-lightTip',
            customClass: 'sty1-message',
            duration: 3000,
            offset: 32,
            center: true
          })
          return
        } catch (ex) {
          this.$message({
            message: 'Copy to clipboard failed.',
            iconClass: 'mico-lightTip',
            customClass: 'sty1-message',
            duration: 3000,
            offset: 32,
            center: true
          })
          return false
        } finally {
          document.body.removeChild(textarea)
        }
      }
    },
    handleCellClick (row, column, cell, event) {
      if (this.isCopyTxt) {
        this.isCopyTxt = false
        return
      }
      if (column.label === 'Authorization info' || this.query.tabsActive === 'meid') {
        return
      }
      this.toDetail(row)
    },
    toDetail (row) {
      this.$router.push({
        path: '/dashboard/account-detail',
        query: Object.assign({}, {
          blockchainId: row.blockchainId,
          userWalletAddress: row.userWalletAddress,
          zkmeId: row.zkmeId,
          kycProgramId: row.kycProgramId,
          status: row.kycStatus,
          account: row.account,
          popBindingId: row.popBindingId
        }, this.level === 2 ? {
          userId: row.userId
        } : {})
      })
    },
    handleTabsClk (val) {
      if (this.query.tabsActive === 'meid') {
        this.closeSelect()
        this.selectall = false
        this.selectAccountAll = 0
        this.selectCount = 0
        this.selectPageAccount = {}
      }
      this.$router.push({
        query: Object.assign({}, this.query, {
          tabsActive: this.query.tabsActive,
          type: '',
          keyword: '',
          page: 1
        })
      })
    },
    rowClassName () {
      if (this.query.tabsActive === 'zis') {
        return 'taplight'
      }
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/_dashboard.scss" scoped></style>
