.mui-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.mui-aside {
  width: 270px;
  background: #E6EDED;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  height: 100vh;
  z-index: 11;

  .logo-header {
    position: relative;
    padding: 30px 31px 54px 40px;
    &::before {
      content: '';
      position: absolute;
      width: 190px;
      height: 1px;
      background-color: #CEDBDB;
      bottom: 0;
    }
  }
  .logo {
    position: relative;
    box-sizing: border-box;

    >span {
      display: inline-block;
      width: 115px;
      height: 36px;
      background: url('~@/assets/img/logo.png') no-repeat;
      background-size: 100%;
    }
  }

  .business {
    color: #005563;
    font-size: 12px;
    font-weight: 700;
    line-height: 150%;
    padding: 0.5px 15px;
    border-radius: 6.5px 6.5px 6.5px 0;
    background-color: #A9E1D3;
    margin-left: 4px;
  }

  .scrollbox {
    height: 100%;
    padding-top: 16px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.mui-content {
  overflow-y: auto;
  margin-left: 270px;
  min-height: 100%;
  height: 100vh;
}

.mui-header {
  position: sticky;
  top: 0;
  z-index: 999;
  padding: 26px 60px 26px 0;
  font-weight: 700;
  line-height: 18px;
  color: #33585C;
  background-color: #fff;

  img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }

  i {
    font-size: 16px;
    margin-left: 4px;
  }
}

.setup {
  padding: 40px;

  i {
    font-size: 30px;
    color: #738C8F;
  }

  i+i {
    margin-left: 20px;
  }
}

@media screen and (max-width: 1200px) {
  .mui-aside {
    width: 150px;

    .logo-header {
      padding: 30px 15px 62px 15px;
      &::before {
        width: 70px;
        left: 37px;
      }      
    }
    .logo {
      position: relative;
      box-sizing: border-box;

      >span {
        width: 66px;
        height: 20px;
        background: url('~@/assets/img/logo0.png') no-repeat;
        background-size: 100%;
      }
    }
    .business {
      transform: scale(0.6126);
      transform-origin: left;
    }

    .scrollbox {
      .menu-active {
        left: 1px;
      }
    }
  }
  .mui-content {
    margin-left: 150px;
  }
}
.user-set li {
  padding: 5px 8px;
  line-height: 22px;
  overflow: hidden;
  color: #002E33;
  border-radius: 6px;
  &:hover {
    background: #F7F7F7;
  }
}
.user-set li + li {
  margin-top: 2px;
}