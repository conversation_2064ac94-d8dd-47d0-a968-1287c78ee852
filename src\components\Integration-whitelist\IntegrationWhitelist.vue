<template>
  <div class="mui-fl-vert mui-fl-col asd">
    <div class="mui-fl-vert igTabs">
      <div :class="['igTexT', 'igcur', !tabsFlg && 'borb']" @click="changeTabs(false, 'Whitelist')" style="margin-right: 40px;">
        Whitelist</div>
      <div :style="{ opacity: !whiteList.officialUrls.length ? '0.5' : '1' }"
        :class="['igTexT', 'igcur', 'mui-fl-vert', tabsFlg && 'borb']" @click="changeTabs(true, 'Configuration')">

        <m-popover v-if="!  whiteList.officialUrls.length" width="154px" placement="top" trigger="hover"
          popper-class="NotRecord_popover WhiteList" >
          <div slot="reference" class="mui-fl-central">
            <i class="mico-lock cfLock"></i>
          </div>
          <div style="text-align: left;">
            To unlock this part, please save your information in the <span style="font-weight: 700;">Whitelist</span> first.
          </div>
        </m-popover>
        Configuration
      </div>
    </div>
    <div v-if="!tabsFlg" style="max-width: 820px;">
      <div class="igTexT">Whitelist information</div>
      <div class="igTexts igtip">To enhance your security and optimize your experience, we kindly request your permission
        to add your domain to our whitelist.</div>
      <div class="igTexT" style="font-size: 18px;">Your official URL?</div>

      <m-form :model="data" :rules="rules" class="url sty2-form" v-for="(data, index) of whiteUrlList" :key="index"
        :hide-required-asterisk="true" @submit.native.prevent>
        <m-form-item prop="url">
          <div class="mui-fl-vert creatURL" @mouseenter="deleteFlg = index" @mouseleave="deleteFlg = ''">
            <div class="igTexts urlText">URL{{ index + 1 }}</div>
            <m-input data-input ref="inp" class="sty1-input sty3-input creatinp" v-if="subText === 'Save'" type="text" placeholder="Please enter..." @clear="dc(index)" @input="changeURL(index)"
              v-model="data.url">
              <i v-if="data.url" @click="dc(index, data, 'celarIput')" slot="suffix" class="mcico-celar"></i>
            </m-input>
            <a data-a v-else class="openUrl" :href="data.url" target="_blank">{{ data.url }}</a>
            <m-popover
                v-if="subText === 'Save' && whiteUrlList.length === 1"
                :disabled="whiteUrlList.length !== 1"
                width="154px" placement="top"
                trigger="hover"
                popper-class="NotRecord_popover WhiteList"
                content="At least 1 URL is required"
                >
              <div v-show="deleteFlg === index" slot="reference"
                :class="['delete', 'igcur', whiteUrlList.length === 1 && 'unDelete']" @click="whiteUrlListaDel(index, data)">
                Delete</div>
            </m-popover>
            <div v-else-if="subText === 'Save' && whiteUrlList.length > 1 && deleteFlg === index" style="margin-top: 20px;" class="delete igcur" @click="whiteUrlListaDel(index, data)">
              Delete
            </div>
            </div>
          </m-form-item>
      </m-form>
      <div v-if="subText === 'Save' && whiteUrlList.length <= 9" class="addurl igcur mui-fl-vert" @click="whiteUrlListaAdd"><i class="mico-creat"></i>Add new URL
      </div>
      <div>
        <m-button :class="['save', saveFlg]" @click="save">{{ subText }}</m-button>
      </div>
    </div>

    <div v-else class="mui-fl-vert mui-fl-col">
      <div class="con-title mui-fl-central"><i class="mico-file"></i> <a href="https://docs.zk.me/zkme-dochub/" target="_blank"
          class="documentations">View support documentations</a></div>
      <div class="configuration">
        <!-- <div class="igTexT mui-fl-vert mb" style="font-size: 24px">
          <div>
            Your account
          </div>
          <div v-if="whiteList.pubKeyGenerated" class="igTexts lock mui-fl-vert"><i class="mico-lock"></i>Locked</div>
          <div v-else class="igTexts lock mui-fl-vert"><i class="mico-unLock"></i>Unlock</div>
        </div>
        <div class="con-text">Account address</div>
        <div class="igTexts mbs">This account is utilized for invoking the zkMe smart contract and storing authorization
          information from your users.</div> -->
        <!-- <div class="mb mui-fl-vert edAddress">
          <div class="addressName">
            <div class="igTexts">EVM address</div>
            <div class="igTexts">Cosmos address(sei)</div>
            <div class="igTexts">Cosmos address(neutron)</div>
            <div class="igTexts">Aptos address</div>
          </div>
          <div class="addressWl">
            <div class="igTexts addresscor">{{ wallet.address }}
              <m-popover placement="top" width="64" trigger="manual" :close-delay="1000" v-model="popoverFlg1"
                popper-class="NotRecord_popover WhiteList" content="Copied!">
                <i slot="reference" class="mico-copy igcur" @click="copy(wallet.address, 'evm')"></i>
              </m-popover>
            </div>
            <div class="igTexts addresscor">{{ wallet.seiAddress }}
              <m-popover placement="top" width="64" trigger="manual" :close-delay="1000" v-model="popoverFlg2"
                popper-class="NotRecord_popover WhiteList" content="Copied!">
                <i slot="reference" class="mico-copy igcur" @click="copy(wallet.seiAddress, 'com')"></i>
              </m-popover></div>
            <div class="igTexts addresscor">{{ wallet.neutronAddress }}
              <m-popover placement="top" width="64" trigger="manual" :close-delay="1000" v-model="popoverFlg3"
                popper-class="NotRecord_popover WhiteList" content="Copied!">
                <i slot="reference" class="mico-copy igcur" @click="copy(wallet.neutronAddress, 'neutron')"></i>
              </m-popover></div>
            <div class="igTexts addresscor">{{ wallet.aptosAddress }}
              <m-popover placement="top" width="64" trigger="manual" :close-delay="1000" v-model="popoverFlg4"
                popper-class="NotRecord_popover WhiteList" content="Copied!">
                <i slot="reference" class="mico-copy igcur" @click="copy(wallet.aptosAddress, 'aptos')"></i>
              </m-popover></div>
          </div>
        </div> -->
        <div class="mui-fl-vert mbs">
          <div class="con-text" style="font-size: 24px;">Your Private key</div>
          <div v-if="whiteList.pubKeyGenerated" class="igTexts lock mui-fl-vert">
            <i class="mico-lock"></i>
            {{ whiteList.pubKeyGenerated ? 'Locked' : 'Unlock' }}
          </div>
        </div>
        <div class="dwJson">
          <div class="igTexts dwTip mui-fl-vert">
            <img class="private" src="@/assets/img/white-list-private.png" alt="">
            <div class="diaLockText">
              This private key is used for decrypting users' original KYC files. We will not store this private key, so please ensure its proper safekeeping.
            </div>
          </div>
          <div class="igTexts addresscor dwrad">
            <div class="mui-fl-vert" @click="!whiteList.pubKeyGenerated && (radio = !radio)" style="cursor: pointer;">
              <div v-if="!radio && !whiteList.pubKeyGenerated" class="radio"></div>
              <i v-else class="mcico-success2 diaIcon"></i>
              <div>I am aware of this and will ensure its proper safekeeping.</div>
            </div>
          </div>
          <m-button v-if="!whiteList.pubKeyGenerated" @click="dwJson" class="save dwbut" :style="{ opacity: !radio ? '0.5' : '1' }">
            Download JSON file
          </m-button>
          <div v-else class="jsonSs mui-fl-vert">
            <i class="mcico-success2"></i>
            JSON file downloaded
          </div>
        </div>
      </div>
    </div>

    <m-dialog
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="dialogTableVisible"
      custom-class="sty1-dialog sty4-dialog"
      @close="closeCode"
    >
      <div slot="title" class="mui-fl-vert">
        <i class="mcico-success2 diaIcon"></i>
        <div class="con-text">JSON file downloaded</div>
      </div>
      <div>
        <div class="igTexts diaPls">Please keep your documents safe, as they can not be recovered once lost. </div>
        <div class="igTexts diaTip">Now enter your verification code on your file to lock <br/> your account information.</div>
        <div class="container">
          <div class="inputBox">
            <div>
              <input v-for="(item, index) in captchas" :key="index" v-model="item.num" :id="'captcha' + index"
                @input="inputFinish(index)" @focus="adjust(index)" @keydown="inputDirection(index, $event)" class="auth-b"
                type="tel" maxlength="1" :style="{ borderColor: !checkFlg ? '#f0f0f0' : '#EE6969' }" />
            </div>
          </div>
        </div>
        <div class="mui-fl-central">
          <m-popover width="308px" placement="top" trigger="hover"
          popper-class="NotRecord_popover WhiteList jsonTip" >
          <div slot="reference" class="jsonTipText">
            <div class="igTexts lockDialog">Why is it necessary to lock? <i class="mico-question"></i></div>
          </div>
          <div style="text-align: left;">
            zkMe won't save your JSON file. If your account<br/>
            information is not locked, re-entering the page will<br/>
            randomly generate a new address and the<br/>
            corresponding JSON file.
          </div>
        </m-popover>
        </div>
        <div class="mui-fl-central gTexts diaLockBut" @click="checkCode">
          <span>Lock account information</span>
        </div>
        <div class="mui-fl-central igTexts diaFoot igcur lockDialog" style="color: #33585C;" @click="dwJson">Download failed? Re-download</div>
      </div>
    </m-dialog>

    <m-dialog
      :show-close="false"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="leaveTip"
      custom-class="sty1-dialog sty4-dialog"
    >
      <div slot="title" class="mui-fl-vert">
        <i class="mico-warning diaIcon"></i>
        <div class="leaveTipTitle">Confirm to leave the page?</div>
      </div>
      <div v-if="!tabsFlg" class="igTexts leaveTipText">Still have unsaved information.</div>
      <div v-else class="igTexts leaveTipText">Account information is not locked.
        Refreshing the page will generate a new account address and private key.<br>
         The current information will become invalid.</div>
      <div slot="footer" class="dialog-footer">
        <m-button class="c igTexts" @click="leaveTip = false">
          <span>Cancel</span>
        </m-button>
        <m-button class="l igTexts" type="primary" @click="leave">
          <span>Leave</span>
        </m-button>
      </div>
    </m-dialog>

  </div>
</template>

<script>
import { Wallet } from 'ethers'
const sjcl = window.sjcl
export default {
  props: {
    whiteList: {
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      pk: '',
      url: '',
      inpIndex: 0,
      leaveTip: false,
      checkFlg: false,
      captchaCode: '',
      activeInput: 0,
      popoverFlg1: false,
      popoverFlg2: false,
      popoverFlg3: false,
      popoverFlg4: false,
      walletFlg: false,
      captchas: [{ num: '' }, { num: '' }, { num: '' }, { num: '' }],
      dialogTableVisible: false,
      dialogFlg: '',
      pubKeyGenerated: true,
      radio: false,
      tabsFlg: false,
      deleteFlg: 1,
      saveFlg: '',
      subText: 'Save',
      callbakTip: false,
      whiteUrlList: [],
      routeLeave: '/',
      wallet: {
        address: '',
        seiAddress: '',
        neutronAddress: '',
        aptosAddress: '',
        privateKey: ''
      }
    }
  },
  mounted () {
    if (!this.whiteList.officialUrls.length) {
      this.whiteUrlList.push({ url: '', flg: false })
    } else {
      this.whiteList.officialUrls.map(x => this.whiteUrlList.push({ url: x, flg: false }))
      this.subText = 'Modify'
      this.$emit('leaveFlg', 'Modify')
      this.saveFlg = ''
    }
    this.walletFlg = this.whiteList.pubKeyGenerated
    this.findUrl()
  },
  computed: {
    rules () {
      return {
        url: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callbak, source) => {
              const reg = /^(https?:\/\/)/
              if (value) {
                try {
                // eslint-disable-next-line no-new
                  new URL(value)
                } catch (error) {
                  return callbak('Please enter the correct URL.')
                }
              }
              if (!value.replace(reg).includes('undefined') && value) {
                callbak('Please enter the correct URL.')
                this.saveFlg = 'saveDe'
              } else {
                if (!value) {
                  this.saveFlg = 'saveDe'
                  return
                }
                if (this.inpIndex === 'error') {
                  callbak('Please enter the correct URL.')
                  this.saveFlg = 'saveDe'
                } else {
                  if ((this.inpIndex === 'delete' || this.inpIndex === 'pass') && this.whiteUrlList.length > 1) return
                  for (const i in this.whiteUrlList) {
                    if (this.whiteUrlList[i].url.length >= 100) {
                      callbak('Please enter the correct URL.')
                      this.saveFlg = 'saveDe'
                      return
                    }
                    if (this.whiteUrlList[i].url === value && this.whiteUrlList.length > 1 && i !== this.inpIndex.toString()) {
                      callbak('Please enter a different URL.')
                      this.callbakTip = true
                      this.saveFlg = 'saveDe'
                      return
                    } else if (!this.whiteUrlList[i].url && this.whiteUrlList.length > 1) {
                      this.saveFlg = 'saveDe'
                      continue
                    } else {
                      this.saveFlg = ''
                    }
                  }
                }
              }
            }
          }
        ]
      }
    }
  },
  watch: {
    'whiteList.officialUrls' (val, oldval) {
      if (!oldval.length && val.length) {
        this.changeTabs(true)
      }
    },
    tabsFlg (val) {
      if (val) {
        if (!this.whiteList.pubKeyGenerated) {
          this.radio = false
        }
        this.closeCode()
      }
    }
  },
  methods: {
    closeCode () {
      setTimeout(() => {
        this.generateMchKeys()
      }, 0)
    },
    copy (data, index) {
      const inp = document.createElement('input')
      document.body.appendChild(inp)
      inp.value = data
      inp.select()
      document.execCommand('Copy')
      document.body.removeChild(inp)
      if (index === 'evm') {
        this.popoverFlg1 = true
      } else if (index === 'com') {
        this.popoverFlg2 = true
      } else if (index === 'neutron') {
        this.popoverFlg3 = true
      } else if (index === 'aptos') {
        this.popoverFlg4 = true
      }
      let time = null
      time = setTimeout(() => {
        this.popoverFlg1 = false
        this.popoverFlg2 = false
        this.popoverFlg3 = false
        this.popoverFlg4 = false
        clearTimeout(time)
      }, 1000)
    },
    changeURL (index) {
      try {
        this.inpIndex = 'delete'
        // const url = new URL(this.whiteUrlList[index].url)
        // this.whiteUrlList[index].url = url.origin
        setTimeout(() => {
          this.dc(index, '', 'changeURL')
        })
      } catch (error) {
        this.inpIndex = 'error'
      }
    },
    async checkCode () {
      let code = ''
      this.captchas.map(v => { code += v.num })
      if (code === btoa(this.whiteList.randDigits)) {
        // const wallets = [{ walletAddress: this.wallet.address, type: 1 }, { walletAddress: this.wallet.seiAddress, type: 2 }, { walletAddress: this.wallet.neutronAddress, type: 3 }, { walletAddress: this.wallet.aptosAddress, type: 4 }]
        const reqobj = {
          publicKey: this.pk,
          randDigits: code
        }
        const rp = await this.$api.request('apikey.updateMchConfig', reqobj, {}, true)
        if (rp.code === 80000000) {
          this.walletFlg = true
          this.$emit('updataList')
          this.dialogTableVisible = false
        }
      } else {
        this.checkFlg = true
      }
    },
    async generateMchKeys () {
      if (this.walletFlg) return
      const w = Wallet.createRandom()
      this.wallet.privateKey = w.privateKey
      // eslint-disable-next-line new-cap
      const point = sjcl.ecc.curves.c256.G.mult(new sjcl.bn(this.wallet.privateKey))
      this.pk = sjcl.codec.hex.fromBits(point.toBits())
    },
    adjust (index) {
      if (this.checkFlg) {
        this.checkFlg = false
        this.captchas = [{ num: '' }, { num: '' }, { num: '' }, { num: '' }]
      }
      const dom = document.getElementById('captcha' + this.activeInput)
      if (index !== this.activeInput && dom) {
        dom.focus()
      }
      if (!this.captchas.filter(x => x.num !== '').length) {
        const dom = document.getElementById('captcha0')
        if (dom) {
          dom.focus()
        }
      }
    },
    // 前后输入方向
    inputDirection (index, event) {
      const value = this.captchas[index].num
      if (event.keyCode === 8 && value === '') {
        const dom = document.getElementById('captcha' + (index - 1))
        this.activeInput = index - 1
        if (dom) dom.focus()
      }
      if (event.keyCode !== 8 && value !== '') {
        const dom = document.getElementById('captcha' + (index + 1))
        this.activeInput = index + 1
        if (dom) dom.focus()
      }
    },
    // 输入框
    inputFinish (index) {
      if (isNaN(Number(this.captchas[index].num))) {
        this.captchas[index].num = ''
        return
      }
      this.checkFlg = false
      const value = this.captchas[index].num
      this.activeInput = value ? index + 1 : index - 1
      const dom = document.getElementById('captcha' + this.activeInput)
      if (dom) dom.focus()
      if (index === this.captchas.length - 1) {
        this.captchaCode = this.captchas.map((a) => a.num).join('')
      }
      if (!this.captchas.find(x => !x.num)) {
        let code = ''
        this.captchas.map(v => { code += v.num })
        if (code === btoa(this.whiteList.randDigits)) {
          this.checkFlg = false
        } else {
          this.checkFlg = true
        }
      }
    },
    dwJson () {
      if (!this.radio) return
      this.dialogTableVisible = true
      const type = 'application/json'
      const enc = new TextEncoder()
      const blob = new Blob([enc.encode(JSON.stringify({
        privateKey: this.wallet.privateKey,
        code: btoa(this.whiteList.randDigits)
      }))], { type })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'privateKey.json'
      a.click()
      a.remove()
    },
    changeTabs (val, i) {
      // if (this.subText === 'Save' && this.whiteList.officialUrls.length) {
      //   for (const i in this.whiteUrlList) {
      //     if (this.whiteList.officialUrls[i] !== this.whiteUrlList[i].url) {
      //       this.leaveTip = true
      //     }
      //   }
      // }
      // if (this.tabsFlg && !val && !this.whiteList.pubKeyGenerated) {
      //   this.leaveTip = true
      // }
      if (typeof val === 'string') {
        this.routeLeave = val
      } else {
        this.routeLeave = ''
      }
      if ((this.subText === 'Save' && this.whiteList.officialUrls.length) || (this.whiteUrlList.find(x => x.url) && !this.whiteList.officialUrls.length && this.routeLeave)) {
        this.leaveTip = true
        this.$emit('leaveFlg', 'Save')
      }
      if (!this.whiteList.officialUrls.length || this.leaveTip || typeof val !== 'boolean') return
      this.tabsFlg = val
      this.$emit('changeTabs', val)
    },
    async save () {
      if (this.saveFlg === '' && this.subText !== 'Modify') {
        const urls = []
        this.whiteUrlList.map(x => urls.push(x.url))
        const rp = await this.$api.request('apikey.updateUrls', urls.join(','), {}, true)
        if (rp.code === 80000000) {
          this.$emit('updataList')
        } else {
          this.inpIndex = 'error'
          rp.msg.split(',').map(x => {
            this.$refs.inp[x].elForm.validate((res) => {})
          })
          return
        }
      }
      if (this.saveFlg !== 'saveDe' && this.subText !== 'Modify') {
        this.subText = 'Modify'
        this.$emit('leaveFlg', 'Modify')
        this.$message({
          message: 'Save success',
          iconClass: 'mcico-success',
          customClass: 'sty4-message',
          duration: 3000,
          offset: 32,
          center: true
        })
      } else if (this.saveFlg !== 'saveDe' && this.subText === 'Modify') {
        this.subText = 'Save'
        this.$emit('leaveFlg', 'Save')
      }
    },
    whiteUrlListaAdd () {
      this.whiteUrlList.push({ url: '', flg: false })
      this.saveFlg = 'saveDe'
    },
    whiteUrlListaDel (index, data) {
      if (this.whiteUrlList.length > 1) {
        this.whiteUrlList.splice(index, 1)
        this.inpIndex = 'delete'
        this.findUrl()
        setTimeout(() => {
          this.dc(index, '', 'delete')
        })
      }
    },
    findUrl () {
      for (const index in this.whiteUrlList) {
        if (!this.whiteUrlList[index].url) {
          this.saveFlg = 'saveDe'
          return
        } else {
          this.saveFlg = ''
        }
      }
    },
    dc (index, data, text) {
      if (text === 'celarIput') {
        data.url = ''
      }
      for (const i in this.whiteUrlList) {
        this.whiteUrlList[i].flg = false
        this.inpIndex = i
        if (this.whiteUrlList.filter(x => x.url === this.whiteUrlList[i].url).length > 1 && !this.whiteUrlList.filter(x => x.flg).length) {
          if (!this.whiteUrlList.filter(x => x.flg).length) {
            this.whiteUrlList[i].flg = true
            this.inpIndex = 'pass'
            this.$refs.inp[i].elForm.validate((res) => {})
          }
        } else {
          this.whiteUrlList[i].flg = false
          this.$refs.inp[i].elForm.validate((res) => {})
        }
      }
      if (text === 'delete' || text === 'changeURL') return
      this.$refs.inp[index].focus()
      this.saveFlg = 'saveDe'
    },
    leave () {
      if (this.routeLeave) {
        this.$emit('leaveFlg', 'leave')
        this.$router.push({ name: this.routeLeave })
        return
      }
      if (!this.tabsFlg) {
        this.subText = 'Modify'
        this.$emit('leaveFlg', 'Modify')
        this.saveFlg = ''
        this.tabsFlg = true
        this.leaveTip = false
        this.$emit('changeTabs', true)
        this.whiteUrlList = []
        for (const i in this.whiteList.officialUrls) {
          this.whiteUrlList.push({
            url: this.whiteList.officialUrls[i]
          })
        }
      } else {
        this.leaveTip = false
        this.tabsFlg = false
        this.$emit('changeTabs', false)
      }
    }
  }
}
</script>
<style lang="scss" scoped src="../../assets/css/components/_IntegrationWhitelist.scss"></style>
