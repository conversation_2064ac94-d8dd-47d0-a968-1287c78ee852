<template>
  <div>
    <div class="pg-title1 mui-fl-vert" v-if="hasNoZkKyc">
      <i class="mcico-active-activation"></i>
      zkKYC
    </div>
    <div class="Search-title mui-fl-vert" v-else>
      <i class="mico-Fallback" style="cursor: pointer;" @click="goback"></i>
      <span class="fg"></span>
      <span>{{ kycTitle }}</span>
      <span class="kycLevel">{{ kycLevel }}</span>
    </div>

    <div class="kycmain mui-fl-hori">
      <div class="whole">
        <!-- <div class="kyctitle" v-show="hasNoZkKyc" style="margin-bottom: 32px">
          😊 Start creating your first KYC program!
        </div> -->

        <ul class="form">
          <li v-for="(item, index) of stepsList" :key="item.id" :class="{ 'li-mar': index !== 0 }">
            <h3 class="h3-mar"
              v-if="(item.id <= 4 && kycMode <= 2) || (item.id === 5 && kycMode >= 2)">
              {{ item.title }}
              <i v-show="item.id === 3" class="mico-help-linear" @click="showHelp" />
              <m-popover trigger="click" v-show="item.id === 4" placement="top" width="400" :close-delay="1000"
                popper-class="NotRecord_popover WhiteList Kyc_from_tip" content="The platform enforces identity uniqueness
                by verifying matches in either the document number hash or
                the combined name and date of birth hash. Any detected match
                indicates that the identity has already been authenticated
                within the current KYC program.">
                <i slot="reference" class="mico-help-linear"></i>
              </m-popover>
            </h3>

            <template v-if="item.id === 1 && kycMode <= 2">
              <div class="network-box" v-for="(network, nIndex) of item.adminKycPropertyBoList" :key="nIndex">
                <div class="mui-fl-vert network-title">
                  <p>{{ network.kycVerifyProperty }}</p>
                  <p>Selected: {{ getChainSelectedNum(network.value) }}</p>
                </div>

                <ul class="mui-fl-vert mui-fl-wrap">
                  <li v-for="chain of network.value" :key="chain.id" class="mui-fl-central network-tag"
                    :class="{ 'tag-checked': chain.isSelect, 'tag-checked-hover': chain.isSelect }"
                    @click="selectChain(chain)">
                    <img :src="chain.symbol" alt="">
                    <p>{{ chain.value }}</p>
                  </li>
                </ul>
              </div>
            </template>

            <template v-else-if="item.id === 2 && kycMode <= 2">
              <div class="requirement-box">
                <div class="age-box" v-for="(requirement, rIndex) of item.adminKycPropertyBoList" :key="rIndex"
                  :class="{ 'active-border': rIndex !== item.adminKycPropertyBoList.length - 1 }">
                  <div class="mui-fl-vert switch-box">
                    <m-switch v-model="requirement.status" active-color="#005563" inactive-color="#E6EDED"
                      :disabled="requirement.disabled" @change="ageSwitchchange">
                    </m-switch>
                    <p>{{ requirement.kycVerifyProperty }}</p>
                  </div>

                  <template v-if="requirement.kycVerifyProperty === 'Age'">
                    <ul class="expand-box" v-if="requirement.status">
                      <li class="t1">
                        Passable age:
                      </li>
                      <li class="mui-fl-vert">
                        <p class="t1 expand-p-mar">Over</p>
                        <m-select
                          v-model="age.age"
                          class="sty2-select"
                          placeholder="Please select"
                          :popper-append-to-body="false"
                          @change="ageChange($event, requirement.value)"
                        >
                          <m-option
                            v-for="item in requirement.value"
                            :key="item.id"
                            :label="item.value"
                            :value="item.value">
                          </m-option>
                        </m-select>
                      </li>
                    </ul>
                  </template>

                  <template v-else>
                    <tranfer-component
                      v-if="requirement.status"
                      :country-list="countryList"
                      :select-country="{}"
                      id="selectCountry"
                      @select="countrySecect"
                      @change="isChangeAny = true"
                      :page-type="pageType"
                    />
                  </template>
                </div>
              </div>
            </template>

            <template v-else-if="item.id === 3 && kycMode <= 2">
              <div class="requirement-box">
                <ul class="age-box">
                  <li v-for="aml of item.adminKycPropertyBoList" :key="aml.id" class="mui-fl-vert switch-box">
                    <m-switch v-model="aml.status" active-color="#005563" inactive-color="#E6EDED" @change="ageSwitchchange"
                      :disabled="aml.disabled">
                    </m-switch>
                    <p>{{ aml.kycVerifyProperty }}</p>
                  </li>
                </ul>
              </div>
            </template>

            <template v-else-if="item.id === 4 && kycMode <= 2">
              <div class="requirement-box">
                <ul class="age-box">
                  <li v-for="aml of item.adminKycPropertyBoList" :key="aml.id" class="mui-fl-vert switch-box">
                    <m-switch v-model="aml.status" active-color="#005563" inactive-color="#E6EDED" @change="ageSwitchchange"
                      :disabled="aml.disabled">
                    </m-switch>
                    <p>{{ aml.kycVerifyProperty }}</p>
                  </li>
                </ul>
              </div>
            </template>

            <template v-else-if="item.id === 5 && kycMode >= 2">
              <div class="requirement-box">
                <div class="age-box">
                  <div class="condition-text">Please set the location condition:</div>
                  <template>
                    <tranfer-component
                    :country-list="locationList"
                    :select-country="selectCountry"
                    id="selectGeolocation"
                    :is-geolocation="kycMode"
                    @select="countrySecect"
                    @change="isChangeAny = true"
                    @geolocationSelect="geolocationSelect"
                    :page-type="pageType"
                  />
                  </template>
                </div>
              </div>
            </template>
          </li>
        </ul>
      </div>
      <div class="kycbut">
        <m-button class="sty2-button" :disabled="saveDisabled" @click="save">Save</m-button>
        <!-- <m-button class="kycCancel" @click="routerleave()" v-if="!entry.Exhibition">Cancel</m-button> -->
      </div>
    </div>

    <kyc-form
      :dialogTableVisible="dialogTableVisible"
      @close="dialogTableVisible = false"
      :confirm-list="confirmList"
      :select-country="selectCountry"
      :select-geolocation="selectGeolocation"
      :form="form"
      :page-type="pageType"
     />
    <help :visible="helpVisible" @close="helpVisible = false" />
    <!-- 离开页面 -->
    <kyc-warning model="Stay" :warningtip="warningtip" @Leave="Leave" />
  </div>
</template>
<script>
import KycWarning from '@/components/kyc_warning/KycWarning.vue'
import Help from './components/Help.vue'
import KycForm from './components/KycForm.vue'
import TranferComponent from './components/TranferComponent.vue'
import * as types from '@/store/mutation-types'
export default {
  components: { KycForm, TranferComponent, Help, KycWarning },
  data () {
    return {
      titleTxt: '',
      form: {
        ids: [],
        countryIds: [],
        programName: '',
        status: 0
      },
      age: {
        ageId: null,
        age: '18'
      },
      dialogTableVisible: false,
      helpVisible: false,
      warningtip: false,
      countryList: [],
      locationList: [],
      confirmList: [],
      isChangeAny: false, // 这个表单你动过啦? true : false
      stepsList: [],
      selectCountry: {
        avaliableList: [],
        unavailableList: []
      },
      selectGeolocation: {
        avaliableList: [],
        unavailableList: []
      },
      pageType: 'Create',
      toRoute: '',
      permission: '',
      isSame: true
    }
  },
  computed: {
    formDetail () {
      return this.$store.state.kyc.kycFormDetail
    },
    duplicateForm () {
      return this.$store.state.kyc.duplicateForm
    },
    hasNoZkKyc () {
      return Number(this.$route.query.hasNoZkKyc) || null
    },
    kycTitle () {
      return this.$store.state.kyc.kycTitle
    },
    saveDisabled () {
      let disabled = false
      const selectGeolocation = this.selectGeolocation.avaliableList.some(x => x.childrens.some(y => y.isSelect))
      const selectCountry = this.selectCountry.avaliableList.some(x => x.childrens.some(y => y.isSelect))
      if (!this.stepsList.length) {
        return true
      }
      const list = this.stepsList[0].adminKycPropertyBoList
      disabled = (!(list[0]?.value.find(r => r.isSelect) || list[1]?.value.find(r => r.isSelect))) && this.kycMode !== 3
      if (this.kycMode === 1 && !selectCountry) {
        disabled = true
      }
      if ((this.kycMode === 2 && !selectCountry) || (this.kycMode === 2 && !this.isSame && !selectGeolocation)) {
        disabled = true
      }
      if (this.kycMode === 3 && !selectGeolocation) {
        disabled = true
      }
      return disabled
    },
    kycMode () {
      return this.$route.query.mode ? Number(this.$route.query.mode) : this.$store.state.kyc.category
    },
    kycLevel () {
      let levelTitle = ''
      const level = (localStorage.getItem('zkmeAdminUser') && JSON.parse(localStorage.getItem('zkmeAdminUser')).level) || this.$store.state.auth.user.level
      switch (Number(level)) {
        case 0:
          levelTitle = 'On-chain Mint'
          break
        case 1:
          levelTitle = 'On-chain Transactional'
          break
        case 2:
          levelTitle = 'Cross-chain'
          break
      }
      return levelTitle
    }
  },
  async beforeRouteLeave (to, from, next) {
    if (this.isChangeAny && !this.dialogTableVisible) {
      this.warningtip = true
      this.toRoute = to.path
      return false
    } else {
      this.$store.commit(types.SET_KYCFORM_DETAIL, null)
      this.$store.commit(types.SET_DUPLICATE_FORM, null)
      if (to.name === 'Login' && this.$store.state.auth.user) {
        const rp = await this.$store.dispatch('logout')
        if (rp.code === 80000000) {
          next()
        }
      } else {
        next()
      }
      // this.$store.commit(types.SET_KYC_TITLE, 'Create program')
    }
  },
  async mounted () {
    const rp = await this.$api.request('apikey.mchInfo', {}, {}, true)
    if (rp.code !== 80000000) {
      return
    } else {
      if (!rp.data.apiAccessPermissions.filter(x => x.name.includes('KYC') && x.valid).length) {
        this.permission = 'No access. Contact us to get access to zkMe APIs: <a href="mailto: <EMAIL>" class="jumpLink"><EMAIL></a>'
      } else if (!rp.data.pubKeyGenerated) {
        this.permission = 'Please complete the configuration in the <a href="/integration" class="jumpLink">Integration setting</a> before saving the program.'
      }
    }
    await this.getCountryList()
    this.getkycprlist()
  },
  methods: {
    goback () {
      if (this.isChangeAny) {
        this.warningtip = true
        return
      }
      this.$store.commit(types.SET_KYCFORM_DETAIL, null)
      this.$store.commit(types.SET_DUPLICATE_FORM, null)
      // this.$store.commit(types.SET_KYC_TITLE, 'Create program')
      this.$router.back()
    },

    moveAndModify (arr, targetIndex) {
      // 复制数组以避免修改原始数组
      arr = arr.map(x => {
        return {
          ...x,
          ids: x.id
        }
      })
      const newArr = [...arr]

      // 获取最后一个对象
      const lastObject = newArr.pop()

      // 将最后一个对象插入到指定位置
      newArr.splice(targetIndex, 0, lastObject)

      newArr[targetIndex].ids = targetIndex

      // 修改 ID 值
      for (let i = targetIndex; i < newArr.length; i++) {
        newArr[i].ids = i + 1
      }

      return newArr
    },

    async getCountryList () {
      try {
        const rp = await this.$api.request('kyc.getCountryList')
        if (rp.code === 80000000) {
          rp.data = this.moveAndModify(rp.data, 118)
          this.countryList = rp.data
          this.locationList = rp.data
          // this.filterList(rp.data)
        }
      } catch (error) {
        console.log('getCountryList error: ', error)
      }
    },

    /**
     * status: 状态 0 默认启用 1 停用且不可开 2 默认开启不可停用 3默认不开启
     */
    async getkycprlist () {
      try {
        let data = []
        if (this.duplicateForm?.kycList) {
          // 复制
          data = this.duplicateForm.kycList
          this.countryList = this.duplicateForm.countryInfoBoList
          this.locationList = this.duplicateForm.locationInfoBoList
          this.pageType = 'Copy'
        } else if (this.formDetail?.id) {
          // 更新
          data = [...this.formDetail.kycList]
          this.countryList = this.formDetail.countryInfoBoList
          this.locationList = this.formDetail.locationInfoBoList
          this.pageType = 'Modify'
        } else {
          // 创建
          const rp = await this.$api.request('kyc.getKycPropertyList', { category: 2 })
          if (rp.code === 80000000) {
            data = rp.data
          }
          this.pageType = 'Create'
        }

        const list = []
        data.forEach((i, d) => {
          let title = d + 1 + '. ' + i.levelDescription
          let adminKycPropertyBoList = i.adminKycPropertyBoList
          if (i.id === 1) {
            const length = i.adminKycPropertyBoList.length - 1
            if (this.kycLevel === 'Advance') {
              i.adminKycPropertyBoList[0].value = i.adminKycPropertyBoList[0].value.filter(x => x.value !== 'Aptos')
            }
            i.adminKycPropertyBoList[length].value = i.adminKycPropertyBoList[length].value.filter(x => x.value !== 'Ethereum Goerli Testnet')
          }
          if (i.id === 2) {
            adminKycPropertyBoList = adminKycPropertyBoList.map((r, index) => {
              const find = r.value.find(a => a.isSelect)
              if (find && index === 0) {
                this.age = {
                  age: find.value,
                  ageId: find.id
                }
              }

              return {
                ...r,
                disabled: r.status === 2 || r.status === 1,
                status: !!((r.status === 0 || r.status === 2)),
                value: r.kycVerifyProperty === 'Age'
                  ? r.value.map(a => {
                    return { ...a, isSelect: a.isSelect ?? a.value === '18' }
                  })
                  : r.value
              }
            })
          } else if (i.id === 3) {
            adminKycPropertyBoList = adminKycPropertyBoList.map(r => {
              const statu = r.value.map(x => x.isSelect)
              return {
                ...r,
                disabled: r.status === 2 || r.status === 1,
                status: statu[0] !== null ? statu[0] : !!((r.status === 0 || r.status === 2)),
                id: r.value[0].id
              }
            })
          } else if (i.id === 4) {
            adminKycPropertyBoList = adminKycPropertyBoList.map(r => {
              const statu = r.value.map(x => x.isSelect)
              return {
                ...r,
                disabled: r.status === 2 || r.status === 1,
                status: statu[0] !== null ? statu[0] : !!((r.status === 0 || r.status === 2)),
                id: r.value[0].id
              }
            })
          } else if (i.id === 5) {
            if (this.kycMode === 3) {
              title = '1. ' + 'Location check'
            } else if (this.kycLevel === 'Cross-chain') {
              title = '4. ' + 'Location check'
            } else {
              title = '5. ' + 'Location check'
            }
          }
          list.push({ title, adminKycPropertyBoList, status: i.status, id: i.id })
        })
        this.stepsList = list
      } catch (error) {
        console.log('getkycprlist error: ', error)
      }
    },

    Leave (val) {
      if (val === 'Back') {
        this.isChangeAny = false
        this.$store.commit(types.SET_KYCFORM_DETAIL, null)
        this.$store.commit(types.SET_DUPLICATE_FORM, null)
        // this.$store.commit(types.SET_KYC_TITLE, 'Create program')
        this.toRoute ? this.$router.push(this.toRoute) : this.$router.back()
      }
      this.warningtip = false
    },

    countrySecect ({ availables, unavailables, id }) {
      this[id] = {
        avaliableList: availables,
        unavailableList: unavailables
      }
    },
    geolocationSelect (val) {
      this.isSame = val
    },
    save () {
      if (this.permission) {
        this.$message({
          dangerouslyUseHTMLString: true,
          duration: 0,
          offset: 32,
          center: true,
          customClass: 'sty1-message sty5-message',
          type: 'success',
          iconClass: 'mico-warning',
          showClose: true,
          message: this.permission
        })
        return
      }
      let ids = []
      let countryBoList = []
      let locationIds = []
      this.confirmList = []
      if (!this.isSame && this.kycMode === 2) {
        this.selectGeolocation = Object.assign({}, this.selectCountry)
      }
      this.stepsList.filter(x => x.id <= 4 || (x.id === 5 && this.kycMode >= 2)).forEach(i => {
        let title
        let list
        let exhibit = true
        if (i.id === 1 && this.kycMode <= 2) {
          title = 'Network'
          list = i.adminKycPropertyBoList.map(r => {
            const filter = r.value.filter(n => n.isSelect)
            ids = ids.concat(filter.map(a => a.id))
            return { ...r, value: filter }
          })
          this.confirmList.push({ title, id: i.id, adminKycPropertyBoList: [...list], exhibit: exhibit })
        } else if (i.id === 2 && this.kycMode <= 2) {
          title = 'Passable requirements'
          list = i.adminKycPropertyBoList
          const filter = i.adminKycPropertyBoList[0].value.filter(n => n.isSelect)
          ids = ids.concat(filter.map(a => a.id))
          if (i.adminKycPropertyBoList[1].status) {
            this.selectCountry.avaliableList.forEach(i => {
              countryBoList = countryBoList.concat(i.childrens.map(x => {
                const doc = []
                for (const i in x.selectDocuments) {
                  if (x.selectDocuments[i]) {
                    doc.push(i.slice(-1))
                  }
                }
                return {
                  id: x.id,
                  isSelect: x.isSelect ? 1 : 0,
                  supportDocuments: doc
                }
              }))
            })
            ids = ids.concat(i.adminKycPropertyBoList[1].value[0].id)
          } else {
            countryBoList = this.countryList.map(i => i.id)
          }
          this.confirmList.push({ title, id: i.id, adminKycPropertyBoList: [...list], exhibit: exhibit })
        } else if (i.id === 3 && this.kycMode <= 2) {
          title = 'AML Screening'
          list = i.adminKycPropertyBoList
          ids = ids.concat(list.filter(x => x.status).map(y => y.id))
          exhibit = i.adminKycPropertyBoList.filter(x => x.status).length
          this.confirmList.push({ title, id: i.id, adminKycPropertyBoList: [...list], exhibit: exhibit })
        } else if (i.id === 4 && this.kycMode <= 2) {
          title = 'Uniqueness check'
          list = i.adminKycPropertyBoList
          ids = ids.concat(list.filter(x => x.status).map(y => y.id))
          exhibit = i.adminKycPropertyBoList.filter(x => x.status).length
          this.confirmList.push({ title, id: i.id, adminKycPropertyBoList: [...list], exhibit: exhibit })
        } else if (i.id === 5 && this.kycMode >= 2) {
          title = 'Location Check'
          list = i.adminKycPropertyBoList
          const filter = i.adminKycPropertyBoList[0].value.filter(n => n.id)
          ids = ids.concat(filter.map(a => a.id))
          this.selectGeolocation.avaliableList.forEach(i => {
            locationIds = locationIds.concat(i.childrens.filter(r => r.isSelect).map(x => x.id))
          })
          this.confirmList.push({ title, id: i.id, adminKycPropertyBoList: [...list], exhibit: exhibit })
          // const selectGeolocationList = []
          // this.selectGeolocation.avaliableList.forEach(x => {
          //   const ids = x.childrens.filter(y => y.isSelect)
          //   ids.length && selectGeolocationList.push({
          //     letter: x.letter,
          //     childrens: ids
          //   })
          // })
          // this.selectGeolocation.avaliableList = selectGeolocationList
        }
      })

      this.form = {
        ids,
        countryBoList,
        programName: '',
        status: 0,
        locationIds
      }
      this.dialogTableVisible = true
    },

    ageSwitchchange (val) {
      this.isChangeAny = true
    },

    ageChange (val, list) {
      this.isChangeAny = true
      this.age.ageId = list.find(i => i.value === val).id
      list.forEach(i => {
        if (i.value === val) {
          i.isSelect = true
        } else {
          i.isSelect = false
        }
      })
    },

    showHelp () {
      this.helpVisible = true
    },

    selectChain (item) {
      item.isSelect = !item.isSelect
      this.isChangeAny = true
    },

    getChainSelectedNum (list) {
      return list.filter(i => i.isSelect).length
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/views/kycpage/_not_recorded.scss" scoped></style>
