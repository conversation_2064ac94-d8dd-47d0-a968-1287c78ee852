(self["webpackChunkzkme_web"]=self["webpackChunkzkme_web"]||[]).push([[895],{69895:function(A,t,e){"use strict";e.r(t),e.d(t,{default:function(){return D}});var s=function(){var A=this,t=A._self._c;return t("div",{staticClass:"pg-account-detail",attrs:{id:"pg-account-detail"}},[t("div",{staticClass:"sty1-bread-crumb mui-fl-vert mui-shr-0"},[t("i",{staticClass:"mcico-dashboard taplight2",on:{click:function(t){return A.$router.back()}}}),t("span",[A._v("/")]),A._v(" Applicant Data "),t("span",[A._v("/")]),t("div",{staticStyle:{"font-weight":"700"}},[A._v("KYT")])]),t("div",{staticClass:"mui-fl-btw kyt-title-mg-top24"},[t("div",{staticClass:"mui-fl-1"},[t("span",{staticClass:"kyt-title-text"},[A._v("Wallet")]),t("div",{staticClass:"kyt-bor"},[t("m-skeleton",{staticStyle:{position:"relative"},attrs:{rows:7,loading:A.skeleton.tokens,animated:""}},[t("div",{staticClass:"mui-fl-end kyt-wallet-token",staticStyle:{height:"64px","margin-bottom":"16px"}},[t("div",{staticClass:"tokenImgsty"},[t("i",{class:["chainImg",`icon-${A.requestParams.replace(".e","")}`]})]),t("m-select",{staticClass:"sty1-select kyt-select",on:{change:function(t){return A.setSelect(t,"chain")}},scopedSlots:A._u([{key:"prefix",fn:function(){return[t("div",{staticClass:"prefix"},["ETH-Base"===A.tokenList[0]?.coin?t("img",{staticStyle:{width:"22px",height:"22px"},attrs:{src:e(14689),alt:""}}):t("i",{class:[`icon-${A.tokenList[0]?.coin}`]})])]},proxy:!0}]),model:{value:A.chain,callback:function(t){A.chain=t},expression:"chain"}},A._l(A.selectList,(function(A){return t("m-option",{key:A.token,attrs:{label:A.chain,value:A}})})),1)],1),t("div",[t("span",{staticClass:"kyt-content-text"},[A._v(A._s(A.tokenName))]),t("span",{staticClass:"kyt-segmentation"},[A._v("|")]),t("span",{staticClass:"kyt-chain-text"},[A._v("EOA")])]),t("div",{staticClass:"kyt-token-title-text kyt-wallectAddress"},[A._v(A._s(A.userWalletAddress))]),t("div",{staticClass:"kyt-token-title-text"},[A._v("Asset Held:")]),t("div",{staticClass:"mui-fl-vert mui-fl-wrap"},A._l(A.tokenList,(function(e,s){return t("div",{key:s,staticClass:"kyt-tokenList"},[t("i",{class:[`icon-${e?.coin.replace(".e","")}`],on:{click:function(t){return A.setSelect(e,"token")}}})])})),0)])],1)]),t("div",{staticClass:"mui-fl-1"},[t("span",{staticClass:"kyt-title-text"},[A._v("Overview")]),t("div",{staticClass:"kyt-bor"},[t("m-skeleton",{staticStyle:{height:"100%"},attrs:{rows:7,loading:A.skeleton.addressOverview,animated:""}},[A.addressOverview?t("div",{staticClass:"mui-fl-btw"},[t("div",{staticClass:"kyt-content-analysis-text-mg4 kyt-content-analysis-text-mg32"},[t("div",[t("div",{staticClass:"kyt-content-analysis-text"},[A._v("Balance")]),t("div",{staticClass:"kyt-content-text"},[A._v(A._s(A._f("thousandth")(A.addressOverview?.balance))+" "+A._s(A.tokenName))])]),t("div",{staticClass:"kyt-title-mg-top24"},[t("div",{staticClass:"kyt-content-analysis-text"},[A._v("Total received")]),t("div",{staticClass:"kyt-content-text"},[A._v(A._s(A._f("thousandth")(A.addressOverview?.total_received))+" "+A._s(A.tokenName))])]),t("div",{staticClass:"kyt-title-mg-top24"},[t("div",{staticClass:"kyt-content-analysis-text"},[A._v("First seen (UTC)")]),t("div",{staticClass:"kyt-content-text"},[A._v(A._s(A._f("formatUTCDate")(1e3*A.addressOverview?.first_seen)))])]),t("div",{staticClass:"kyt-title-mg-top24"},[t("div",{staticClass:"kyt-content-analysis-text"},[A._v("Incoming txn")]),t("div",{staticClass:"kyt-content-text"},[A._v(A._s(A._f("thousandth")(A.addressOverview?.received_txs_count)))])])]),t("div",{staticClass:"kyt-content-analysis-text-mg4"},[t("div",[t("div",{staticClass:"kyt-content-analysis-text"},[A._v("Txs count")]),t("div",{staticClass:"kyt-content-text"},[A._v(A._s(A._f("thousandth")(A.addressOverview?.txs_count)))])]),t("div",{staticClass:"kyt-title-mg-top24"},[t("div",{staticClass:"kyt-content-analysis-text"},[A._v("Total spent")]),t("div",{staticClass:"kyt-content-text"},[A._v(A._s(A._f("thousandth")(A.addressOverview?.total_spent))+" "+A._s(A.tokenName))])]),t("div",{staticClass:"kyt-title-mg-top24"},[t("div",{staticClass:"kyt-content-analysis-text"},[A._v("Last seen (UTC)")]),t("div",{staticClass:"kyt-content-text"},[A._v(A._s(A._f("formatUTCDate")(1e3*A.addressOverview?.last_seen)))])]),t("div",{staticClass:"kyt-title-mg-top24"},[t("div",{staticClass:"kyt-content-analysis-text"},[A._v("Outgoing txn")]),t("div",{staticClass:"kyt-content-text"},[A._v(A._s(A.addressOverview?.spent_txs_count))])])])]):t("div",{staticClass:"no-data mui-fl-col mui-fl-central"},[t("img",{attrs:{src:e(26169),alt:""}}),t("div",[A._v("No Content")])])])],1)]),t("div",{staticClass:"mui-fl-1"},[t("span",{staticClass:"kyt-title-text mui-fl-vert"},[A._v(" AML Risk Score "),t("m-popover",{attrs:{"popper-class":"NotRecord_popover kyt_popover",placement:"bottom-end",trigger:"hover",offset:230}},[t("div",{staticClass:"mui-fl-vert",attrs:{slot:"reference"},slot:"reference"},[t("i",{staticClass:"mico-help-linear"})]),A._t("default",(function(){return[t("div",{staticStyle:{"word-break":"break-word"}},[A._v(" The system evaluates the AML risk score based on three factors: 1) the entity of the address, 2) the transactions associated with the address, and 3) the malicious wallet address database. High-risk entities, such as mixers, or addresses with funds connected to known risky entities, are flagged as risky addresses. Additionally, verified addresses involved in ransomware, scams, and stolen crypto listed in the malicious wallet address database are considered high risk. The following risk levels and corresponding scores are assigned: ")])]})),t("m-table",{staticStyle:{width:"300px"},attrs:{border:"",data:A.tableData}},[t("m-table-column",{attrs:{width:"149",property:"level",label:"Risk Level"}}),t("m-table-column",{attrs:{width:"149",property:"score",label:"Risk Score"}})],1)],2)],1),t("div",{staticClass:"kyt-bor kyt-title-mg-right0"},[t("m-skeleton",{staticStyle:{height:"100%"},attrs:{rows:7,loading:A.skeleton.riskscore,animated:""}},[A.riskscore?t("div",{staticClass:"mui-fl-btw"},[t("div",{staticClass:"moderateb"},[t("div",{class:["kyt-score-text","mui-fl-hori","moderate","kyt-content-title-text",`moderate-${A.riskscore?.risk_level||"Low"}`],staticStyle:{width:"62px"}},[A._v(A._s(A.riskscore?.risk_level||"Low"))]),t("div",{staticClass:"kyt-score-price-text mui-fl-hori moderatenb"},[A._v(A._s(A.riskscore?.score||0))])]),t("div",[t("div",{staticClass:"e-chart-radar",attrs:{id:"AML"}})])]):A._e(),A.riskscore&&A.paginatedData.length?t("div",{staticClass:"kyt-warn-text mui-flex"},[t("div",[t("i",{staticClass:"mico-warning"})]),t("div",{staticClass:"mui-fl-btw mui-fl-vert",staticStyle:{width:"100%"}},[A.riskscore?.detail_list?t("div",[A._v(A._s(A.riskscore?.detail_list.toString().replace(/,/g,", ")))]):A._e(),t("div",{staticClass:"detail",on:{click:A.detail}},[A._v("Detail")])])]):A._e(),A.riskscore?A._e():t("div",{staticClass:"no-data mui-fl-col mui-fl-central"},[t("img",{attrs:{src:e(26169),alt:""}}),t("div",[A._v("No Content")])])])],1)])]),t("div",{staticClass:"mui-fl-btw kyt-title-mg-top40"},[t("div",{staticClass:"mui-fl-1"},[t("span",{staticClass:"kyt-title-text mui-fl-vert"},[A._v(" Transaction Actions Analysis "),t("m-popover",{attrs:{"popper-class":"NotRecord_popover kyt_popover",placement:"top-start",trigger:"hover",offset:230}},[t("div",{staticClass:"mui-fl-vert",attrs:{slot:"reference"},slot:"reference"},[t("i",{staticClass:"mico-help-linear"})]),A._t("default",(function(){return[A._v(" The transaction action analysis module will analyze all historical "),t("br"),A._v("transactions associated with the address and provide a comprehensive "),t("br"),A._v("summary in an easily understandable way. Through this analysis module, it is easy to create a behavioral profile of the target address. ")]}))],2)],1),t("div",{staticClass:"kyt-bor kyt-title-mg-right0"},[t("m-skeleton",{class:[A.addressAction?"mui-fl-btw":"mui-fl-hori"],attrs:{rows:6,loading:A.skeleton.addressAction,animated:""}},[t("div",{directives:[{name:"show",rawName:"v-show",value:A.addressAction,expression:"addressAction"}],staticStyle:{"min-width":"600px"}},[t("span",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v("Incoming Transaction Actions")]),t("div",{directives:[{name:"show",rawName:"v-show",value:A.transaction.Incoming,expression:"transaction.Incoming"}],staticClass:"mui-fl-hori",staticStyle:{"max-width":"650px"}},[t("div",{staticClass:"mui-fl-central",staticStyle:{position:"relative"}},[t("div",{staticClass:"kyt-transaction-actions"},[t("div",{staticClass:"mui-fl-hori"},[t("img",{attrs:{src:e(29304),alt:""}})]),t("div",{staticClass:"title mui-fl-hori"},[A._v(A._s(A.incoming.name||"Exchange"))]),t("div",{staticClass:"kyt-analysis-token-text mui-fl-hori"},[A._v(A._s(A.incoming.rate||100)+"%")])]),t("div",{staticClass:"eChart",attrs:{id:"Incoming"}})]),t("m-skeleton",{staticClass:"mui-fl-vert",attrs:{rows:5,loading:!A.addressAction,animated:""}},[t("div",{staticClass:"mui-fl-wrap",staticStyle:{width:"100%"}},A._l(A.addressAction?.[0],(function(e,s){return t("div",{key:s,staticStyle:{width:"auto",padding:"24px 40px 0"}},[t("div",{staticClass:"kyt-content-analysis-text mui-fl-vert"},[t("span",{staticClass:"kyt-incoming-pilot",style:{background:e.styColor}}),A._v(A._s(e.name)+" ")]),t("div",{staticClass:"kyt-analysis-price-text"},[A._v(A._s(e.rate)+"%")])])})),0)])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:!A.transaction.Incoming,expression:"!transaction.Incoming"}],staticClass:"mui-fl-central",staticStyle:{height:"calc(100% - 50px)"}},[t("div",{staticClass:"no-data mui-fl-col mui-fl-central"},[t("img",{attrs:{src:e(26169),alt:""}}),t("div",{staticClass:"mui-fl-hori"},[A._v("No Content")])])])]),t("div",{directives:[{name:"show",rawName:"v-show",value:A.addressAction,expression:"addressAction"}],staticStyle:{"min-width":"600px"}},[t("span",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v("Outgoing Transaction Actions")]),t("div",{directives:[{name:"show",rawName:"v-show",value:A.transaction.Outgoing,expression:"transaction.Outgoing"}],staticClass:"mui-flex",staticStyle:{"max-width":"650px"}},[t("div",{staticClass:"mui-fl-central",staticStyle:{position:"relative"}},[t("div",{staticClass:"kyt-transaction-actions"},[t("div",{staticClass:"mui-fl-hori"},[t("img",{attrs:{src:e(37634),alt:""}})]),t("div",{staticClass:"title mui-fl-hori"},[A._v(A._s(A.outgoing.name||"Exchange"))]),t("div",{staticClass:"kyt-analysis-token-text mui-fl-hori"},[A._v(A._s(A.outgoing.rate||100)+"%")])]),t("div",{staticClass:"eChart",attrs:{id:"Outgoing"}})]),t("m-skeleton",{staticClass:"mui-fl-vert",attrs:{rows:5,loading:!A.addressAction,animated:""}},[t("div",{staticClass:"mui-fl-wrap",staticStyle:{width:"100%"}},A._l(A.addressAction?.[1],(function(e,s){return t("div",{key:s,staticStyle:{width:"auto",padding:"24px 40px 0"}},[t("div",{staticClass:"kyt-content-analysis-text mui-fl-vert"},[t("span",{staticClass:"kyt-incoming-pilot",style:{background:e.styColor}}),A._v(A._s(e.name)+" ")]),t("div",{staticClass:"kyt-analysis-price-text"},[A._v(A._s(e.rate)+"%")])])})),0)])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:!A.transaction.Outgoing,expression:"!transaction.Outgoing"}],staticClass:"mui-fl-central",staticStyle:{height:"calc(100% - 50px)"}},[t("div",{staticClass:"no-data mui-fl-col mui-fl-central"},[t("img",{attrs:{src:e(26169),alt:""}}),t("div",{staticClass:"mui-fl-hori"},[A._v("No Content")])])])]),A.addressAction?A._e():t("div",{staticClass:"no-data"},[t("img",{attrs:{src:e(26169),alt:""}}),t("div",{staticClass:"mui-fl-hori"},[A._v("No Content")])])])],1)])]),t("div",{staticClass:"mui-fl-btw kyt-title-mg-top40"},[t("div",{staticClass:"mui-fl-1"},[t("span",{staticClass:"kyt-title-text mui-fl-btw"},[t("div",{staticClass:"mui-fl-vert"},[A._v(" Address Labels "),t("m-popover",{attrs:{"popper-class":"NotRecord_popover kyt_popover",placement:"top-start",trigger:"hover",offset:230}},[t("div",{staticClass:"mui-fl-vert",attrs:{slot:"reference"},slot:"reference"},[t("i",{staticClass:"mico-help-linear"})]),A._t("default",(function(){return[A._v(" Address labels consist of three categories, which include the associated "),t("br"),A._v(" entity, on-chain behavior, and some off-chain data. ")]}))],2)],1),t("div",{staticClass:"kyt-token-title-text mui-fl-vert",staticStyle:{cursor:"pointer","line-height":"25px"},on:{click:A.copy}},[A._v(" Copy all "),t("i",{staticClass:"mico-copy"})])]),t("div",{staticClass:"kyt-bor kyt-title-mg-right0"},[t("m-skeleton",{attrs:{rows:6,loading:A.skeleton.addressLabels,animated:""}},[A.addressLabels&&A.addressLabels?.length?t("div",{staticClass:"mui-fl-wrap"},A._l(A.addressLabels,(function(e,s){return t("div",{key:s},[t("div",{staticClass:"kyt-analysis-text kyt-price-box kyt-labels-box mui-fl-vert"},[t("i",{staticClass:"mico-tag"}),A._v(" "+A._s(e)+" ")])])})),0):t("div",{staticClass:"mui-fl-central"},[t("div",{staticClass:"no-data"},[t("img",{attrs:{src:e(26169),alt:""}}),t("div",{staticClass:"mui-fl-hori"},[A._v("No Content")])])])])],1)])]),t("div",{staticClass:"kyt-title-mg-top40",staticStyle:{"min-width":"800px"}},[t("span",{staticClass:"kyt-title-text mui-fl-vert"},[A._v(" Address Profile Analysis "),t("m-popover",{attrs:{"popper-class":"NotRecord_popover kyt_popover",placement:"top-start",trigger:"hover",offset:230}},[t("div",{staticClass:"mui-fl-vert",attrs:{slot:"reference"},slot:"reference"},[t("i",{staticClass:"mico-help-linear"})]),A._t("default",(function(){return[A._v(" The address profile analysis module can generate a comprehensive summary in an easy-to-understand format through analyzing all interactions associated with the address. ")]}))],2)],1),t("div",{staticClass:"kyt-bor kyt-title-mg-right0"},[t("m-skeleton",{attrs:{rows:6,loading:A.skeleton.addressTrace,animated:""}},[A.addressTrace?t("div",{staticClass:"mui-fl-btw"},[t("div",{staticClass:"mui-fl-btw mui-fl-vert_end kyt-Profile-text-height",staticStyle:{"max-width":"576px"}},[t("div",[t("div",{staticClass:"kyt-analysis-text kyt-analysis-text-mg kyt-price-text"},[A._v("Platform Interaction")]),t("div",{staticClass:"kyt-price-box kyt-price-analysis-box"},[t("div",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v(A._s(A.addressTrace?.exchange?.count))]),t("div",{staticClass:"kyt-address-analysis-text mui-fl-hori"},[A._v("Exchange")])])]),t("div",[t("div",{staticClass:"kyt-price-box kyt-price-analysis-box"},[t("div",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v(A._s(A.addressTrace?.dex?.count))]),t("div",{staticClass:"kyt-address-analysis-text mui-fl-hori"},[A._v("DeFi")])])]),t("div",[t("div",{staticClass:"kyt-price-box kyt-price-analysis-box"},[t("div",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v(A._s(A.addressTrace?.mixer?.count))]),t("div",{staticClass:"kyt-address-analysis-text mui-fl-hori"},[A._v("Coin Mixer")])])]),t("div",[t("div",{staticClass:"kyt-price-box kyt-price-analysis-box"},[t("div",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v(A._s(A.addressTrace?.nft?.count))]),t("div",{staticClass:"kyt-address-analysis-text mui-fl-hori"},[A._v("NFT")])])])]),t("div",{staticClass:"mui-fl-btw mui-fl-vert_end kyt-Profile-text-height",staticStyle:{"max-width":"432px"}},[t("div",[t("div",{staticClass:"kyt-analysis-text kyt-analysis-text-mg kyt-price-text"},[A._v("Related Events")]),t("div",{staticClass:"kyt-price-box kyt-price-analysis-box"},[t("div",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v(A._s(A.addressTrace?.stealing?.count))]),t("div",{staticClass:"kyt-address-analysis-text mui-fl-hori"},[A._v("Theft")])])]),t("div",[t("div",{staticClass:"kyt-price-box kyt-price-analysis-box"},[t("div",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v(A._s(A.addressTrace?.phishing?.count))]),t("div",{staticClass:"kyt-address-analysis-text mui-fl-hori"},[A._v("Phishing")])])]),t("div",[t("div",{staticClass:"kyt-price-box kyt-price-analysis-box"},[t("div",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v(A._s(A.addressTrace?.phishing?.count))]),t("div",{staticClass:"kyt-address-analysis-text mui-fl-hori"},[A._v("Ransom")])])])]),t("div",{staticClass:"mui-fl-btw mui-fl-vert_end kyt-Profile-text-height",staticStyle:{"max-width":"432px"}},[t("div",[t("div",{staticClass:"kyt-analysis-text kyt-analysis-text-mg kyt-price-text"},[A._v("Related Information")]),t("div",{staticClass:"kyt-price-box kyt-price-analysis-box"},[t("div",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v(A._s(A.addressTrace?.wallet?.count))]),t("div",{staticClass:"kyt-address-analysis-text mui-fl-hori"},[A._v("Wallet")])])]),t("div",[t("div",{staticClass:"kyt-price-box kyt-price-analysis-box"},[t("div",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v(A._s(A.addressTrace?.ens?.count))]),t("div",{staticClass:"kyt-address-analysis-text mui-fl-hori"},[A._v("ENS")])])]),t("div",[t("div",{staticClass:"kyt-price-box kyt-price-analysis-box"},[t("div",{staticClass:"kyt-analysis-text mui-fl-hori"},[A._v(A._s(A.addressTrace?.twitter?.count))]),t("div",{staticClass:"kyt-address-analysis-text mui-fl-hori"},[A._v("Twitter")])])])])]):t("div",{staticClass:"no-data"},[t("div",{staticClass:"mui-fl-hori"},[t("img",{attrs:{src:e(26169),alt:""}})]),t("div",{staticClass:"mui-fl-hori"},[A._v("No Content")])])])],1)]),t("div",{staticClass:"mui-fl-col txn-graph"},[t("span",{staticClass:"kyt-title-text mui-fl-vert"},[A._v(" Transaction Graph "),t("m-popover",{attrs:{"popper-class":"NotRecord_popover kyt_popover",placement:"top-start",trigger:"hover",offset:230}},[t("div",{staticClass:"mui-fl-vert",attrs:{slot:"reference"},slot:"reference"},[t("i",{staticClass:"mico-help-linear"})]),A._t("default",(function(){return[A._v(" The transaction graph displays the relationship of all the incoming and "),t("br"),A._v("outgoing transactions of the address. It can perform data filtering and "),t("br"),A._v("address search through the table on the right side. ")]}))],2)],1),A.transactionsInvestigationDataLoading?t("div",{staticClass:"txn-graph-loading-or-empty mui-fl-central"},[t("div",{staticClass:"mui-fl-col"},[t("LoadingLottie",{attrs:{size:"40px"}}),t("p",{staticClass:"loading-txt"},[A._v("Loading...")])],1)]):A.transactionsInvestigationDataLoading||A.transactionsInvestigation?.in||A.transactionsInvestigation?.out?t("KytTransactionGraph",{attrs:{coin:A.tokenName,decimals:A.decimals,requestParams:A.selectValue,transactionsInvestigationData:A.transactionsInvestigation,walletAddress:this.userWalletAddress,graphStartTime:null!==A.addressOverview?.first_seen?A.addressOverview?.first_seen:Date.now(),graphEndTime:null!==A.addressOverview?.last_seen?A.addressOverview?.last_seen:Date.now(),graphFilterStartTime:A.graphFilterStartTime,graphFilterEndTime:A.graphFilterEndTime},on:{paginateTxnInvestigation:A.paginateTxnInvestigation}}):t("div",{staticClass:"no-data mui-fl-central mui-fl-col"},[t("img",{attrs:{src:e(26169),alt:""}}),t("div",[A._v("No Content")])])],1),A.paginatedData.length?t("kytDetails",{attrs:{kytDetailstip:A.kytDetailstip,paginatedData:A.paginatedData,labelList:A.labelList},on:{detail:A.detail}}):A._e()],1)},r=[],n=(e(44114),e(98992),e(54520),e(72577),e(3949),function(){var A=this,t=A._self._c;return t("m-dialog",{staticClass:"sty2-dialog dialog",attrs:{visible:A.kytDetailstips,"show-close":!0,width:"1036px"},on:{"update:visible":function(t){A.kytDetailstips=t},close:A.closedia}},[t("div",{staticClass:"title"},[A._v(" Risk Score details ")]),t("div",{staticClass:"in-txns-table table-wrap mui-fl-col mui-fl-btw"},[t("m-table",{staticClass:"sty2-table sty3-table",attrs:{data:A.tableList,"default-sort":{prop:"Volume(USD)%",order:"descending"}}},A._l(A.labelList,(function(e,s){return t("m-table-column",{key:s,attrs:{label:e,sortable:"Volume(USD)%"===e,prop:"percent"},scopedSlots:A._u([{key:"default",fn:function({row:s}){return["Risk Type"===e?t("span",{staticClass:"sender-add",staticStyle:{"text-transform":"capitalize"}},[A._v(A._s(s.type.replace("_"," ")+" Address"))]):A._e(),"Address/Risk label"===e?t("span",{staticClass:"sender-add"},[A._v(A._s(A._f("formatPubKey")(s.address,4,4))+" ("+A._s(s.label)+") "),t("i",{staticClass:"mico-copy",on:{click:function(t){return A.copy(s.address,s.label)}}})]):A._e(),"Volume(USD)%"===e?t("span",{staticClass:"sender-add"},[A._v(" "+A._s(s.volume?`$${s.volume}`:"-")+" ("+A._s(s.percent)+"%)")]):A._e()]}}],null,!0)})})),1),t("div",{staticClass:"table-pagination mui-fl-end"},[t("m-pagination",{directives:[{name:"show",rawName:"v-show",value:A.paginatedData.length>0,expression:"paginatedData.length > 0"}],staticClass:"sty1-pagination",attrs:{background:"","page-size":A.pageSize,"current-page":A.currentPage,layout:"prev, pager, next",total:A.paginatedData.length},on:{"current-change":function(t){return A.sizeChange(A.paginatedData,A.currentPage)},"update:currentPage":function(t){A.currentPage=t},"update:current-page":function(t){A.currentPage=t}}})],1)],1)])}),i=[],a={props:{kytDetailstip:{type:Boolean,default:!1},paginatedData:{type:Array,default:()=>[]},labelList:{type:Array,default:()=>[]}},data(){return{kytDetailstips:!1,tableList:this.paginatedData.slice(0,9),currentPage:1,pageSize:10}},watch:{kytDetailstip(A){this.kytDetailstips=A}},methods:{sizeChange(A,t){const e=(t-1)*this.pageSize,s=t*this.pageSize;this.tableList=A.slice(e,s)},closedia(){this.$emit("detail")},copy(A,t){const e=document.createElement("input");e.value=A+`(${t})`,document.body.appendChild(e),e.select(),document.execCommand("Copy"),e.remove(),this.$message({message:"Copied",iconClass:"mcico-success",customClass:"sty4-message",duration:3e3,offset:32,center:!0})}}},o=a,B=e(81656),c=(0,B.A)(o,n,i,!1,null,"491882a6",null),l=c.exports,g=l,u=function(){var A=this,t=A._self._c;return t("div",{ref:"container",staticClass:"main-container mui-fl-vert",staticStyle:{"min-width":"1200px"}},[t("KytTransactionDetailTable",{attrs:{walletAddress:A.walletAddress,coin:A.coin,"from-address":A.txnDetailFromAddress,"to-address":A.txnDetailToAddress,decimals:A.decimals,"hash-list":A.tableHashList,visible:A.tableVisible,requestParams:A.requestParams},on:{close:function(t){A.tableVisible=!1}}}),t("div",{ref:"downloadImgRender",staticClass:"download-img-renderer"}),t("div",{ref:"graphContainer",staticClass:"graph-container mui-fl-vert mui-fl-central",on:{mousedown:A.startDrag,mousemove:A.onDrag,mouseup:A.stopDrag,mouseleave:A.stopDrag}},[t("div",{ref:"graph",staticClass:"center-container"},[A._l(A.topLeftTopItems,(function(e,s){return t("div",{key:`top-left-${s}`,staticClass:"left-side item-top-left"},[t("div",{staticClass:"graph-item mui-fl-vert",on:{click:function(t){return A.openTxnDetailTable(e.address,A.walletAddress,e.tx_hash_list)}}},[t("div",{staticClass:"item-amount"},[A._v(" "+A._s(A._f("thousandth")(e.amount))+" "+A._s(A.coin)+" ")]),t("div",{staticClass:"mui-shr-0"},[t("p",{staticClass:"item-label"},[A._v(A._s(A.handleLabel(e.label)))]),e.address?t("p",{staticClass:"item-address"},[A._v(A._s(A._f("simplifyAddress")(e.address,6,5)))]):A._e()]),t("div",{class:["item-icon","mui-fl-central",2===e.type&&"warn"]},[t("i",{class:A.handleIcon(e)})])]),t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"93",height:74+80*(A.topLeftTopItems.length-s-1),viewBox:"0 0 93 "+(74+80*(A.topLeftTopItems.length-s-1)),fill:"none"}},[t("path",{attrs:{d:A.createSvg(74+80*(A.topLeftTopItems.length-s-1)),fill:"#A9E1D3"}})])])})),A.cenLeftItem?t("div",{staticClass:"left-side item-cen-left"},[t("div",{staticClass:"graph-item mui-fl-vert",on:{click:function(t){return A.openTxnDetailTable(A.cenLeftItem.address,A.walletAddress,A.cenLeftItem.tx_hash_list)}}},[t("div",{staticClass:"item-amount"},[A._v(" "+A._s(A._f("thousandth")(A.cenLeftItem.amount))+" "+A._s(A.coin)+" ")]),t("div",{staticClass:"mui-shr-0"},[t("p",{staticClass:"item-label"},[A._v(A._s(A.handleLabel(A.cenLeftItem.label)))]),A.cenLeftItem.address?t("p",{staticClass:"item-address"},[A._v(A._s(A._f("simplifyAddress")(A.cenLeftItem.address,6,5)))]):A._e()]),t("div",{class:["item-icon","mui-fl-central",2===A.cenLeftItem.type&&"warn"]},[t("i",{class:A.handleIcon(A.cenLeftItem)})])]),t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"93",height:"6",viewBox:"0 0 93 6",fill:"none"}},[t("path",{attrs:{d:"M90 3L85 0.113249V5.88675L90 3ZM90 2.5H45.5V3.5H90V2.5ZM45.5 2.5H0V3.5H45.5V2.5Z",fill:"#A9E1D3"}})])]):A._e(),A._l(A.botLeftItems,(function(e,s){return t("div",{key:`bot-left-${s}`,staticClass:"left-side item-bot-left"},[t("div",{staticClass:"graph-item mui-fl-vert",on:{click:function(t){return A.openTxnDetailTable(e.address,A.walletAddress,e.tx_hash_list)}}},[t("div",{staticClass:"item-amount"},[A._v(" "+A._s(A._f("thousandth")(e.amount))+" "+A._s(A.coin)+" ")]),t("div",{staticClass:"mui-shr-0"},[t("p",{staticClass:"item-label"},[A._v(A._s(A.handleLabel(e.label)))]),e.address?t("p",{staticClass:"item-address"},[A._v(A._s(A._f("simplifyAddress")(e.address,6,5)))]):A._e()]),t("div",{class:["item-icon","mui-fl-central",2===e.type&&"warn"]},[t("i",{class:A.handleIcon(e)})])]),t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"93",height:74+80*(A.botLeftItems.length-s-1),viewBox:"0 0 93 "+(74+80*(A.botLeftItems.length-s-1)),fill:"none"}},[t("g",{attrs:{transform:`scale(1, -1) translate(0, ${-74-80*(A.botLeftItems.length-s-1)})`}},[t("path",{attrs:{d:A.createSvg(74+80*(A.botLeftItems.length-s-1)),fill:"#A9E1D3"}})])])])})),t("div",{staticClass:"wallet mui-fl-col mui-fl-central"},[A._m(0),t("span",[A._v(A._s(A._f("simplifyAddress")(A.walletAddress,6,5)))])]),A._l(A.topRightItems,(function(e,s){return t("div",{key:`top-right-${s}`,staticClass:"right-side item-top-right"},[t("div",{staticClass:"graph-item mui-fl-vert",on:{click:function(t){return A.openTxnDetailTable(A.walletAddress,e.address,e.tx_hash_list)}}},[t("div",{class:["item-icon","mui-fl-central",2===e.type&&"warn"]},[t("i",{class:A.handleIcon(e)})]),t("div",{staticClass:"mui-shr-0"},[t("p",{staticClass:"item-label"},[A._v(A._s(A.handleLabel(e.label)))]),e.address?t("p",{staticClass:"item-address"},[A._v(A._s(A._f("simplifyAddress")(e.address,6,5)))]):A._e()]),t("div",{staticClass:"item-amount"},[A._v(" "+A._s(A._f("thousandth")(e.amount))+" "+A._s(A.coin)+" ")])]),t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"93",height:74+80*(A.topRightItems.length-s-1),viewBox:"0 0 93 "+(74+80*(A.topRightItems.length-s-1)),fill:"none"}},[t("g",{attrs:{transform:`scale(1, -1) translate(0, ${-74-80*(A.topRightItems.length-s-1)})`}},[t("path",{attrs:{d:A.createSvg(74+80*(A.topRightItems.length-s-1)),fill:"#A9E1D3"}})])])])})),A.cenRightItem?t("div",{staticClass:"right-side item-cen-right"},[t("div",{staticClass:"graph-item mui-fl-vert",on:{click:function(t){return A.openTxnDetailTable(A.walletAddress,A.cenRightItem.address,A.cenRightItem.tx_hash_list)}}},[t("div",{class:["item-icon","mui-fl-central",2===A.cenRightItem.type&&"warn"]},[t("i",{class:A.handleIcon(A.cenRightItem)})]),t("div",{staticClass:"mui-shr-0"},[t("p",{staticClass:"item-label"},[A._v(A._s(A.handleLabel(A.cenRightItem.label)))]),A.cenRightItem.address?t("p",{staticClass:"item-address"},[A._v(A._s(A._f("simplifyAddress")(A.cenRightItem.address,6,5)))]):A._e()]),t("div",{staticClass:"item-amount"},[A._v(" "+A._s(A._f("thousandth")(A.cenRightItem.amount))+" "+A._s(A.coin)+" ")])]),t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"90",height:"6",viewBox:"0 0 90 6",fill:"none"}},[t("path",{attrs:{d:"M90 3L85 0.113249V5.88675L90 3ZM85.5 2.5H45.5V3.5H85.5V2.5ZM45.5 2.5H0V3.5H45.5V2.5Z",fill:"#A9E1D3"}})])]):A._e(),A._l(A.botRightItems,(function(e,s){return t("div",{key:`bot-right-${s}`,staticClass:"right-side item-bot-right"},[t("div",{staticClass:"graph-item mui-fl-vert",on:{click:function(t){return A.openTxnDetailTable(A.walletAddress,e.address,e.tx_hash_list)}}},[t("div",{class:["item-icon","mui-fl-central",2===e.type&&"warn"]},[t("i",{class:A.handleIcon(e)})]),t("div",{staticClass:"mui-shr-0"},[t("p",{staticClass:"item-label"},[A._v(A._s(A.handleLabel(e.label)))]),e.address?t("p",{staticClass:"item-address"},[A._v(A._s(A._f("simplifyAddress")(e.address,6,5)))]):A._e()]),t("div",{staticClass:"item-amount"},[A._v(" "+A._s(A._f("thousandth")(e.amount))+" "+A._s(A.coin)+" ")])]),t("svg",{attrs:{xmlns:"http://www.w3.org/2000/svg",width:"93",height:74+80*s,viewBox:"0 0 93 "+(74+80*s),fill:"none"}},[t("path",{attrs:{d:A.createSvg(74+80*s),fill:"#A9E1D3"}})])])}))],2),t("div",{staticClass:"toolbar"},[t("button",{on:{click:A.zoomIn}},[t("i",{staticClass:"mico-amplify"})]),t("button",{on:{click:A.zoomOut}},[t("i",{staticClass:"mico-reduce"})]),t("button",{on:{click:A.recenter}},[t("i",{staticClass:"mico-center"})]),t("button",{on:{click:A.downloadGraph}},[t("i",{staticClass:"mico-download1"})]),t("button",{on:{click:A.toggleFullscreen}},[t("i",{staticClass:"mico-full-screen"})]),t("button",{on:{click:A.cleanData}},[t("i",{staticClass:"mico-clean"})]),t("button",{on:{click:A.revertData}},[t("i",{staticClass:"mico-reduction"})]),A.graphTotalPages>1?t("button",{on:{click:A.prevGraph}},[t("i",{staticClass:"mico-chevron-left-double"})]):A._e(),A.graphTotalPages>1?t("button",{on:{click:A.nextGraph}},[t("i",{staticClass:"mico-chevron-right-double"})]):A._e()])]),A.dataFilterExpanded?t("div",{staticClass:"filter-data-con"},[t("button",{staticClass:"fold-button",on:{click:function(t){A.dataFilterExpanded=!1}}},[t("i",{staticClass:"mico-chevron-right-double"})]),t("div",{staticClass:"date-picker"},[t("p",{staticClass:"title"},[A._v("Date")]),t("m-date-picker",{staticClass:"sty2-date-editor mui-shr-0",attrs:{"popper-class":"sty1-date-popper width-1 ","prefix-icon":"mico-date","value-format":"timestamp",format:"yyyy.MM.dd",type:"daterange","range-separator":"-","start-placeholder":"start","end-placeholder":"end","unlink-panels":"",clearable:!1,editable:!1,"picker-options":A.pickerOptions,"default-time":["00:00:00","23:59:59"]},on:{change:A.handleChangeDate},model:{value:A.picker,callback:function(t){A.picker=t},expression:"picker"}})],1),t("div",{staticClass:"amount-range"},[t("p",{staticClass:"title"},[A._v("Amount Range")]),t("div",{staticClass:"mui-fl-btw"},[t("m-input",{staticClass:"sty1-input-search width-265 marg-l16",attrs:{type:"number",placeholder:"Minimum",clearable:""},on:{change:A.filterDataByConditions,clear:A.filterDataByConditions},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&A._k(t.keyCode,"enter",13,t.key,"Enter")?null:A.filterDataByConditions.apply(null,arguments)}},model:{value:A.query.minAmount,callback:function(t){A.$set(A.query,"minAmount",t)},expression:"query.minAmount"}}),t("m-input",{staticClass:"sty1-input-search width-265 marg-l16",attrs:{type:"number",placeholder:"Maximum",clearable:""},on:{change:A.filterDataByConditions,clear:A.filterDataByConditions},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&A._k(t.keyCode,"enter",13,t.key,"Enter")?null:A.filterDataByConditions.apply(null,arguments)}},model:{value:A.query.maxAmount,callback:function(t){A.$set(A.query,"maxAmount",t)},expression:"query.maxAmount"}})],1)]),t("div",{staticClass:"data-filter"},[t("p",{staticClass:"title"},[A._v("Filter")]),t("div",{staticClass:"mui-fl-btw"},[t("m-select",{staticClass:"sty1-select width-265 padding-1",attrs:{"popper-class":"sty1-popper"},on:{change:A.filterDataByConditions},model:{value:A.query.filterTxnType,callback:function(t){A.$set(A.query,"filterTxnType",t)},expression:"query.filterTxnType"}},A._l(A.filterTxnOptions,(function(A){return t("m-option",{key:A.value,attrs:{label:A.label,value:A.value}})})),1),t("m-select",{staticClass:"sty1-select width-265 padding-1",attrs:{"popper-class":"sty1-popper"},on:{change:A.filterDataByConditions},model:{value:A.query.filterAddressType,callback:function(t){A.$set(A.query,"filterAddressType",t)},expression:"query.filterAddressType"}},A._l(A.filterAddressOptions,(function(A){return t("m-option",{key:A.value,attrs:{label:A.label,value:A.value}})})),1)],1)]),t("div",{staticClass:"analysis-con"},[t("span",{staticClass:"analysis-title"},[A._v("Analysis")]),t("span",{staticClass:"analysis-address"},[A._v(A._s(A.walletAddress))])]),t("m-input",{staticClass:"sty1-input-search width-1 marg-l16",attrs:{placeholder:"Search by address / label","prefix-icon":"mico-search",clearable:""},on:{change:A.filterDataByConditions},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&A._k(t.keyCode,"enter",13,t.key,"Enter")?null:A.filterDataByConditions.apply(null,arguments)}},model:{value:A.query.search,callback:function(t){A.$set(A.query,"search",t)},expression:"query.search"}}),A.inTxns.length?t("div",{staticClass:"in-txns-table table-wrap mui-fl-col mui-fl-btw"},[t("m-table",{staticClass:"sty2-table",attrs:{data:A.paginatedData(A.inTxns,A.inTxnsCurPage)},on:{"sort-change":A.handleSortChangeIntxns}},[t("m-table-column",{attrs:{label:"Show","max-width":"50"},scopedSlots:A._u([{key:"default",fn:function({row:e}){return[t("m-checkbox",{staticClass:"sty1-checkbox",on:{change:function(t){return A.handleInCheckbox(e)}},model:{value:e.selected,callback:function(t){A.$set(e,"selected",t)},expression:"row.selected"}})]}}],null,!1,3071212749)}),t("m-table-column",{attrs:{label:"Sender","min-width":"295"},scopedSlots:A._u([{key:"default",fn:function({row:e}){return[t("p",{staticClass:"sender-add"},[A._v(A._s(A._f("simplifyAddress")(e.address,6,5)))]),t("p",{staticClass:"sender-lab"},[A._v(A._s(A.handleLabel(e.label)))])]}}],null,!1,3006168208)}),t("m-table-column",{attrs:{prop:"tx_hash_list.length",sortable:"custom",label:"Txn","min-width":"76"},scopedSlots:A._u([{key:"default",fn:function({row:e}){return[t("p",{staticClass:"txns-count",on:{click:function(t){return A.openTxnDetailTable(e.address,A.walletAddress,e.tx_hash_list)}}},[A._v(A._s(e.tx_hash_list.length))])]}}],null,!1,517905550)}),t("m-table-column",{attrs:{prop:"amount",label:A.coin,sortable:"","min-width":"90"},scopedSlots:A._u([{key:"default",fn:function({row:t}){return[A._v(" "+A._s(A._f("thousandth")(t.amount))+" ")]}}],null,!1,3704065288)})],1),t("div",{staticClass:"table-pagination mui-fl-end"},[t("m-pagination",{directives:[{name:"show",rawName:"v-show",value:A.inTxns.length>0,expression:"inTxns.length > 0"}],staticClass:"sty1-pagination sty4-cell",attrs:{background:"","current-page":A.inTxnsCurPage,"page-size":A.itemsPerPage,layout:"prev, pager, next",total:A.inTxns.length},on:{"update:currentPage":function(t){A.inTxnsCurPage=t},"update:current-page":function(t){A.inTxnsCurPage=t}}})],1)],1):A._e(),A.outTxns.length?t("div",{staticClass:"out-txns-table table-wrap mui-fl-col mui-fl-btw"},[t("m-table",{staticClass:"sty2-table",attrs:{data:A.paginatedData(A.outTxns,A.outTxnsCurPage)},on:{"sort-change":A.handleSortChangeOuttxns}},[t("m-table-column",{attrs:{label:"Show","max-width":"50"},scopedSlots:A._u([{key:"default",fn:function({row:e}){return[t("m-checkbox",{staticClass:"sty1-checkbox",on:{change:function(t){return A.handleOutCheckbox(e)}},model:{value:e.selected,callback:function(t){A.$set(e,"selected",t)},expression:"row.selected"}})]}}],null,!1,213247844)}),t("m-table-column",{attrs:{label:"Recipient","min-width":"295"},scopedSlots:A._u([{key:"default",fn:function({row:e}){return[t("p",{staticClass:"sender-add"},[A._v(A._s(A._f("simplifyAddress")(e.address,6,5)))]),t("p",{staticClass:"sender-lab"},[A._v(A._s(A.handleLabel(e.label)))])]}}],null,!1,3006168208)}),t("m-table-column",{attrs:{label:"Txn",sortable:"custom","min-width":"76"},scopedSlots:A._u([{key:"default",fn:function({row:e}){return[t("p",{staticClass:"txns-count",on:{click:function(t){return A.openTxnDetailTable(A.walletAddress,e.address,e.tx_hash_list)}}},[A._v(A._s(e.tx_hash_list.length))])]}}],null,!1,3048417902)}),t("m-table-column",{attrs:{prop:"amount",label:A.coin,sortable:"","min-width":"90"},scopedSlots:A._u([{key:"default",fn:function({row:t}){return[A._v(" "+A._s(A._f("thousandth")(t.amount))+" ")]}}],null,!1,3704065288)})],1),t("div",{staticClass:"table-pagination mui-fl-end"},[t("m-pagination",{directives:[{name:"show",rawName:"v-show",value:A.outTxns.length>0,expression:"outTxns.length > 0"}],staticClass:"sty1-pagination sty4-cell",attrs:{background:"","current-page":A.outTxnsCurPage,"page-size":A.itemsPerPage,layout:"prev, pager, next",total:A.outTxns.length},on:{"update:currentPage":function(t){A.outTxnsCurPage=t},"update:current-page":function(t){A.outTxnsCurPage=t}}})],1)],1):A._e()],1):A._e(),A.dataFilterExpanded?A._e():t("button",{staticClass:"expand-button",on:{click:function(t){A.dataFilterExpanded=!0}}},[t("i",{staticClass:"mico-chevron-left-double"})])],1)},w=[function(){var A=this,t=A._self._c;return t("div",{staticClass:"wallet-icon mui-fl-central"},[t("i",{staticClass:"mico-wallet"})])}],Q=(e(37550),e(52125)),d=e.n(Q),h=function(){var A=this,t=A._self._c;return t("m-dialog",{staticClass:"txn-detail-dialog",attrs:{title:"Transaction Details",visible:A.visible,"close-on-click-modal":!1,"close-on-press-escape":!1},on:{close:A.close}},[t("m-table",{directives:[{name:"loading",rawName:"v-loading",value:A.loading,expression:"loading"}],staticClass:"sty2-table",attrs:{data:A.tableData}},[t("m-table-column",{attrs:{label:"Time(UTC)","min-width":"164"},scopedSlots:A._u([{key:"default",fn:function({row:e}){return[t("p",[A._v(A._s(A._f("formatUTCDate")(e.time)))])]}}])}),t("m-table-column",{attrs:{label:"Sender","min-width":"220"},scopedSlots:A._u([{key:"default",fn:function(){return[t("p",[A._v(A._s(A._f("simplifyAddress")(A.fromAddress,8,7))+" "),t("i",{staticClass:"mico-copy",on:{click:function(t){return A.copy(A.fromAddress)}}})])]},proxy:!0}])}),t("m-table-column",{attrs:{label:"Recipient","min-width":"220"},scopedSlots:A._u([{key:"default",fn:function(){return[t("p",[A._v(A._s(A._f("simplifyAddress")(A.toAddress,8,7))+" "),t("i",{staticClass:"mico-copy",on:{click:function(t){return A.copy(A.toAddress)}}})])]},proxy:!0}])}),t("m-table-column",{attrs:{label:"Amount","min-width":"160"},scopedSlots:A._u([{key:"default",fn:function({row:e}){return[t("p",[A._v(A._s(A._f("thousandth")(A.formatNumber(e.amount)))+" "+A._s(A.coin))])]}}])}),t("m-table-column",{attrs:{"min-width":"220",label:"TXID"},scopedSlots:A._u([{key:"default",fn:function({row:e}){return[t("p",[A._v(A._s(A._f("simplifyAddress")(e.txId,8,7))+" "),t("i",{staticClass:"mico-copy",on:{click:function(t){return A.copy(e.txId)}}}),t("i",{staticClass:"mico-arrow-up-from-arc",on:{click:function(t){return A.blockchainExplore(e.txId)}}})])]}}])})],1),t("div",{staticClass:"table-pagination mui-fl-end"},[t("m-pagination",{directives:[{name:"show",rawName:"v-show",value:A.hashList.length>0,expression:"hashList.length > 0"}],staticClass:"sty1-pagination sty3-cell",attrs:{background:"","current-page":A.currPage,"page-size":A.itemsPerPage,layout:"prev, pager, next",total:A.hashList.length},on:{"update:currentPage":function(t){A.currPage=t},"update:current-page":function(t){A.currPage=t}}})],1)],1)},C=[],U={name:"KytTransactionDetailTable",props:{hashList:{type:Array,required:!0,default:()=>[]},visible:{type:Boolean,required:!0,default:!1},walletAddress:{type:String,required:!0},fromAddress:{type:String,required:!0},toAddress:{type:String,required:!0},decimals:{type:Number,required:!0},coin:{type:String,required:!0},requestParams:{type:String,required:!0}},data(){return{currPage:1,itemsPerPage:10,tableData:[],firstLoaded:!1,loading:!1,controller:new AbortController}},watch:{async hashList(A,t){A!==t&&(await this.loadTxnsDetail(),0===t.length&&(this.firstLoaded=!0))},async currPage(A,t){A!==t&&await this.loadTxnsDetail()}},methods:{paginatedData(A,t){const e=(t-1)*this.itemsPerPage,s=t*this.itemsPerPage;return A.slice(e,s)},close(){this.firstLoaded=!1,this.$emit("close")},formatNumber(A){A=Number(A.toFixed(4));const t=Math.round(1e6*A)/1e6,e=t.toString().split(".");return e[1]?`${e[0]}.${e[1].padEnd(6,"0").replace(/0+$/,"")}`:e[0]},async loadTxnsDetail(A=3){this.loading=!0;try{const t={hashList:this.paginatedData(this.hashList,this.currPage),from:this.fromAddress,to:this.toAddress,address:this.walletAddress,decimals:this.decimals||18,chain:this.requestParams,symbol:this.coin};this.controller.abort(),this.controller=new AbortController;const e=await this.$api.request("kyt.txnsDetail",t,this.controller,!0);if(8e7===e.code)this.tableData=e.data,this.loading=!1;else if(A<=1)return console.log(`Failed to fetch data. Retrying... (${A-1} attempts left)`),this.loadTxnsDetail(A-1)}catch(t){if(A<=1)throw t;return console.log(`Failed to fetch data. Retrying... (${A-1} attempts left)`),this.loadTxnsDetail(A-1)}},copy(A){const t=document.createElement("input");document.body.appendChild(t),t.value=A,t.select(),document.execCommand("Copy"),document.body.removeChild(t),this.$message({message:"Copied",iconClass:"mcico-success",customClass:"sty4-message",duration:3e3,offset:32,center:!0})},blockchainExplore(A){let t="";switch(this.requestParams){case"ERC20":t="https://etherscan.io/tx/";break;case"BEP20":t="https://bscscan.com/tx/";break;case"Polygon":t="https://polygonscan.com/tx/";break;case"Avalanche":t="https://avascan.info/blockchain/c/tx/";break;case"Arbitrum":t="https://arbiscan.io/tx/";break;case"Optimism":t="https://optimistic.etherscan.io/tx/";break;case"Base":t="https://basescan.org/tx/";break}window.open(`${t}${A}`,"_blank")}}},F=U,f=(0,B.A)(F,h,C,!1,null,null,null),p=f.exports,m=p,y={components:{KytTransactionDetailTable:m},name:"KytTransactionGraph",props:{transactionsInvestigationData:{type:Object,required:!0,default:()=>({in:[],out:[],page:1,total_pages:1,transactions_on_page:1})},walletAddress:{type:String,required:!0,default:()=>""},graphStartTime:{type:Number,required:0},graphEndTime:{type:Number,required:0},graphFilterStartTime:{type:Number,required:!1},graphFilterEndTime:{type:Number,required:!1},requestParams:{type:String,required:!0},coin:{type:String,required:!0,default:()=>"ETH"},decimals:{type:Number,required:!0}},data(){return{inTxns:[],outTxns:[],originInTxns:[],originOutTxns:[],scale:1,dragging:!1,lastX:0,lastY:0,offsetX:0,offsetY:0,startTime:null,endTime:null,filterStartTime:null,filterEndTime:null,filterTxnOptions:[{label:"All txs",value:0},{label:"Only incoming",value:1},{label:"Only outgoing",value:2}],filterAddressOptions:[{label:"All addresses",value:0},{label:"Entity address",value:3},{label:"Malicious address",value:2},{label:"Unknown address",value:1}],query:{search:"",filterTxnType:0,filterAddressType:0,minAmount:null,maxAmount:null},inTxnsCurPage:1,outTxnsCurPage:1,itemsPerPage:5,dataFilterExpanded:!0,graphCurPage:1,graphTotalPages:1,tableVisible:!1,tableHashList:[],txnDetailFromAddress:"",txnDetailToAddress:"",isZooming:!1}},watch:{transactionsInvestigationData(A,t){A!==t&&(this.inTxns=this.transactionsInvestigationData.in,this.outTxns=this.transactionsInvestigationData.out,this.inTxns.forEach((A=>{this.$set(A,"selected",!0)})),this.outTxns.forEach((A=>{this.$set(A,"selected",!0)})),this.originInTxns=JSON.parse(JSON.stringify(this.inTxns)),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns)),this.graphCurPage=this.transactionsInvestigationData.page,this.graphTotalPages=this.transactionsInvestigationData.total_pages)},graphStartTime(){this.startTime=1e3*this.graphStartTime},graphEndTime(){this.endTime=1e3*this.graphEndTime},graphFilterStartTime(){this.filterStartTime=this.graphFilterStartTime},graphFilterEndTime(){this.filterEndTime=this.graphFilterEndTime}},mounted(){this.inTxns=this.transactionsInvestigationData.in,this.outTxns=this.transactionsInvestigationData.out,this.inTxns.forEach((A=>{this.$set(A,"selected",!0)})),this.outTxns.forEach((A=>{this.$set(A,"selected",!0)})),this.originInTxns=JSON.parse(JSON.stringify(this.inTxns)),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns)),this.graphCurPage=this.transactionsInvestigationData.page,this.graphTotalPages=this.transactionsInvestigationData.total_pages,this.startTime=1e3*this.graphStartTime,this.endTime=1e3*this.graphEndTime,this.filterStartTime=this.graphFilterStartTime,this.filterEndTime=this.graphFilterEndTime,this.$refs.graphContainer.addEventListener("wheel",this.onWheel)},computed:{picker:{get(){return this.filterStartTime||this.startTime?[this.filterStartTime||this.startTime,this.filterEndTime||this.endTime]:""},set(A){this.filterStartTime=A?new Date(A[0]).getTime():"",this.filterEndTime=A?new Date(A[1]).getTime():""}},pickerOptions(){const A=new Date(1e3*this.graphStartTime);A.setDate(A.getDate()-1);const t=A.getTime(),e=new Date(1e3*this.graphEndTime);e.setHours(23,59,59,999);const s=e.getTime();return{disabledDate(A){return A.getTime()<t||A.getTime()>s}}},filteredInTxns(){return this.inTxns.filter((A=>A.selected))},filteredOutTxns(){return this.outTxns.filter((A=>A.selected))},topLeftTopItems(){return this.filteredInTxns.length>=2?this.filteredInTxns.slice(0,Math.floor(this.filteredInTxns.length/2)):[]},cenLeftItem(){return this.filteredInTxns.length?1===this.filteredInTxns.length?this.filteredInTxns[0]:this.filteredInTxns[Math.floor(this.filteredInTxns.length/2)]:null},botLeftItems(){return this.filteredInTxns.length>=3?this.filteredInTxns.slice(Math.floor(this.filteredInTxns.length/2)+1,this.filteredInTxns.length):[]},topRightItems(){return this.filteredOutTxns.length>=2?this.filteredOutTxns.slice(0,Math.floor(this.filteredOutTxns.length/2)):[]},cenRightItem(){return this.filteredOutTxns.length?1===this.filteredOutTxns.length?this.filteredOutTxns[0]:this.filteredOutTxns[Math.floor(this.filteredOutTxns.length/2)]:null},botRightItems(){return this.filteredOutTxns.length>=3?this.filteredOutTxns.slice(Math.floor(this.filteredOutTxns.length/2)+1,this.filteredOutTxns.length):[]}},beforeDestroy(){this.$refs.graphContainer.removeEventListener("wheel",this.onWheel)},methods:{onWheel(A){A.preventDefault();const t=this.$refs.graph.getBoundingClientRect(),e=A.clientX-t.left,s=A.clientY-t.top,r=.05,n=.03,i=3;let a=A.deltaY<0?this.scale+r:this.scale-r;a=Math.max(n,Math.min(i,a));const o=a/this.scale;this.offsetX=(1-o)*e+o*this.offsetX,this.offsetY=(1-o)*s+o*this.offsetY,this.scale=a,this.applyTransform()},createSvg(A){const t=`M90 ${A-3}L85 ${A-5.887}V${A}L90 ${A-3}ZM85.5 ${A-2.5}H75.5V${A-3.5}H85.5V${A-2.5}Z`,e=`M45 ${A-33}V31H46V${A-33}H45ZM15.5 1.5H0V0.5H15.5V1.5ZM75.5 ${A-2.5}C58.6553 ${A-2.5} 45 ${A-17.155} 45 ${A-33}H46C46 ${A-16.708} 59.2076 ${A-3.5} 75.5 ${A-3.5}V${A-2.5}ZM45 31C45 14.7076 31.7924 1.5 15.5 1.5V0.5C32.3447 0.5 46 14.1553 46 31H45Z`;return t+e},handleLabel(A){if(A.includes(",")){const t=A.split(",");return t[t.length-1]}return A||"Normal"},handleIcon(A){return 2===A.type?"mico-warning1":""===A.label?"mico-that-person":"mico-on-chain"},async downloadGraph(){try{const A=this.$message({message:"Downloading...",iconClass:"mcico-success",customClass:"sty4-message",duration:0,offset:32,center:!0});setTimeout((async()=>{const t=Math.max(this.filteredInTxns.length,this.filteredOutTxns.length)*(this.topLeftTopItems.length>this.botLeftItems.length||this.topRightItems.length>this.botRightItems.length?90:80),e=this.$refs.graphContainer,s=e.cloneNode(!0);s.removeChild(s.children[1]),s.setAttribute("style",`height: ${t||270}px !important`),this.$refs.downloadImgRender.children.length&&(this.$refs.downloadImgRender.innerHTML=""),this.$refs.downloadImgRender.appendChild(s),this.$refs.downloadImgRender.firstChild.firstChild.setAttribute("style","transform: scale(1) translate3d(0px, 0px, 0px);");const r=await d()(s,{backgroundColor:"#f2f7f7"});this.$refs.downloadImgRender.removeChild(s);const n=document.createElement("a");n.href=r.toDataURL("image/png"),n.download="captured-image.png",A.close(),n.click(),this.$message({message:"Success",iconClass:"mcico-success",customClass:"sty4-message",duration:3e3,offset:32,center:!0})}))}catch(A){console.log(A)}},zoomIn(){this.scale+=.05,this.applyTransform()},zoomOut(){this.scale=Math.max(this.scale-.05,.03),this.applyTransform()},recenter(){const A=Math.max(this.filteredInTxns.length,this.filteredOutTxns.length)*(this.topLeftTopItems.length>this.botLeftItems.length||this.topRightItems.length>this.botRightItems.length?90:80);this.scale=A>1020?1020/A:1,this.lastX=0,this.lastY=0,this.offsetX=0,this.offsetY=0,this.applyTransform()},toggleFullscreen(){const A=this.$refs.container;document.addEventListener("fullscreenchange",this.onFullscreenChange),document.fullscreenElement?document.exitFullscreen().then((()=>{A.scrollIntoView({behavior:"smooth"})})).catch(console.error):(this.dataFilterExpanded&&(this.dataFilterExpanded=!1),A.requestFullscreen().catch(console.error))},onFullscreenChange(){document.fullscreenElement||this.$refs.container.scrollIntoView({behavior:"smooth"})},applyTransform(){this.$refs.graph.style.transform=`scale(${this.scale}) translate3d(${this.offsetX}px, ${this.offsetY}px, 0)`},startDrag(A){this.dragging=!0,this.lastX=A.clientX,this.lastY=A.clientY},onDrag(A){if(!this.dragging)return;const t=A.clientX-this.lastX,e=A.clientY-this.lastY;this.offsetX+=t,this.offsetY+=e,this.applyTransform(),this.lastX=A.clientX,this.lastY=A.clientY},stopDrag(){this.dragging=!1},handleChangeDate(){this.graphCurPage=1,this.$emit("paginateTxnInvestigation",this.graphCurPage,this.picker[0],this.picker[1])},paginatedData(A,t){const e=(t-1)*this.itemsPerPage,s=t*this.itemsPerPage;return A.slice(e,s)},filterDataByConditions(){switch(this.query.filterTxnType){case 0:default:this.inTxns=this.transactionsInvestigationData.in,this.outTxns=this.transactionsInvestigationData.out,this.originInTxns=JSON.parse(JSON.stringify(this.inTxns)),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns));break;case 1:this.inTxns=this.transactionsInvestigationData.in,this.outTxns=[],this.originInTxns=JSON.parse(JSON.stringify(this.inTxns)),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns));break;case 2:this.outTxns=this.transactionsInvestigationData.out,this.inTxns=[],this.originInTxns=JSON.parse(JSON.stringify(this.inTxns)),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns));break}this.filterDataByAddressType(),this.filterDataByAmountRange(),this.filterDataByAddressOrLabel()},filterDataByAddressOrLabel(){const A=this.query.search;A.trim().length&&(this.inTxns.length&&(this.inTxns=this.inTxns.filter((t=>t.address.toLowerCase().includes(A.trim().toLowerCase())||t.label.toLowerCase().includes(A.trim().toLowerCase()))),this.originInTxns=JSON.parse(JSON.stringify(this.inTxns))),this.outTxns.length&&(this.outTxns=this.outTxns.filter((t=>t.address.toLowerCase().includes(A.trim().toLowerCase())||t.label.toLowerCase().includes(A.trim().toLowerCase()))),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns))))},filterDataByAddressType(){const A=this.query.filterAddressType;0!==A&&(this.inTxns.length&&(this.inTxns=this.inTxns.filter((t=>t.type===A)),this.originInTxns=JSON.parse(JSON.stringify(this.inTxns))),this.outTxns.length&&(this.outTxns=this.outTxns.filter((t=>t.type===A)),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns))))},filterDataByAmountRange(){const A=this.query.minAmount,t=this.query.maxAmount;(A||t)&&(A&&t?(this.inTxns.length&&(this.inTxns=this.inTxns.filter((e=>e.amount>=A&&e.amount<=t)),this.originInTxns=JSON.parse(JSON.stringify(this.inTxns))),this.outTxns.length&&(this.outTxns=this.outTxns.filter((e=>e.amount>=A&&e.amount<=t)),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns)))):A?(this.inTxns.length&&(this.inTxns=this.inTxns.filter((t=>t.amount>=A)),this.originInTxns=JSON.parse(JSON.stringify(this.inTxns))),this.outTxns.length&&(this.outTxns=this.outTxns.filter((t=>t.amount>=A)),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns)))):(this.inTxns.length&&(this.inTxns=this.inTxns.filter((A=>A.amount<=t)),this.originInTxns=JSON.parse(JSON.stringify(this.inTxns))),this.outTxns.length&&(this.outTxns=this.outTxns.filter((A=>A.amount<=t)),this.originInTxns=JSON.parse(JSON.stringify(this.inTxns)))))},cleanData(){this.inTxns.forEach((A=>{this.$set(A,"selected",!1)})),this.outTxns.forEach((A=>{this.$set(A,"selected",!1)})),this.originInTxns=JSON.parse(JSON.stringify(this.inTxns)),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns))},revertData(){this.inTxns.forEach((A=>{this.$set(A,"selected",!0)})),this.outTxns.forEach((A=>{this.$set(A,"selected",!0)})),this.originInTxns=JSON.parse(JSON.stringify(this.inTxns)),this.originOutTxns=JSON.parse(JSON.stringify(this.outTxns))},openTxnDetailTable(A,t,e){this.tableHashList=e,this.txnDetailFromAddress=A,this.txnDetailToAddress=t,this.tableVisible=!0},nextGraph(){this.graphCurPage+1<=this.graphTotalPages?(this.graphCurPage+=1,this.$emit("paginateTxnInvestigation",this.graphCurPage,null,null)):this.$message({message:"No more pages available.",type:"warning"})},prevGraph(){this.graphCurPage-1>=1?(this.graphCurPage-=1,this.$emit("paginateTxnInvestigation",this.graphCurPage,null,null)):this.$message({message:"No previous pages available.",type:"warning"})},handleSortChangeIntxns({prop:A,order:t}){"amount"===A?"ascending"===t?this.inTxns.sort(((A,t)=>Number(A.amount)>Number(t.amount)?1:-1)):"descending"===t?this.inTxns.sort(((A,t)=>Number(A.amount)<Number(t.amount)?1:-1)):this.inTxns=JSON.parse(JSON.stringify(this.originInTxns)):"ascending"===t?this.inTxns.sort(((A,t)=>A.tx_hash_list.length>t.tx_hash_list.length?1:-1)):"descending"===t?this.inTxns.sort(((A,t)=>A.tx_hash_list.length<t.tx_hash_list.length?1:-1)):this.inTxns=JSON.parse(JSON.stringify(this.originInTxns))},handleSortChangeOuttxns({prop:A,order:t}){"amount"===A?"ascending"===t?this.outTxns.sort(((A,t)=>Number(A.amount)>Number(t.amount)?1:-1)):"descending"===t?this.outTxns.sort(((A,t)=>Number(A.amount)<Number(t.amount)?1:-1)):this.outTxns=JSON.parse(JSON.stringify(this.originOutTxns)):"ascending"===t?this.outTxns.sort(((A,t)=>A.tx_hash_list.length>t.tx_hash_list.length?1:-1)):"descending"===t?this.outTxns.sort(((A,t)=>A.tx_hash_list.length<t.tx_hash_list.length?1:-1)):this.outTxns=JSON.parse(JSON.stringify(this.originOutTxns))},handleInCheckbox(A){this.originInTxns.some((t=>{if(t.address===A.address)return t.selected=A.selected,!0}))},handleOutCheckbox(A){this.originOutTxns.some((t=>{if(t.address===A.address)return t.selected=A.selected,!0}))}}},H=y,v=(0,B.A)(H,u,w,!1,null,"5acbf266",null),E=v.exports,I=E,b=e(5332),x=e(9435),K={components:{KytTransactionGraph:I,LoadingLottie:b.A,kytDetails:g},data(){return{RP:null,incoming:{},outgoing:{},addressLabels:null,addressOverview:null,addressAction:[],transactionsInvestigation:null,addressTrace:{},riskscore:null,tokenList:[],kycProgramId:"",transaction:{Incoming:!1,Outgoing:!1},tableData:[{level:"Severe",score:"91-100"},{level:"High",score:"71-90"},{level:"Moderate",score:"31-70"},{level:"Low",score:"0-30"}],selectList:[{chain:"Ethereum",token:"ERC20"},{chain:"BSC",token:"BEP20"},{chain:"Polygon",token:"Polygon"},{chain:"Avalanche",token:"Avalanche"},{chain:"Arbitrum One",token:"Arbitrum"},{chain:"Base",token:"Base"}],chain:"Ethereum",selectValue:"ERC20",symbol:"ETH",tokenName:"",skeleton:{addressLabels:!1,addressOverview:!1,riskscore:!1,transactionsInvestigation:!1,addressAction:!1,addressTrace:!1,tokens:!1},findAddress:"",labelList:["Risk Type","Address/Risk label","Volume(USD)%"],paginatedData:[],scoreText:"",requestParams:"ETH",kytDetailstip:!1,transactionsInvestigationDataLoading:!0,transactionsInvestigationData:{},graphFilterStartTime:null,graphFilterEndTime:null,decimals:18,controller:new AbortController}},computed:{},watch:{},async created(){this.blockchainId=this.$route.query.blockchainId,this.kycProgramId=this.$route.query.kycProgramId,"7"===this.blockchainId?(this.chain="Polygon",this.selectValue="Polygon",this.symbol="MATIC",this.tokenName="Polygon"):"8"===this.blockchainId&&(this.chain="Base",this.selectValue="Base",this.symbol="ETH",this.tokenName="Base"),this.userWalletAddress=this.$route.query.userWalletAddress,this.zkmeId=this.$route.query.zkmeId,await this.getTokenList(),this.getDetail()},methods:{async setSelect(A,t){this.skeleton={addressLabels:!1,addressOverview:!1,riskscore:!1,transactionsInvestigation:!1,addressAction:!1,addressTrace:!1,tokens:!1},this.paginatedData=[],this.transactionsInvestigationDataLoading=!0,this.decimals=Number(A.decimals),this.controller.abort(),this.controller=new AbortController,"chain"===t?(this.chain=A.chain,this.selectValue=A.token,this.symbol=A.symbol,await this.getTokenList(),await this.getDetail()):"token"===t?(this.requestParams=A.coin,this.symbol=A.symbol,this.tokenName=A.symbol,await this.getDetail()):"findAddress"===t&&this.findAddress&&(this.userWalletAddress=this.findAddress,await this.getTokenList(),await this.getDetail())},setAxisChart(){const A=x.Ts(document.getElementById("AML"));let t=[],e="",s="";t="Low"===this.riskscore?.risk_level?[33,33,33]:this.riskscore?.hacking_event||this.riskscore.risk_detail.find((A=>("malicious"===A.type||"suspected_malicious"===A.type)&&A.address===this.userWalletAddress))?[33,this.riskscore.score,33]:this.riskscore?.risk_detail&&1===this.riskscore.risk_detail.length&&this.riskscore.risk_detail.find((A=>("high_risk"===A.type||"medium_risk"===A.type)&&A.address===this.userWalletAddress))?[this.riskscore.score,33,33]:this.riskscore?.risk_detail&&this.riskscore.risk_detail.length?[33,33,this.riskscore.score]:[33,33,33],this.riskscore.score>=91&&this.riskscore.score<=100?(e="rgba(254, 164, 145, 0.5)",s="#FEA491"):this.riskscore.score>=71&&this.riskscore.score<=90?(e="rgba(255, 226, 142, 0.5)",s="#FFE28E"):this.riskscore.score>=31&&this.riskscore.score<=70?(e="rgba(100, 171, 255, 0.5)",s="#64ABFF"):this.riskscore.score<=30&&(e="rgba(169, 225, 211, 0.5)",s="#005563"),A.setOption({tooltip:{trigger:"axis"},legend:{left:"center"},radar:[{indicator:[{text:"Risky entity",max:100},{text:"Hacking event",max:100},{text:"Suspicious txn",max:100}],axisName:{padding:[-3,-50],color:"#738C8F",fontSize:12,lineHeight:16,fontWeight:500,verticalAlign:"top"},center:["50%","63%"],radius:100,splitNumber:3,splitArea:{areaStyle:{color:["#F7F7F7","#FFFFFF"]}},axisLine:{lineStyle:{color:"#005563"}}}],series:[{type:"radar",itemStyle:{color:"#005563",borderColor:["#005563","red","red"]},lineStyle:{width:1},data:[{value:t,symbolSize:4,areaStyle:{color:e},lineStyle:{color:s}},{value:[100,100,100],symbolSize:0}]}]})},setPieChart(A,t){const e=["#64ABFF","#FEA491","#88F0DA","#FFE28E","#C08EFF","#FF8EB7","#8EFF93","#8EE4FF","#8EA0FF","#FF8E8E","#EF8EFF","#F6FF8E","#FFC48E","#8EA0FF","#8EF8FF","#8EFFD0","#FFB26C","#B56CFF","#FF6C6C"];var s=x.Ts(document.getElementById(A));t.forEach(((A,t)=>{A.name=A.action,A.value=A.count,A.rate=A.proportion,A.styColor=e[t]})),this.addressAction.push(t),s.setOption({tooltip:{show:!1},color:e,series:[{type:"pie",radius:["65%","95%"],top:0,bottom:0,left:0,startAngle:0,avoidLabelOverlap:!1,label:{show:!1,position:"center"},labelLine:{show:!1},data:t}]}),"Incoming"===A&&(this.incoming=t[0]),"Outgoing"===A&&(this.outgoing=t[0]),s.on("mousemove",(t=>{const{value:e,name:s,color:r,rate:n}=t.data;"Incoming"===A&&(this.incoming={value:e,name:s,color:r,rate:n}),"Outgoing"===A&&(this.outgoing={value:e,name:s,color:r,rate:n})})),s.on("mouseout",(()=>{"Incoming"===A&&(this.incoming=t[0]),"Outgoing"===A&&(this.outgoing=t[0])}))},requestSet(A,t){return new Promise(((t,e)=>{(async()=>{try{const e=await this.$api.request(`kyt.${A}`,{coin:this.requestParams,walletAddress:this.userWalletAddress},this.controller,!0);t(Object.assign(e,{flg:A}))}catch(s){e(A)}})()}))},async getDetail(){const A=await this.$api.request("dashboard.getDetail",{blockchainId:this.blockchainId,userWalletAddress:this.userWalletAddress,zkmeId:this.zkmeId,kycProgramId:this.kycProgramId});8e7===A.code&&(this.RP=A.data);const t=["addressLabels","addressOverview","riskscore","transactionsInvestigation","addressAction","addressTrace"];t.forEach(((A,t)=>{this.skeleton[A]=!0,this.requestSet(A,t).then((t=>{if(this.skeleton[t.flg]=!1,8e7===t.code&&"addressLabels"===t.flg)this.addressLabels=t.data?.data.label_list.filter((A=>A));else if(8e7===t.code&&"addressOverview"===t.flg)this.addressOverview=t.data?.data,this.graphFilterStartTime=null,this.graphFilterEndTime=null;else if(8e7===t.code&&"riskscore"===t.flg)this.riskscore=t.data.data,this.paginatedData=t.data.data.risk_detail,setTimeout((()=>{this.setAxisChart()}));else if(8e7===t.code&&"transactionsInvestigation"===t.flg)this.transactionsInvestigation=t.data?.data,this.transactionsInvestigationDataLoading=!1;else if(8e7===t.code&&"addressAction"===t.flg)"UnsupportedAddressType"===t.data.msg?this.addressAction=null:setTimeout((()=>{this.addressAction=[],t.data?.action_dic?.received_txs.length?(this.transaction.Incoming=!0,this.setPieChart("Incoming",t.data?.action_dic?.received_txs)):this.transaction.Incoming=!1,t.data?.action_dic?.spent_txs.length?(this.transaction.Outgoing=!0,this.setPieChart("Outgoing",t.data?.action_dic?.spent_txs)):this.transaction.Outgoing=!1}));else if(8e7===t.code&&"addressTrace"===t.flg)if("UnsupportedAddressType"===t.data.msg)this.addressTrace=null;else{const A={};for(const e in t.data.data)for(const s in t.data.data[e])Object.assign(A,{[s]:t.data.data[e][s]});this.addressTrace=A}else 8e7!==t.code&&"transactionsInvestigation"===t.flg?(this.transactionsInvestigationDataLoading=!1,this[A]=null):this[A]=null})).catch((A=>{this.skeleton[A]=!1,this.transactionsInvestigation=null,this.transactionsInvestigationDataLoading=!1}))}))},generateRandomColor(){const A=Math.floor(256*Math.random()),t=Math.floor(256*Math.random()),e=Math.floor(256*Math.random()),s="#"+(1<<24|A<<16|t<<8|e).toString(16).slice(1);return s},async getTokenList(){this.skeleton.tokens=!0;const A=await this.$api.request("kyt.tokens",{chain:this.selectValue,address:this.userWalletAddress},{},!0);this.skeleton.tokens=!1,8e7===A.code&&(this.tokenList=A.data,this.requestParams=A.data[0].coin,this.tokenName=A.data[0].symbol)},async paginateTxnInvestigation(A,t,e){try{this.transactionsInvestigationDataLoading=!0,t&&(this.graphFilterStartTime=t),e&&(this.graphFilterEndTime=e);const s=await this.$api.request("kyt.transactionsInvestigation",{coin:"ETH",walletAddress:this.userWalletAddress,page:A,...t?{startTimestamp:t/1e3}:{},...e?{endTimestamp:e/1e3}:{}},{},!0);8e7===s.code?(this.transactionsInvestigation=s.data.data,this.transactionsInvestigationDataLoading=!1):(this.transactionsInvestigation=null,this.transactionsInvestigationDataLoading=!1)}catch(s){console.log(s),this.transactionsInvestigationDataLoading=!1}},downloadPdf(){this.$message({message:"Coming soon",iconClass:"mico-lightTip",customClass:"sty1-message",duration:3e3,offset:32,center:!0})},detail(){this.kytDetailstip=!this.kytDetailstip},copy(){if(!this.addressLabels)return;const A=document.createElement("input");A.value=this.addressLabels.toString(),document.body.appendChild(A),A.select(),document.execCommand("Copy"),A.remove(),this.$message({message:"Copied",iconClass:"mcico-success",customClass:"sty4-message",duration:3e3,offset:32,center:!0})},updateWalletAddress(A){this.userWalletAddress=A}}},L=K,T=(0,B.A)(L,s,r,!1,null,"49b6ea14",null),D=T.exports},52125:function(A,t,e){e(44114),e(16573),e(78100),e(77936),e(69479),e(37467),e(44732),e(79577),e(98992),e(23215),e(54520),e(72577),e(3949),e(81454),e(8872),e(37550),
/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 Niklas von Hertzen <https://hertzen.com>
 * Released under MIT License
 */
function(t,e){A.exports=e()}(0,(function(){"use strict";
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.
    Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.
    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */var A=function(t,e){return A=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,t){A.__proto__=t}||function(A,t){for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&(A[e]=t[e])},A(t,e)};function t(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function s(){this.constructor=t}A(t,e),t.prototype=null===e?Object.create(e):(s.prototype=e.prototype,new s)}var e=function(){return e=Object.assign||function(A){for(var t,e=1,s=arguments.length;e<s;e++)for(var r in t=arguments[e],t)Object.prototype.hasOwnProperty.call(t,r)&&(A[r]=t[r]);return A},e.apply(this,arguments)};function s(A,t,e,s){function r(A){return A instanceof e?A:new e((function(t){t(A)}))}return new(e||(e=Promise))((function(e,n){function i(A){try{o(s.next(A))}catch(Jt){n(Jt)}}function a(A){try{o(s["throw"](A))}catch(Jt){n(Jt)}}function o(A){A.done?e(A.value):r(A.value).then(i,a)}o((s=s.apply(A,t||[])).next())}))}function r(A,t){var e,s,r,n,i={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return n={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(n[Symbol.iterator]=function(){return this}),n;function a(A){return function(t){return o([A,t])}}function o(n){if(e)throw new TypeError("Generator is already executing.");while(i)try{if(e=1,s&&(r=2&n[0]?s["return"]:n[0]?s["throw"]||((r=s["return"])&&r.call(s),0):s.next)&&!(r=r.call(s,n[1])).done)return r;switch(s=0,r&&(n=[2&n[0],r.value]),n[0]){case 0:case 1:r=n;break;case 4:return i.label++,{value:n[1],done:!1};case 5:i.label++,s=n[1],n=[0];continue;case 7:n=i.ops.pop(),i.trys.pop();continue;default:if(r=i.trys,!(r=r.length>0&&r[r.length-1])&&(6===n[0]||2===n[0])){i=0;continue}if(3===n[0]&&(!r||n[1]>r[0]&&n[1]<r[3])){i.label=n[1];break}if(6===n[0]&&i.label<r[1]){i.label=r[1],r=n;break}if(r&&i.label<r[2]){i.label=r[2],i.ops.push(n);break}r[2]&&i.ops.pop(),i.trys.pop();continue}n=t.call(A,i)}catch(Jt){n=[6,Jt],s=0}finally{e=r=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}}function n(A,t,e){if(e||2===arguments.length)for(var s,r=0,n=t.length;r<n;r++)!s&&r in t||(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return A.concat(s||t)}for(var i=function(){function A(A,t,e,s){this.left=A,this.top=t,this.width=e,this.height=s}return A.prototype.add=function(t,e,s,r){return new A(this.left+t,this.top+e,this.width+s,this.height+r)},A.fromClientRect=function(t,e){return new A(e.left+t.windowBounds.left,e.top+t.windowBounds.top,e.width,e.height)},A.fromDOMRectList=function(t,e){var s=Array.from(e).find((function(A){return 0!==A.width}));return s?new A(s.left+t.windowBounds.left,s.top+t.windowBounds.top,s.width,s.height):A.EMPTY},A.EMPTY=new A(0,0,0,0),A}(),a=function(A,t){return i.fromClientRect(A,t.getBoundingClientRect())},o=function(A){var t=A.body,e=A.documentElement;if(!t||!e)throw new Error("Unable to get document size");var s=Math.max(Math.max(t.scrollWidth,e.scrollWidth),Math.max(t.offsetWidth,e.offsetWidth),Math.max(t.clientWidth,e.clientWidth)),r=Math.max(Math.max(t.scrollHeight,e.scrollHeight),Math.max(t.offsetHeight,e.offsetHeight),Math.max(t.clientHeight,e.clientHeight));return new i(0,0,s,r)},B=function(A){var t=[],e=0,s=A.length;while(e<s){var r=A.charCodeAt(e++);if(r>=55296&&r<=56319&&e<s){var n=A.charCodeAt(e++);56320===(64512&n)?t.push(((1023&r)<<10)+(1023&n)+65536):(t.push(r),e--)}else t.push(r)}return t},c=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var e=A.length;if(!e)return"";var s=[],r=-1,n="";while(++r<e){var i=A[r];i<=65535?s.push(i):(i-=65536,s.push(55296+(i>>10),i%1024+56320)),(r+1===e||s.length>16384)&&(n+=String.fromCharCode.apply(String,s),s.length=0)}return n},l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",g="undefined"===typeof Uint8Array?[]:new Uint8Array(256),u=0;u<l.length;u++)g[l.charCodeAt(u)]=u;for(var w="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Q="undefined"===typeof Uint8Array?[]:new Uint8Array(256),d=0;d<w.length;d++)Q[w.charCodeAt(d)]=d;for(var h=function(A){var t,e,s,r,n,i=.75*A.length,a=A.length,o=0;"="===A[A.length-1]&&(i--,"="===A[A.length-2]&&i--);var B="undefined"!==typeof ArrayBuffer&&"undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint8Array.prototype.slice?new ArrayBuffer(i):new Array(i),c=Array.isArray(B)?B:new Uint8Array(B);for(t=0;t<a;t+=4)e=Q[A.charCodeAt(t)],s=Q[A.charCodeAt(t+1)],r=Q[A.charCodeAt(t+2)],n=Q[A.charCodeAt(t+3)],c[o++]=e<<2|s>>4,c[o++]=(15&s)<<4|r>>2,c[o++]=(3&r)<<6|63&n;return B},C=function(A){for(var t=A.length,e=[],s=0;s<t;s+=2)e.push(A[s+1]<<8|A[s]);return e},U=function(A){for(var t=A.length,e=[],s=0;s<t;s+=4)e.push(A[s+3]<<24|A[s+2]<<16|A[s+1]<<8|A[s]);return e},F=5,f=11,p=2,m=f-F,y=65536>>F,H=1<<F,v=H-1,E=1024>>F,I=y+E,b=I,x=32,K=b+x,L=65536>>f,T=1<<m,D=T-1,k=function(A,t,e){return A.slice?A.slice(t,e):new Uint16Array(Array.prototype.slice.call(A,t,e))},S=function(A,t,e){return A.slice?A.slice(t,e):new Uint32Array(Array.prototype.slice.call(A,t,e))},O=function(A,t){var e=h(A),s=Array.isArray(e)?U(e):new Uint32Array(e),r=Array.isArray(e)?C(e):new Uint16Array(e),n=24,i=k(r,n/2,s[4]/2),a=2===s[5]?k(r,(n+s[4])/2):S(s,Math.ceil((n+s[4])/4));return new M(s[0],s[1],s[2],s[3],i,a)},M=function(){function A(A,t,e,s,r,n){this.initialValue=A,this.errorValue=t,this.highStart=e,this.highValueIndex=s,this.index=r,this.data=n}return A.prototype.get=function(A){var t;if(A>=0){if(A<55296||A>56319&&A<=65535)return t=this.index[A>>F],t=(t<<p)+(A&v),this.data[t];if(A<=65535)return t=this.index[y+(A-55296>>F)],t=(t<<p)+(A&v),this.data[t];if(A<this.highStart)return t=K-L+(A>>f),t=this.index[t],t+=A>>F&D,t=this.index[t],t=(t<<p)+(A&v),this.data[t];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),V="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",G="undefined"===typeof Uint8Array?[]:new Uint8Array(256),P=0;P<V.length;P++)G[V.charCodeAt(P)]=P;var N="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",R=50,_=1,J=2,X=3,Y=4,W=5,Z=7,q=8,z=9,j=10,$=11,AA=12,tA=13,eA=14,sA=15,rA=16,nA=17,iA=18,aA=19,oA=20,BA=21,cA=22,lA=23,gA=24,uA=25,wA=26,QA=27,dA=28,hA=29,CA=30,UA=31,FA=32,fA=33,pA=34,mA=35,yA=36,HA=37,vA=38,EA=39,IA=40,bA=41,xA=42,KA=43,LA=[9001,65288],TA="!",DA="×",kA="÷",SA=O(N),OA=[CA,yA],MA=[_,J,X,W],VA=[j,q],GA=[QA,wA],PA=MA.concat(VA),NA=[vA,EA,IA,pA,mA],RA=[sA,tA],_A=function(A,t){void 0===t&&(t="strict");var e=[],s=[],r=[];return A.forEach((function(A,n){var i=SA.get(A);if(i>R?(r.push(!0),i-=R):r.push(!1),-1!==["normal","auto","loose"].indexOf(t)&&-1!==[8208,8211,12316,12448].indexOf(A))return s.push(n),e.push(rA);if(i===Y||i===$){if(0===n)return s.push(n),e.push(CA);var a=e[n-1];return-1===PA.indexOf(a)?(s.push(s[n-1]),e.push(a)):(s.push(n),e.push(CA))}return s.push(n),i===UA?e.push("strict"===t?BA:HA):i===xA||i===hA?e.push(CA):i===KA?A>=131072&&A<=196605||A>=196608&&A<=262141?e.push(HA):e.push(CA):void e.push(i)})),[s,e,r]},JA=function(A,t,e,s){var r=s[e];if(Array.isArray(A)?-1!==A.indexOf(r):A===r){var n=e;while(n<=s.length){n++;var i=s[n];if(i===t)return!0;if(i!==j)break}}if(r===j){n=e;while(n>0){n--;var a=s[n];if(Array.isArray(A)?-1!==A.indexOf(a):A===a){var o=e;while(o<=s.length){o++;i=s[o];if(i===t)return!0;if(i!==j)break}}if(a!==j)break}}return!1},XA=function(A,t){var e=A;while(e>=0){var s=t[e];if(s!==j)return s;e--}return 0},YA=function(A,t,e,s,r){if(0===e[s])return DA;var n=s-1;if(Array.isArray(r)&&!0===r[n])return DA;var i=n-1,a=n+1,o=t[n],B=i>=0?t[i]:0,c=t[a];if(o===J&&c===X)return DA;if(-1!==MA.indexOf(o))return TA;if(-1!==MA.indexOf(c))return DA;if(-1!==VA.indexOf(c))return DA;if(XA(n,t)===q)return kA;if(SA.get(A[n])===$)return DA;if((o===FA||o===fA)&&SA.get(A[a])===$)return DA;if(o===Z||c===Z)return DA;if(o===z)return DA;if(-1===[j,tA,sA].indexOf(o)&&c===z)return DA;if(-1!==[nA,iA,aA,gA,dA].indexOf(c))return DA;if(XA(n,t)===cA)return DA;if(JA(lA,cA,n,t))return DA;if(JA([nA,iA],BA,n,t))return DA;if(JA(AA,AA,n,t))return DA;if(o===j)return kA;if(o===lA||c===lA)return DA;if(c===rA||o===rA)return kA;if(-1!==[tA,sA,BA].indexOf(c)||o===eA)return DA;if(B===yA&&-1!==RA.indexOf(o))return DA;if(o===dA&&c===yA)return DA;if(c===oA)return DA;if(-1!==OA.indexOf(c)&&o===uA||-1!==OA.indexOf(o)&&c===uA)return DA;if(o===QA&&-1!==[HA,FA,fA].indexOf(c)||-1!==[HA,FA,fA].indexOf(o)&&c===wA)return DA;if(-1!==OA.indexOf(o)&&-1!==GA.indexOf(c)||-1!==GA.indexOf(o)&&-1!==OA.indexOf(c))return DA;if(-1!==[QA,wA].indexOf(o)&&(c===uA||-1!==[cA,sA].indexOf(c)&&t[a+1]===uA)||-1!==[cA,sA].indexOf(o)&&c===uA||o===uA&&-1!==[uA,dA,gA].indexOf(c))return DA;if(-1!==[uA,dA,gA,nA,iA].indexOf(c)){var l=n;while(l>=0){var g=t[l];if(g===uA)return DA;if(-1===[dA,gA].indexOf(g))break;l--}}if(-1!==[QA,wA].indexOf(c)){l=-1!==[nA,iA].indexOf(o)?i:n;while(l>=0){g=t[l];if(g===uA)return DA;if(-1===[dA,gA].indexOf(g))break;l--}}if(vA===o&&-1!==[vA,EA,pA,mA].indexOf(c)||-1!==[EA,pA].indexOf(o)&&-1!==[EA,IA].indexOf(c)||-1!==[IA,mA].indexOf(o)&&c===IA)return DA;if(-1!==NA.indexOf(o)&&-1!==[oA,wA].indexOf(c)||-1!==NA.indexOf(c)&&o===QA)return DA;if(-1!==OA.indexOf(o)&&-1!==OA.indexOf(c))return DA;if(o===gA&&-1!==OA.indexOf(c))return DA;if(-1!==OA.concat(uA).indexOf(o)&&c===cA&&-1===LA.indexOf(A[a])||-1!==OA.concat(uA).indexOf(c)&&o===iA)return DA;if(o===bA&&c===bA){var u=e[n],w=1;while(u>0){if(u--,t[u]!==bA)break;w++}if(w%2!==0)return DA}return o===FA&&c===fA?DA:kA},WA=function(A,t){t||(t={lineBreak:"normal",wordBreak:"normal"});var e=_A(A,t.lineBreak),s=e[0],r=e[1],n=e[2];"break-all"!==t.wordBreak&&"break-word"!==t.wordBreak||(r=r.map((function(A){return-1!==[uA,CA,xA].indexOf(A)?HA:A})));var i="keep-all"===t.wordBreak?n.map((function(t,e){return t&&A[e]>=19968&&A[e]<=40959})):void 0;return[s,r,i]},ZA=function(){function A(A,t,e,s){this.codePoints=A,this.required=t===TA,this.start=e,this.end=s}return A.prototype.slice=function(){return c.apply(void 0,this.codePoints.slice(this.start,this.end))},A}(),qA=function(A,t){var e=B(A),s=WA(e,t),r=s[0],n=s[1],i=s[2],a=e.length,o=0,c=0;return{next:function(){if(c>=a)return{done:!0,value:null};var A=DA;while(c<a&&(A=YA(e,n,r,++c,i))===DA);if(A!==DA||c===a){var t=new ZA(e,A,o,c);return o=c,{value:t,done:!1}}return{done:!0,value:null}}}},zA=1,jA=2,$A=4,At=8,tt=10,et=47,st=92,rt=9,nt=32,it=34,at=61,ot=35,Bt=36,ct=37,lt=39,gt=40,ut=41,wt=95,Qt=45,dt=33,ht=60,Ct=62,Ut=64,Ft=91,ft=93,pt=61,mt=123,yt=63,Ht=125,vt=124,Et=126,It=128,bt=65533,xt=42,Kt=43,Lt=44,Tt=58,Dt=59,kt=46,St=0,Ot=8,Mt=11,Vt=14,Gt=31,Pt=127,Nt=-1,Rt=48,_t=97,Jt=101,Xt=102,Yt=117,Wt=122,Zt=65,qt=69,zt=70,jt=85,$t=90,Ae=function(A){return A>=Rt&&A<=57},te=function(A){return A>=55296&&A<=57343},ee=function(A){return Ae(A)||A>=Zt&&A<=zt||A>=_t&&A<=Xt},se=function(A){return A>=_t&&A<=Wt},re=function(A){return A>=Zt&&A<=$t},ne=function(A){return se(A)||re(A)},ie=function(A){return A>=It},ae=function(A){return A===tt||A===rt||A===nt},oe=function(A){return ne(A)||ie(A)||A===wt},Be=function(A){return oe(A)||Ae(A)||A===Qt},ce=function(A){return A>=St&&A<=Ot||A===Mt||A>=Vt&&A<=Gt||A===Pt},le=function(A,t){return A===st&&t!==tt},ge=function(A,t,e){return A===Qt?oe(t)||le(t,e):!!oe(A)||!(A!==st||!le(A,t))},ue=function(A,t,e){return A===Kt||A===Qt?!!Ae(t)||t===kt&&Ae(e):Ae(A===kt?t:A)},we=function(A){var t=0,e=1;A[t]!==Kt&&A[t]!==Qt||(A[t]===Qt&&(e=-1),t++);var s=[];while(Ae(A[t]))s.push(A[t++]);var r=s.length?parseInt(c.apply(void 0,s),10):0;A[t]===kt&&t++;var n=[];while(Ae(A[t]))n.push(A[t++]);var i=n.length,a=i?parseInt(c.apply(void 0,n),10):0;A[t]!==qt&&A[t]!==Jt||t++;var o=1;A[t]!==Kt&&A[t]!==Qt||(A[t]===Qt&&(o=-1),t++);var B=[];while(Ae(A[t]))B.push(A[t++]);var l=B.length?parseInt(c.apply(void 0,B),10):0;return e*(r+a*Math.pow(10,-i))*Math.pow(10,o*l)},Qe={type:2},de={type:3},he={type:4},Ce={type:13},Ue={type:8},Fe={type:21},fe={type:9},pe={type:10},me={type:11},ye={type:12},He={type:14},ve={type:23},Ee={type:1},Ie={type:25},be={type:24},xe={type:26},Ke={type:27},Le={type:28},Te={type:29},De={type:31},ke={type:32},Se=function(){function A(){this._value=[]}return A.prototype.write=function(A){this._value=this._value.concat(B(A))},A.prototype.read=function(){var A=[],t=this.consumeToken();while(t!==ke)A.push(t),t=this.consumeToken();return A},A.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case it:return this.consumeStringToken(it);case ot:var t=this.peekCodePoint(0),e=this.peekCodePoint(1),s=this.peekCodePoint(2);if(Be(t)||le(e,s)){var r=ge(t,e,s)?jA:zA,n=this.consumeName();return{type:5,value:n,flags:r}}break;case Bt:if(this.peekCodePoint(0)===at)return this.consumeCodePoint(),Ce;break;case lt:return this.consumeStringToken(lt);case gt:return Qe;case ut:return de;case xt:if(this.peekCodePoint(0)===at)return this.consumeCodePoint(),He;break;case Kt:if(ue(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case Lt:return he;case Qt:var i=A,a=this.peekCodePoint(0),o=this.peekCodePoint(1);if(ue(i,a,o))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(ge(i,a,o))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(a===Qt&&o===Ct)return this.consumeCodePoint(),this.consumeCodePoint(),be;break;case kt:if(ue(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case et:if(this.peekCodePoint(0)===xt){this.consumeCodePoint();while(1){var B=this.consumeCodePoint();if(B===xt&&(B=this.consumeCodePoint(),B===et))return this.consumeToken();if(B===Nt)return this.consumeToken()}}break;case Tt:return xe;case Dt:return Ke;case ht:if(this.peekCodePoint(0)===dt&&this.peekCodePoint(1)===Qt&&this.peekCodePoint(2)===Qt)return this.consumeCodePoint(),this.consumeCodePoint(),Ie;break;case Ut:var l=this.peekCodePoint(0),g=this.peekCodePoint(1),u=this.peekCodePoint(2);if(ge(l,g,u)){n=this.consumeName();return{type:7,value:n}}break;case Ft:return Le;case st:if(le(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case ft:return Te;case pt:if(this.peekCodePoint(0)===at)return this.consumeCodePoint(),Ue;break;case mt:return me;case Ht:return ye;case Yt:case jt:var w=this.peekCodePoint(0),Q=this.peekCodePoint(1);return w!==Kt||!ee(Q)&&Q!==yt||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case vt:if(this.peekCodePoint(0)===at)return this.consumeCodePoint(),fe;if(this.peekCodePoint(0)===vt)return this.consumeCodePoint(),Fe;break;case Et:if(this.peekCodePoint(0)===at)return this.consumeCodePoint(),pe;break;case Nt:return ke}return ae(A)?(this.consumeWhiteSpace(),De):Ae(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):oe(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:c(A)}},A.prototype.consumeCodePoint=function(){var A=this._value.shift();return"undefined"===typeof A?-1:A},A.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},A.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},A.prototype.consumeUnicodeRangeToken=function(){var A=[],t=this.consumeCodePoint();while(ee(t)&&A.length<6)A.push(t),t=this.consumeCodePoint();var e=!1;while(t===yt&&A.length<6)A.push(t),t=this.consumeCodePoint(),e=!0;if(e){var s=parseInt(c.apply(void 0,A.map((function(A){return A===yt?Rt:A}))),16),r=parseInt(c.apply(void 0,A.map((function(A){return A===yt?zt:A}))),16);return{type:30,start:s,end:r}}var n=parseInt(c.apply(void 0,A),16);if(this.peekCodePoint(0)===Qt&&ee(this.peekCodePoint(1))){this.consumeCodePoint(),t=this.consumeCodePoint();var i=[];while(ee(t)&&i.length<6)i.push(t),t=this.consumeCodePoint();r=parseInt(c.apply(void 0,i),16);return{type:30,start:n,end:r}}return{type:30,start:n,end:n}},A.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&this.peekCodePoint(0)===gt?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===gt?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},A.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===Nt)return{type:22,value:""};var t=this.peekCodePoint(0);if(t===lt||t===it){var e=this.consumeStringToken(this.consumeCodePoint());return 0===e.type&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===Nt||this.peekCodePoint(0)===ut)?(this.consumeCodePoint(),{type:22,value:e.value}):(this.consumeBadUrlRemnants(),ve)}while(1){var s=this.consumeCodePoint();if(s===Nt||s===ut)return{type:22,value:c.apply(void 0,A)};if(ae(s))return this.consumeWhiteSpace(),this.peekCodePoint(0)===Nt||this.peekCodePoint(0)===ut?(this.consumeCodePoint(),{type:22,value:c.apply(void 0,A)}):(this.consumeBadUrlRemnants(),ve);if(s===it||s===lt||s===gt||ce(s))return this.consumeBadUrlRemnants(),ve;if(s===st){if(!le(s,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),ve;A.push(this.consumeEscapedCodePoint())}else A.push(s)}},A.prototype.consumeWhiteSpace=function(){while(ae(this.peekCodePoint(0)))this.consumeCodePoint()},A.prototype.consumeBadUrlRemnants=function(){while(1){var A=this.consumeCodePoint();if(A===ut||A===Nt)return;le(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},A.prototype.consumeStringSlice=function(A){var t=5e4,e="";while(A>0){var s=Math.min(t,A);e+=c.apply(void 0,this._value.splice(0,s)),A-=s}return this._value.shift(),e},A.prototype.consumeStringToken=function(A){var t="",e=0;do{var s=this._value[e];if(s===Nt||void 0===s||s===A)return t+=this.consumeStringSlice(e),{type:0,value:t};if(s===tt)return this._value.splice(0,e),Ee;if(s===st){var r=this._value[e+1];r!==Nt&&void 0!==r&&(r===tt?(t+=this.consumeStringSlice(e),e=-1,this._value.shift()):le(s,r)&&(t+=this.consumeStringSlice(e),t+=c(this.consumeEscapedCodePoint()),e=-1))}e++}while(1)},A.prototype.consumeNumber=function(){var A=[],t=$A,e=this.peekCodePoint(0);e!==Kt&&e!==Qt||A.push(this.consumeCodePoint());while(Ae(this.peekCodePoint(0)))A.push(this.consumeCodePoint());e=this.peekCodePoint(0);var s=this.peekCodePoint(1);if(e===kt&&Ae(s)){A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=At;while(Ae(this.peekCodePoint(0)))A.push(this.consumeCodePoint())}e=this.peekCodePoint(0),s=this.peekCodePoint(1);var r=this.peekCodePoint(2);if((e===qt||e===Jt)&&((s===Kt||s===Qt)&&Ae(r)||Ae(s))){A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=At;while(Ae(this.peekCodePoint(0)))A.push(this.consumeCodePoint())}return[we(A),t]},A.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),t=A[0],e=A[1],s=this.peekCodePoint(0),r=this.peekCodePoint(1),n=this.peekCodePoint(2);if(ge(s,r,n)){var i=this.consumeName();return{type:15,number:t,flags:e,unit:i}}return s===ct?(this.consumeCodePoint(),{type:16,number:t,flags:e}):{type:17,number:t,flags:e}},A.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(ee(A)){var t=c(A);while(ee(this.peekCodePoint(0))&&t.length<6)t+=c(this.consumeCodePoint());ae(this.peekCodePoint(0))&&this.consumeCodePoint();var e=parseInt(t,16);return 0===e||te(e)||e>1114111?bt:e}return A===Nt?bt:A},A.prototype.consumeName=function(){var A="";while(1){var t=this.consumeCodePoint();if(Be(t))A+=c(t);else{if(!le(t,this.peekCodePoint(0)))return this.reconsumeCodePoint(t),A;A+=c(this.consumeEscapedCodePoint())}}},A}(),Oe=function(){function A(A){this._tokens=A}return A.create=function(t){var e=new Se;return e.write(t),new A(e.read())},A.parseValue=function(t){return A.create(t).parseComponentValue()},A.parseValues=function(t){return A.create(t).parseComponentValues()},A.prototype.parseComponentValue=function(){var A=this.consumeToken();while(31===A.type)A=this.consumeToken();if(32===A.type)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var t=this.consumeComponentValue();do{A=this.consumeToken()}while(31===A.type);if(32===A.type)return t;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},A.prototype.parseComponentValues=function(){var A=[];while(1){var t=this.consumeComponentValue();if(32===t.type)return A;A.push(t),A.push()}},A.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},A.prototype.consumeSimpleBlock=function(A){var t={type:A,values:[]},e=this.consumeToken();while(1){if(32===e.type||Xe(e,A))return t;this.reconsumeToken(e),t.values.push(this.consumeComponentValue()),e=this.consumeToken()}},A.prototype.consumeFunction=function(A){var t={name:A.value,values:[],type:18};while(1){var e=this.consumeToken();if(32===e.type||3===e.type)return t;this.reconsumeToken(e),t.values.push(this.consumeComponentValue())}},A.prototype.consumeToken=function(){var A=this._tokens.shift();return"undefined"===typeof A?ke:A},A.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},A}(),Me=function(A){return 15===A.type},Ve=function(A){return 17===A.type},Ge=function(A){return 20===A.type},Pe=function(A){return 0===A.type},Ne=function(A,t){return Ge(A)&&A.value===t},Re=function(A){return 31!==A.type},_e=function(A){return 31!==A.type&&4!==A.type},Je=function(A){var t=[],e=[];return A.forEach((function(A){if(4===A.type){if(0===e.length)throw new Error("Error parsing function args, zero tokens for arg");return t.push(e),void(e=[])}31!==A.type&&e.push(A)})),e.length&&t.push(e),t},Xe=function(A,t){return 11===t&&12===A.type||(28===t&&29===A.type||2===t&&3===A.type)},Ye=function(A){return 17===A.type||15===A.type},We=function(A){return 16===A.type||Ye(A)},Ze=function(A){return A.length>1?[A[0],A[1]]:[A[0]]},qe={type:17,number:0,flags:$A},ze={type:16,number:50,flags:$A},je={type:16,number:100,flags:$A},$e=function(A,t,e){var s=A[0],r=A[1];return[As(s,t),As("undefined"!==typeof r?r:s,e)]},As=function(A,t){if(16===A.type)return A.number/100*t;if(Me(A))switch(A.unit){case"rem":case"em":return 16*A.number;case"px":default:return A.number}return A.number},ts="deg",es="grad",ss="rad",rs="turn",ns={name:"angle",parse:function(A,t){if(15===t.type)switch(t.unit){case ts:return Math.PI*t.number/180;case es:return Math.PI/200*t.number;case ss:return t.number;case rs:return 2*Math.PI*t.number}throw new Error("Unsupported angle type")}},is=function(A){return 15===A.type&&(A.unit===ts||A.unit===es||A.unit===ss||A.unit===rs)},as=function(A){var t=A.filter(Ge).map((function(A){return A.value})).join(" ");switch(t){case"to bottom right":case"to right bottom":case"left top":case"top left":return[qe,qe];case"to top":case"bottom":return os(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[qe,je];case"to right":case"left":return os(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[je,je];case"to bottom":case"top":return os(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[je,qe];case"to left":case"right":return os(270)}return 0},os=function(A){return Math.PI*A/180},Bs={name:"color",parse:function(A,t){if(18===t.type){var e=hs[t.name];if("undefined"===typeof e)throw new Error('Attempting to parse an unsupported color function "'+t.name+'"');return e(A,t.values)}if(5===t.type){if(3===t.value.length){var s=t.value.substring(0,1),r=t.value.substring(1,2),n=t.value.substring(2,3);return gs(parseInt(s+s,16),parseInt(r+r,16),parseInt(n+n,16),1)}if(4===t.value.length){s=t.value.substring(0,1),r=t.value.substring(1,2),n=t.value.substring(2,3);var i=t.value.substring(3,4);return gs(parseInt(s+s,16),parseInt(r+r,16),parseInt(n+n,16),parseInt(i+i,16)/255)}if(6===t.value.length){s=t.value.substring(0,2),r=t.value.substring(2,4),n=t.value.substring(4,6);return gs(parseInt(s,16),parseInt(r,16),parseInt(n,16),1)}if(8===t.value.length){s=t.value.substring(0,2),r=t.value.substring(2,4),n=t.value.substring(4,6),i=t.value.substring(6,8);return gs(parseInt(s,16),parseInt(r,16),parseInt(n,16),parseInt(i,16)/255)}}if(20===t.type){var a=Us[t.value.toUpperCase()];if("undefined"!==typeof a)return a}return Us.TRANSPARENT}},cs=function(A){return 0===(255&A)},ls=function(A){var t=255&A,e=255&A>>8,s=255&A>>16,r=255&A>>24;return t<255?"rgba("+r+","+s+","+e+","+t/255+")":"rgb("+r+","+s+","+e+")"},gs=function(A,t,e,s){return(A<<24|t<<16|e<<8|Math.round(255*s))>>>0},us=function(A,t){if(17===A.type)return A.number;if(16===A.type){var e=3===t?1:255;return 3===t?A.number/100*e:Math.round(A.number/100*e)}return 0},ws=function(A,t){var e=t.filter(_e);if(3===e.length){var s=e.map(us),r=s[0],n=s[1],i=s[2];return gs(r,n,i,1)}if(4===e.length){var a=e.map(us),o=(r=a[0],n=a[1],i=a[2],a[3]);return gs(r,n,i,o)}return 0};function Qs(A,t,e){return e<0&&(e+=1),e>=1&&(e-=1),e<1/6?(t-A)*e*6+A:e<.5?t:e<2/3?6*(t-A)*(2/3-e)+A:A}var ds=function(A,t){var e=t.filter(_e),s=e[0],r=e[1],n=e[2],i=e[3],a=(17===s.type?os(s.number):ns.parse(A,s))/(2*Math.PI),o=We(r)?r.number/100:0,B=We(n)?n.number/100:0,c="undefined"!==typeof i&&We(i)?As(i,1):1;if(0===o)return gs(255*B,255*B,255*B,1);var l=B<=.5?B*(o+1):B+o-B*o,g=2*B-l,u=Qs(g,l,a+1/3),w=Qs(g,l,a),Q=Qs(g,l,a-1/3);return gs(255*u,255*w,255*Q,c)},hs={hsl:ds,hsla:ds,rgb:ws,rgba:ws},Cs=function(A,t){return Bs.parse(A,Oe.create(t).parseComponentValue())},Us={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},Fs={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(A,t){return t.map((function(A){if(Ge(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},fs={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},ps=function(A,t){var e=Bs.parse(A,t[0]),s=t[1];return s&&We(s)?{color:e,stop:s}:{color:e,stop:null}},ms=function(A,t){var e=A[0],s=A[A.length-1];null===e.stop&&(e.stop=qe),null===s.stop&&(s.stop=je);for(var r=[],n=0,i=0;i<A.length;i++){var a=A[i].stop;if(null!==a){var o=As(a,t);o>n?r.push(o):r.push(n),n=o}else r.push(null)}var B=null;for(i=0;i<r.length;i++){var c=r[i];if(null===c)null===B&&(B=i);else if(null!==B){for(var l=i-B,g=r[B-1],u=(c-g)/(l+1),w=1;w<=l;w++)r[B+w-1]=u*w;B=null}}return A.map((function(A,e){var s=A.color;return{color:s,stop:Math.max(Math.min(1,r[e]/t),0)}}))},ys=function(A,t,e){var s=t/2,r=e/2,n=As(A[0],t)-s,i=r-As(A[1],e);return(Math.atan2(i,n)+2*Math.PI)%(2*Math.PI)},Hs=function(A,t,e){var s="number"===typeof A?A:ys(A,t,e),r=Math.abs(t*Math.sin(s))+Math.abs(e*Math.cos(s)),n=t/2,i=e/2,a=r/2,o=Math.sin(s-Math.PI/2)*a,B=Math.cos(s-Math.PI/2)*a;return[r,n-B,n+B,i-o,i+o]},vs=function(A,t){return Math.sqrt(A*A+t*t)},Es=function(A,t,e,s,r){var n=[[0,0],[0,t],[A,0],[A,t]];return n.reduce((function(A,t){var n=t[0],i=t[1],a=vs(e-n,s-i);return(r?a<A.optimumDistance:a>A.optimumDistance)?{optimumCorner:t,optimumDistance:a}:A}),{optimumDistance:r?1/0:-1/0,optimumCorner:null}).optimumCorner},Is=function(A,t,e,s,r){var n=0,i=0;switch(A.size){case 0:0===A.shape?n=i=Math.min(Math.abs(t),Math.abs(t-s),Math.abs(e),Math.abs(e-r)):1===A.shape&&(n=Math.min(Math.abs(t),Math.abs(t-s)),i=Math.min(Math.abs(e),Math.abs(e-r)));break;case 2:if(0===A.shape)n=i=Math.min(vs(t,e),vs(t,e-r),vs(t-s,e),vs(t-s,e-r));else if(1===A.shape){var a=Math.min(Math.abs(e),Math.abs(e-r))/Math.min(Math.abs(t),Math.abs(t-s)),o=Es(s,r,t,e,!0),B=o[0],c=o[1];n=vs(B-t,(c-e)/a),i=a*n}break;case 1:0===A.shape?n=i=Math.max(Math.abs(t),Math.abs(t-s),Math.abs(e),Math.abs(e-r)):1===A.shape&&(n=Math.max(Math.abs(t),Math.abs(t-s)),i=Math.max(Math.abs(e),Math.abs(e-r)));break;case 3:if(0===A.shape)n=i=Math.max(vs(t,e),vs(t,e-r),vs(t-s,e),vs(t-s,e-r));else if(1===A.shape){a=Math.max(Math.abs(e),Math.abs(e-r))/Math.max(Math.abs(t),Math.abs(t-s));var l=Es(s,r,t,e,!1);B=l[0],c=l[1];n=vs(B-t,(c-e)/a),i=a*n}break}return Array.isArray(A.size)&&(n=As(A.size[0],s),i=2===A.size.length?As(A.size[1],r):n),[n,i]},bs=function(A,t){var e=os(180),s=[];return Je(t).forEach((function(t,r){if(0===r){var n=t[0];if(20===n.type&&"to"===n.value)return void(e=as(t));if(is(n))return void(e=ns.parse(A,n))}var i=ps(A,t);s.push(i)})),{angle:e,stops:s,type:1}},xs=function(A,t){var e=os(180),s=[];return Je(t).forEach((function(t,r){if(0===r){var n=t[0];if(20===n.type&&-1!==["top","left","right","bottom"].indexOf(n.value))return void(e=as(t));if(is(n))return void(e=(ns.parse(A,n)+os(270))%os(360))}var i=ps(A,t);s.push(i)})),{angle:e,stops:s,type:1}},Ks=function(A,t){var e=os(180),s=[],r=1,n=0,i=3,a=[];return Je(t).forEach((function(t,e){var n=t[0];if(0===e){if(Ge(n)&&"linear"===n.value)return void(r=1);if(Ge(n)&&"radial"===n.value)return void(r=2)}if(18===n.type)if("from"===n.name){var i=Bs.parse(A,n.values[0]);s.push({stop:qe,color:i})}else if("to"===n.name){i=Bs.parse(A,n.values[0]);s.push({stop:je,color:i})}else if("color-stop"===n.name){var a=n.values.filter(_e);if(2===a.length){i=Bs.parse(A,a[1]);var o=a[0];Ve(o)&&s.push({stop:{type:16,number:100*o.number,flags:o.flags},color:i})}}})),1===r?{angle:(e+os(180))%os(360),stops:s,type:r}:{size:i,shape:n,stops:s,position:a,type:r}},Ls="closest-side",Ts="farthest-side",Ds="closest-corner",ks="farthest-corner",Ss="circle",Os="ellipse",Ms="cover",Vs="contain",Gs=function(A,t){var e=0,s=3,r=[],n=[];return Je(t).forEach((function(t,i){var a=!0;if(0===i){var o=!1;a=t.reduce((function(A,t){if(o)if(Ge(t))switch(t.value){case"center":return n.push(ze),A;case"top":case"left":return n.push(qe),A;case"right":case"bottom":return n.push(je),A}else(We(t)||Ye(t))&&n.push(t);else if(Ge(t))switch(t.value){case Ss:return e=0,!1;case Os:return e=1,!1;case"at":return o=!0,!1;case Ls:return s=0,!1;case Ms:case Ts:return s=1,!1;case Vs:case Ds:return s=2,!1;case ks:return s=3,!1}else if(Ye(t)||We(t))return Array.isArray(s)||(s=[]),s.push(t),!1;return A}),a)}if(a){var B=ps(A,t);r.push(B)}})),{size:s,shape:e,stops:r,position:n,type:2}},Ps=function(A,t){var e=0,s=3,r=[],n=[];return Je(t).forEach((function(t,i){var a=!0;if(0===i?a=t.reduce((function(A,t){if(Ge(t))switch(t.value){case"center":return n.push(ze),!1;case"top":case"left":return n.push(qe),!1;case"right":case"bottom":return n.push(je),!1}else if(We(t)||Ye(t))return n.push(t),!1;return A}),a):1===i&&(a=t.reduce((function(A,t){if(Ge(t))switch(t.value){case Ss:return e=0,!1;case Os:return e=1,!1;case Vs:case Ls:return s=0,!1;case Ts:return s=1,!1;case Ds:return s=2,!1;case Ms:case ks:return s=3,!1}else if(Ye(t)||We(t))return Array.isArray(s)||(s=[]),s.push(t),!1;return A}),a)),a){var o=ps(A,t);r.push(o)}})),{size:s,shape:e,stops:r,position:n,type:2}},Ns=function(A){return 1===A.type},Rs=function(A){return 2===A.type},_s={name:"image",parse:function(A,t){if(22===t.type){var e={url:t.value,type:0};return A.cache.addImage(t.value),e}if(18===t.type){var s=Ys[t.name];if("undefined"===typeof s)throw new Error('Attempting to parse an unsupported image function "'+t.name+'"');return s(A,t.values)}throw new Error("Unsupported image type "+t.type)}};function Js(A){return!(20===A.type&&"none"===A.value)&&(18!==A.type||!!Ys[A.name])}var Xs,Ys={"linear-gradient":bs,"-moz-linear-gradient":xs,"-ms-linear-gradient":xs,"-o-linear-gradient":xs,"-webkit-linear-gradient":xs,"radial-gradient":Gs,"-moz-radial-gradient":Ps,"-ms-radial-gradient":Ps,"-o-radial-gradient":Ps,"-webkit-radial-gradient":Ps,"-webkit-gradient":Ks},Ws={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(A,t){if(0===t.length)return[];var e=t[0];return 20===e.type&&"none"===e.value?[]:t.filter((function(A){return _e(A)&&Js(A)})).map((function(t){return _s.parse(A,t)}))}},Zs={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(A,t){return t.map((function(A){if(Ge(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},qs={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(A,t){return Je(t).map((function(A){return A.filter(We)})).map(Ze)}},zs={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(A,t){return Je(t).map((function(A){return A.filter(Ge).map((function(A){return A.value})).join(" ")})).map(js)}},js=function(A){switch(A){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}};(function(A){A["AUTO"]="auto",A["CONTAIN"]="contain",A["COVER"]="cover"})(Xs||(Xs={}));var $s,Ar={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(A,t){return Je(t).map((function(A){return A.filter(tr)}))}},tr=function(A){return Ge(A)||We(A)},er=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},sr=er("top"),rr=er("right"),nr=er("bottom"),ir=er("left"),ar=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:1,parse:function(A,t){return Ze(t.filter(We))}}},or=ar("top-left"),Br=ar("top-right"),cr=ar("bottom-right"),lr=ar("bottom-left"),gr=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,t){switch(t){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},ur=gr("top"),wr=gr("right"),Qr=gr("bottom"),dr=gr("left"),hr=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,t){return Me(t)?t.number:0}}},Cr=hr("top"),Ur=hr("right"),Fr=hr("bottom"),fr=hr("left"),pr={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},mr={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(A,t){switch(t){case"rtl":return 1;case"ltr":default:return 0}}},yr={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(A,t){return t.filter(Ge).reduce((function(A,t){return A|Hr(t.value)}),0)}},Hr=function(A){switch(A){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},vr={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(A,t){switch(t){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},Er={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(A,t){return 20===t.type&&"normal"===t.value?0:17===t.type||15===t.type?t.number:0}};(function(A){A["NORMAL"]="normal",A["STRICT"]="strict"})($s||($s={}));var Ir,br={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,t){switch(t){case"strict":return $s.STRICT;case"normal":default:return $s.NORMAL}}},xr={name:"line-height",initialValue:"normal",prefix:!1,type:4},Kr=function(A,t){return Ge(A)&&"normal"===A.value?1.2*t:17===A.type?t*A.number:We(A)?As(A,t):t},Lr={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(A,t){return 20===t.type&&"none"===t.value?null:_s.parse(A,t)}},Tr={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(A,t){switch(t){case"inside":return 0;case"outside":default:return 1}}},Dr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(A,t){switch(t){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},kr=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:4}},Sr=kr("top"),Or=kr("right"),Mr=kr("bottom"),Vr=kr("left"),Gr={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(A,t){return t.filter(Ge).map((function(A){switch(A.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}}))}},Pr={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(A,t){switch(t){case"break-word":return"break-word";case"normal":default:return"normal"}}},Nr=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Rr=Nr("top"),_r=Nr("right"),Jr=Nr("bottom"),Xr=Nr("left"),Yr={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(A,t){switch(t){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},Wr={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(A,t){switch(t){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},Zr={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,t){return 1===t.length&&Ne(t[0],"none")?[]:Je(t).map((function(t){for(var e={color:Us.TRANSPARENT,offsetX:qe,offsetY:qe,blur:qe},s=0,r=0;r<t.length;r++){var n=t[r];Ye(n)?(0===s?e.offsetX=n:1===s?e.offsetY=n:e.blur=n,s++):e.color=Bs.parse(A,n)}return e}))}},qr={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(A,t){switch(t){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},zr={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(A,t){if(20===t.type&&"none"===t.value)return null;if(18===t.type){var e=An[t.name];if("undefined"===typeof e)throw new Error('Attempting to parse an unsupported transform function "'+t.name+'"');return e(t.values)}return null}},jr=function(A){var t=A.filter((function(A){return 17===A.type})).map((function(A){return A.number}));return 6===t.length?t:null},$r=function(A){var t=A.filter((function(A){return 17===A.type})).map((function(A){return A.number})),e=t[0],s=t[1];t[2],t[3];var r=t[4],n=t[5];t[6],t[7],t[8],t[9],t[10],t[11];var i=t[12],a=t[13];return t[14],t[15],16===t.length?[e,s,r,n,i,a]:null},An={matrix:jr,matrix3d:$r},tn={type:16,number:50,flags:$A},en=[tn,tn],sn={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(A,t){var e=t.filter(We);return 2!==e.length?en:[e[0],e[1]]}},rn={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(A,t){switch(t){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}};(function(A){A["NORMAL"]="normal",A["BREAK_ALL"]="break-all",A["KEEP_ALL"]="keep-all"})(Ir||(Ir={}));for(var nn={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,t){switch(t){case"break-all":return Ir.BREAK_ALL;case"keep-all":return Ir.KEEP_ALL;case"normal":default:return Ir.NORMAL}}},an={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(A,t){if(20===t.type)return{auto:!0,order:0};if(Ve(t))return{auto:!1,order:t.number};throw new Error("Invalid z-index number parsed")}},on={name:"time",parse:function(A,t){if(15===t.type)switch(t.unit.toLowerCase()){case"s":return 1e3*t.number;case"ms":return t.number}throw new Error("Unsupported time type")}},Bn={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(A,t){return Ve(t)?t.number:1}},cn={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},ln={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(A,t){return t.filter(Ge).map((function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0})).filter((function(A){return 0!==A}))}},gn={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(A,t){var e=[],s=[];return t.forEach((function(A){switch(A.type){case 20:case 0:e.push(A.value);break;case 17:e.push(A.number.toString());break;case 4:s.push(e.join(" ")),e.length=0;break}})),e.length&&s.push(e.join(" ")),s.map((function(A){return-1===A.indexOf(" ")?A:"'"+A+"'"}))}},un={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},wn={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(A,t){if(Ve(t))return t.number;if(Ge(t))switch(t.value){case"bold":return 700;case"normal":default:return 400}return 400}},Qn={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(A,t){return t.filter(Ge).map((function(A){return A.value}))}},dn={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(A,t){switch(t){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},hn=function(A,t){return 0!==(A&t)},Cn={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(A,t){if(0===t.length)return[];var e=t[0];return 20===e.type&&"none"===e.value?[]:t}},Un={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(A,t){if(0===t.length)return null;var e=t[0];if(20===e.type&&"none"===e.value)return null;for(var s=[],r=t.filter(Re),n=0;n<r.length;n++){var i=r[n],a=r[n+1];if(20===i.type){var o=a&&Ve(a)?a.number:1;s.push({counter:i.value,increment:o})}}return s}},Fn={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(A,t){if(0===t.length)return[];for(var e=[],s=t.filter(Re),r=0;r<s.length;r++){var n=s[r],i=s[r+1];if(Ge(n)&&"none"!==n.value){var a=i&&Ve(i)?i.number:0;e.push({counter:n.value,reset:a})}}return e}},fn={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(A,t){return t.filter(Me).map((function(t){return on.parse(A,t)}))}},pn={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(A,t){if(0===t.length)return null;var e=t[0];if(20===e.type&&"none"===e.value)return null;var s=[],r=t.filter(Pe);if(r.length%2!==0)return null;for(var n=0;n<r.length;n+=2){var i=r[n].value,a=r[n+1].value;s.push({open:i,close:a})}return s}},mn=function(A,t,e){if(!A)return"";var s=A[Math.min(t,A.length-1)];return s?e?s.open:s.close:""},yn={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,t){return 1===t.length&&Ne(t[0],"none")?[]:Je(t).map((function(t){for(var e={color:255,offsetX:qe,offsetY:qe,blur:qe,spread:qe,inset:!1},s=0,r=0;r<t.length;r++){var n=t[r];Ne(n,"inset")?e.inset=!0:Ye(n)?(0===s?e.offsetX=n:1===s?e.offsetY=n:2===s?e.blur=n:e.spread=n,s++):e.color=Bs.parse(A,n)}return e}))}},Hn={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(A,t){var e=[0,1,2],s=[];return t.filter(Ge).forEach((function(A){switch(A.value){case"stroke":s.push(1);break;case"fill":s.push(0);break;case"markers":s.push(2);break}})),e.forEach((function(A){-1===s.indexOf(A)&&s.push(A)})),s}},vn={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},En={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(A,t){return Me(t)?t.number:0}},In=function(){function A(A,t){var e,s;this.animationDuration=Kn(A,fn,t.animationDuration),this.backgroundClip=Kn(A,Fs,t.backgroundClip),this.backgroundColor=Kn(A,fs,t.backgroundColor),this.backgroundImage=Kn(A,Ws,t.backgroundImage),this.backgroundOrigin=Kn(A,Zs,t.backgroundOrigin),this.backgroundPosition=Kn(A,qs,t.backgroundPosition),this.backgroundRepeat=Kn(A,zs,t.backgroundRepeat),this.backgroundSize=Kn(A,Ar,t.backgroundSize),this.borderTopColor=Kn(A,sr,t.borderTopColor),this.borderRightColor=Kn(A,rr,t.borderRightColor),this.borderBottomColor=Kn(A,nr,t.borderBottomColor),this.borderLeftColor=Kn(A,ir,t.borderLeftColor),this.borderTopLeftRadius=Kn(A,or,t.borderTopLeftRadius),this.borderTopRightRadius=Kn(A,Br,t.borderTopRightRadius),this.borderBottomRightRadius=Kn(A,cr,t.borderBottomRightRadius),this.borderBottomLeftRadius=Kn(A,lr,t.borderBottomLeftRadius),this.borderTopStyle=Kn(A,ur,t.borderTopStyle),this.borderRightStyle=Kn(A,wr,t.borderRightStyle),this.borderBottomStyle=Kn(A,Qr,t.borderBottomStyle),this.borderLeftStyle=Kn(A,dr,t.borderLeftStyle),this.borderTopWidth=Kn(A,Cr,t.borderTopWidth),this.borderRightWidth=Kn(A,Ur,t.borderRightWidth),this.borderBottomWidth=Kn(A,Fr,t.borderBottomWidth),this.borderLeftWidth=Kn(A,fr,t.borderLeftWidth),this.boxShadow=Kn(A,yn,t.boxShadow),this.color=Kn(A,pr,t.color),this.direction=Kn(A,mr,t.direction),this.display=Kn(A,yr,t.display),this.float=Kn(A,vr,t.cssFloat),this.fontFamily=Kn(A,gn,t.fontFamily),this.fontSize=Kn(A,un,t.fontSize),this.fontStyle=Kn(A,dn,t.fontStyle),this.fontVariant=Kn(A,Qn,t.fontVariant),this.fontWeight=Kn(A,wn,t.fontWeight),this.letterSpacing=Kn(A,Er,t.letterSpacing),this.lineBreak=Kn(A,br,t.lineBreak),this.lineHeight=Kn(A,xr,t.lineHeight),this.listStyleImage=Kn(A,Lr,t.listStyleImage),this.listStylePosition=Kn(A,Tr,t.listStylePosition),this.listStyleType=Kn(A,Dr,t.listStyleType),this.marginTop=Kn(A,Sr,t.marginTop),this.marginRight=Kn(A,Or,t.marginRight),this.marginBottom=Kn(A,Mr,t.marginBottom),this.marginLeft=Kn(A,Vr,t.marginLeft),this.opacity=Kn(A,Bn,t.opacity);var r=Kn(A,Gr,t.overflow);this.overflowX=r[0],this.overflowY=r[r.length>1?1:0],this.overflowWrap=Kn(A,Pr,t.overflowWrap),this.paddingTop=Kn(A,Rr,t.paddingTop),this.paddingRight=Kn(A,_r,t.paddingRight),this.paddingBottom=Kn(A,Jr,t.paddingBottom),this.paddingLeft=Kn(A,Xr,t.paddingLeft),this.paintOrder=Kn(A,Hn,t.paintOrder),this.position=Kn(A,Wr,t.position),this.textAlign=Kn(A,Yr,t.textAlign),this.textDecorationColor=Kn(A,cn,null!==(e=t.textDecorationColor)&&void 0!==e?e:t.color),this.textDecorationLine=Kn(A,ln,null!==(s=t.textDecorationLine)&&void 0!==s?s:t.textDecoration),this.textShadow=Kn(A,Zr,t.textShadow),this.textTransform=Kn(A,qr,t.textTransform),this.transform=Kn(A,zr,t.transform),this.transformOrigin=Kn(A,sn,t.transformOrigin),this.visibility=Kn(A,rn,t.visibility),this.webkitTextStrokeColor=Kn(A,vn,t.webkitTextStrokeColor),this.webkitTextStrokeWidth=Kn(A,En,t.webkitTextStrokeWidth),this.wordBreak=Kn(A,nn,t.wordBreak),this.zIndex=Kn(A,an,t.zIndex)}return A.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&0===this.visibility},A.prototype.isTransparent=function(){return cs(this.backgroundColor)},A.prototype.isTransformed=function(){return null!==this.transform},A.prototype.isPositioned=function(){return 0!==this.position},A.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},A.prototype.isFloating=function(){return 0!==this.float},A.prototype.isInlineLevel=function(){return hn(this.display,4)||hn(this.display,33554432)||hn(this.display,268435456)||hn(this.display,536870912)||hn(this.display,67108864)||hn(this.display,134217728)},A}(),bn=function(){function A(A,t){this.content=Kn(A,Cn,t.content),this.quotes=Kn(A,pn,t.quotes)}return A}(),xn=function(){function A(A,t){this.counterIncrement=Kn(A,Un,t.counterIncrement),this.counterReset=Kn(A,Fn,t.counterReset)}return A}(),Kn=function(A,t,e){var s=new Se,r=null!==e&&"undefined"!==typeof e?e.toString():t.initialValue;s.write(r);var n=new Oe(s.read());switch(t.type){case 2:var i=n.parseComponentValue();return t.parse(A,Ge(i)?i.value:t.initialValue);case 0:return t.parse(A,n.parseComponentValue());case 1:return t.parse(A,n.parseComponentValues());case 4:return n.parseComponentValue();case 3:switch(t.format){case"angle":return ns.parse(A,n.parseComponentValue());case"color":return Bs.parse(A,n.parseComponentValue());case"image":return _s.parse(A,n.parseComponentValue());case"length":var a=n.parseComponentValue();return Ye(a)?a:qe;case"length-percentage":var o=n.parseComponentValue();return We(o)?o:qe;case"time":return on.parse(A,n.parseComponentValue())}break}},Ln="data-html2canvas-debug",Tn=function(A){var t=A.getAttribute(Ln);switch(t){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},Dn=function(A,t){var e=Tn(A);return 1===e||t===e},kn=function(){function A(A,t){this.context=A,this.textNodes=[],this.elements=[],this.flags=0,Dn(t,3),this.styles=new In(A,window.getComputedStyle(t,null)),ba(t)&&(this.styles.animationDuration.some((function(A){return A>0}))&&(t.style.animationDuration="0s"),null!==this.styles.transform&&(t.style.transform="none")),this.bounds=a(this.context,t),Dn(t,4)&&(this.flags|=16)}return A}(),Sn="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",On="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Mn="undefined"===typeof Uint8Array?[]:new Uint8Array(256),Vn=0;Vn<On.length;Vn++)Mn[On.charCodeAt(Vn)]=Vn;for(var Gn=function(A){var t,e,s,r,n,i=.75*A.length,a=A.length,o=0;"="===A[A.length-1]&&(i--,"="===A[A.length-2]&&i--);var B="undefined"!==typeof ArrayBuffer&&"undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint8Array.prototype.slice?new ArrayBuffer(i):new Array(i),c=Array.isArray(B)?B:new Uint8Array(B);for(t=0;t<a;t+=4)e=Mn[A.charCodeAt(t)],s=Mn[A.charCodeAt(t+1)],r=Mn[A.charCodeAt(t+2)],n=Mn[A.charCodeAt(t+3)],c[o++]=e<<2|s>>4,c[o++]=(15&s)<<4|r>>2,c[o++]=(3&r)<<6|63&n;return B},Pn=function(A){for(var t=A.length,e=[],s=0;s<t;s+=2)e.push(A[s+1]<<8|A[s]);return e},Nn=function(A){for(var t=A.length,e=[],s=0;s<t;s+=4)e.push(A[s+3]<<24|A[s+2]<<16|A[s+1]<<8|A[s]);return e},Rn=5,_n=11,Jn=2,Xn=_n-Rn,Yn=65536>>Rn,Wn=1<<Rn,Zn=Wn-1,qn=1024>>Rn,zn=Yn+qn,jn=zn,$n=32,Ai=jn+$n,ti=65536>>_n,ei=1<<Xn,si=ei-1,ri=function(A,t,e){return A.slice?A.slice(t,e):new Uint16Array(Array.prototype.slice.call(A,t,e))},ni=function(A,t,e){return A.slice?A.slice(t,e):new Uint32Array(Array.prototype.slice.call(A,t,e))},ii=function(A,t){var e=Gn(A),s=Array.isArray(e)?Nn(e):new Uint32Array(e),r=Array.isArray(e)?Pn(e):new Uint16Array(e),n=24,i=ri(r,n/2,s[4]/2),a=2===s[5]?ri(r,(n+s[4])/2):ni(s,Math.ceil((n+s[4])/4));return new ai(s[0],s[1],s[2],s[3],i,a)},ai=function(){function A(A,t,e,s,r,n){this.initialValue=A,this.errorValue=t,this.highStart=e,this.highValueIndex=s,this.index=r,this.data=n}return A.prototype.get=function(A){var t;if(A>=0){if(A<55296||A>56319&&A<=65535)return t=this.index[A>>Rn],t=(t<<Jn)+(A&Zn),this.data[t];if(A<=65535)return t=this.index[Yn+(A-55296>>Rn)],t=(t<<Jn)+(A&Zn),this.data[t];if(A<this.highStart)return t=Ai-ti+(A>>_n),t=this.index[t],t+=A>>Rn&si,t=this.index[t],t=(t<<Jn)+(A&Zn),this.data[t];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),oi="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Bi="undefined"===typeof Uint8Array?[]:new Uint8Array(256),ci=0;ci<oi.length;ci++)Bi[oi.charCodeAt(ci)]=ci;var li,gi=1,ui=2,wi=3,Qi=4,di=5,hi=7,Ci=8,Ui=9,Fi=10,fi=11,pi=12,mi=13,yi=14,Hi=15,vi=function(A){var t=[],e=0,s=A.length;while(e<s){var r=A.charCodeAt(e++);if(r>=55296&&r<=56319&&e<s){var n=A.charCodeAt(e++);56320===(64512&n)?t.push(((1023&r)<<10)+(1023&n)+65536):(t.push(r),e--)}else t.push(r)}return t},Ei=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var e=A.length;if(!e)return"";var s=[],r=-1,n="";while(++r<e){var i=A[r];i<=65535?s.push(i):(i-=65536,s.push(55296+(i>>10),i%1024+56320)),(r+1===e||s.length>16384)&&(n+=String.fromCharCode.apply(String,s),s.length=0)}return n},Ii=ii(Sn),bi="×",xi="÷",Ki=function(A){return Ii.get(A)},Li=function(A,t,e){var s=e-2,r=t[s],n=t[e-1],i=t[e];if(n===ui&&i===wi)return bi;if(n===ui||n===wi||n===Qi)return xi;if(i===ui||i===wi||i===Qi)return xi;if(n===Ci&&-1!==[Ci,Ui,fi,pi].indexOf(i))return bi;if((n===fi||n===Ui)&&(i===Ui||i===Fi))return bi;if((n===pi||n===Fi)&&i===Fi)return bi;if(i===mi||i===di)return bi;if(i===hi)return bi;if(n===gi)return bi;if(n===mi&&i===yi){while(r===di)r=t[--s];if(r===yi)return bi}if(n===Hi&&i===Hi){var a=0;while(r===Hi)a++,r=t[--s];if(a%2===0)return bi}return xi},Ti=function(A){var t=vi(A),e=t.length,s=0,r=0,n=t.map(Ki);return{next:function(){if(s>=e)return{done:!0,value:null};var A=bi;while(s<e&&(A=Li(t,n,++s))===bi);if(A!==bi||s===e){var i=Ei.apply(null,t.slice(r,s));return r=s,{value:i,done:!1}}return{done:!0,value:null}}}},Di=function(A){var t,e=Ti(A),s=[];while(!(t=e.next()).done)t.value&&s.push(t.value.slice());return s},ki=function(A){var t=123;if(A.createRange){var e=A.createRange();if(e.getBoundingClientRect){var s=A.createElement("boundtest");s.style.height=t+"px",s.style.display="block",A.body.appendChild(s),e.selectNode(s);var r=e.getBoundingClientRect(),n=Math.round(r.height);if(A.body.removeChild(s),n===t)return!0}}return!1},Si=function(A){var t=A.createElement("boundtest");t.style.width="50px",t.style.display="block",t.style.fontSize="12px",t.style.letterSpacing="0px",t.style.wordSpacing="0px",A.body.appendChild(t);var e=A.createRange();t.innerHTML="function"===typeof"".repeat?"&#128104;".repeat(10):"";var s=t.firstChild,r=B(s.data).map((function(A){return c(A)})),n=0,i={},a=r.every((function(A,t){e.setStart(s,n),e.setEnd(s,n+A.length);var r=e.getBoundingClientRect();n+=A.length;var a=r.x>i.x||r.y>i.y;return i=r,0===t||a}));return A.body.removeChild(t),a},Oi=function(){return"undefined"!==typeof(new Image).crossOrigin},Mi=function(){return"string"===typeof(new XMLHttpRequest).responseType},Vi=function(A){var t=new Image,e=A.createElement("canvas"),s=e.getContext("2d");if(!s)return!1;t.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{s.drawImage(t,0,0),e.toDataURL()}catch(Jt){return!1}return!0},Gi=function(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]},Pi=function(A){var t=A.createElement("canvas"),e=100;t.width=e,t.height=e;var s=t.getContext("2d");if(!s)return Promise.reject(!1);s.fillStyle="rgb(0, 255, 0)",s.fillRect(0,0,e,e);var r=new Image,n=t.toDataURL();r.src=n;var i=Ni(e,e,0,0,r);return s.fillStyle="red",s.fillRect(0,0,e,e),Ri(i).then((function(t){s.drawImage(t,0,0);var r=s.getImageData(0,0,e,e).data;s.fillStyle="red",s.fillRect(0,0,e,e);var i=A.createElement("div");return i.style.backgroundImage="url("+n+")",i.style.height=e+"px",Gi(r)?Ri(Ni(e,e,0,0,i)):Promise.reject(!1)})).then((function(A){return s.drawImage(A,0,0),Gi(s.getImageData(0,0,e,e).data)})).catch((function(){return!1}))},Ni=function(A,t,e,s,r){var n="http://www.w3.org/2000/svg",i=document.createElementNS(n,"svg"),a=document.createElementNS(n,"foreignObject");return i.setAttributeNS(null,"width",A.toString()),i.setAttributeNS(null,"height",t.toString()),a.setAttributeNS(null,"width","100%"),a.setAttributeNS(null,"height","100%"),a.setAttributeNS(null,"x",e.toString()),a.setAttributeNS(null,"y",s.toString()),a.setAttributeNS(null,"externalResourcesRequired","true"),i.appendChild(a),a.appendChild(r),i},Ri=function(A){return new Promise((function(t,e){var s=new Image;s.onload=function(){return t(s)},s.onerror=e,s.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},_i={get SUPPORT_RANGE_BOUNDS(){var A=ki(document);return Object.defineProperty(_i,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_WORD_BREAKING(){var A=_i.SUPPORT_RANGE_BOUNDS&&Si(document);return Object.defineProperty(_i,"SUPPORT_WORD_BREAKING",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=Vi(document);return Object.defineProperty(_i,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"===typeof Array.from&&"function"===typeof window.fetch?Pi(document):Promise.resolve(!1);return Object.defineProperty(_i,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=Oi();return Object.defineProperty(_i,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A=Mi();return Object.defineProperty(_i,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(_i,"SUPPORT_CORS_XHR",{value:A}),A},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var A=!("undefined"===typeof Intl||!Intl.Segmenter);return Object.defineProperty(_i,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:A}),A}},Ji=function(){function A(A,t){this.text=A,this.bounds=t}return A}(),Xi=function(A,t,e,s){var r=zi(t,e),n=[],a=0;return r.forEach((function(t){if(e.textDecorationLine.length||t.trim().length>0)if(_i.SUPPORT_RANGE_BOUNDS){var r=Wi(s,a,t.length).getClientRects();if(r.length>1){var o=Zi(t),B=0;o.forEach((function(t){n.push(new Ji(t,i.fromDOMRectList(A,Wi(s,B+a,t.length).getClientRects()))),B+=t.length}))}else n.push(new Ji(t,i.fromDOMRectList(A,r)))}else{var c=s.splitText(t.length);n.push(new Ji(t,Yi(A,s))),s=c}else _i.SUPPORT_RANGE_BOUNDS||(s=s.splitText(t.length));a+=t.length})),n},Yi=function(A,t){var e=t.ownerDocument;if(e){var s=e.createElement("html2canvaswrapper");s.appendChild(t.cloneNode(!0));var r=t.parentNode;if(r){r.replaceChild(s,t);var n=a(A,s);return s.firstChild&&r.replaceChild(s.firstChild,s),n}}return i.EMPTY},Wi=function(A,t,e){var s=A.ownerDocument;if(!s)throw new Error("Node has no owner document");var r=s.createRange();return r.setStart(A,t),r.setEnd(A,t+e),r},Zi=function(A){if(_i.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(t.segment(A)).map((function(A){return A.segment}))}return Di(A)},qi=function(A,t){if(_i.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(e.segment(A)).map((function(A){return A.segment}))}return $i(A,t)},zi=function(A,t){return 0!==t.letterSpacing?Zi(A):qi(A,t)},ji=[32,160,4961,65792,65793,4153,4241],$i=function(A,t){var e,s=qA(A,{lineBreak:t.lineBreak,wordBreak:"break-word"===t.overflowWrap?"break-word":t.wordBreak}),r=[],n=function(){if(e.value){var A=e.value.slice(),t=B(A),s="";t.forEach((function(A){-1===ji.indexOf(A)?s+=c(A):(s.length&&r.push(s),r.push(c(A)),s="")})),s.length&&r.push(s)}};while(!(e=s.next()).done)n();return r},Aa=function(){function A(A,t,e){this.text=ta(t.data,e.textTransform),this.textBounds=Xi(A,this.text,e,t)}return A}(),ta=function(A,t){switch(t){case 1:return A.toLowerCase();case 3:return A.replace(ea,sa);case 2:return A.toUpperCase();default:return A}},ea=/(^|\s|:|-|\(|\))([a-z])/g,sa=function(A,t,e){return A.length>0?t+e.toUpperCase():A},ra=function(A){function e(t,e){var s=A.call(this,t,e)||this;return s.src=e.currentSrc||e.src,s.intrinsicWidth=e.naturalWidth,s.intrinsicHeight=e.naturalHeight,s.context.cache.addImage(s.src),s}return t(e,A),e}(kn),na=function(A){function e(t,e){var s=A.call(this,t,e)||this;return s.canvas=e,s.intrinsicWidth=e.width,s.intrinsicHeight=e.height,s}return t(e,A),e}(kn),ia=function(A){function e(t,e){var s=A.call(this,t,e)||this,r=new XMLSerializer,n=a(t,e);return e.setAttribute("width",n.width+"px"),e.setAttribute("height",n.height+"px"),s.svg="data:image/svg+xml,"+encodeURIComponent(r.serializeToString(e)),s.intrinsicWidth=e.width.baseVal.value,s.intrinsicHeight=e.height.baseVal.value,s.context.cache.addImage(s.svg),s}return t(e,A),e}(kn),aa=function(A){function e(t,e){var s=A.call(this,t,e)||this;return s.value=e.value,s}return t(e,A),e}(kn),oa=function(A){function e(t,e){var s=A.call(this,t,e)||this;return s.start=e.start,s.reversed="boolean"===typeof e.reversed&&!0===e.reversed,s}return t(e,A),e}(kn),Ba=[{type:15,flags:0,unit:"px",number:3}],ca=[{type:16,flags:0,number:50}],la=function(A){return A.width>A.height?new i(A.left+(A.width-A.height)/2,A.top,A.height,A.height):A.width<A.height?new i(A.left,A.top+(A.height-A.width)/2,A.width,A.width):A},ga=function(A){var t=A.type===Qa?new Array(A.value.length+1).join("•"):A.value;return 0===t.length?A.placeholder||"":t},ua="checkbox",wa="radio",Qa="password",da=707406591,ha=function(A){function e(t,e){var s=A.call(this,t,e)||this;switch(s.type=e.type.toLowerCase(),s.checked=e.checked,s.value=ga(e),s.type!==ua&&s.type!==wa||(s.styles.backgroundColor=3739148031,s.styles.borderTopColor=s.styles.borderRightColor=s.styles.borderBottomColor=s.styles.borderLeftColor=2779096575,s.styles.borderTopWidth=s.styles.borderRightWidth=s.styles.borderBottomWidth=s.styles.borderLeftWidth=1,s.styles.borderTopStyle=s.styles.borderRightStyle=s.styles.borderBottomStyle=s.styles.borderLeftStyle=1,s.styles.backgroundClip=[0],s.styles.backgroundOrigin=[0],s.bounds=la(s.bounds)),s.type){case ua:s.styles.borderTopRightRadius=s.styles.borderTopLeftRadius=s.styles.borderBottomRightRadius=s.styles.borderBottomLeftRadius=Ba;break;case wa:s.styles.borderTopRightRadius=s.styles.borderTopLeftRadius=s.styles.borderBottomRightRadius=s.styles.borderBottomLeftRadius=ca;break}return s}return t(e,A),e}(kn),Ca=function(A){function e(t,e){var s=A.call(this,t,e)||this,r=e.options[e.selectedIndex||0];return s.value=r&&r.text||"",s}return t(e,A),e}(kn),Ua=function(A){function e(t,e){var s=A.call(this,t,e)||this;return s.value=e.value,s}return t(e,A),e}(kn),Fa=function(A){function e(t,e){var s=A.call(this,t,e)||this;s.src=e.src,s.width=parseInt(e.width,10)||0,s.height=parseInt(e.height,10)||0,s.backgroundColor=s.styles.backgroundColor;try{if(e.contentWindow&&e.contentWindow.document&&e.contentWindow.document.documentElement){s.tree=ya(t,e.contentWindow.document.documentElement);var r=e.contentWindow.document.documentElement?Cs(t,getComputedStyle(e.contentWindow.document.documentElement).backgroundColor):Us.TRANSPARENT,n=e.contentWindow.document.body?Cs(t,getComputedStyle(e.contentWindow.document.body).backgroundColor):Us.TRANSPARENT;s.backgroundColor=cs(r)?cs(n)?s.styles.backgroundColor:n:r}}catch(Jt){}return s}return t(e,A),e}(kn),fa=["OL","UL","MENU"],pa=function(A,t,e,s){for(var r=t.firstChild,n=void 0;r;r=n)if(n=r.nextSibling,Ea(r)&&r.data.trim().length>0)e.textNodes.push(new Aa(A,r,e.styles));else if(Ia(r))if(Ja(r)&&r.assignedNodes)r.assignedNodes().forEach((function(t){return pa(A,t,e,s)}));else{var i=ma(A,r);i.styles.isVisible()&&(Ha(r,i,s)?i.flags|=4:va(i.styles)&&(i.flags|=2),-1!==fa.indexOf(r.tagName)&&(i.flags|=8),e.elements.push(i),r.slot,r.shadowRoot?pa(A,r.shadowRoot,i,s):Ra(r)||ka(r)||_a(r)||pa(A,r,i,s))}},ma=function(A,t){return Va(t)?new ra(A,t):Oa(t)?new na(A,t):ka(t)?new ia(A,t):Ka(t)?new aa(A,t):La(t)?new oa(A,t):Ta(t)?new ha(A,t):_a(t)?new Ca(A,t):Ra(t)?new Ua(A,t):Ga(t)?new Fa(A,t):new kn(A,t)},ya=function(A,t){var e=ma(A,t);return e.flags|=4,pa(A,t,e,e),e},Ha=function(A,t,e){return t.styles.isPositionedWithZIndex()||t.styles.opacity<1||t.styles.isTransformed()||Sa(A)&&e.styles.isTransparent()},va=function(A){return A.isPositioned()||A.isFloating()},Ea=function(A){return A.nodeType===Node.TEXT_NODE},Ia=function(A){return A.nodeType===Node.ELEMENT_NODE},ba=function(A){return Ia(A)&&"undefined"!==typeof A.style&&!xa(A)},xa=function(A){return"object"===typeof A.className},Ka=function(A){return"LI"===A.tagName},La=function(A){return"OL"===A.tagName},Ta=function(A){return"INPUT"===A.tagName},Da=function(A){return"HTML"===A.tagName},ka=function(A){return"svg"===A.tagName},Sa=function(A){return"BODY"===A.tagName},Oa=function(A){return"CANVAS"===A.tagName},Ma=function(A){return"VIDEO"===A.tagName},Va=function(A){return"IMG"===A.tagName},Ga=function(A){return"IFRAME"===A.tagName},Pa=function(A){return"STYLE"===A.tagName},Na=function(A){return"SCRIPT"===A.tagName},Ra=function(A){return"TEXTAREA"===A.tagName},_a=function(A){return"SELECT"===A.tagName},Ja=function(A){return"SLOT"===A.tagName},Xa=function(A){return A.tagName.indexOf("-")>0},Ya=function(){function A(){this.counters={}}return A.prototype.getCounterValue=function(A){var t=this.counters[A];return t&&t.length?t[t.length-1]:1},A.prototype.getCounterValues=function(A){var t=this.counters[A];return t||[]},A.prototype.pop=function(A){var t=this;A.forEach((function(A){return t.counters[A].pop()}))},A.prototype.parse=function(A){var t=this,e=A.counterIncrement,s=A.counterReset,r=!0;null!==e&&e.forEach((function(A){var e=t.counters[A.counter];e&&0!==A.increment&&(r=!1,e.length||e.push(1),e[Math.max(0,e.length-1)]+=A.increment)}));var n=[];return r&&s.forEach((function(A){var e=t.counters[A.counter];n.push(A.counter),e||(e=t.counters[A.counter]=[]),e.push(A.reset)})),n},A}(),Wa={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},Za={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},qa={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},za={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},ja=function(A,t,e,s,r,n){return A<t||A>e?lo(A,r,n.length>0):s.integers.reduce((function(t,e,r){while(A>=e)A-=e,t+=s.values[r];return t}),"")+n},$a=function(A,t,e,s){var r="";do{e||A--,r=s(A)+r,A/=t}while(A*t>=t);return r},Ao=function(A,t,e,s,r){var n=e-t+1;return(A<0?"-":"")+($a(Math.abs(A),n,s,(function(A){return c(Math.floor(A%n)+t)}))+r)},to=function(A,t,e){void 0===e&&(e=". ");var s=t.length;return $a(Math.abs(A),s,!1,(function(A){return t[Math.floor(A%s)]}))+e},eo=1,so=2,ro=4,no=8,io=function(A,t,e,s,r,n){if(A<-9999||A>9999)return lo(A,4,r.length>0);var i=Math.abs(A),a=r;if(0===i)return t[0]+a;for(var o=0;i>0&&o<=4;o++){var B=i%10;0===B&&hn(n,eo)&&""!==a?a=t[B]+a:B>1||1===B&&0===o||1===B&&1===o&&hn(n,so)||1===B&&1===o&&hn(n,ro)&&A>100||1===B&&o>1&&hn(n,no)?a=t[B]+(o>0?e[o-1]:"")+a:1===B&&o>0&&(a=e[o-1]+a),i=Math.floor(i/10)}return(A<0?s:"")+a},ao="十百千萬",oo="拾佰仟萬",Bo="マイナス",co="마이너스",lo=function(A,t,e){var s=e?". ":"",r=e?"、":"",n=e?", ":"",i=e?" ":"";switch(t){case 0:return"•"+i;case 1:return"◦"+i;case 2:return"◾"+i;case 5:var a=Ao(A,48,57,!0,s);return a.length<4?"0"+a:a;case 4:return to(A,"〇一二三四五六七八九",r);case 6:return ja(A,1,3999,Wa,3,s).toLowerCase();case 7:return ja(A,1,3999,Wa,3,s);case 8:return Ao(A,945,969,!1,s);case 9:return Ao(A,97,122,!1,s);case 10:return Ao(A,65,90,!1,s);case 11:return Ao(A,1632,1641,!0,s);case 12:case 49:return ja(A,1,9999,Za,3,s);case 35:return ja(A,1,9999,Za,3,s).toLowerCase();case 13:return Ao(A,2534,2543,!0,s);case 14:case 30:return Ao(A,6112,6121,!0,s);case 15:return to(A,"子丑寅卯辰巳午未申酉戌亥",r);case 16:return to(A,"甲乙丙丁戊己庚辛壬癸",r);case 17:case 48:return io(A,"零一二三四五六七八九",ao,"負",r,so|ro|no);case 47:return io(A,"零壹貳參肆伍陸柒捌玖",oo,"負",r,eo|so|ro|no);case 42:return io(A,"零一二三四五六七八九",ao,"负",r,so|ro|no);case 41:return io(A,"零壹贰叁肆伍陆柒捌玖",oo,"负",r,eo|so|ro|no);case 26:return io(A,"〇一二三四五六七八九","十百千万",Bo,r,0);case 25:return io(A,"零壱弐参四伍六七八九","拾百千万",Bo,r,eo|so|ro);case 31:return io(A,"영일이삼사오육칠팔구","십백천만",co,n,eo|so|ro);case 33:return io(A,"零一二三四五六七八九","十百千萬",co,n,0);case 32:return io(A,"零壹貳參四五六七八九","拾百千",co,n,eo|so|ro);case 18:return Ao(A,2406,2415,!0,s);case 20:return ja(A,1,19999,za,3,s);case 21:return Ao(A,2790,2799,!0,s);case 22:return Ao(A,2662,2671,!0,s);case 22:return ja(A,1,10999,qa,3,s);case 23:return to(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return to(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return Ao(A,3302,3311,!0,s);case 28:return to(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",r);case 29:return to(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",r);case 34:return Ao(A,3792,3801,!0,s);case 37:return Ao(A,6160,6169,!0,s);case 38:return Ao(A,4160,4169,!0,s);case 39:return Ao(A,2918,2927,!0,s);case 40:return Ao(A,1776,1785,!0,s);case 43:return Ao(A,3046,3055,!0,s);case 44:return Ao(A,3174,3183,!0,s);case 45:return Ao(A,3664,3673,!0,s);case 46:return Ao(A,3872,3881,!0,s);case 3:default:return Ao(A,48,57,!0,s)}},go="data-html2canvas-ignore",uo=function(){function A(A,t,e){if(this.context=A,this.options=e,this.scrolledElements=[],this.referenceElement=t,this.counters=new Ya,this.quoteDepth=0,!t.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(t.ownerDocument.documentElement,!1)}return A.prototype.toIFrame=function(A,t){var e=this,n=Qo(A,t);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");var i=A.defaultView.pageXOffset,a=A.defaultView.pageYOffset,o=n.contentWindow,B=o.document,c=Uo(n).then((function(){return s(e,void 0,void 0,(function(){var A,e;return r(this,(function(s){switch(s.label){case 0:return this.scrolledElements.forEach(yo),o&&(o.scrollTo(t.left,t.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||o.scrollY===t.top&&o.scrollX===t.left||(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(o.scrollX-t.left,o.scrollY-t.top,0,0))),A=this.options.onclone,e=this.clonedReferenceElement,"undefined"===typeof e?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:B.fonts&&B.fonts.ready?[4,B.fonts.ready]:[3,2];case 1:s.sent(),s.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,Co(B)]:[3,4];case 3:s.sent(),s.label=4;case 4:return"function"===typeof A?[2,Promise.resolve().then((function(){return A(B,e)})).then((function(){return n}))]:[2,n]}}))}))}));return B.open(),B.write(po(document.doctype)+"<html></html>"),mo(this.referenceElement.ownerDocument,i,a),B.replaceChild(B.adoptNode(this.documentElement),B.documentElement),B.close(),c},A.prototype.createElementClone=function(A){if(Dn(A,2),Oa(A))return this.createCanvasClone(A);if(Ma(A))return this.createVideoClone(A);if(Pa(A))return this.createStyleClone(A);var t=A.cloneNode(!1);return Va(t)&&(Va(A)&&A.currentSrc&&A.currentSrc!==A.src&&(t.src=A.currentSrc,t.srcset=""),"lazy"===t.loading&&(t.loading="eager")),Xa(t)?this.createCustomElementClone(t):t},A.prototype.createCustomElementClone=function(A){var t=document.createElement("html2canvascustomelement");return fo(A.style,t),t},A.prototype.createStyleClone=function(A){try{var t=A.sheet;if(t&&t.cssRules){var e=[].slice.call(t.cssRules,0).reduce((function(A,t){return t&&"string"===typeof t.cssText?A+t.cssText:A}),""),s=A.cloneNode(!1);return s.textContent=e,s}}catch(Jt){if(this.context.logger.error("Unable to access cssRules property",Jt),"SecurityError"!==Jt.name)throw Jt}return A.cloneNode(!1)},A.prototype.createCanvasClone=function(A){var t;if(this.options.inlineImages&&A.ownerDocument){var e=A.ownerDocument.createElement("img");try{return e.src=A.toDataURL(),e}catch(Jt){this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var s=A.cloneNode(!1);try{s.width=A.width,s.height=A.height;var r=A.getContext("2d"),n=s.getContext("2d");if(n)if(!this.options.allowTaint&&r)n.putImageData(r.getImageData(0,0,A.width,A.height),0,0);else{var i=null!==(t=A.getContext("webgl2"))&&void 0!==t?t:A.getContext("webgl");if(i){var a=i.getContextAttributes();!1===(null===a||void 0===a?void 0:a.preserveDrawingBuffer)&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}n.drawImage(A,0,0)}return s}catch(Jt){this.context.logger.info("Unable to clone canvas as it is tainted",A)}return s},A.prototype.createVideoClone=function(A){var t=A.ownerDocument.createElement("canvas");t.width=A.offsetWidth,t.height=A.offsetHeight;var e=t.getContext("2d");try{return e&&(e.drawImage(A,0,0,t.width,t.height),this.options.allowTaint||e.getImageData(0,0,t.width,t.height)),t}catch(Jt){this.context.logger.info("Unable to clone video as it is tainted",A)}var s=A.ownerDocument.createElement("canvas");return s.width=A.offsetWidth,s.height=A.offsetHeight,s},A.prototype.appendChildNode=function(A,t,e){Ia(t)&&(Na(t)||t.hasAttribute(go)||"function"===typeof this.options.ignoreElements&&this.options.ignoreElements(t))||this.options.copyStyles&&Ia(t)&&Pa(t)||A.appendChild(this.cloneNode(t,e))},A.prototype.cloneChildNodes=function(A,t,e){for(var s=this,r=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;r;r=r.nextSibling)if(Ia(r)&&Ja(r)&&"function"===typeof r.assignedNodes){var n=r.assignedNodes();n.length&&n.forEach((function(A){return s.appendChildNode(t,A,e)}))}else this.appendChildNode(t,r,e)},A.prototype.cloneNode=function(A,t){if(Ea(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var e=A.ownerDocument.defaultView;if(e&&Ia(A)&&(ba(A)||xa(A))){var s=this.createElementClone(A);s.style.transitionProperty="none";var r=e.getComputedStyle(A),n=e.getComputedStyle(A,":before"),i=e.getComputedStyle(A,":after");this.referenceElement===A&&ba(s)&&(this.clonedReferenceElement=s),Sa(s)&&xo(s);var a=this.counters.parse(new xn(this.context,r)),o=this.resolvePseudoContent(A,s,n,li.BEFORE);Xa(A)&&(t=!0),Ma(A)||this.cloneChildNodes(A,s,t),o&&s.insertBefore(o,s.firstChild);var B=this.resolvePseudoContent(A,s,i,li.AFTER);return B&&s.appendChild(B),this.counters.pop(a),(r&&(this.options.copyStyles||xa(A))&&!Ga(A)||t)&&fo(r,s),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([s,A.scrollLeft,A.scrollTop]),(Ra(A)||_a(A))&&(Ra(s)||_a(s))&&(s.value=A.value),s}return A.cloneNode(!1)},A.prototype.resolvePseudoContent=function(A,t,e,s){var r=this;if(e){var n=e.content,i=t.ownerDocument;if(i&&n&&"none"!==n&&"-moz-alt-content"!==n&&"none"!==e.display){this.counters.parse(new xn(this.context,e));var a=new bn(this.context,e),o=i.createElement("html2canvaspseudoelement");fo(e,o),a.content.forEach((function(t){if(0===t.type)o.appendChild(i.createTextNode(t.value));else if(22===t.type){var e=i.createElement("img");e.src=t.value,e.style.opacity="1",o.appendChild(e)}else if(18===t.type){if("attr"===t.name){var s=t.values.filter(Ge);s.length&&o.appendChild(i.createTextNode(A.getAttribute(s[0].value)||""))}else if("counter"===t.name){var n=t.values.filter(_e),B=n[0],c=n[1];if(B&&Ge(B)){var l=r.counters.getCounterValue(B.value),g=c&&Ge(c)?Dr.parse(r.context,c.value):3;o.appendChild(i.createTextNode(lo(l,g,!1)))}}else if("counters"===t.name){var u=t.values.filter(_e),w=(B=u[0],u[1]);c=u[2];if(B&&Ge(B)){var Q=r.counters.getCounterValues(B.value),d=c&&Ge(c)?Dr.parse(r.context,c.value):3,h=w&&0===w.type?w.value:"",C=Q.map((function(A){return lo(A,d,!1)})).join(h);o.appendChild(i.createTextNode(C))}}}else if(20===t.type)switch(t.value){case"open-quote":o.appendChild(i.createTextNode(mn(a.quotes,r.quoteDepth++,!0)));break;case"close-quote":o.appendChild(i.createTextNode(mn(a.quotes,--r.quoteDepth,!1)));break;default:o.appendChild(i.createTextNode(t.value))}})),o.className=Eo+" "+Io;var B=s===li.BEFORE?" "+Eo:" "+Io;return xa(t)?t.className.baseValue+=B:t.className+=B,o}}},A.destroy=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)},A}();(function(A){A[A["BEFORE"]=0]="BEFORE",A[A["AFTER"]=1]="AFTER"})(li||(li={}));var wo,Qo=function(A,t){var e=A.createElement("iframe");return e.className="html2canvas-container",e.style.visibility="hidden",e.style.position="fixed",e.style.left="-10000px",e.style.top="0px",e.style.border="0",e.width=t.width.toString(),e.height=t.height.toString(),e.scrolling="no",e.setAttribute(go,"true"),A.body.appendChild(e),e},ho=function(A){return new Promise((function(t){A.complete?t():A.src?(A.onload=t,A.onerror=t):t()}))},Co=function(A){return Promise.all([].slice.call(A.images,0).map(ho))},Uo=function(A){return new Promise((function(t,e){var s=A.contentWindow;if(!s)return e("No window assigned for iframe");var r=s.document;s.onload=A.onload=function(){s.onload=A.onload=null;var e=setInterval((function(){r.body.childNodes.length>0&&"complete"===r.readyState&&(clearInterval(e),t(A))}),50)}}))},Fo=["all","d","content"],fo=function(A,t){for(var e=A.length-1;e>=0;e--){var s=A.item(e);-1===Fo.indexOf(s)&&t.style.setProperty(s,A.getPropertyValue(s))}return t},po=function(A){var t="";return A&&(t+="<!DOCTYPE ",A.name&&(t+=A.name),A.internalSubset&&(t+=A.internalSubset),A.publicId&&(t+='"'+A.publicId+'"'),A.systemId&&(t+='"'+A.systemId+'"'),t+=">"),t},mo=function(A,t,e){A&&A.defaultView&&(t!==A.defaultView.pageXOffset||e!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(t,e)},yo=function(A){var t=A[0],e=A[1],s=A[2];t.scrollLeft=e,t.scrollTop=s},Ho=":before",vo=":after",Eo="___html2canvas___pseudoelement_before",Io="___html2canvas___pseudoelement_after",bo='{\n    content: "" !important;\n    display: none !important;\n}',xo=function(A){Ko(A,"."+Eo+Ho+bo+"\n         ."+Io+vo+bo)},Ko=function(A,t){var e=A.ownerDocument;if(e){var s=e.createElement("style");s.textContent=t,A.appendChild(s)}},Lo=function(){function A(){}return A.getOrigin=function(t){var e=A._link;return e?(e.href=t,e.href=e.href,e.protocol+e.hostname+e.port):"about:blank"},A.isSameOrigin=function(t){return A.getOrigin(t)===A._origin},A.setContext=function(t){A._link=t.document.createElement("a"),A._origin=A.getOrigin(t.location.href)},A._origin="about:blank",A}(),To=function(){function A(A,t){this.context=A,this._options=t,this._cache={}}return A.prototype.addImage=function(A){var t=Promise.resolve();return this.has(A)?t:Go(A)||Oo(A)?((this._cache[A]=this.loadImage(A)).catch((function(){})),t):t},A.prototype.match=function(A){return this._cache[A]},A.prototype.loadImage=function(A){return s(this,void 0,void 0,(function(){var t,e,s,n,i=this;return r(this,(function(r){switch(r.label){case 0:return t=Lo.isSameOrigin(A),e=!Mo(A)&&!0===this._options.useCORS&&_i.SUPPORT_CORS_IMAGES&&!t,s=!Mo(A)&&!t&&!Go(A)&&"string"===typeof this._options.proxy&&_i.SUPPORT_CORS_XHR&&!e,t||!1!==this._options.allowTaint||Mo(A)||Go(A)||s||e?(n=A,s?[4,this.proxy(n)]:[3,2]):[2];case 1:n=r.sent(),r.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise((function(A,t){var s=new Image;s.onload=function(){return A(s)},s.onerror=t,(Vo(n)||e)&&(s.crossOrigin="anonymous"),s.src=n,!0===s.complete&&setTimeout((function(){return A(s)}),500),i._options.imageTimeout>0&&setTimeout((function(){return t("Timed out ("+i._options.imageTimeout+"ms) loading image")}),i._options.imageTimeout)}))];case 3:return[2,r.sent()]}}))}))},A.prototype.has=function(A){return"undefined"!==typeof this._cache[A]},A.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},A.prototype.proxy=function(A){var t=this,e=this._options.proxy;if(!e)throw new Error("No proxy defined");var s=A.substring(0,256);return new Promise((function(r,n){var i=_i.SUPPORT_RESPONSE_TYPE?"blob":"text",a=new XMLHttpRequest;a.onload=function(){if(200===a.status)if("text"===i)r(a.response);else{var A=new FileReader;A.addEventListener("load",(function(){return r(A.result)}),!1),A.addEventListener("error",(function(A){return n(A)}),!1),A.readAsDataURL(a.response)}else n("Failed to proxy resource "+s+" with status code "+a.status)},a.onerror=n;var o=e.indexOf("?")>-1?"&":"?";if(a.open("GET",""+e+o+"url="+encodeURIComponent(A)+"&responseType="+i),"text"!==i&&a instanceof XMLHttpRequest&&(a.responseType=i),t._options.imageTimeout){var B=t._options.imageTimeout;a.timeout=B,a.ontimeout=function(){return n("Timed out ("+B+"ms) proxying "+s)}}a.send()}))},A}(),Do=/^data:image\/svg\+xml/i,ko=/^data:image\/.*;base64,/i,So=/^data:image\/.*/i,Oo=function(A){return _i.SUPPORT_SVG_DRAWING||!Po(A)},Mo=function(A){return So.test(A)},Vo=function(A){return ko.test(A)},Go=function(A){return"blob"===A.substr(0,4)},Po=function(A){return"svg"===A.substr(-3).toLowerCase()||Do.test(A)},No=function(){function A(A,t){this.type=0,this.x=A,this.y=t}return A.prototype.add=function(t,e){return new A(this.x+t,this.y+e)},A}(),Ro=function(A,t,e){return new No(A.x+(t.x-A.x)*e,A.y+(t.y-A.y)*e)},_o=function(){function A(A,t,e,s){this.type=1,this.start=A,this.startControl=t,this.endControl=e,this.end=s}return A.prototype.subdivide=function(t,e){var s=Ro(this.start,this.startControl,t),r=Ro(this.startControl,this.endControl,t),n=Ro(this.endControl,this.end,t),i=Ro(s,r,t),a=Ro(r,n,t),o=Ro(i,a,t);return e?new A(this.start,s,i,o):new A(o,a,n,this.end)},A.prototype.add=function(t,e){return new A(this.start.add(t,e),this.startControl.add(t,e),this.endControl.add(t,e),this.end.add(t,e))},A.prototype.reverse=function(){return new A(this.end,this.endControl,this.startControl,this.start)},A}(),Jo=function(A){return 1===A.type},Xo=function(){function A(A){var t=A.styles,e=A.bounds,s=$e(t.borderTopLeftRadius,e.width,e.height),r=s[0],n=s[1],i=$e(t.borderTopRightRadius,e.width,e.height),a=i[0],o=i[1],B=$e(t.borderBottomRightRadius,e.width,e.height),c=B[0],l=B[1],g=$e(t.borderBottomLeftRadius,e.width,e.height),u=g[0],w=g[1],Q=[];Q.push((r+a)/e.width),Q.push((u+c)/e.width),Q.push((n+w)/e.height),Q.push((o+l)/e.height);var d=Math.max.apply(Math,Q);d>1&&(r/=d,n/=d,a/=d,o/=d,c/=d,l/=d,u/=d,w/=d);var h=e.width-a,C=e.height-l,U=e.width-c,F=e.height-w,f=t.borderTopWidth,p=t.borderRightWidth,m=t.borderBottomWidth,y=t.borderLeftWidth,H=As(t.paddingTop,A.bounds.width),v=As(t.paddingRight,A.bounds.width),E=As(t.paddingBottom,A.bounds.width),I=As(t.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=r>0||n>0?Yo(e.left+y/3,e.top+f/3,r-y/3,n-f/3,wo.TOP_LEFT):new No(e.left+y/3,e.top+f/3),this.topRightBorderDoubleOuterBox=r>0||n>0?Yo(e.left+h,e.top+f/3,a-p/3,o-f/3,wo.TOP_RIGHT):new No(e.left+e.width-p/3,e.top+f/3),this.bottomRightBorderDoubleOuterBox=c>0||l>0?Yo(e.left+U,e.top+C,c-p/3,l-m/3,wo.BOTTOM_RIGHT):new No(e.left+e.width-p/3,e.top+e.height-m/3),this.bottomLeftBorderDoubleOuterBox=u>0||w>0?Yo(e.left+y/3,e.top+F,u-y/3,w-m/3,wo.BOTTOM_LEFT):new No(e.left+y/3,e.top+e.height-m/3),this.topLeftBorderDoubleInnerBox=r>0||n>0?Yo(e.left+2*y/3,e.top+2*f/3,r-2*y/3,n-2*f/3,wo.TOP_LEFT):new No(e.left+2*y/3,e.top+2*f/3),this.topRightBorderDoubleInnerBox=r>0||n>0?Yo(e.left+h,e.top+2*f/3,a-2*p/3,o-2*f/3,wo.TOP_RIGHT):new No(e.left+e.width-2*p/3,e.top+2*f/3),this.bottomRightBorderDoubleInnerBox=c>0||l>0?Yo(e.left+U,e.top+C,c-2*p/3,l-2*m/3,wo.BOTTOM_RIGHT):new No(e.left+e.width-2*p/3,e.top+e.height-2*m/3),this.bottomLeftBorderDoubleInnerBox=u>0||w>0?Yo(e.left+2*y/3,e.top+F,u-2*y/3,w-2*m/3,wo.BOTTOM_LEFT):new No(e.left+2*y/3,e.top+e.height-2*m/3),this.topLeftBorderStroke=r>0||n>0?Yo(e.left+y/2,e.top+f/2,r-y/2,n-f/2,wo.TOP_LEFT):new No(e.left+y/2,e.top+f/2),this.topRightBorderStroke=r>0||n>0?Yo(e.left+h,e.top+f/2,a-p/2,o-f/2,wo.TOP_RIGHT):new No(e.left+e.width-p/2,e.top+f/2),this.bottomRightBorderStroke=c>0||l>0?Yo(e.left+U,e.top+C,c-p/2,l-m/2,wo.BOTTOM_RIGHT):new No(e.left+e.width-p/2,e.top+e.height-m/2),this.bottomLeftBorderStroke=u>0||w>0?Yo(e.left+y/2,e.top+F,u-y/2,w-m/2,wo.BOTTOM_LEFT):new No(e.left+y/2,e.top+e.height-m/2),this.topLeftBorderBox=r>0||n>0?Yo(e.left,e.top,r,n,wo.TOP_LEFT):new No(e.left,e.top),this.topRightBorderBox=a>0||o>0?Yo(e.left+h,e.top,a,o,wo.TOP_RIGHT):new No(e.left+e.width,e.top),this.bottomRightBorderBox=c>0||l>0?Yo(e.left+U,e.top+C,c,l,wo.BOTTOM_RIGHT):new No(e.left+e.width,e.top+e.height),this.bottomLeftBorderBox=u>0||w>0?Yo(e.left,e.top+F,u,w,wo.BOTTOM_LEFT):new No(e.left,e.top+e.height),this.topLeftPaddingBox=r>0||n>0?Yo(e.left+y,e.top+f,Math.max(0,r-y),Math.max(0,n-f),wo.TOP_LEFT):new No(e.left+y,e.top+f),this.topRightPaddingBox=a>0||o>0?Yo(e.left+Math.min(h,e.width-p),e.top+f,h>e.width+p?0:Math.max(0,a-p),Math.max(0,o-f),wo.TOP_RIGHT):new No(e.left+e.width-p,e.top+f),this.bottomRightPaddingBox=c>0||l>0?Yo(e.left+Math.min(U,e.width-y),e.top+Math.min(C,e.height-m),Math.max(0,c-p),Math.max(0,l-m),wo.BOTTOM_RIGHT):new No(e.left+e.width-p,e.top+e.height-m),this.bottomLeftPaddingBox=u>0||w>0?Yo(e.left+y,e.top+Math.min(F,e.height-m),Math.max(0,u-y),Math.max(0,w-m),wo.BOTTOM_LEFT):new No(e.left+y,e.top+e.height-m),this.topLeftContentBox=r>0||n>0?Yo(e.left+y+I,e.top+f+H,Math.max(0,r-(y+I)),Math.max(0,n-(f+H)),wo.TOP_LEFT):new No(e.left+y+I,e.top+f+H),this.topRightContentBox=a>0||o>0?Yo(e.left+Math.min(h,e.width+y+I),e.top+f+H,h>e.width+y+I?0:a-y+I,o-(f+H),wo.TOP_RIGHT):new No(e.left+e.width-(p+v),e.top+f+H),this.bottomRightContentBox=c>0||l>0?Yo(e.left+Math.min(U,e.width-(y+I)),e.top+Math.min(C,e.height+f+H),Math.max(0,c-(p+v)),l-(m+E),wo.BOTTOM_RIGHT):new No(e.left+e.width-(p+v),e.top+e.height-(m+E)),this.bottomLeftContentBox=u>0||w>0?Yo(e.left+y+I,e.top+F,Math.max(0,u-(y+I)),w-(m+E),wo.BOTTOM_LEFT):new No(e.left+y+I,e.top+e.height-(m+E))}return A}();(function(A){A[A["TOP_LEFT"]=0]="TOP_LEFT",A[A["TOP_RIGHT"]=1]="TOP_RIGHT",A[A["BOTTOM_RIGHT"]=2]="BOTTOM_RIGHT",A[A["BOTTOM_LEFT"]=3]="BOTTOM_LEFT"})(wo||(wo={}));var Yo=function(A,t,e,s,r){var n=(Math.sqrt(2)-1)/3*4,i=e*n,a=s*n,o=A+e,B=t+s;switch(r){case wo.TOP_LEFT:return new _o(new No(A,B),new No(A,B-a),new No(o-i,t),new No(o,t));case wo.TOP_RIGHT:return new _o(new No(A,t),new No(A+i,t),new No(o,B-a),new No(o,B));case wo.BOTTOM_RIGHT:return new _o(new No(o,t),new No(o,t+a),new No(A+i,B),new No(A,B));case wo.BOTTOM_LEFT:default:return new _o(new No(o,B),new No(o-i,B),new No(A,t+a),new No(A,t))}},Wo=function(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]},Zo=function(A){return[A.topLeftContentBox,A.topRightContentBox,A.bottomRightContentBox,A.bottomLeftContentBox]},qo=function(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]},zo=function(){function A(A,t,e){this.offsetX=A,this.offsetY=t,this.matrix=e,this.type=0,this.target=6}return A}(),jo=function(){function A(A,t){this.path=A,this.target=t,this.type=1}return A}(),$o=function(){function A(A){this.opacity=A,this.type=2,this.target=6}return A}(),AB=function(A){return 0===A.type},tB=function(A){return 1===A.type},eB=function(A){return 2===A.type},sB=function(A,t){return A.length===t.length&&A.some((function(A,e){return A===t[e]}))},rB=function(A,t,e,s,r){return A.map((function(A,n){switch(n){case 0:return A.add(t,e);case 1:return A.add(t+s,e);case 2:return A.add(t+s,e+r);case 3:return A.add(t,e+r)}return A}))},nB=function(){function A(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return A}(),iB=function(){function A(A,t){if(this.container=A,this.parent=t,this.effects=[],this.curves=new Xo(this.container),this.container.styles.opacity<1&&this.effects.push(new $o(this.container.styles.opacity)),null!==this.container.styles.transform){var e=this.container.bounds.left+this.container.styles.transformOrigin[0].number,s=this.container.bounds.top+this.container.styles.transformOrigin[1].number,r=this.container.styles.transform;this.effects.push(new zo(e,s,r))}if(0!==this.container.styles.overflowX){var n=Wo(this.curves),i=qo(this.curves);sB(n,i)?this.effects.push(new jo(n,6)):(this.effects.push(new jo(n,2)),this.effects.push(new jo(i,4)))}}return A.prototype.getEffects=function(A){var t=-1===[2,3].indexOf(this.container.styles.position),e=this.parent,s=this.effects.slice(0);while(e){var r=e.effects.filter((function(A){return!tB(A)}));if(t||0!==e.container.styles.position||!e.parent){if(s.unshift.apply(s,r),t=-1===[2,3].indexOf(e.container.styles.position),0!==e.container.styles.overflowX){var n=Wo(e.curves),i=qo(e.curves);sB(n,i)||s.unshift(new jo(i,6))}}else s.unshift.apply(s,r);e=e.parent}return s.filter((function(t){return hn(t.target,A)}))},A}(),aB=function(A,t,e,s){A.container.elements.forEach((function(r){var n=hn(r.flags,4),i=hn(r.flags,2),a=new iB(r,A);hn(r.styles.display,2048)&&s.push(a);var o=hn(r.flags,8)?[]:s;if(n||i){var B=n||r.styles.isPositioned()?e:t,c=new nB(a);if(r.styles.isPositioned()||r.styles.opacity<1||r.styles.isTransformed()){var l=r.styles.zIndex.order;if(l<0){var g=0;B.negativeZIndex.some((function(A,t){return l>A.element.container.styles.zIndex.order?(g=t,!1):g>0})),B.negativeZIndex.splice(g,0,c)}else if(l>0){var u=0;B.positiveZIndex.some((function(A,t){return l>=A.element.container.styles.zIndex.order?(u=t+1,!1):u>0})),B.positiveZIndex.splice(u,0,c)}else B.zeroOrAutoZIndexOrTransformedOrOpacity.push(c)}else r.styles.isFloating()?B.nonPositionedFloats.push(c):B.nonPositionedInlineLevel.push(c);aB(a,c,n?c:e,o)}else r.styles.isInlineLevel()?t.inlineLevel.push(a):t.nonInlineLevel.push(a),aB(a,t,e,o);hn(r.flags,8)&&oB(r,o)}))},oB=function(A,t){for(var e=A instanceof oa?A.start:1,s=A instanceof oa&&A.reversed,r=0;r<t.length;r++){var n=t[r];n.container instanceof aa&&"number"===typeof n.container.value&&0!==n.container.value&&(e=n.container.value),n.listValue=lo(e,n.container.styles.listStyleType,!0),e+=s?-1:1}},BB=function(A){var t=new iB(A,null),e=new nB(t),s=[];return aB(t,e,e,s),oB(t.container,s),e},cB=function(A,t){switch(t){case 0:return QB(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return QB(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return QB(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);case 3:default:return QB(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}},lB=function(A,t){switch(t){case 0:return QB(A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox,A.topRightBorderBox,A.topRightBorderDoubleOuterBox);case 1:return QB(A.topRightBorderBox,A.topRightBorderDoubleOuterBox,A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox);case 2:return QB(A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox,A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox);case 3:default:return QB(A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox,A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox)}},gB=function(A,t){switch(t){case 0:return QB(A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox,A.topRightBorderDoubleInnerBox,A.topRightPaddingBox);case 1:return QB(A.topRightBorderDoubleInnerBox,A.topRightPaddingBox,A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox);case 2:return QB(A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox,A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox);case 3:default:return QB(A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox,A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox)}},uB=function(A,t){switch(t){case 0:return wB(A.topLeftBorderStroke,A.topRightBorderStroke);case 1:return wB(A.topRightBorderStroke,A.bottomRightBorderStroke);case 2:return wB(A.bottomRightBorderStroke,A.bottomLeftBorderStroke);case 3:default:return wB(A.bottomLeftBorderStroke,A.topLeftBorderStroke)}},wB=function(A,t){var e=[];return Jo(A)?e.push(A.subdivide(.5,!1)):e.push(A),Jo(t)?e.push(t.subdivide(.5,!0)):e.push(t),e},QB=function(A,t,e,s){var r=[];return Jo(A)?r.push(A.subdivide(.5,!1)):r.push(A),Jo(e)?r.push(e.subdivide(.5,!0)):r.push(e),Jo(s)?r.push(s.subdivide(.5,!0).reverse()):r.push(s),Jo(t)?r.push(t.subdivide(.5,!1).reverse()):r.push(t),r},dB=function(A){var t=A.bounds,e=A.styles;return t.add(e.borderLeftWidth,e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth),-(e.borderTopWidth+e.borderBottomWidth))},hB=function(A){var t=A.styles,e=A.bounds,s=As(t.paddingLeft,e.width),r=As(t.paddingRight,e.width),n=As(t.paddingTop,e.width),i=As(t.paddingBottom,e.width);return e.add(s+t.borderLeftWidth,n+t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth+s+r),-(t.borderTopWidth+t.borderBottomWidth+n+i))},CB=function(A,t){return 0===A?t.bounds:2===A?hB(t):dB(t)},UB=function(A,t){return 0===A?t.bounds:2===A?hB(t):dB(t)},FB=function(A,t,e){var s=CB(yB(A.styles.backgroundOrigin,t),A),r=UB(yB(A.styles.backgroundClip,t),A),n=mB(yB(A.styles.backgroundSize,t),e,s),i=n[0],a=n[1],o=$e(yB(A.styles.backgroundPosition,t),s.width-i,s.height-a),B=HB(yB(A.styles.backgroundRepeat,t),o,n,s,r),c=Math.round(s.left+o[0]),l=Math.round(s.top+o[1]);return[B,c,l,i,a]},fB=function(A){return Ge(A)&&A.value===Xs.AUTO},pB=function(A){return"number"===typeof A},mB=function(A,t,e){var s=t[0],r=t[1],n=t[2],i=A[0],a=A[1];if(!i)return[0,0];if(We(i)&&a&&We(a))return[As(i,e.width),As(a,e.height)];var o=pB(n);if(Ge(i)&&(i.value===Xs.CONTAIN||i.value===Xs.COVER)){if(pB(n)){var B=e.width/e.height;return B<n!==(i.value===Xs.COVER)?[e.width,e.width/n]:[e.height*n,e.height]}return[e.width,e.height]}var c=pB(s),l=pB(r),g=c||l;if(fB(i)&&(!a||fB(a))){if(c&&l)return[s,r];if(!o&&!g)return[e.width,e.height];if(g&&o){var u=c?s:r*n,w=l?r:s/n;return[u,w]}var Q=c?s:e.width,d=l?r:e.height;return[Q,d]}if(o){var h=0,C=0;return We(i)?h=As(i,e.width):We(a)&&(C=As(a,e.height)),fB(i)?h=C*n:a&&!fB(a)||(C=h/n),[h,C]}var U=null,F=null;if(We(i)?U=As(i,e.width):a&&We(a)&&(F=As(a,e.height)),null===U||a&&!fB(a)||(F=c&&l?U/s*r:e.height),null!==F&&fB(i)&&(U=c&&l?F/r*s:e.width),null!==U&&null!==F)return[U,F];throw new Error("Unable to calculate background-size for element")},yB=function(A,t){var e=A[t];return"undefined"===typeof e?A[0]:e},HB=function(A,t,e,s,r){var n=t[0],i=t[1],a=e[0],o=e[1];switch(A){case 2:return[new No(Math.round(s.left),Math.round(s.top+i)),new No(Math.round(s.left+s.width),Math.round(s.top+i)),new No(Math.round(s.left+s.width),Math.round(o+s.top+i)),new No(Math.round(s.left),Math.round(o+s.top+i))];case 3:return[new No(Math.round(s.left+n),Math.round(s.top)),new No(Math.round(s.left+n+a),Math.round(s.top)),new No(Math.round(s.left+n+a),Math.round(s.height+s.top)),new No(Math.round(s.left+n),Math.round(s.height+s.top))];case 1:return[new No(Math.round(s.left+n),Math.round(s.top+i)),new No(Math.round(s.left+n+a),Math.round(s.top+i)),new No(Math.round(s.left+n+a),Math.round(s.top+i+o)),new No(Math.round(s.left+n),Math.round(s.top+i+o))];default:return[new No(Math.round(r.left),Math.round(r.top)),new No(Math.round(r.left+r.width),Math.round(r.top)),new No(Math.round(r.left+r.width),Math.round(r.height+r.top)),new No(Math.round(r.left),Math.round(r.height+r.top))]}},vB="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",EB="Hidden Text",IB=function(){function A(A){this._data={},this._document=A}return A.prototype.parseMetrics=function(A,t){var e=this._document.createElement("div"),s=this._document.createElement("img"),r=this._document.createElement("span"),n=this._document.body;e.style.visibility="hidden",e.style.fontFamily=A,e.style.fontSize=t,e.style.margin="0",e.style.padding="0",e.style.whiteSpace="nowrap",n.appendChild(e),s.src=vB,s.width=1,s.height=1,s.style.margin="0",s.style.padding="0",s.style.verticalAlign="baseline",r.style.fontFamily=A,r.style.fontSize=t,r.style.margin="0",r.style.padding="0",r.appendChild(this._document.createTextNode(EB)),e.appendChild(r),e.appendChild(s);var i=s.offsetTop-r.offsetTop+2;e.removeChild(r),e.appendChild(this._document.createTextNode(EB)),e.style.lineHeight="normal",s.style.verticalAlign="super";var a=s.offsetTop-e.offsetTop+2;return n.removeChild(e),{baseline:i,middle:a}},A.prototype.getMetrics=function(A,t){var e=A+" "+t;return"undefined"===typeof this._data[e]&&(this._data[e]=this.parseMetrics(A,t)),this._data[e]},A}(),bB=function(){function A(A,t){this.context=A,this.options=t}return A}(),xB=1e4,KB=function(A){function e(t,e){var s=A.call(this,t,e)||this;return s._activeEffects=[],s.canvas=e.canvas?e.canvas:document.createElement("canvas"),s.ctx=s.canvas.getContext("2d"),e.canvas||(s.canvas.width=Math.floor(e.width*e.scale),s.canvas.height=Math.floor(e.height*e.scale),s.canvas.style.width=e.width+"px",s.canvas.style.height=e.height+"px"),s.fontMetrics=new IB(document),s.ctx.scale(s.options.scale,s.options.scale),s.ctx.translate(-e.x,-e.y),s.ctx.textBaseline="bottom",s._activeEffects=[],s.context.logger.debug("Canvas renderer initialized ("+e.width+"x"+e.height+") with scale "+e.scale),s}return t(e,A),e.prototype.applyEffects=function(A){var t=this;while(this._activeEffects.length)this.popEffect();A.forEach((function(A){return t.applyEffect(A)}))},e.prototype.applyEffect=function(A){this.ctx.save(),eB(A)&&(this.ctx.globalAlpha=A.opacity),AB(A)&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),tB(A)&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},e.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},e.prototype.renderStack=function(A){return s(this,void 0,void 0,(function(){var t;return r(this,(function(e){switch(e.label){case 0:return t=A.element.container.styles,t.isVisible()?[4,this.renderStackContent(A)]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},e.prototype.renderNode=function(A){return s(this,void 0,void 0,(function(){return r(this,(function(t){switch(t.label){case 0:return hn(A.container.flags,16),A.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(A)]:[3,3];case 1:return t.sent(),[4,this.renderNodeContent(A)];case 2:t.sent(),t.label=3;case 3:return[2]}}))}))},e.prototype.renderTextWithLetterSpacing=function(A,t,e){var s=this;if(0===t)this.ctx.fillText(A.text,A.bounds.left,A.bounds.top+e);else{var r=Zi(A.text);r.reduce((function(t,r){return s.ctx.fillText(r,t,A.bounds.top+e),t+s.ctx.measureText(r).width}),A.bounds.left)}},e.prototype.createFontStyle=function(A){var t=A.fontVariant.filter((function(A){return"normal"===A||"small-caps"===A})).join(""),e=SB(A.fontFamily).join(", "),s=Me(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,t,A.fontWeight,s,e].join(" "),e,s]},e.prototype.renderTextNode=function(A,t){return s(this,void 0,void 0,(function(){var e,s,n,i,a,o,B,c,l=this;return r(this,(function(r){return e=this.createFontStyle(t),s=e[0],n=e[1],i=e[2],this.ctx.font=s,this.ctx.direction=1===t.direction?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",a=this.fontMetrics.getMetrics(n,i),o=a.baseline,B=a.middle,c=t.paintOrder,A.textBounds.forEach((function(A){c.forEach((function(e){switch(e){case 0:l.ctx.fillStyle=ls(t.color),l.renderTextWithLetterSpacing(A,t.letterSpacing,o);var s=t.textShadow;s.length&&A.text.trim().length&&(s.slice(0).reverse().forEach((function(e){l.ctx.shadowColor=ls(e.color),l.ctx.shadowOffsetX=e.offsetX.number*l.options.scale,l.ctx.shadowOffsetY=e.offsetY.number*l.options.scale,l.ctx.shadowBlur=e.blur.number,l.renderTextWithLetterSpacing(A,t.letterSpacing,o)})),l.ctx.shadowColor="",l.ctx.shadowOffsetX=0,l.ctx.shadowOffsetY=0,l.ctx.shadowBlur=0),t.textDecorationLine.length&&(l.ctx.fillStyle=ls(t.textDecorationColor||t.color),t.textDecorationLine.forEach((function(t){switch(t){case 1:l.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top+o),A.bounds.width,1);break;case 2:l.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top),A.bounds.width,1);break;case 3:l.ctx.fillRect(A.bounds.left,Math.ceil(A.bounds.top+B),A.bounds.width,1);break}})));break;case 1:t.webkitTextStrokeWidth&&A.text.trim().length&&(l.ctx.strokeStyle=ls(t.webkitTextStrokeColor),l.ctx.lineWidth=t.webkitTextStrokeWidth,l.ctx.lineJoin=window.chrome?"miter":"round",l.ctx.strokeText(A.text,A.bounds.left,A.bounds.top+o)),l.ctx.strokeStyle="",l.ctx.lineWidth=0,l.ctx.lineJoin="miter";break}}))})),[2]}))}))},e.prototype.renderReplacedElement=function(A,t,e){if(e&&A.intrinsicWidth>0&&A.intrinsicHeight>0){var s=hB(A),r=qo(t);this.path(r),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(e,0,0,A.intrinsicWidth,A.intrinsicHeight,s.left,s.top,s.width,s.height),this.ctx.restore()}},e.prototype.renderNodeContent=function(A){return s(this,void 0,void 0,(function(){var t,s,n,a,o,B,c,l,g,u,w,Q,d,h,C,U,F,f,p;return r(this,(function(r){switch(r.label){case 0:this.applyEffects(A.getEffects(4)),t=A.container,s=A.curves,n=t.styles,a=0,o=t.textNodes,r.label=1;case 1:return a<o.length?(B=o[a],[4,this.renderTextNode(B,n)]):[3,4];case 2:r.sent(),r.label=3;case 3:return a++,[3,1];case 4:if(!(t instanceof ra))return[3,8];r.label=5;case 5:return r.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return U=r.sent(),this.renderReplacedElement(t,s,U),[3,8];case 7:return r.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof na&&this.renderReplacedElement(t,s,t.canvas),!(t instanceof ia))return[3,12];r.label=9;case 9:return r.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return U=r.sent(),this.renderReplacedElement(t,s,U),[3,12];case 11:return r.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof Fa&&t.tree?(c=new e(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}),[4,c.render(t.tree)]):[3,14];case 13:l=r.sent(),t.width&&t.height&&this.ctx.drawImage(l,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),r.label=14;case 14:if(t instanceof ha&&(g=Math.min(t.bounds.width,t.bounds.height),t.type===ua?t.checked&&(this.ctx.save(),this.path([new No(t.bounds.left+.39363*g,t.bounds.top+.79*g),new No(t.bounds.left+.16*g,t.bounds.top+.5549*g),new No(t.bounds.left+.27347*g,t.bounds.top+.44071*g),new No(t.bounds.left+.39694*g,t.bounds.top+.5649*g),new No(t.bounds.left+.72983*g,t.bounds.top+.23*g),new No(t.bounds.left+.84*g,t.bounds.top+.34085*g),new No(t.bounds.left+.39363*g,t.bounds.top+.79*g)]),this.ctx.fillStyle=ls(da),this.ctx.fill(),this.ctx.restore()):t.type===wa&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+g/2,t.bounds.top+g/2,g/4,0,2*Math.PI,!0),this.ctx.fillStyle=ls(da),this.ctx.fill(),this.ctx.restore())),LB(t)&&t.value.length){switch(u=this.createFontStyle(n),f=u[0],w=u[1],Q=this.fontMetrics.getMetrics(f,w).baseline,this.ctx.font=f,this.ctx.fillStyle=ls(n.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=DB(t.styles.textAlign),p=hB(t),d=0,t.styles.textAlign){case 1:d+=p.width/2;break;case 2:d+=p.width;break}h=p.add(d,0,0,-p.height/2+1),this.ctx.save(),this.path([new No(p.left,p.top),new No(p.left+p.width,p.top),new No(p.left+p.width,p.top+p.height),new No(p.left,p.top+p.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Ji(t.value,h),n.letterSpacing,Q),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!hn(t.styles.display,2048))return[3,20];if(null===t.styles.listStyleImage)return[3,19];if(C=t.styles.listStyleImage,0!==C.type)return[3,18];U=void 0,F=C.url,r.label=15;case 15:return r.trys.push([15,17,,18]),[4,this.context.cache.match(F)];case 16:return U=r.sent(),this.ctx.drawImage(U,t.bounds.left-(U.width+10),t.bounds.top),[3,18];case 17:return r.sent(),this.context.logger.error("Error loading list-style-image "+F),[3,18];case 18:return[3,20];case 19:A.listValue&&-1!==t.styles.listStyleType&&(f=this.createFontStyle(n)[0],this.ctx.font=f,this.ctx.fillStyle=ls(n.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",p=new i(t.bounds.left,t.bounds.top+As(t.styles.paddingTop,t.bounds.width),t.bounds.width,Kr(n.lineHeight,n.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new Ji(A.listValue,p),n.letterSpacing,Kr(n.lineHeight,n.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),r.label=20;case 20:return[2]}}))}))},e.prototype.renderStackContent=function(A){return s(this,void 0,void 0,(function(){var t,e,s,n,i,a,o,B,c,l,g,u,w,Q,d;return r(this,(function(r){switch(r.label){case 0:return hn(A.element.container.flags,16),[4,this.renderNodeBackgroundAndBorders(A.element)];case 1:r.sent(),t=0,e=A.negativeZIndex,r.label=2;case 2:return t<e.length?(d=e[t],[4,this.renderStack(d)]):[3,5];case 3:r.sent(),r.label=4;case 4:return t++,[3,2];case 5:return[4,this.renderNodeContent(A.element)];case 6:r.sent(),s=0,n=A.nonInlineLevel,r.label=7;case 7:return s<n.length?(d=n[s],[4,this.renderNode(d)]):[3,10];case 8:r.sent(),r.label=9;case 9:return s++,[3,7];case 10:i=0,a=A.nonPositionedFloats,r.label=11;case 11:return i<a.length?(d=a[i],[4,this.renderStack(d)]):[3,14];case 12:r.sent(),r.label=13;case 13:return i++,[3,11];case 14:o=0,B=A.nonPositionedInlineLevel,r.label=15;case 15:return o<B.length?(d=B[o],[4,this.renderStack(d)]):[3,18];case 16:r.sent(),r.label=17;case 17:return o++,[3,15];case 18:c=0,l=A.inlineLevel,r.label=19;case 19:return c<l.length?(d=l[c],[4,this.renderNode(d)]):[3,22];case 20:r.sent(),r.label=21;case 21:return c++,[3,19];case 22:g=0,u=A.zeroOrAutoZIndexOrTransformedOrOpacity,r.label=23;case 23:return g<u.length?(d=u[g],[4,this.renderStack(d)]):[3,26];case 24:r.sent(),r.label=25;case 25:return g++,[3,23];case 26:w=0,Q=A.positiveZIndex,r.label=27;case 27:return w<Q.length?(d=Q[w],[4,this.renderStack(d)]):[3,30];case 28:r.sent(),r.label=29;case 29:return w++,[3,27];case 30:return[2]}}))}))},e.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},e.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},e.prototype.formatPath=function(A){var t=this;A.forEach((function(A,e){var s=Jo(A)?A.start:A;0===e?t.ctx.moveTo(s.x,s.y):t.ctx.lineTo(s.x,s.y),Jo(A)&&t.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)}))},e.prototype.renderRepeat=function(A,t,e,s){this.path(A),this.ctx.fillStyle=t,this.ctx.translate(e,s),this.ctx.fill(),this.ctx.translate(-e,-s)},e.prototype.resizeImage=function(A,t,e){var s;if(A.width===t&&A.height===e)return A;var r=null!==(s=this.canvas.ownerDocument)&&void 0!==s?s:document,n=r.createElement("canvas");n.width=Math.max(1,t),n.height=Math.max(1,e);var i=n.getContext("2d");return i.drawImage(A,0,0,A.width,A.height,0,0,t,e),n},e.prototype.renderBackgroundImage=function(A){return s(this,void 0,void 0,(function(){var t,e,s,n,i,a;return r(this,(function(o){switch(o.label){case 0:t=A.styles.backgroundImage.length-1,e=function(e){var n,i,a,o,B,c,l,g,u,w,Q,d,h,C,U,F,f,p,m,y,H,v,E,I,b,x,K,L,T,D,k;return r(this,(function(r){switch(r.label){case 0:if(0!==e.type)return[3,5];n=void 0,i=e.url,r.label=1;case 1:return r.trys.push([1,3,,4]),[4,s.context.cache.match(i)];case 2:return n=r.sent(),[3,4];case 3:return r.sent(),s.context.logger.error("Error loading background-image "+i),[3,4];case 4:return n&&(a=FB(A,t,[n.width,n.height,n.width/n.height]),F=a[0],v=a[1],E=a[2],m=a[3],y=a[4],C=s.ctx.createPattern(s.resizeImage(n,m,y),"repeat"),s.renderRepeat(F,C,v,E)),[3,6];case 5:Ns(e)?(o=FB(A,t,[null,null,null]),F=o[0],v=o[1],E=o[2],m=o[3],y=o[4],B=Hs(e.angle,m,y),c=B[0],l=B[1],g=B[2],u=B[3],w=B[4],Q=document.createElement("canvas"),Q.width=m,Q.height=y,d=Q.getContext("2d"),h=d.createLinearGradient(l,u,g,w),ms(e.stops,c).forEach((function(A){return h.addColorStop(A.stop,ls(A.color))})),d.fillStyle=h,d.fillRect(0,0,m,y),m>0&&y>0&&(C=s.ctx.createPattern(Q,"repeat"),s.renderRepeat(F,C,v,E))):Rs(e)&&(U=FB(A,t,[null,null,null]),F=U[0],f=U[1],p=U[2],m=U[3],y=U[4],H=0===e.position.length?[ze]:e.position,v=As(H[0],m),E=As(H[H.length-1],y),I=Is(e,v,E,m,y),b=I[0],x=I[1],b>0&&x>0&&(K=s.ctx.createRadialGradient(f+v,p+E,0,f+v,p+E,b),ms(e.stops,2*b).forEach((function(A){return K.addColorStop(A.stop,ls(A.color))})),s.path(F),s.ctx.fillStyle=K,b!==x?(L=A.bounds.left+.5*A.bounds.width,T=A.bounds.top+.5*A.bounds.height,D=x/b,k=1/D,s.ctx.save(),s.ctx.translate(L,T),s.ctx.transform(1,0,0,D,0,0),s.ctx.translate(-L,-T),s.ctx.fillRect(f,k*(p-T)+T,m,y*k),s.ctx.restore()):s.ctx.fill())),r.label=6;case 6:return t--,[2]}}))},s=this,n=0,i=A.styles.backgroundImage.slice(0).reverse(),o.label=1;case 1:return n<i.length?(a=i[n],[5,e(a)]):[3,4];case 2:o.sent(),o.label=3;case 3:return n++,[3,1];case 4:return[2]}}))}))},e.prototype.renderSolidBorder=function(A,t,e){return s(this,void 0,void 0,(function(){return r(this,(function(s){return this.path(cB(e,t)),this.ctx.fillStyle=ls(A),this.ctx.fill(),[2]}))}))},e.prototype.renderDoubleBorder=function(A,t,e,n){return s(this,void 0,void 0,(function(){var s,i;return r(this,(function(r){switch(r.label){case 0:return t<3?[4,this.renderSolidBorder(A,e,n)]:[3,2];case 1:return r.sent(),[2];case 2:return s=lB(n,e),this.path(s),this.ctx.fillStyle=ls(A),this.ctx.fill(),i=gB(n,e),this.path(i),this.ctx.fill(),[2]}}))}))},e.prototype.renderNodeBackgroundAndBorders=function(A){return s(this,void 0,void 0,(function(){var t,e,s,n,i,a,o,B,c=this;return r(this,(function(r){switch(r.label){case 0:return this.applyEffects(A.getEffects(2)),t=A.container.styles,e=!cs(t.backgroundColor)||t.backgroundImage.length,s=[{style:t.borderTopStyle,color:t.borderTopColor,width:t.borderTopWidth},{style:t.borderRightStyle,color:t.borderRightColor,width:t.borderRightWidth},{style:t.borderBottomStyle,color:t.borderBottomColor,width:t.borderBottomWidth},{style:t.borderLeftStyle,color:t.borderLeftColor,width:t.borderLeftWidth}],n=TB(yB(t.backgroundClip,0),A.curves),e||t.boxShadow.length?(this.ctx.save(),this.path(n),this.ctx.clip(),cs(t.backgroundColor)||(this.ctx.fillStyle=ls(t.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(A.container)]):[3,2];case 1:r.sent(),this.ctx.restore(),t.boxShadow.slice(0).reverse().forEach((function(t){c.ctx.save();var e=Wo(A.curves),s=t.inset?0:xB,r=rB(e,-s+(t.inset?1:-1)*t.spread.number,(t.inset?1:-1)*t.spread.number,t.spread.number*(t.inset?-2:2),t.spread.number*(t.inset?-2:2));t.inset?(c.path(e),c.ctx.clip(),c.mask(r)):(c.mask(e),c.ctx.clip(),c.path(r)),c.ctx.shadowOffsetX=t.offsetX.number+s,c.ctx.shadowOffsetY=t.offsetY.number,c.ctx.shadowColor=ls(t.color),c.ctx.shadowBlur=t.blur.number,c.ctx.fillStyle=t.inset?ls(t.color):"rgba(0,0,0,1)",c.ctx.fill(),c.ctx.restore()})),r.label=2;case 2:i=0,a=0,o=s,r.label=3;case 3:return a<o.length?(B=o[a],0!==B.style&&!cs(B.color)&&B.width>0?2!==B.style?[3,5]:[4,this.renderDashedDottedBorder(B.color,B.width,i,A.curves,2)]:[3,11]):[3,13];case 4:return r.sent(),[3,11];case 5:return 3!==B.style?[3,7]:[4,this.renderDashedDottedBorder(B.color,B.width,i,A.curves,3)];case 6:return r.sent(),[3,11];case 7:return 4!==B.style?[3,9]:[4,this.renderDoubleBorder(B.color,B.width,i,A.curves)];case 8:return r.sent(),[3,11];case 9:return[4,this.renderSolidBorder(B.color,i,A.curves)];case 10:r.sent(),r.label=11;case 11:i++,r.label=12;case 12:return a++,[3,3];case 13:return[2]}}))}))},e.prototype.renderDashedDottedBorder=function(A,t,e,n,i){return s(this,void 0,void 0,(function(){var s,a,o,B,c,l,g,u,w,Q,d,h,C,U,F,f;return r(this,(function(r){return this.ctx.save(),s=uB(n,e),a=cB(n,e),2===i&&(this.path(a),this.ctx.clip()),Jo(a[0])?(o=a[0].start.x,B=a[0].start.y):(o=a[0].x,B=a[0].y),Jo(a[1])?(c=a[1].end.x,l=a[1].end.y):(c=a[1].x,l=a[1].y),g=0===e||2===e?Math.abs(o-c):Math.abs(B-l),this.ctx.beginPath(),3===i?this.formatPath(s):this.formatPath(a.slice(0,2)),u=t<3?3*t:2*t,w=t<3?2*t:t,3===i&&(u=t,w=t),Q=!0,g<=2*u?Q=!1:g<=2*u+w?(d=g/(2*u+w),u*=d,w*=d):(h=Math.floor((g+w)/(u+w)),C=(g-h*u)/(h-1),U=(g-(h+1)*u)/h,w=U<=0||Math.abs(w-C)<Math.abs(w-U)?C:U),Q&&(3===i?this.ctx.setLineDash([0,u+w]):this.ctx.setLineDash([u,w])),3===i?(this.ctx.lineCap="round",this.ctx.lineWidth=t):this.ctx.lineWidth=2*t+1.1,this.ctx.strokeStyle=ls(A),this.ctx.stroke(),this.ctx.setLineDash([]),2===i&&(Jo(a[0])&&(F=a[3],f=a[0],this.ctx.beginPath(),this.formatPath([new No(F.end.x,F.end.y),new No(f.start.x,f.start.y)]),this.ctx.stroke()),Jo(a[1])&&(F=a[1],f=a[2],this.ctx.beginPath(),this.formatPath([new No(F.end.x,F.end.y),new No(f.start.x,f.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]}))}))},e.prototype.render=function(A){return s(this,void 0,void 0,(function(){var t;return r(this,(function(e){switch(e.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=ls(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),t=BB(A),[4,this.renderStack(t)];case 1:return e.sent(),this.applyEffects([]),[2,this.canvas]}}))}))},e}(bB),LB=function(A){return A instanceof Ua||(A instanceof Ca||A instanceof ha&&A.type!==wa&&A.type!==ua)},TB=function(A,t){switch(A){case 0:return Wo(t);case 2:return Zo(t);case 1:default:return qo(t)}},DB=function(A){switch(A){case 1:return"center";case 2:return"right";case 0:default:return"left"}},kB=["-apple-system","system-ui"],SB=function(A){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?A.filter((function(A){return-1===kB.indexOf(A)})):A},OB=function(A){function e(t,e){var s=A.call(this,t,e)||this;return s.canvas=e.canvas?e.canvas:document.createElement("canvas"),s.ctx=s.canvas.getContext("2d"),s.options=e,s.canvas.width=Math.floor(e.width*e.scale),s.canvas.height=Math.floor(e.height*e.scale),s.canvas.style.width=e.width+"px",s.canvas.style.height=e.height+"px",s.ctx.scale(s.options.scale,s.options.scale),s.ctx.translate(-e.x,-e.y),s.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+e.width+"x"+e.height+" at "+e.x+","+e.y+") with scale "+e.scale),s}return t(e,A),e.prototype.render=function(A){return s(this,void 0,void 0,(function(){var t,e;return r(this,(function(s){switch(s.label){case 0:return t=Ni(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,A),[4,MB(t)];case 1:return e=s.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=ls(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(e,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}}))}))},e}(bB),MB=function(A){return new Promise((function(t,e){var s=new Image;s.onload=function(){t(s)},s.onerror=e,s.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},VB=function(){function A(A){var t=A.id,e=A.enabled;this.id=t,this.enabled=e,this.start=Date.now()}return A.prototype.debug=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&("undefined"!==typeof window&&window.console&&"function"===typeof console.debug?console.debug.apply(console,n([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.prototype.getTime=function(){return Date.now()-this.start},A.prototype.info=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&"undefined"!==typeof window&&window.console&&"function"===typeof console.info&&console.info.apply(console,n([this.id,this.getTime()+"ms"],A))},A.prototype.warn=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&("undefined"!==typeof window&&window.console&&"function"===typeof console.warn?console.warn.apply(console,n([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.prototype.error=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&("undefined"!==typeof window&&window.console&&"function"===typeof console.error?console.error.apply(console,n([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.instances={},A}(),GB=function(){function A(t,e){var s;this.windowBounds=e,this.instanceName="#"+A.instanceCount++,this.logger=new VB({id:this.instanceName,enabled:t.logging}),this.cache=null!==(s=t.cache)&&void 0!==s?s:new To(this,t)}return A.instanceCount=1,A}(),PB=function(A,t){return void 0===t&&(t={}),NB(A,t)};"undefined"!==typeof window&&Lo.setContext(window);var NB=function(A,t){return s(void 0,void 0,void 0,(function(){var s,n,B,c,l,g,u,w,Q,d,h,C,U,F,f,p,m,y,H,v,E,I,b,x,K,L,T,D,k,S,O,M,V,G,P,N,R,_,J;return r(this,(function(r){switch(r.label){case 0:if(!A||"object"!==typeof A)return[2,Promise.reject("Invalid element provided as first argument")];if(s=A.ownerDocument,!s)throw new Error("Element is not attached to a Document");if(n=s.defaultView,!n)throw new Error("Document is not attached to a Window");return B={allowTaint:null!==(b=t.allowTaint)&&void 0!==b&&b,imageTimeout:null!==(x=t.imageTimeout)&&void 0!==x?x:15e3,proxy:t.proxy,useCORS:null!==(K=t.useCORS)&&void 0!==K&&K},c=e({logging:null===(L=t.logging)||void 0===L||L,cache:t.cache},B),l={windowWidth:null!==(T=t.windowWidth)&&void 0!==T?T:n.innerWidth,windowHeight:null!==(D=t.windowHeight)&&void 0!==D?D:n.innerHeight,scrollX:null!==(k=t.scrollX)&&void 0!==k?k:n.pageXOffset,scrollY:null!==(S=t.scrollY)&&void 0!==S?S:n.pageYOffset},g=new i(l.scrollX,l.scrollY,l.windowWidth,l.windowHeight),u=new GB(c,g),w=null!==(O=t.foreignObjectRendering)&&void 0!==O&&O,Q={allowTaint:null!==(M=t.allowTaint)&&void 0!==M&&M,onclone:t.onclone,ignoreElements:t.ignoreElements,inlineImages:w,copyStyles:w},u.logger.debug("Starting document clone with size "+g.width+"x"+g.height+" scrolled to "+-g.left+","+-g.top),d=new uo(u,A,Q),h=d.clonedReferenceElement,h?[4,d.toIFrame(s,g)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return C=r.sent(),U=Sa(h)||Da(h)?o(h.ownerDocument):a(u,h),F=U.width,f=U.height,p=U.left,m=U.top,y=RB(u,h,t.backgroundColor),H={canvas:t.canvas,backgroundColor:y,scale:null!==(G=null!==(V=t.scale)&&void 0!==V?V:n.devicePixelRatio)&&void 0!==G?G:1,x:(null!==(P=t.x)&&void 0!==P?P:0)+p,y:(null!==(N=t.y)&&void 0!==N?N:0)+m,width:null!==(R=t.width)&&void 0!==R?R:Math.ceil(F),height:null!==(_=t.height)&&void 0!==_?_:Math.ceil(f)},w?(u.logger.debug("Document cloned, using foreign object rendering"),I=new OB(u,H),[4,I.render(h)]):[3,3];case 2:return v=r.sent(),[3,5];case 3:return u.logger.debug("Document cloned, element located at "+p+","+m+" with size "+F+"x"+f+" using computed rendering"),u.logger.debug("Starting DOM parsing"),E=ya(u,h),y===E.styles.backgroundColor&&(E.styles.backgroundColor=Us.TRANSPARENT),u.logger.debug("Starting renderer for element at "+H.x+","+H.y+" with size "+H.width+"x"+H.height),I=new KB(u,H),[4,I.render(E)];case 4:v=r.sent(),r.label=5;case 5:return(null===(J=t.removeContainer)||void 0===J||J)&&(uo.destroy(C)||u.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),u.logger.debug("Finished rendering"),[2,v]}}))}))},RB=function(A,t,e){var s=t.ownerDocument,r=s.documentElement?Cs(A,getComputedStyle(s.documentElement).backgroundColor):Us.TRANSPARENT,n=s.body?Cs(A,getComputedStyle(s.body).backgroundColor):Us.TRANSPARENT,i="string"===typeof e?Cs(A,e):null===e?Us.TRANSPARENT:4294967295;return t===s.documentElement?cs(r)?cs(n)?i:n:r:i};return PB}))},29304:function(A){"use strict";A.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADcAAAA3CAMAAACfBSJ0AAAAaVBMVEUAAACI8NqJ8NuI8NmH79eH8NqI8NqH79mH79mH79iJ79mH79mJ8NmJ8NuH8NuJ8duI8Nr////w/fum9OOX8t+P8dzS+vG09ei89+vh+/bL+O+09ujD9+2e8+Gt9eXL+e619uja+vTp/Phqi0yzAAAAEHRSTlMA3++QIM+/YFBAMICwr89/KSkvCgAAAYlJREFUSMe91tuOgjAQBuACguJhd6YnwRUU3/8ht7XVusS0TDfxv9PwZaaUtMP+nbbaF/HUTbWZq+8SFqVYv6pVnQRBrp5sWwIh5fZRzTAS9BULIKa+szWQs3PlyCkN20BGWsaqHFcx1uS4PWNfOa6IvBahLyPZiTPacC0JTnU9PnM7ymVOeHQRg+Zoo8e06xzqhfs5Tq7fY9R51ncq/COvNzTp4o7b/mCWQRuoYk4ganiTE+KQcMd37gdRfMSpUeU4ybGnuMBwojg1Ss+4pLjJPh9Y2vkPozdCBAZdwknE02NhGFhy3wGRg4OOhc8Pou6EKDz0zC9Px50wVVTY8VBOxh1MboXzJjQknOKIZ/iTs11p1PlOPQwMB0g6uOJrq8o0iVeIuQBDY/IWWMrBwF2vrkc+LD53ZX8/w4T1vaSc1x36dMT7QWqrtKTfK/JiT8SEo08GhxzX5N/TbY6z812Zc02b7OjODYU1uVzuXJc/R/qKBWHLTbWQ9UJZ7tgsm6qpi3gOVct8fgH9lKsDWaoqfAAAAABJRU5ErkJggg=="},37634:function(A){"use strict";A.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADcAAAA3CAMAAACfBSJ0AAAAZlBMVEUAAABjqv9lrP9kq/9kq/9jqv9gp/9kq/9kq/9kqv9iqv9kqv9jrP9jqf9lqv9gqv9kq//////s9f+LwP+ey/94tf+82v93tf9usP/1+v/F4P+n0P/Y6v+x1f+Bu/+Vxf/O5f/i7/+lr5j4AAAAEHRSTlMA39/vz5Agv6+AYEBQUDAwJ0+HHwAAAZNJREFUSMe91mtvgyAUxnGsut52OQ94QbTafv8vOWF1Z2s28bBk/zdNGn8BgkHUnzuXh2y9Yl++PKpjTpvanb6qpyIKWD59srecBOWvy2gzE8H7iDsSVgR2InFHHk5UPrNnSuisVJniSqX2Ke6gVJHidkpl9EvGNZPYmUuHOe1qgbP9gM9uVb3NmSEM1TVmdDpQN8VdHxAGQ6HpCp+uIq4PqLf8T1vdMNevOw00hh4aHdDZNWcARz/UAGPEVT+5CjD/4uxkU1yrMUgcM1wlzk71nela4q7z88w2ud7/DrMwzKiPuBZoloWBWXTfqYOmBTIjjY5WXQOYBTKrALfuDKAt7zi/7fW6o+vHCh8n4SjirAYu9K2LX2nMkcECmWGkqKMKX6dqG/h3Yc0x5Im1N2YxR6Ne5upPUT1uPnfbIZxhxvuhpq2Oj0N0vfD70DrPXE0iF2TDJ2LESW4GhxS3T/9On1Ocv9/lcpaFq6DcnZSvSBou/V6nXnMRe+N7aybY8nk07rRR5kf10HO5L7L1DuVZ3XsHd66rj15cZ+4AAAAASUVORK5CYII="},14689:function(A){"use strict";A.exports="data:image/png;base64,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"},67979:function(A,t,e){"use strict";var s=e(28551);A.exports=function(){var A=s(this),t="";return A.hasIndices&&(t+="d"),A.global&&(t+="g"),A.ignoreCase&&(t+="i"),A.multiline&&(t+="m"),A.dotAll&&(t+="s"),A.unicode&&(t+="u"),A.unicodeSets&&(t+="v"),A.sticky&&(t+="y"),t}},69479:function(A,t,e){"use strict";var s=e(44576),r=e(43724),n=e(62106),i=e(67979),a=e(79039),o=s.RegExp,B=o.prototype,c=r&&a((function(){var A=!0;try{o(".","d")}catch(c){A=!1}var t={},e="",s=A?"dgimsy":"gimsy",r=function(A,s){Object.defineProperty(t,A,{get:function(){return e+=s,!0}})},n={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var i in A&&(n.hasIndices="d"),n)r(i,n[i]);var a=Object.getOwnPropertyDescriptor(B,"flags").get.call(t);return a!==s||e!==s}));c&&n(B,"flags",{configurable:!0,get:i})}}]);