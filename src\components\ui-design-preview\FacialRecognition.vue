<template>
    <div class="bg">
        <header class="mui-fl-central sty1-header">
            <i class="popico-back header-back" @click="$router.back()" />
            Proof-of-Citizenship (PoC)
        </header>
        <div class="mui-fl-vert step-box">
            <i class="popico-face-id mui-fl-central icon" />
            <p class="c1">Facial Recognition (3/3)</p>
        </div>
        <h2 class="t1">Facial Recognition</h2>
        <p class="c2">
            We need to verify your face to make sure it's a real person. Please click the button
            below to continue.
        </p>
        <FacialSvg class="img" :themeMode="themeMode" :themeColor1="themeColor1" :themeColor2="themeColor2" :themeColor3="themeColor3" />

        <div class="mui-fl-central sty1-footer">
            <m-button class="width-3" type="primary" round size="large">Continue</m-button>
        </div>
    </div>
</template>
<script>
import { FacialSvg } from '@/components/ui-design-preview'
export default {
  name: 'FacialRecognition',
  components: { FacialSvg },
  props: {
    themeColor1: {
      type: String,
      required: true,
      default: () => {
        return '#005563'
      }
    },
    themeColor2: {
      type: String,
      required: true,
      default: () => {
        return '#CCDDE0'
      }
    },
    themeColor3: {
      type: String,
      required: true,
      default: () => {
        return '#E8F4F5'
      }
    },
    themeMode: {
      type: String,
      required: true,
      default: () => {
        return 'light'
      }
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/ui-design-preview/_facial_recognition.scss" scoped></style>
