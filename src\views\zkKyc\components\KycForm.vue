<template>
  <div>
    <m-dialog
      :show-close="!createAddressLoading"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="Confirm your program"
      :visible="dialogTableVisible"
      custom-class="sty1-dialog"
      width="610px"
      @close="close">
      <div>
        <div class="dialog_Name_nb">Program Name</div>
        <div>
          <MuiInput :closeflg="true" @input="input" :error="dialogwarning" v-model="dialoginp" class="inp" />
        </div>
        <div class="dialog_warning" v-if="dialogwarning && dialoginp">Numbers or letters only, no more than 50 characters.</div>
        <div class="dialog_warning" v-else-if="dialogwarning && !dialoginp">Please enter your program name.</div>
        <div class="progress-content">
          <h3>KYC Program</h3>
          <kyc-detail ref="detail" :list="confirmList" :select-country="selectCountry" :select-geolocation="selectGeolocation" :kycInfo="1" />
        </div>
        <div class="confirm-btns mui-fl-end">
          <m-button class="kycSave" :disabled="createAddressLoading" @click="close">Cancel</m-button>
          <m-button style="margin-left: 12px" class="kycCancel" @click="awaitConfirm" v-loading="createAddressLoading">{{ createAddressLoading ? '' : 'Confirm'}}</m-button>
        </div>
      </div>
    </m-dialog>
  </div>
</template>
<script>
import MuiInput from '@/components/mui-input/MuiInput.vue'
import KycDetail from './KycDetail.vue'
import * as types from '@/store/mutation-types'
import { Wallet } from 'ethers'
import { DirectSecp256k1HdWallet } from '@cosmjs/proto-signing'
import { AptosAccount } from 'aptos'
import { WalletContractV4 } from '@ton/ton'
import { mnemonicToPrivateKey, mnemonicNew } from '@ton/crypto'
export default {
  components: { MuiInput, KycDetail },
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: false
    },
    confirmList: {
      type: Array,
      required: true
    },
    selectCountry: {
      type: Object,
      required: true
    },
    selectGeolocation: {
      type: Object,
      required: true
    },
    form: {
      type: Object,
      required: true
    },
    pageType: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      dialogwarning: false,
      dialoginp: '',
      createAddressLoading: false
    }
  },
  computed: {
    formDetail () {
      return this.$store.state.kyc.kycFormDetail
    },
    duplicateForm () {
      return this.$store.state.kyc.duplicateForm
    },
    programName () {
      if (this.formDetail?.id) {
        return this.formDetail.programName
      }
      if (this.duplicateForm?.programName) {
        return this.duplicateForm.programName
      }
      return ''
    },
    kycMode () {
      return Number(this.$route.query.mode)
    }
  },
  mounted () {
    if (this.programName) {
      this.dialoginp = this.programName
    }
  },
  watch: {
    programName () {
      if (this.programName) {
        this.dialoginp = this.programName
      }
    }
  },
  methods: {
    input () {
      const zg = /^[0-9a-zA-Z ]*$/

      if (this.dialoginp.length > 50 || !this.dialoginp.length || !zg.test(this.dialoginp)) {
        this.dialogwarning = true
      } else {
        this.dialogwarning = false
      }
    },
    awaitConfirm () {
      this.input()
      if (this.dialogwarning) {
        return
      }
      this.createAddressLoading = true
      setTimeout(() => {
        this.confirm()
      }, 1000)
    },

    async confirm () {
      let data = {}
      if (this.pageType === 'Modify') {
        // 更新
        data = {
          ...this.form,
          id: this.formDetail.id,
          programName: this.dialoginp,
          status: 2,
          userId: new Date().getFullYear(),
          category: this.kycMode
        }
      } else {
        const wallets = {}
        const w = Wallet.createRandom()
        wallets.address = w.address
        const wallet = await DirectSecp256k1HdWallet.generate(12, {
          prefix: 'sei'
        })
        const neutronWallet = await DirectSecp256k1HdWallet.generate(12, {
          prefix: 'neutron'
        })
        const mnemonic = await mnemonicNew()
        const keyPair = await mnemonicToPrivateKey(mnemonic)
        const tonWallet = WalletContractV4.create({
          workchain: 0, publicKey: keyPair.publicKey
        }).address.toString({
          bounceable: false
          // testOnly: true // 测试网，如果是主网需要去掉该字段
        })
        const aptos = new AptosAccount().address().hex()
        const account = await wallet.getAccounts()
        const neutronAccount = await neutronWallet.getAccounts()
        wallets.seiAddress = account[0].address
        wallets.neutronAddress = neutronAccount[0].address
        wallets.aptosAddress = aptos
        // 创建/复制
        data = {
          ...this.form,
          walletAddressTon: tonWallet,
          walletAddressEvm: wallets.address,
          walletAddressSei: wallets.seiAddress,
          walletAddressNeutron: wallets.neutronAddress,
          walletAddressAptos: wallets.aptosAddress,
          programName: this.dialoginp,
          status: 2,
          userId: new Date().getFullYear(),
          category: this.kycMode
        }
      }

      // status: -1 delete, 1 apply, 2 create, 3 update, 4 expired
      const rp = await this.$api.request(this.pageType === 'Modify' ? 'kyc.updateKycProgram' : 'kyc.createKycProgram', data)
      this.createAddressLoading = false
      if (rp.code === ********) {
        this.$store.commit(types.SET_KYCFORM_DETAIL, null)
        this.$store.commit(types.SET_DUPLICATE_FORM, null)
        this.$store.commit(types.SET_KYC_TITLE, 'Create program')
        this.$router.push('/zk-kyc')
      }
    },
    close () {
      this.dialoginp = ''
      if (this.kycMode === 3) {
        this.selectGeolocation.avaliableList.length && this.$refs.detail.selectOption(this.selectGeolocation.avaliableList[0].childrens[0], 5)
      } else {
        this.selectCountry.avaliableList.length && this.$refs.detail.selectOption(this.selectCountry.avaliableList[0].childrens[0], 2)
        this.selectGeolocation.avaliableList.length && this.$refs.detail.selectOption(this.selectGeolocation.avaliableList[0].childrens[0], 5)
      }
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog_Name_nb {
  margin-bottom: 8px;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #738C8F;
}
.inp {
  width: 269px;
  margin-bottom: 4px;
}
.dialog_warning {
  margin-top: 6px;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #EE6969;
}

.progress-content {
  margin-top: 24px;
}

h3 {
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
  color: #002E33;
}

.confirm-btns {
  .kycSave, .kycCancel {
    width: 109px;
    height: 36px;
    background-color: #F7F7F7;
    color: #33585C;
    border-radius: 26px;
    border: none;
    margin-top: 24px;
  }
  .kycCancel ::v-deep{
    background-color: #A9E1D3;
    color: #002E33;
    margin-left: 12px;
    .el-loading-mask {
      background: transparent;
      transform: scale(0.5);
    }
  }
}
</style>
