<template>
  <div ref="container" class="main-container mui-fl-vert" style="min-width: 1200px;">
    <KytTransactionDetailTable
    :walletAddress="walletAddress"
    :coin="coin"
    :from-address="txnDetailFromAddress" :to-address="txnDetailToAddress"
    :decimals="decimals" :hash-list="tableHashList"
    :visible="tableVisible"
    :requestParams="requestParams"
    @close="tableVisible = false"
    />
    <div ref="downloadImgRender" class="download-img-renderer"></div>
    <div ref="graphContainer" class="graph-container mui-fl-vert mui-fl-central" @mousedown="startDrag" @mousemove="onDrag" @mouseup="stopDrag"
      @mouseleave="stopDrag">
      <div ref="graph" class="center-container">
        <div class="left-side item-top-left" v-for="(item, index) of topLeftTopItems" :key="`top-left-${index}`">
          <div class="graph-item mui-fl-vert" @click="openTxnDetailTable(item.address, walletAddress , item.tx_hash_list)">
            <div class="item-amount">
              {{ item.amount | thousandth }}
              {{ coin }}
            </div>
            <div class="mui-shr-0">
              <p class="item-label">{{ handleLabel(item.label) }}</p>
              <p class="item-address" v-if="item.address">{{ item.address | simplifyAddress(6 ,5) }}</p>
            </div>
            <div :class="['item-icon', 'mui-fl-central', item.type === 2 && 'warn']">
              <i :class="handleIcon(item)"></i>
            </div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" width="93" :height="74 + (topLeftTopItems.length - index - 1) * 80"
            :viewBox="`0 0 93 ${74 + (topLeftTopItems.length - index - 1) * 80}`" fill="none">
            <path :d="createSvg(74 + (topLeftTopItems.length - index - 1) * 80)" :fill="'#A9E1D3'"></path>
          </svg>
        </div>
        <div v-if="cenLeftItem" class="left-side item-cen-left">
          <div class="graph-item mui-fl-vert" @click="openTxnDetailTable(cenLeftItem.address, walletAddress , cenLeftItem.tx_hash_list)">
            <div class="item-amount">
              {{ cenLeftItem.amount | thousandth }}
              {{ coin }}
            </div>
            <div class="mui-shr-0">
              <p class="item-label">{{ handleLabel(cenLeftItem.label) }}</p>
              <p class="item-address" v-if="cenLeftItem.address">{{ cenLeftItem.address | simplifyAddress(6, 5) }}</p>
            </div>
            <div :class="['item-icon', 'mui-fl-central', cenLeftItem.type === 2 && 'warn']">
              <i :class="handleIcon(cenLeftItem)"></i>
            </div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" width="93" height="6" viewBox="0 0 93 6" fill="none">
            <path d="M90 3L85 0.113249V5.88675L90 3ZM90 2.5H45.5V3.5H90V2.5ZM45.5 2.5H0V3.5H45.5V2.5Z" fill="#A9E1D3" />
          </svg>
        </div>
        <div class="left-side item-bot-left" v-for="(item, index) of botLeftItems" :key="`bot-left-${index}`">
          <div class="graph-item mui-fl-vert" @click="openTxnDetailTable(item.address, walletAddress , item.tx_hash_list)">
            <div class="item-amount">
              {{ item.amount | thousandth }}
              {{ coin }}
            </div>
            <div class="mui-shr-0">
              <p class="item-label">{{ handleLabel(item.label) }}</p>
              <p class="item-address" v-if="item.address">{{ item.address | simplifyAddress(6, 5) }}</p>
            </div>
            <div :class="['item-icon', 'mui-fl-central', item.type === 2 && 'warn']">
              <i :class="handleIcon(item)"></i>
            </div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" width="93" :height="74 + (botLeftItems.length - index - 1) * 80"
            :viewBox="`0 0 93 ${74 + (botLeftItems.length - index - 1) * 80}`" fill="none">
            <g :transform="`scale(1, -1) translate(0, ${-74 - (botLeftItems.length - index - 1) * 80})`">
              <path :d="createSvg(74 + (botLeftItems.length - index - 1) * 80)" fill="#A9E1D3"></path>
            </g>
          </svg>
        </div>
        <div class="wallet mui-fl-col mui-fl-central">
          <div class="wallet-icon mui-fl-central">
            <i class="mico-wallet"></i>
          </div>
          <span>{{ walletAddress | simplifyAddress(6, 5) }}</span>
        </div>
        <div class="right-side item-top-right" v-for="(item, index) of topRightItems" :key="`top-right-${index}`">
          <div class="graph-item mui-fl-vert" @click="openTxnDetailTable(walletAddress, item.address, item.tx_hash_list)">
            <div :class="['item-icon', 'mui-fl-central', item.type === 2 && 'warn']">
              <i :class="handleIcon(item)"></i>
            </div>
            <div class="mui-shr-0">
              <p class="item-label">{{ handleLabel(item.label) }}</p>
              <p class="item-address" v-if="item.address">{{ item.address | simplifyAddress(6, 5) }}</p>
            </div>
            <div class="item-amount">
              {{ item.amount | thousandth }}
              {{ coin }}
            </div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" width="93" :height="74 + (topRightItems.length - index - 1) * 80"
            :viewBox="`0 0 93 ${74 + (topRightItems.length - index - 1) * 80}`" fill="none">
            <g :transform="`scale(1, -1) translate(0, ${-74 - (topRightItems.length - index - 1) * 80})`">
              <path :d="createSvg(74 + (topRightItems.length - index - 1) * 80)" fill="#A9E1D3"></path>
            </g>
          </svg>
        </div>
        <div v-if="cenRightItem" class="right-side item-cen-right">
          <div class="graph-item mui-fl-vert" @click="openTxnDetailTable(walletAddress, cenRightItem.address, cenRightItem.tx_hash_list)">
            <div :class="['item-icon', 'mui-fl-central', cenRightItem.type === 2 && 'warn']">
              <i :class="handleIcon(cenRightItem)"></i>
            </div>
            <div class="mui-shr-0">
              <p class="item-label">{{ handleLabel(cenRightItem.label) }}</p>
              <p class="item-address" v-if="cenRightItem.address">{{ cenRightItem.address | simplifyAddress(6, 5) }}</p>
            </div>
            <div class="item-amount">
              {{ cenRightItem.amount | thousandth }}
              {{ coin }}
            </div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" width="90" height="6" viewBox="0 0 90 6" fill="none">
            <path d="M90 3L85 0.113249V5.88675L90 3ZM85.5 2.5H45.5V3.5H85.5V2.5ZM45.5 2.5H0V3.5H45.5V2.5Z" fill="#A9E1D3"/>
          </svg>
        </div>
        <div class="right-side item-bot-right" v-for="(item, index) of botRightItems" :key="`bot-right-${index}`">
          <div class="graph-item mui-fl-vert" @click="openTxnDetailTable(walletAddress, item.address, item.tx_hash_list)">
            <div :class="['item-icon', 'mui-fl-central', item.type === 2 && 'warn']">
              <i :class="handleIcon(item)"></i>
            </div>
            <div class="mui-shr-0">
              <p class="item-label">{{ handleLabel(item.label) }}</p>
              <p class="item-address" v-if="item.address">{{ item.address | simplifyAddress(6, 5) }}</p>
            </div>
            <div class="item-amount">
              {{ item.amount | thousandth }}
              {{ coin }}
            </div>
          </div>
          <svg xmlns="http://www.w3.org/2000/svg" width="93" :height="74 + (index) * 80"
            :viewBox="`0 0 93 ${74 + (index) * 80}`" fill="none">
            <path :d="createSvg(74 + (index) * 80)" :fill="'#A9E1D3'"></path>
          </svg>
        </div>
      </div>
      <div class="toolbar">
        <button @click="zoomIn"><i class="mico-amplify"></i></button>
        <button @click="zoomOut"><i class="mico-reduce"></i></button>
        <button @click="recenter"><i class="mico-center"></i></button>
        <button @click="downloadGraph"><i class="mico-download1"></i></button>
        <button @click="toggleFullscreen"><i class="mico-full-screen"></i></button>
        <button @click="cleanData"><i class="mico-clean"></i></button>
        <button @click="revertData"><i class="mico-reduction"></i></button>
        <button v-if="graphTotalPages > 1" @click="prevGraph"><i class="mico-chevron-left-double"></i></button>
        <button v-if="graphTotalPages > 1" @click="nextGraph"><i class="mico-chevron-right-double"></i></button>
      </div>
    </div>

    <div v-if="dataFilterExpanded" class="filter-data-con">
      <button class="fold-button" @click="dataFilterExpanded = false"><i class="mico-chevron-right-double"></i></button>
      <div class="date-picker">
        <p class="title">Date</p>
        <m-date-picker class="sty2-date-editor mui-shr-0" popper-class="sty1-date-popper width-1 " prefix-icon="mico-date"
          v-model="picker" value-format="timestamp" format="yyyy.MM.dd" type="daterange" range-separator="-"
          start-placeholder="start" end-placeholder="end" unlink-panels :clearable="false" :editable="false"
          :picker-options="pickerOptions"
          @change="handleChangeDate" :default-time="['00:00:00', '23:59:59']">
        </m-date-picker>
      </div>
      <div class="amount-range">
        <p class="title">Amount Range</p>
        <div class="mui-fl-btw">
          <m-input v-model="query.minAmount" type="number" placeholder="Minimum"
        class="sty1-input-search width-265 marg-l16" @keyup.native.enter="filterDataByConditions" @change="filterDataByConditions" @clear="filterDataByConditions" clearable></m-input>
        <m-input v-model="query.maxAmount" type="number" placeholder="Maximum"
        class="sty1-input-search width-265 marg-l16" @keyup.native.enter="filterDataByConditions" @change="filterDataByConditions" @clear="filterDataByConditions" clearable></m-input>
        </div>
      </div>
      <div class="data-filter">
        <p class="title">Filter</p>
        <div class="mui-fl-btw">
          <m-select v-model="query.filterTxnType" class="sty1-select width-265 padding-1" @change="filterDataByConditions" popper-class="sty1-popper">
            <m-option v-for="item in filterTxnOptions" :key="item.value" :label="item.label" :value="item.value">
            </m-option>
          </m-select>
          <m-select v-model="query.filterAddressType" class="sty1-select width-265 padding-1" @change="filterDataByConditions" popper-class="sty1-popper">
            <m-option v-for="item in filterAddressOptions" :key="item.value" :label="item.label" :value="item.value">
            </m-option>
          </m-select>
        </div>
      </div>
      <div class="analysis-con">
        <span class="analysis-title">Analysis</span>
        <span class="analysis-address">{{ walletAddress }}</span>
      </div>
      <m-input v-model="query.search" placeholder="Search by address / label" prefix-icon="mico-search"
        class="sty1-input-search width-1 marg-l16" @keyup.native.enter="filterDataByConditions" @change="filterDataByConditions" clearable></m-input>
      <div v-if="inTxns.length" class="in-txns-table table-wrap mui-fl-col mui-fl-btw">
        <m-table :data="paginatedData(inTxns, inTxnsCurPage)" @sort-change="handleSortChangeIntxns" class="sty2-table">
          <m-table-column label="Show" max-width="50">
            <template #default="{ row }">
              <m-checkbox v-model="row.selected" @change="handleInCheckbox(row)" class="sty1-checkbox" />
            </template>
          </m-table-column>
          <m-table-column label="Sender" min-width="295">
            <template #default="{ row }">
              <p class="sender-add">{{ row.address | simplifyAddress(6, 5) }}</p>
              <p class="sender-lab">{{ handleLabel(row.label) }}</p>
            </template>
          </m-table-column>
          <m-table-column prop="tx_hash_list.length" sortable="custom" label="Txn" min-width="76">
            <template #default="{ row }">
              <p class="txns-count" @click="openTxnDetailTable(row.address, walletAddress ,row.tx_hash_list)">{{ row.tx_hash_list.length }}</p>
            </template>
          </m-table-column>
          <m-table-column prop="amount" :label="coin" sortable min-width="90">
            <template #default="{ row }">
              {{ row.amount | thousandth }}
            </template>
          </m-table-column>
        </m-table>
        <div class="table-pagination mui-fl-end">
          <m-pagination
            v-show="inTxns.length > 0"
            class="sty1-pagination sty4-cell"
            background
            :current-page.sync="inTxnsCurPage"
            :page-size="itemsPerPage"
            layout="prev, pager, next"
            :total="inTxns.length"
          >
          </m-pagination>
        </div>
      </div>
      <div v-if="outTxns.length" class="out-txns-table table-wrap mui-fl-col mui-fl-btw">
        <m-table :data="paginatedData(outTxns, outTxnsCurPage)" @sort-change="handleSortChangeOuttxns" class="sty2-table">
          <m-table-column label="Show" max-width="50">
            <template #default="{ row }">
              <m-checkbox v-model="row.selected" @change="handleOutCheckbox(row)" class="sty1-checkbox" />
            </template>
          </m-table-column>
          <m-table-column label="Recipient" min-width="295">
            <template #default="{ row }">
              <p class="sender-add">{{ row.address | simplifyAddress(6, 5) }}</p>
              <p class="sender-lab">{{ handleLabel(row.label) }}</p>
            </template>
          </m-table-column>
          <m-table-column label="Txn" sortable="custom" min-width="76">
            <template #default="{ row }">
              <p class="txns-count" @click="openTxnDetailTable(walletAddress, row.address, row.tx_hash_list)">{{ row.tx_hash_list.length }}</p>
            </template>
          </m-table-column>
          <m-table-column prop="amount" :label="coin" sortable min-width="90">
            <template #default="{ row }">
              {{ row.amount | thousandth }}
            </template>
          </m-table-column>
        </m-table>
        <div class="table-pagination mui-fl-end">
          <m-pagination
            v-show="outTxns.length > 0"
            class="sty1-pagination sty4-cell"
            background
            :current-page.sync="outTxnsCurPage"
            :page-size="itemsPerPage"
            layout="prev, pager, next"
            :total="outTxns.length"
          >
          </m-pagination>
        </div>
      </div>
    </div>

    <button v-if="!dataFilterExpanded" class="expand-button" @click="dataFilterExpanded = true"><i class="mico-chevron-left-double"></i></button>
  </div>
</template>
<script>
import html2canvas from 'html2canvas'
import KytTransactionDetailTable from '@/components/kyt_transaction_detail_table'
export default {
  components: { KytTransactionDetailTable },
  name: 'KytTransactionGraph',
  props: {
    transactionsInvestigationData: {
      type: Object,
      required: true,
      default: () => {
        return {
          in: [],
          out: [],
          page: 1,
          total_pages: 1,
          transactions_on_page: 1
        }
      }
    },
    walletAddress: {
      type: String,
      required: true,
      default: () => {
        return ''
      }
    },
    graphStartTime: {
      type: Number,
      required: 0
    },
    graphEndTime: {
      type: Number,
      required: 0
    },
    graphFilterStartTime: {
      type: Number,
      required: false
    },
    graphFilterEndTime: {
      type: Number,
      required: false
    },
    requestParams: {
      type: String,
      required: true
    },
    coin: {
      type: String,
      required: true,
      default: () => {
        return 'ETH'
      }
    },
    decimals: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      inTxns: [],
      outTxns: [],
      originInTxns: [],
      originOutTxns: [],
      scale: 1,
      dragging: false,
      lastX: 0,
      lastY: 0,
      offsetX: 0,
      offsetY: 0,
      startTime: null,
      endTime: null,
      filterStartTime: null,
      filterEndTime: null,
      filterTxnOptions: [
        {
          label: 'All txs',
          value: 0
        },
        {
          label: 'Only incoming',
          value: 1
        },
        {
          label: 'Only outgoing',
          value: 2
        }
      ],
      filterAddressOptions: [
        {
          label: 'All addresses',
          value: 0
        },
        {
          label: 'Entity address',
          value: 3
        },
        {
          label: 'Malicious address',
          value: 2
        },
        {
          label: 'Unknown address',
          value: 1
        }
      ],
      query: {
        search: '',
        filterTxnType: 0,
        filterAddressType: 0,
        minAmount: null,
        maxAmount: null
      },
      inTxnsCurPage: 1,
      outTxnsCurPage: 1,
      itemsPerPage: 5,
      dataFilterExpanded: true,
      graphCurPage: 1,
      graphTotalPages: 1,
      tableVisible: false,
      tableHashList: [],
      txnDetailFromAddress: '',
      txnDetailToAddress: '',
      isZooming: false
    }
  },
  watch: {
    transactionsInvestigationData (newVal, oldVal) {
      if (newVal !== oldVal) {
        this.inTxns = this.transactionsInvestigationData.in
        this.outTxns = this.transactionsInvestigationData.out
        this.inTxns.forEach((item) => {
          this.$set(item, 'selected', true)
        })
        this.outTxns.forEach((item) => {
          this.$set(item, 'selected', true)
        })
        this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
        this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
        this.graphCurPage = this.transactionsInvestigationData.page
        this.graphTotalPages = this.transactionsInvestigationData.total_pages
      }
    },
    graphStartTime () {
      this.startTime = this.graphStartTime * 1000
    },
    graphEndTime () {
      this.endTime = this.graphEndTime * 1000
    },
    graphFilterStartTime () {
      this.filterStartTime = this.graphFilterStartTime
    },
    graphFilterEndTime () {
      this.filterEndTime = this.graphFilterEndTime
    }
  },
  mounted () {
    this.inTxns = this.transactionsInvestigationData.in
    this.outTxns = this.transactionsInvestigationData.out
    this.inTxns.forEach((item) => {
      this.$set(item, 'selected', true)
    })
    this.outTxns.forEach((item) => {
      this.$set(item, 'selected', true)
    })
    this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
    this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
    this.graphCurPage = this.transactionsInvestigationData.page
    this.graphTotalPages = this.transactionsInvestigationData.total_pages
    this.startTime = this.graphStartTime * 1000
    this.endTime = this.graphEndTime * 1000
    this.filterStartTime = this.graphFilterStartTime
    this.filterEndTime = this.graphFilterEndTime

    this.$refs.graphContainer.addEventListener('wheel', this.onWheel)
  },
  computed: {
    picker: {
      get () {
        return this.filterStartTime || this.startTime
          ? [this.filterStartTime || this.startTime, this.filterEndTime || this.endTime]
          : ''
      },
      set (val) {
        this.filterStartTime = val ? new Date(val[0]).getTime() : ''
        this.filterEndTime = val ? new Date(val[1]).getTime() : ''
      }
    },
    pickerOptions () {
      const startDate = new Date(this.graphStartTime * 1000)

      startDate.setDate(startDate.getDate() - 1)

      const startTime = startDate.getTime()

      const endDate = new Date(this.graphEndTime * 1000)

      endDate.setHours(23, 59, 59, 999)

      const endTime = endDate.getTime()
      return {
        disabledDate (time) {
          return time.getTime() < startTime || time.getTime() > endTime
        }
      }
    },
    filteredInTxns () {
      return this.inTxns.filter(x => x.selected)
    },
    filteredOutTxns () {
      return this.outTxns.filter(x => x.selected)
    },
    topLeftTopItems () {
      return this.filteredInTxns.length >= 2 ? this.filteredInTxns.slice(0, Math.floor(this.filteredInTxns.length / 2)) : []
    },
    cenLeftItem () {
      if (this.filteredInTxns.length) {
        return this.filteredInTxns.length === 1 ? this.filteredInTxns[0] : this.filteredInTxns[Math.floor(this.filteredInTxns.length / 2)]
      } else {
        return null
      }
    },
    botLeftItems () {
      return this.filteredInTxns.length >= 3 ? this.filteredInTxns.slice(Math.floor(this.filteredInTxns.length / 2) + 1, this.filteredInTxns.length) : []
    },
    topRightItems () {
      return this.filteredOutTxns.length >= 2 ? this.filteredOutTxns.slice(0, Math.floor(this.filteredOutTxns.length / 2)) : []
    },
    cenRightItem () {
      if (this.filteredOutTxns.length) {
        return this.filteredOutTxns.length === 1 ? this.filteredOutTxns[0] : this.filteredOutTxns[Math.floor(this.filteredOutTxns.length / 2)]
      } else {
        return null
      }
    },
    botRightItems () {
      return this.filteredOutTxns.length >= 3 ? this.filteredOutTxns.slice(Math.floor(this.filteredOutTxns.length / 2) + 1, this.filteredOutTxns.length) : []
    }
  },
  beforeDestroy () {
    this.$refs.graphContainer.removeEventListener('wheel', this.onWheel)
  },
  methods: {
    onWheel (e) {
      e.preventDefault()

      const rect = this.$refs.graph.getBoundingClientRect()
      const mouseX = e.clientX - rect.left
      const mouseY = e.clientY - rect.top

      // 计算缩放量
      const scaleAmount = 0.05
      const minScale = 0.03
      const maxScale = 3

      let newScale = e.deltaY < 0 ? this.scale + scaleAmount : this.scale - scaleAmount
      newScale = Math.max(minScale, Math.min(maxScale, newScale))

      const scaleRatio = newScale / this.scale
      this.offsetX = (1 - scaleRatio) * mouseX + scaleRatio * this.offsetX
      this.offsetY = (1 - scaleRatio) * mouseY + scaleRatio * this.offsetY

      this.scale = newScale

      this.applyTransform()
    },
    // 画线
    createSvg (height) {
      const staticPart = `M90 ${height - 3}L85 ${height - 5.887}V${height}L90 ${height - 3}ZM85.5 ${height - 2.5}H75.5V${height - 3.5}H85.5V${height - 2.5}Z`

      const dynamicPart = `M45 ${height - 33}V31H46V${height - 33}H45ZM15.5 1.5H0V0.5H15.5V1.5ZM75.5 ${height - 2.5}C58.6553 ${height - 2.5} 45 ${height - 17.155} 45 ${height - 33}H46C46 ${height - 16.708} 59.2076 ${height - 3.5} 75.5 ${height - 3.5}V${height - 2.5}ZM45 31C45 14.7076 31.7924 1.5 15.5 1.5V0.5C32.3447 0.5 46 14.1553 46 31H45Z`

      return staticPart + dynamicPart
    },
    // 处理接口返回的Label
    handleLabel (label) {
      if (label.includes(',')) {
        const parts = label.split(',')
        return parts[parts.length - 1]
      } else {
        return label || 'Normal'
      }
    },
    handleIcon (item) {
      if (item.type === 2) {
        return 'mico-warning1'
      } else if (item.label === '') {
        return 'mico-that-person'
      } else {
        return 'mico-on-chain'
      }
    },
    async downloadGraph () {
      try {
        const downloading = this.$message({
          message: 'Downloading...',
          iconClass: 'mcico-success',
          customClass: 'sty4-message',
          duration: 0,
          offset: 32,
          center: true
        })
        setTimeout(async () => {
          const height = Math.max(this.filteredInTxns.length, this.filteredOutTxns.length) *
          (this.topLeftTopItems.length > this.botLeftItems.length || this.topRightItems.length > this.botRightItems.length ? 90 : 80)
          const element = this.$refs.graphContainer
          const clone = element.cloneNode(true)
          clone.removeChild(clone.children[1])
          clone.setAttribute('style', `height: ${height || 270}px !important`)
          if (this.$refs.downloadImgRender.children.length) {
            this.$refs.downloadImgRender.innerHTML = ''
          }
          this.$refs.downloadImgRender.appendChild(clone)
          this.$refs.downloadImgRender.firstChild.firstChild.setAttribute('style', 'transform: scale(1) translate3d(0px, 0px, 0px);')
          const canvas = await html2canvas(clone, {
            backgroundColor: '#f2f7f7'
          })
          this.$refs.downloadImgRender.removeChild(clone)
          const a = document.createElement('a')

          a.href = canvas.toDataURL('image/png')

          a.download = 'captured-image.png'

          downloading.close()
          a.click()
          this.$message({
            message: 'Success',
            iconClass: 'mcico-success',
            customClass: 'sty4-message',
            duration: 3000,
            offset: 32,
            center: true
          })
        })
      } catch (err) {
        console.log(err)
      }
    },
    // 放大图片
    zoomIn () {
      this.scale += 0.05
      this.applyTransform()
    },
    // 缩小图片
    zoomOut () {
      this.scale = Math.max(this.scale - 0.05, 0.03)
      this.applyTransform()
    },
    // 重新定位到中间
    recenter () {
      const height = Math.max(this.filteredInTxns.length, this.filteredOutTxns.length) *
        (this.topLeftTopItems.length > this.botLeftItems.length || this.topRightItems.length > this.botRightItems.length ? 90 : 80)
      if (height > 1020) {
        this.scale = 1020 / height
      } else {
        this.scale = 1
      }
      this.lastX = 0
      this.lastY = 0
      this.offsetX = 0
      this.offsetY = 0
      this.applyTransform()
    },
    // 切换成全屏
    toggleFullscreen () {
      const elem = this.$refs.container
      document.addEventListener('fullscreenchange', this.onFullscreenChange)

      if (!document.fullscreenElement) {
        if (this.dataFilterExpanded) {
          this.dataFilterExpanded = false
        }

        elem.requestFullscreen().catch(console.error)
      } else {
        document.exitFullscreen().then(() => {
          elem.scrollIntoView({ behavior: 'smooth' })
        }).catch(console.error)
      }
    },
    onFullscreenChange () {
      if (!document.fullscreenElement) {
        this.$refs.container.scrollIntoView({ behavior: 'smooth' })
      }
    },
    applyTransform () {
      this.$refs.graph.style.transform = `scale(${this.scale}) translate3d(${this.offsetX}px, ${this.offsetY}px, 0)`
    },
    startDrag (e) {
      this.dragging = true
      this.lastX = e.clientX
      this.lastY = e.clientY
    },
    onDrag (e) {
      if (!this.dragging) return

      const dx = e.clientX - this.lastX
      const dy = e.clientY - this.lastY

      this.offsetX += dx
      this.offsetY += dy

      this.applyTransform()

      this.lastX = e.clientX
      this.lastY = e.clientY
    },
    stopDrag () {
      this.dragging = false
    },
    handleChangeDate () {
      this.graphCurPage = 1
      this.$emit('paginateTxnInvestigation', this.graphCurPage, this.picker[0], this.picker[1])
    },
    paginatedData (data, currentPage) {
      const start = (currentPage - 1) * this.itemsPerPage
      const end = currentPage * this.itemsPerPage
      return data.slice(start, end)
    },
    filterDataByConditions () {
      // Filter Txn Type
      switch (this.query.filterTxnType) {
        case 0:
        default:
          this.inTxns = this.transactionsInvestigationData.in
          this.outTxns = this.transactionsInvestigationData.out
          this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
          this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
          break
        case 1:
          this.inTxns = this.transactionsInvestigationData.in
          this.outTxns = []
          this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
          this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
          break
        case 2:
          this.outTxns = this.transactionsInvestigationData.out
          this.inTxns = []
          this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
          this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
          break
      }

      this.filterDataByAddressType()
      this.filterDataByAmountRange()
      this.filterDataByAddressOrLabel()
    },
    filterDataByAddressOrLabel () {
      const keyword = this.query.search
      if (keyword.trim().length) {
        if (this.inTxns.length) {
          this.inTxns = this.inTxns.filter(x => x.address.toLowerCase().includes(keyword.trim().toLowerCase()) || x.label.toLowerCase().includes(keyword.trim().toLowerCase()))
          this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
        }

        if (this.outTxns.length) {
          this.outTxns = this.outTxns.filter(x => x.address.toLowerCase().includes(keyword.trim().toLowerCase()) || x.label.toLowerCase().includes(keyword.trim().toLowerCase()))
          this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
        }
      }
    },
    filterDataByAddressType () {
      const addressType = this.query.filterAddressType
      if (addressType !== 0) {
        if (this.inTxns.length) {
          this.inTxns = this.inTxns.filter(x => x.type === addressType)
          this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
        }

        if (this.outTxns.length) {
          this.outTxns = this.outTxns.filter(x => x.type === addressType)
          this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
        }
      }
    },
    filterDataByAmountRange () {
      const minAmount = this.query.minAmount
      const maxAmount = this.query.maxAmount
      if (minAmount || maxAmount) {
        if (minAmount && maxAmount) {
          if (this.inTxns.length) {
            this.inTxns = this.inTxns.filter(x => x.amount >= minAmount && x.amount <= maxAmount)
            this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
          }

          if (this.outTxns.length) {
            this.outTxns = this.outTxns.filter(x => x.amount >= minAmount && x.amount <= maxAmount)
            this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
          }
        } else if (minAmount) {
          if (this.inTxns.length) {
            this.inTxns = this.inTxns.filter(x => x.amount >= minAmount)
            this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
          }

          if (this.outTxns.length) {
            this.outTxns = this.outTxns.filter(x => x.amount >= minAmount)
            this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
          }
        } else {
          if (this.inTxns.length) {
            this.inTxns = this.inTxns.filter(x => x.amount <= maxAmount)
            this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
          }

          if (this.outTxns.length) {
            this.outTxns = this.outTxns.filter(x => x.amount <= maxAmount)
            this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
          }
        }
      }
    },
    cleanData () {
      this.inTxns.forEach((item) => {
        this.$set(item, 'selected', false)
      })
      this.outTxns.forEach((item) => {
        this.$set(item, 'selected', false)
      })
      this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
      this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
    },
    revertData () {
      this.inTxns.forEach((item) => {
        this.$set(item, 'selected', true)
      })
      this.outTxns.forEach((item) => {
        this.$set(item, 'selected', true)
      })
      this.originInTxns = JSON.parse(JSON.stringify(this.inTxns))
      this.originOutTxns = JSON.parse(JSON.stringify(this.outTxns))
    },
    openTxnDetailTable (fromAddress, toAddress, hashList) {
      this.tableHashList = hashList
      this.txnDetailFromAddress = fromAddress
      this.txnDetailToAddress = toAddress
      this.tableVisible = true
    },
    nextGraph () {
      if (this.graphCurPage + 1 <= this.graphTotalPages) {
        this.graphCurPage += 1
        this.$emit('paginateTxnInvestigation', this.graphCurPage, null, null)
      } else {
        this.$message({
          message: 'No more pages available.',
          type: 'warning'
        })
      }
    },
    prevGraph () {
      if (this.graphCurPage - 1 >= 1) {
        this.graphCurPage -= 1
        this.$emit('paginateTxnInvestigation', this.graphCurPage, null, null)
      } else {
        this.$message({
          message: 'No previous pages available.',
          type: 'warning'
        })
      }
    },
    handleSortChangeIntxns ({ prop, order }) {
      if (prop === 'amount') {
        if (order === 'ascending') {
          this.inTxns.sort((a, b) => (Number(a.amount) > Number(b.amount) ? 1 : -1))
        } else if (order === 'descending') {
          this.inTxns.sort((a, b) => (Number(a.amount) < Number(b.amount) ? 1 : -1))
        } else {
          this.inTxns = JSON.parse(JSON.stringify(this.originInTxns))
        }
      } else {
        if (order === 'ascending') {
          this.inTxns.sort((a, b) => (a.tx_hash_list.length > b.tx_hash_list.length ? 1 : -1))
        } else if (order === 'descending') {
          this.inTxns.sort((a, b) => (a.tx_hash_list.length < b.tx_hash_list.length ? 1 : -1))
        } else {
          this.inTxns = JSON.parse(JSON.stringify(this.originInTxns))
        }
      }
    },
    handleSortChangeOuttxns ({ prop, order }) {
      if (prop === 'amount') {
        if (order === 'ascending') {
          this.outTxns.sort((a, b) => (Number(a.amount) > Number(b.amount) ? 1 : -1))
        } else if (order === 'descending') {
          this.outTxns.sort((a, b) => (Number(a.amount) < Number(b.amount) ? 1 : -1))
        } else {
          this.outTxns = JSON.parse(JSON.stringify(this.originOutTxns))
        }
      } else {
        if (order === 'ascending') {
          this.outTxns.sort((a, b) => (a.tx_hash_list.length > b.tx_hash_list.length ? 1 : -1))
        } else if (order === 'descending') {
          this.outTxns.sort((a, b) => (a.tx_hash_list.length < b.tx_hash_list.length ? 1 : -1))
        } else {
          this.outTxns = JSON.parse(JSON.stringify(this.originOutTxns))
        }
      }
    },
    handleInCheckbox (row) {
      this.originInTxns.some((item) => {
        if (item.address === row.address) {
          item.selected = row.selected
          return true
        }
      })
    },
    handleOutCheckbox (row) {
      this.originOutTxns.some((item) => {
        if (item.address === row.address) {
          item.selected = row.selected
          return true
        }
      })
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/_kyt_txn_graph.scss" scoped></style>
