import Vue from 'vue'
import store from '@/store'
import VueRouter from 'vue-router'
import LeftTopFrame from '@/components/left-top-frame'

Vue.use(VueRouter)

// 解决重复点击路由报错的BUG
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push (location) {
  return originalPush.call(this, location).catch((err) => err)
}

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/sign/Login.vue')
  },
  {
    path: '/signup',
    name: 'CreatAccount',
    component: () => import('@/views/sign/CreatAccount.vue')
  },
  {
    path: '/reset-password',
    name: 'ResetPassword',
    component: () => import('@/views/sign/ForgotPassword.vue')
  },
  {
    path: '/forgot-password/:id(step1|step2)',
    name: 'ForgotPassword',
    component: () => import('@/views/sign/ForgotPassword.vue')
  },
  {
    path: '/set-password/:id(step1|step2)',
    name: 'ForgotPassword',
    component: () => import('@/views/sign/ForgotPassword.vue')
  },
  {
    path: '/expired-link',
    name: 'ExpiredLink',
    component: () => import('@/views/sign/ExpiredLink.vue')
  },
  {
    path: '/',
    component: LeftTopFrame,
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/Dashboard.vue')
      },
      {
        path: '/statistics',
        name: 'Statistics',
        component: () => import('@/views/Dashboard/Statistics.vue')
      },
      {
        path: '/dashboard/account-detail',
        name: 'AccountDetail',
        component: () => import('@/views/Dashboard/AccountDetail.vue')
      },
      {
        path: '/dashboard/account-kyt',
        name: 'AccountKyt',
        component: () => import('@/views/Dashboard/AccountKyt.vue')
      },
      {
        path: '/distributionList',
        name: 'Distribution',
        component: () => import('@/views/distribution/distribution.vue')
      },
      // {
      //   path: '/zk-kyc',
      //   name: 'zkKYC',
      //   component: () => import('@/views/kyc/ZkKYC.vue')
      // },
      {
        path: '/integration',
        name: 'Integration',
        component: () => import('@/views/integration/Integration.vue')
      },
      {
        path: '/uiDesign',
        name: 'Uidesign',
        component: () => import('@/views/integration/Uidesign.vue')
      },
      {
        path: '/activation',
        name: 'Activation',
        component: () => import('@/views/Activation.vue')
      },
      {
        path: '/api',
        name: 'API',
        component: () => import('@/views/resources/API.vue')
      },
      {
        path: '/support',
        name: 'Support',
        component: () => import('@/views/resources/Support.vue')
      },
      {
        path: '/zk-kyc',
        name: 'zkKYC',
        component: () => import('@/views/zkKyc/ZkKyc.vue')
      },
      {
        path: '/zk-kyc/zkkyc-form',
        name: 'zkkycForm',
        component: () => import('@/views/zkKyc/ZkKycForm.vue')
      }
    ],
    meta: {
      // 此字段表明该路由及其所有子路由都需要进行登录校验
      auth: true
    }
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

const isExpiredCheck = lastTime => {
  const now = Date.now()

  return typeof lastTime !== 'number' || (now - lastTime) > 0.5 * 3600000
}

router.beforeEach((to, from, next) => {
  const au = store.state.auth
  if (to.matched.some(record => record.meta.auth)) {
    if (!au.user) {
      store.commit('CLEAN_USER')
      next('/login')
    }
  }
  if ((!au.user || isExpiredCheck(au.user.timestamp)) && ['/forgot-password/step2', '/reset-password'].includes(to.path)) {
    next('/login')
  }
  // next()
  if (['/', '/dashboard', '/zk-kyc/zkkyc-form', '/zk-kyc', '/login', '/signup', '/forgot-password/step1', '/forgot-password/step2', '/reset-password',
    '/set-password/step1', '/set-password/step2', '/integration', '/dashboard/account-detail', '/dashboard/account-kyt', '/uiDesign', '/expired-link',
    '/statistics', '/distributionList'].includes(to.path)) {
    next()
  } else {
    Vue.prototype.$message({
      message: 'Coming soon',
      iconClass: 'mico-lightTip',
      customClass: 'sty1-message',
      duration: 3000,
      offset: 32,
      center: true
    })
  }
})

export default router
