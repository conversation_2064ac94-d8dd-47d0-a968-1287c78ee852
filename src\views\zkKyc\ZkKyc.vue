<template>
  <div style="padding: 0px 60px 0;">
    <div class="pg-title1 mui-fl-vert" v-if="!isSearch">
      <i class="mcico-active-activation"></i>
      Configuration
    </div>
    <div class="Search-title mui-fl-vert" v-else>
      <i class="mico-Fallback icon" @click="back()"></i>
      <span class="fg"></span>
      <span>{{ titleTxt }}</span>
    </div>
    <div class="top_function">
      <div class="top_title">Select verification configuration and create a program</div>
      <div class="top_selected">Selected: {{ isSelect1 && isSelect2 ? 2 : isSelect1 || isSelect2 ? 1 : 0}}</div>
      <div class="mui-fl-vert">
        <ul class="mui-fl-vert mui-fl-wrap">
          <m-button
            class="mui-fl-central network-tag"
            :class="{ 'tag-checked': isSelect1, 'tag-checked-hover': isSelect1 }"
            @click="isSelect1 = !isSelect1"
          >
            <p>Identity verification</p>
          </m-button>
          <m-button
            class="mui-fl-central network-tag"
            :class="{ 'tag-checked': isSelect2, 'tag-checked-hover': isSelect2 }"
            @click="isSelect2 = !isSelect2"
          >
            <p>Location</p>
          </m-button>
        </ul>
        <!-- <m-button class="top_button">Identity verification</m-button>
        <m-button class="top_button">zkGeolocation</m-button> -->
      </div>
      <m-button class="sty5-button" @click="createProgram" :disabled="!isSelect1 && !isSelect2"><i class="mico-creat"></i> Create Program</m-button>
    </div>
    <div class="mui-fl-vert mui-fl-btw">
      <m-checkbox class="sty2-checkbox" v-model="checked1" @change="handleCurrentChange()">Pin current apply</m-checkbox>
      <MuiInput @enter="search" placeholder='Search for program name' v-model="keyword" class="inp" :closeflg="true" :searchflg="true" @search="search" @clear="clear" />
      <!-- <div class="redivision"></div> -->
      <!-- <span> -->

        <!-- <m-dropdown v-if="!tagindex.val" trigger="click" @command="handleCommand">
          <m-button class="dropdownbut recorded_but mui-fl-vert">
            <span>Filter Highest level</span>
            <i class="mico-fold"></i>
          </m-button>
          <m-dropdown-menu slot="dropdown" class="extendlist recorded_list">
            <m-dropdown-item v-for="(i, d) of kycLevel" :key="d" :command="i">{{i.levelName}}</m-dropdown-item>
          </m-dropdown-menu>
        </m-dropdown>
        <m-tag
          v-else
          class="recorded_tag"
          :disable-transitions="true"
          @click="tagclose"
          >
          <div class="mui-fl-vert">
            <div>{{tagindex.val}}</div>
            <i class="mico-close"></i>
          </div>
        </m-tag> -->
      <!-- </span> -->
    </div>
    <m-divider class="sty1-divider"></m-divider>

    <div class="recorded_table-wrap mui-fl-col mui-fl-btw">
      <m-table
        v-if="dataflag"
        :ref="'table'"
        :class="['recorded_table', (checked1 || checked2) && 'table_topping']"
        :data="tableData"
        @cell-click="expandDetails"
        @cell-mouse-enter="cellMouseEnter"
        @cell-mouse-leave="cellMouseLeave"
        :row-class-name=rowClassname
        :cell-class-name=cellClassname
        @sort-change="sortchange"
        :default-sort = "{prop: 'lastUpdateTime', order: 'descending'}"
      >
        <m-table-column prop="date" label="Program Name" min-width="110px">
          <template #default="{ row }">
            <m-popover
              :offset="10"
              popper-class="NotRecord_popover Record_popover"
              :disabled="(row.offsetWidth + 1) < programNameWidth"
              placement="top-start"
              trigger="hover"
              :content="row.programName">
              <div slot="reference" :ref="'programName'">
                <span :ref="'programText'" class="spanprogramName">
                  {{row.programName}}
                </span>
              </div>
            </m-popover>
          </template>
        </m-table-column>

        <!-- <m-table-column sortable="custom" prop="highestLevel" label="Highest Level" min-width="140px">
          <template #header>
            <span>Highest Level</span>
            <span class="caret-wrapper">
              <i class=" mico-sort-up"></i>
              <i class=" mico-sort-dow"></i>
            </span>
          </template>
        </m-table-column>

        <m-table-column sortable="custom" prop="items" label="Items" min-width="100px">
          <template #header>
            <span>Items</span>
            <span class="caret-wrapper">
              <i class=" mico-sort-up"></i>
              <i class=" mico-sort-dow"></i>
            </span>
          </template>
        </m-table-column> -->

        <m-table-column prop="kycProgramId" label="Program No" min-width="130px">
          <template #default="{ row }">
            <div class="mui-fl-vert">
              <span class="kycProgramId">{{ row.kycProgramId }}</span>
              <i class="icon-copy-wallet mico-copy copyIcon" @click.stop="copy(row.kycProgramId)"></i>
            </div>
          </template>
        </m-table-column>

        <m-table-column prop="kycText" label="Program Type" min-width="130px">
          {{ kycText }}
        </m-table-column>

        <m-table-column prop="status" label="Status" min-width="100px">
          <template #default="{ row }">
            <div :class="['mui-fl-vert', row.statusdate.name.trim() + 1]">
              <div class="tableballcolor ball"></div>
              <div class="row_status">{{row.statusdate.name}}</div>
            </div>
          </template>
        </m-table-column>

        <m-table-column sortable="custom" prop="auditedUser" label="Audited User" min-width="135px">
          <template #header>
            <span>Audited User</span>
            <span class="caret-wrapper">
              <i class=" mico-sort-up"></i>
              <i class=" mico-sort-dow"></i>
            </span>
          </template>
          <template #default="{ row }">
            {{ row.auditedUser ? parseFloat(row.auditedUser).toLocaleString() : '--' }}
          </template>
        </m-table-column>

        <m-table-column sortable="custom"  prop="createTime" label="Created Time" min-width="165px">
          <template #header>
            <span>Created Time</span>
            <span class="caret-wrapper">
              <i class=" mico-sort-up"></i>
              <i class=" mico-sort-dow"></i>
            </span>
          </template>
        </m-table-column>

        <m-table-column sortable="custom"  prop="lastUpdateTime" label="Last update time" min-width="165px">
          <template #header>
            <span>Last Update Time</span>
            <span class="caret-wrapper">
              <i class=" mico-sort-up"></i>
              <i class=" mico-sort-dow"></i>
            </span>
          </template>
          <!-- <template #default="{ row }">
            <m-popover
              popper-class="NotRecord_popover Record_popover"
              placement="top-start"
              trigger="hover"
              :offset="20"
              :content="(row.statusdate.name === 'Apply ' ? 'Applied ' : row.statusdate.name) + row.statusdate.timetip + row.lastUpdateTime">
              <div slot="reference">{{row.lastUpdateTime}}</div>
            </m-popover>
          </template> -->
        </m-table-column>

        <!-- <m-table-column prop="kycProgramId" label="Program No." v-if="!isCollapse" min-width="130px">
          <template #default="{ row }">
            <span class="kycProgramId">{{row.kycProgramId || '--'}}
              <div v-if="!row.copystyle && row.program_no" @click.stop="copy(row)" :class="['program_copy', (checked1 || checked2) && 'table_Copy']">Copy</div>
              <div v-if="row.copystyle && row.program_no" :class="['program_copy', 'Copied', (checked1 || checked2) && 'table_Copy']">Copied</div>
            </span>
          </template>
        </m-table-column> -->

        <m-table-column label="Operation" min-width="80px">
          <template>
            <div class="view-edit">
              View Edit
            </div>
          </template>
        </m-table-column>
      </m-table>

      <div v-show="!dataflag" class="no-table-data mui-fl-col mui-fl-vert">
        <img src="~@/assets/img/no-table-data.png" alt="">
        <p>Start creating your first KYC program!</p>
      </div>

      <div class="mui-fl-end">
        <m-pagination
          hide-on-single-page
          v-show="tableData.length"
          class="sty1-pagination sty3-cell"
          @current-change="handleCurrentChange"
          :current-page.sync="page.page"
          layout="prev, pager, next"
          :page-size="10"
          :total="page.total">
        </m-pagination>
      </div>
    </div>

    <drawer-component
      ref="drawerComponent"
      :drawerVisible="drawer"
      :id="Number(update.id)"
      :createTime="update.createTime"
      :has-applied-program="hasAppliedProgram"
      @close="close"
      @update="updateList"
    />

    <KycWarning :model="model" :warningtip="warningtip" @Leave="Leave"></KycWarning>
  </div>
</template>
<script>
import KycWarning from '@/components/kyc_warning/KycWarning.vue'
import MuiInput from '@/components/mui-input/MuiInput.vue'
import DrawerComponent from './components/DrawerComponent.vue'
import * as types from '@/store/mutation-types'
export default {
  components: { KycWarning, MuiInput, DrawerComponent },
  data () {
    return {
      titleTxt: 'Search Results',
      isSearch: false,
      update: {
        state: false,
        createTime: null,
        id: ''
      },
      parameter: {
        time: '',
        auditedUser: '',
        highestLevel: '',
        items: ''
      },
      dataflag: true,
      operationdata: {},
      model: 'Recorded' || 'program',
      drawerCopy: false,
      isCollapse: false,
      keyword: '',
      copystyle: false,
      drawer: false,
      checked1: true,
      checked2: false,
      warningtip: false,
      dialogTableVisible: false,
      lastUpdateTime: 'descending',
      kycLevel: [],
      detailsList: {},
      tableDatas: [],
      tableData: [],
      tagindex: {
        val: '',
        id: ''
      },
      page: {
        page: 1,
        size: 10
      },
      // temchecked1: false,
      // temchecked2: false,
      initialization: {},
      openModified: false,
      programNameWidth: '',
      lv1offsetWidth: [],
      hasAppliedProgram: false,
      isSelect1: true,
      isSelect2: false,
      time: null
    }
  },
  computed: {
    mode () {
      let val = 0
      if (this.isSelect1) {
        val = 1
      } else if (this.isSelect2) {
        val = 3
      }
      if (this.isSelect1 && this.isSelect2) {
        val = 2
      }
      return val
    },
    kycText () {
      let levelTitle = ''
      const level = (localStorage.getItem('zkmeAdminUser') && JSON.parse(localStorage.getItem('zkmeAdminUser')).level) || this.$store.state.auth.user.level
      switch (Number(level)) {
        case 0:
          levelTitle = 'On-chain Mint'
          break
        case 1:
          levelTitle = 'On-chain Transactional'
          break
        case 2:
          levelTitle = 'Cross-chain'
          break
      }
      return levelTitle
    }
  },
  watch: {
    // 分页
    async '$route.query' (val) {
      await this.queryUserKycList()
      this.tableData.length && await this.pushprogramName()
    }
  },
  async created () {
    if (localStorage.getItem('beforeunload')) {
      this.handleCurrentChange()
    }
    // await this.getAllList()
    await this.queryUserKycList()
    this.tableData.length && this.pushprogramName()
    if (this.update.state) {
      this.expandDetails(this.update)
    }
  },
  async mounted () {
    this.onWinResize()
    window.addEventListener('resize', this.onWinResize)
    window.addEventListener('beforeunload', this.beforeunload)
  },
  methods: {
    async updateList () {
      this.drawerVisible = false
      if (localStorage.getItem('beforeunload')) {
        this.handleCurrentChange()
      }
      // await this.getAllList()
      await this.queryUserKycList()
      this.tableData.length && this.pushprogramName()
      if (this.update.state) {
        this.expandDetails(this.update)
      }
    },
    createProgram () {
      window.gtag('event', 'Create_program', {
        app_name: 'zkMe Dashboard'
      })
      this.keyword = ''
      this.$store.commit(types.SET_KYCFORM_DETAIL, null)
      this.$store.commit(types.SET_DUPLICATE_FORM, null)
      this.$store.commit(types.SET_KYC_TITLE, 'Create program')
      // this.$store.commit(types.SET_KYCMODE, this.mode)
      this.$store.commit('SET_KYC_TITLE', 'Create program')
      this.$router.push(`/zk-kyc/zkkyc-form?mode=${this.mode}`)
    },
    updateProgram (val) {
      // $emit('fentry', detailsList, val)
      this.keyword = ''
    },
    beforeunload () {
      // localStorage.setItem('beforeunload', '1')
    },
    pushprogramName () {
      this.programNameWidth = this.programNameWidth || this.$refs.programName?.offsetWidth
      const spanprogramName = [...document.getElementsByClassName('spanprogramName')]
      if (this.tableData.length && spanprogramName.length) {
        this.tableData.forEach(v => {
          spanprogramName.forEach(x => {
            if (v.programName === x.textContent.trim()) {
              this.$set(v, 'offsetWidth', x.offsetWidth)
            }
          })
        })
      }
    },
    onWinResize () {
      this.isCollapse = document.body.clientWidth <= 1000
      setTimeout(() => {
        if (this.$refs.programName) {
          this.programNameWidth = this.$refs.programName?.offsetWidth
        }
      }, 200)
    },
    headercellclassname ({ column }) {
      // if (column.label === 'Last update time') {
      //   column.order = this.lastUpdateTime
      // }
    },
    sortchange ({ column, prop, order }) {
      this.parameter = {
        time: '',
        auditedUser: '',
        highestLevel: '',
        items: ''
      }
      if (order) {
        this.lastUpdateTime = order
      } else {
        this.lastUpdateTime = 'ascending'
        column.order = this.lastUpdateTime
      }
      switch (prop) {
        case 'lastUpdateTime':
          this.parameter.time = this.lastUpdateTime === 'ascending' ? 1 : 0
          break
        case 'createTime':
          this.parameter.time = this.lastUpdateTime === 'ascending' ? 1 : 0
          break
        case 'highestLevel':
          this.parameter.highestLevel = this.lastUpdateTime === 'ascending' ? 1 : 0
          break
        case 'items':
          this.parameter.items = this.lastUpdateTime === 'ascending' ? 1 : 0
          break
        case 'auditedUser':
          this.parameter.auditedUser = this.lastUpdateTime === 'ascending' ? 1 : 0
          break
      }
      this.handleCurrentChange()
    },
    // async getAllList () {
    //   const rp = await this.$api.request('kyc.getAllList')
    //   if (rp.code === 80000000) {
    //     this.kycLevel = rp.data
    //   }
    // },
    handleCurrentChange (val) {
      if (typeof val === 'number') {
        this.page.page = val
        val = false
      }
      // if (val) {
      //   this.lastUpdateTime = 'descending'
      //   this.$refs.table.columns.forEach(v => {
      //     v.order = ''
      //     if (v.label === 'Last update time') {
      //       v.order = 'descending'
      //     }
      //   })
      // }
      this.initialization = val
      const route = {
        page: !val ? this.page.page : 1,
        time: !val ? this.parameter.time : 0,
        auditedUser: !val ? this.parameter.auditedUser : '',
        highestLevel: !val ? this.parameter.highestLevel : '',
        items: !val ? this.parameter.items : '',
        val: !val ? this.tagindex.val : '',
        id: !val ? this.tagindex.id : '',
        checked1: !val ? this.checked1 : true,
        checked2: !val ? this.checked2 : false,
        keyword: !val ? this.keyword : ''
      }
      this.$router.push({ query: route })
    },

    // status: -1 delete, 1 apply, 2 create, 3 update, 4 expired
    async queryUserKycList (val) {
      this.page.page = Number(this.$route.query.page) || 1
      this.page.size = Number(this.$route.query.size) || 10
      this.checked1 = this.$route.query.checked1 === 'true' || this.checked1 || false
      this.checked2 = this.$route.query.checked2 === 'true' || false
      if (!val) {
        this.keyword = this.$route.query.keyword || ''
      }
      this.parameter.time = this.$route.query.time || 0
      this.parameter.auditedUser = this.$route.query.auditedUser || ''
      this.parameter.highestLevel = this.$route.query.highestLevel || ''
      this.parameter.items = this.$route.query.items || ''
      this.tagindex.val = this.$route.query.val || ''
      this.tagindex.id = this.$route.query.id || ''
      const data = {
        auditedUser: this.parameter.auditedUser,
        filterHighestLevel: this.tagindex.id,
        hideUnapplied: this.checked2 ? 1 : 0,
        highestLevel: this.parameter.highestLevel,
        items: this.parameter.items,
        pageReq: this.page,
        pinCurrentApply: this.checked1 ? 1 : 0,
        programName: val ? this.$route.query.keyword : this.keyword,
        time: this.parameter.time
      }
      const rp = await this.$api.request('kyc.queryUserKycList', data)
      if (rp.code === 80000000) {
        // if ((!rp.data.list || !rp.data.list.length) && !this.keyword && !this.tagindex.id) {
        //   this.$router.replace({ path: '/zk-kyc/zkkyc-form?mode=1', query: { hasNoZkKyc: 1 } })
        //   return
        // }
        if (!rp.data.list || !rp.data.list.length) {
          this.dataflag = false
        } else {
          this.dataflag = true
        }
        this.hasAppliedProgram = !!rp.data.list.find(r => Number(r.status) === 1)
        rp.data.list.forEach(v => {
          v.statusdate = this.statuscf(v.status)
        })
        // if (!rp.data.list.length) {
        //   this.retrieval(true, 'No Results')
        // }
        localStorage.setItem('beforeunload', '')
        this.tableData = rp.data.list
        this.page = rp.data.page
        clearTimeout(this.time)
        if (rp.data.list.some(x => x.status === '5')) {
          this.time = setTimeout(() => {
            this.queryUserKycList('updateData')
            if (this.drawer) {
              this.$refs.drawerComponent.queryKycInfo()
            }
          }, 5000)
        }
      }
    },
    statuscf (val) {
      const vlaue = {
        name: '',
        timetip: 'at '
      }
      switch (val) {
        case '-1':
          vlaue.name = 'Delete '
          break
        case '1':
          vlaue.name = 'Apply '
          vlaue.timetip = 'from '
          break
        case '2':
          vlaue.name = 'Created '
          break
        case '3':
          // vlaue.name = 'Update '
          vlaue.name = 'Created '
          break
        case '4':
          vlaue.name = 'Expired '
          break
        case '5':
          vlaue.name = 'Pending '
          break
        default:
          break
      }
      return vlaue
    },
    clear () {
      this.keyword = ''
      this.handleCurrentChange()
      this.retrieval(false)
    },
    close () {
      this.drawer = false
      this.update.id = ''
    },
    search () {
      if (!this.keyword) {
        return
      }
      this.retrieval(true, 'Search Results')
      this.handleCurrentChange()
    },
    retrieval (val, title) {
      this.isSearch = val
      this.titleTxt = title
    },
    tagclose () {
      this.tagindex.val = ''
      this.tagindex.id = ''
      if (!this.keyword) {
        this.retrieval(false)
        this.handleCurrentChange()
      } else {
        this.search()
      }
    },
    handleCommand (val) {
      this.tagindex.val = val.levelName
      this.tagindex.id = val.id
      this.handleCurrentChange()
    },
    cellClassname (row) {
      if (row.column.label === 'Program Name' && row.column.property === 'date') {
        return 'cellClassname'
      }
    },
    rowClassname (row) {
      return this.statuscf(row.row.status).name
    },
    cellMouseEnter (row, column) {
      if (column.label === 'Program No.') {
        this.$set(row, 'program_no', true)
      }
    },
    cellMouseLeave (val, column) {
      if (column.label === 'Program No.') {
        val.program_no = false
      }
    },
    copy (data) {
      const elInput = document.createElement('input')
      elInput.value = data
      document.body.appendChild(elInput)
      elInput.select()
      document.execCommand('Copy')
      elInput.remove()
      this.$message({
        message: 'Copied',
        iconClass: 'mico-lightTip',
        customClass: 'sty1-message',
        duration: 3000,
        offset: 32,
        center: true
      })
    },
    async Leave (val) {
      this.warningtip = false
      if (val === 'Leave') {
        const rp = await this.$api.request('kyc.updateStatus', this.operationdata)
        if (rp.code === 80000000) {
          this.drawer = false
          this.queryUserKycList()
        }
      }
    },
    async expandDetails (row) {
      this.update.id = row.id
      this.update.createTime = new Date(row.createTime).getTime()
      this.drawer = true
      this.$store.commit('SET_CATEGORY', row.category)
    },
    updateStatus (tip, status) {
      if (tip) {
        this.warningtip = true
      }
      this.model = tip
      this.operationdata = {
        id: this.detailsList.id,
        status: status
      }
    },
    back () {
      // this.$router.back()
      this.keyword = ''
      this.handleCurrentChange()
      this.retrieval(false)
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/views/kycpage/_zkkyc.scss" scoped></style>
