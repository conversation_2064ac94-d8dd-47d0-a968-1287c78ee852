<template>
  <div class="mui-fl-col mui-fl-vert mui-fl-btw mui-fl-1">
    <div class="mui-fl-col mui-fl-vert">
      <p class="step2-p1">
        We value your privacy, and have no intention to process or store any of your personal identifiable information
        (PII).
      </p>
      <p class="step2-p2">
        We have partnered with zkMe in order to verify your credentials in zero-knowledge, meaning that we can prove
        your eligibility with:
      </p>
      <ul class="step2-ul">
        <li class="mui-fl-vert" v-for="(item, index) of kycStepList" :key="index">
          <i :class="['mui-shr-0', item.i]"></i>
          <div class="mui-fl-1">
            <p class="t">{{ item.t }}</p>
            <p class="d">{{ item.d }}</p>
          </div>
        </li>
      </ul>
      <div :class="{ 'step2-protocol': 1 }">
        <m-checkbox class="sty1 mui-fl-vert">
          <p class="pro-text"><span>By starting the verification, you agree that you are over 16 years old, have read,
              understood and accepted zkMe's </span> <a>Privacy Policy</a> <span>and</span> <a>User Agreement</a>
            <span>.</span>
          </p>
        </m-checkbox>
      </div>
    </div>

    <div class="bottom-act">
      <ul class="dot mui-fl-central">
        <li v-for="(i, index) of 2" :key="index" :class="{ 'active': 2 === i }"></li>
      </ul>
      <m-button type="primary" size="large" class="width-2 taplight" round>Start verification</m-button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'VerifyIdentityStepTwo',
  data () {
    return {
      kycStepList: [
        {
          i: 'popico-zkme',
          t: 'End-to-End Zero-Knowledge',
          d: 'No personal data is shared with anyone (incl. zkMe).'
        },
        {
          i: 'popico-selective',
          t: 'Selective disclosure',
          d: 'Only Yes/No answers to simple eligibility questions are generated and shared.'
        },
        {
          i: 'popico-self-sovereign',
          t: 'Self-Sovereign',
          d: 'Credential holders can control, ammend revoke eligibility proofs at any time.'
        },
        {
          i: 'popico-anonymous',
          t: 'Anonymous until proven guilty',
          d: 'Only in case a regulator initiates formal bad actor proceedings, will the proof of your eligibility be unlocked and shared.'
        }
      ]
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/ui-design-preview/_home-view.scss" scoped></style>
