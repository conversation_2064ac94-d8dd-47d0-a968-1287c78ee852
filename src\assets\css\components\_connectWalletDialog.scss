.conn-header {
  text-align: center;
  font-weight: 700;
  font-size: 20px;
  line-height: 140%;
}

.options {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  grid-gap: 60px;
  padding: 0px 55px 80px 55px;
  justify-self: center;
}
.options li {
  border-radius: 24px;
  width: 400px;
  height: 140px;
  color: #002E33;
  font-size: 16px;
  font-weight: 700;
  line-height: 25px;
}
.options li img {
  width: 56px;
  height: 56px;
}
.footText {
  color: #7B9395;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  line-height: 140%;
  .t1 {
    margin-bottom: 4px;
    font-weight: 700;
    font-size: 16px;
    color: #002E33;
  }
}
.footText1 {
  color: #7B9395;
  text-align: center;
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  .t1 {
    color: #002E33;
  }
}
.metamaskLogo {
  // width: fit-content;
  .metamaskLogoBox {
    position: relative;
    width: fit-content;
  }
  .img1 {
    width: 180px;
    height: 180px;
  }
  .img2 {
    width: 36px;
    height: 36px;
    position: absolute;
    bottom: 28px;
    right: 24px;
  }
}