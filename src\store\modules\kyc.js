import Vue from 'vue'
import * as types from './../mutation-types'

export default {
  state: {
    // kycMode: null,
    category: null,
    kycLevelList: [],
    kycFormDetail: JSON.parse(localStorage.getItem('kycFormDetail')),
    kycTitle: localStorage.getItem('kycTitle') || 'Create program',
    duplicateForm: JSON.parse(localStorage.getItem('duplicateForm'))
  },
  mutations: {
    [types.SET_CATEGORY] (state, payload) {
      state.category = payload
    },
    [types.SET_KYCLEVEL_LIST] (state, payload) {
      state.kycLevelList = payload
    },
    [types.SET_KYCFORM_DETAIL] (state, payload) {
      state.kycFormDetail = state.kycFormDetail = Object.assign({}, payload)
      localStorage.setItem('kycFormDetail', JSON.stringify(state.kycFormDetail || ''))
    },
    [types.SET_KYC_TITLE] (state, payload) {
      state.kycTitle = payload
      localStorage.setItem('kycTitle', payload)
    },
    [types.SET_DUPLICATE_FORM] (state, payload) {
      state.duplicateForm = state.duplicateForm = Object.assign({}, payload)
      localStorage.setItem('duplicateForm', JSON.stringify(state.duplicateForm || ''))
    }
  },
  actions: {
    async getKycLevelList ({ commit }) {
      const rp = await Vue.prototype.$api.request('kyc.getAllList')
      if (rp.code === 80000000) {
        // rp.data.forEach(v => {
        //   v.text = v.levelName
        //   v.value = v.id
        // })
        commit(types.SET_KYCLEVEL_LIST, rp.data)
      }
    }
  }
}
