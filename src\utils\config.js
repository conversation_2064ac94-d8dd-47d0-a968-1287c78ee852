import metamaskLogo from '@/assets/img/wallets/evm_metamask_logo.png'
import metamaskBg from '@/assets/img/wallets/evm_metamask_bg.png'
// import okxLogo from '@/assets/img/wallets/evm_okx_logo.png'
// import okxBg from '@/assets/img/wallets/evm_okx_bg.png'
// import bitgetLogo from '@/assets/img/wallets/evm_bitget_logo.png'
// import bitgetBg from '@/assets/img/wallets/evm_bitget_bg.png'
// import gateWalletLogo from '@/assets/img/wallets/evm_gate_wallet_logo.png'
// import gateWalletBg from '@/assets/img/wallets/evm_gate_wallet_bg.png'
// import tokenPocketLogo from '@/assets/img/wallets/evm_token_pocket_logo.png'
// import tokenPocketBg from '@/assets/img/wallets/evm_token_pocket_bg.png'
// import xdefiwalletLogo from '@/assets/img/wallets/evm_xdefi_logo.png'
// import xdefiwalletBg from '@/assets/img/wallets/evm_xdefi_bg.png'

export const netWorkType = {
  'Ethereum Goerli Testnet': 'EVM',
  'ZetaChain Athens3 Testnet': 'EVM',
  'Polygon Mumbai Testnet': 'EVM',
  'Scroll Sepolia Testnet': 'EVM',
  Polygon: 'EVM',
  Base: 'EVM',
  'Sei Testnet': 'SEI',
  Manta: 'EVM',
  'Neutron Testnet': 'Neutron',
  'Aptos Testnet': 'Aptos',
  Neutron: 'Neutron',
  Aptos: 'Aptos',
  Arbitrum: 'EVM',
  'BNB Smart Chain': 'EVM',
  Ethereum: 'EVM',
  Ronin: 'EVM',
  'X Layer': 'EVM',
  BounceBit: 'EVM',
  'Plume Testnet': 'EVM',
  'The Open Network': 'TON',
  'Moca Chain Testnet': 'EVM',
  Kaia: 'EVM'
}

export const EVM_WALLET_LIST = [
  {
    id: 1,
    name: 'MetaMask',
    type: 'MetaMask',
    logo: metamaskLogo,
    bgColor: '#FFF7F0',
    bgImg: metamaskBg,
    website: 'https://chromewebstore.google.com/detail/metamask/nkbihfbeogaeaoehlefnkodbefgpgknn?utm_source=ext_app_menu',
    walletType: 0
  }
  // {
  //   id: 2,
  //   name: 'OKX Wallet',
  //   type: 'OKX Wallet',
  //   logo: okxLogo,
  //   bgColor: '#F4F4F4',
  //   bgImg: okxBg,
  //   website: 'https://chromewebstore.google.com/detail/okx-wallet/mcohilncbfahbmgdjkbpemcciiolgcge?utm_source=ext_app_menu',
  //   walletType: 0
  // }
  // {
  //   id: 3,
  //   name: 'Bitget Wallet',
  //   type: 'Bitget Wallet',
  //   logo: bitgetLogo,
  //   bgColor: '#EBFDFF',
  //   bgImg: bitgetBg,
  //   website: 'https://chromewebstore.google.com/detail/bitget-wallet-formerly-bi/jiidiaalihmmhddjgbnbgdfflelocpak?utm_source=ext_app_menu',
  //   walletType: 0
  // },
  // {
  //   id: 4,
  //   name: 'Gate Wallet',
  //   type: 'Gate Wallet',
  //   logo: gateWalletLogo,
  //   bgColor: 'rgba(0, 104, 255, 0.10)',
  //   bgImg: gateWalletBg,
  //   website: 'https://chromewebstore.google.com/detail/gate-wallet/cpmkedoipcpimgecpmgpldfpohjplkpp?utm_source=ext_app_menu',
  //   walletType: 1
  // },
  // {
  //   id: 5,
  //   name: 'TokenPocket',
  //   type: 'TokenPocket',
  //   logo: tokenPocketLogo,
  //   bgColor: 'rgba(49, 133, 255, 0.10)',
  //   bgImg: tokenPocketBg,
  //   website: 'https://chromewebstore.google.com/detail/tokenpocket/mfgccjchihfkkindfppnaooecgfneiii?utm_source=ext_app_menu',
  //   walletType: 0
  // },
  // {
  //   id: 6,
  //   name: 'XDEFI Wallet',
  //   type: 'XDEFI',
  //   logo: xdefiwalletLogo,
  //   bgColor: 'rgba(51, 93, 229, 0.1)',
  //   bgImg: xdefiwalletBg,
  //   website: 'https://chromewebstore.google.com/detail/xdefi-wallet/hmeobnfnfcmdkdcmlblgagmfpfboieaf?utm_source=ext_app_menu',
  //   walletType: 0
  // }
]

export const evmChainIds = ['0x89', '0x2105', '0xa9', '0x1389', '0x8274f', '0x13881', '0x2ee8', '0x1771', '0xa4b1', '0x18230', '0x18231', '0x38', '0x7e4', '0xc4']

export const isMobile = () => {
  return 'ontouchstart' in document.documentElement
}

export const MAINNET_NETWORK_LIST = [
  {
    id: 1,
    icon: 'eth',
    name: 'Eth',
    isOnline: true,
    chainId: '0x1',
    chainName: 'Eth',
    nativeCurrency: {
      name: 'ETH',
      symbol: 'ETH',
      decimals: 18
    },
    rpcUrls: ['https://eth.llamarpc.com'],
    blockExplorerUrls: ['https://ethscan.org'],
    isMainnet: true,
    shortName: 'Eth',
    isSupportMobile: true
  },
  {
    id: 8,
    icon: 'base',
    name: 'Base',
    isOnline: true,
    chainId: '0x2105',
    chainName: 'Base',
    nativeCurrency: {
      name: 'ETH',
      symbol: 'ETH',
      decimals: 18
    },
    rpcUrls: ['https://developer-access-mainnet.base.org'],
    blockExplorerUrls: ['https://basescan.org'],
    isMainnet: true,
    shortName: 'Base',
    isSupportMobile: true
  },
  {
    id: 25,
    icon: 'ton',
    name: 'The Open Network',
    isOnline: true,
    chainId: 'ton',
    chainName: 'The Open Network',
    // rpcUrls: [`https://developer-access-mainnet.base.org`],
    // blockExplorerUrls: ["https://basescan.org"],
    isMainnet: true,
    shortName: 'The Open Network',
    isSupportMobile: true
  },
  {
    id: 28,
    icon: 'solana',
    name: 'Solana',
    isOnline: true,
    chainId: 'solana',
    chainName: 'Solana',
    rpcUrls: ['https://api.mainnet-beta.solana.com'],
    blockExplorerUrls: ['https://explorer.solana.com/'],
    isMainnet: true,
    shortName: 'Solana',
    isSupportMobile: true
  },
  {
    id: 29,
    icon: 'kaia',
    name: 'Kaia',
    isOnline: true,
    chainId: '0x2019',
    chainName: 'Kaia Mainnet',
    nativeCurrency: {
      name: 'KAIA',
      symbol: 'KAIA',
      decimals: 18
    },
    rpcUrls: ['https://public-en.node.kaia.io'],
    blockExplorerUrls: ['https://kaiascope.com'],
    isMainnet: true,
    shortName: 'Kaia',
    isSupportMobile: true
  },
  {
    id: 27,
    icon: 'moca',
    name: 'Moca',
    isOnline: true,
    chainId: '0x141f',
    chainName: 'Moca Testnet',
    nativeCurrency: {
      name: 'Moca',
      symbol: 'Moca',
      decimals: 18
    },
    rpcUrls: ['https://testnet-rpc.mechain.tech'],
    blockExplorerUrls: ['https://testnet-scan.mechain.tech'],
    isMainnet: true,
    shortName: 'Moca',
    isSupportMobile: true
  },
  {
    id: 26,
    icon: 'plume',
    name: 'Plume',
    isOnline: true,
    chainId: '0x18231',
    chainName: 'Plume',
    nativeCurrency: {
      name: 'ETH',
      symbol: 'ETH',
      decimals: 18
    },
    rpcUrls: ['https://phoenix-rpc.plumenetwork.xyz/'],
    blockExplorerUrls: ['https://phoenix-explorer.plumenetwork.xyz/'],
    isMainnet: true,
    shortName: 'Plume',
    isSupportMobile: true
  },
  {
    id: 11,
    icon: 'manta',
    name: 'Manta',
    isOnline: true,
    chainId: '0xa9',
    chainName: 'Manta',
    nativeCurrency: {
      name: 'ETH',
      symbol: 'ETH',
      decimals: 18
    },
    rpcUrls: ['https://pacific-rpc.manta.network/http'],
    blockExplorerUrls: ['https://pacific-explorer.manta.network'],
    isMainnet: true,
    shortName: 'Manta',
    isSupportMobile: true
  },
  {
    id: 22,
    icon: 'bounce',
    name: 'BounceBit',
    isOnline: true,
    chainId: '0x1771',
    chainName: 'BounceBit Mainnet',
    nativeCurrency: {
      name: 'BB',
      symbol: 'BB',
      decimals: 18
    },
    rpcUrls: ['https://fullnode-mainnet.bouncebitapi.com'],
    blockExplorerUrls: ['https://bbscan.io/'],
    isMainnet: true,
    shortName: 'BounceBit',
    isSupportMobile: true
  },
  {
    id: 19,
    icon: 'arbitrum',
    name: 'Arbitrum',
    isOnline: true,
    chainId: '0xa4b1',
    chainName: 'Arbitrum One',
    nativeCurrency: {
      name: 'ETH',
      symbol: 'ETH',
      decimals: 18
    },
    rpcUrls: ['https://arb1.arbitrum.io/rpc'],
    blockExplorerUrls: ['https://arbiscan.io'],
    isMainnet: true,
    shortName: 'Arbitrum',
    isSupportMobile: true
  },
  {
    id: 15,
    icon: 'neutron',
    name: 'Neutron',
    isOnline: true,
    chainId: 'neutron-1',
    chainName: 'Neutron Testnet',
    rpcUrls: ['https://rpc-kralum.neutron-1.neutron.org'],
    blockExplorerUrls: ['https://neutron.celat.one/neutron-1'],
    isMainnet: true,
    shortName: 'Neutron',
    isSupportMobile: false
  },
  {
    id: 18,
    icon: 'bnb',
    name: 'BNB Smart Chain',
    isOnline: true,
    chainId: '0x38',
    chainName: 'BNB Smart Chain',
    nativeCurrency: {
      name: 'BNB',
      symbol: 'BNB',
      decimals: 18
    },
    rpcUrls: ['https://bsc-dataseed.bnbchain.org'],
    blockExplorerUrls: ['https://bscscan.com/'],
    isMainnet: true,
    shortName: 'BNB Smart Chain',
    isSupportMobile: true
  },
  {
    id: 20,
    icon: 'ronin',
    name: 'Ronin',
    isOnline: true,
    chainId: '0x7e4',
    chainName: 'Ronin mainnet',
    nativeCurrency: {
      name: 'RON',
      symbol: 'RON',
      decimals: 18
    },
    rpcUrls: ['https://api.roninchain.com/rpc'],
    blockExplorerUrls: ['https://explorer.roninchain.com'],
    isMainnet: true,
    shortName: 'Ronin',
    isSupportMobile: true
  },
  {
    id: 21,
    icon: 'x-layer',
    name: 'X Layer',
    isOnline: true,
    chainId: '0xc4',
    chainName: 'X Layer mainnet',
    nativeCurrency: {
      name: 'OKB',
      symbol: 'OKB',
      decimals: 18
    },
    rpcUrls: ['https://rpc.xlayer.tech '],
    blockExplorerUrls: ['https://www.okx.com/web3/explorer/xlayer'],
    isMainnet: true,
    shortName: 'X Layer',
    isSupportMobile: true
  },
  {
    id: 3,
    icon: 'linea',
    name: 'Linea',
    isOnline: false,
    chainId: '',
    isMainnet: false,
    shortName: 'Linea',
    isSupportMobile: true
  },
  {
    id: 4,
    icon: 'mantle',
    name: 'Mantle',
    isOnline: false,
    chainId: '',
    isMainnet: false,
    shortName: 'Mantle',
    isSupportMobile: true
  },
  {
    id: 1,
    icon: 'eth',
    name: 'Ethereum',
    isOnline: false,
    chainId: '',
    isMainnet: false,
    shortName: 'Ethereum',
    isSupportMobile: true
  },
  {
    id: 2,
    icon: 'zeta',
    name: 'ZetaChain',
    isOnline: false,
    chainId: '',
    isMainnet: false,
    shortName: 'ZetaChain',
    isSupportMobile: true
  },
  {
    id: 6,
    icon: 'scroll',
    name: 'Scroll',
    isOnline: false,
    chainId: '',
    isMainnet: false,
    shortName: 'Scroll',
    isSupportMobile: true
  },
  {
    id: 9,
    icon: 'neon',
    name: 'Neon',
    isOnline: false,
    chainId: '',
    isMainnet: false,
    shortName: 'Neon',
    isSupportMobile: true
  }
]

export const TIME_ZONE = [
  {
    name: 'UTC−12:00',
    offset: 720,
    country: 'Baker Island (territory of USA)'
  },
  {
    name: 'UTC−11:00',
    offset: 660,
    country: 'American Samoa'
  },
  {
    name: 'UTC−10:00',
    offset: 600,
    country: 'Hawaii (USA)'
  },
  {
    name: 'UTC−09:30',
    offset: 570,
    country: 'Marquesas Islands (French Polynesia)'
  },
  {
    name: 'UTC−09:00',
    offset: 540,
    country: 'Alaska (USA)'
  },
  {
    name: 'UTC−08:00',
    offset: 480,
    country: 'California (USA), British Columbia (Canada)'
  },
  {
    name: 'UTC−07:00',
    offset: 420,
    country: 'Colorado (USA), Alberta (Canada)'
  },
  {
    name: 'UTC−06:00',
    offset: 360,
    country: 'Chicago (USA), Mexico City (Mexico)'
  },
  {
    name: 'UTC−05:00',
    offset: 300,
    country: 'New York (USA), Bogotá (Colombia)'
  },
  {
    name: 'UTC−04:00',
    offset: 240,
    country: 'Santiago (Chile, in winter), Caracas (Venezuela)'
  },
  {
    name: 'UTC−03:30',
    offset: 210,
    country: 'Newfoundland (Canada)'
  },
  {
    name: 'UTC−03:00',
    offset: 180,
    country: 'Buenos Aires (Argentina)'
  },
  {
    name: 'UTC−02:00',
    offset: 120,
    country: 'South Georgia and the South Sandwich Islands'
  },
  {
    name: 'UTC−01:00',
    offset: 60,
    country: 'Azores (Portugal)'
  },
  {
    name: 'UTC±00:00',
    offset: 0,
    country: 'London (UK), Accra (Ghana)'
  },
  {
    name: 'UTC+01:00',
    offset: -60,
    country: 'Berlin (Germany), Lagos (Nigeria)'
  },
  {
    name: 'UTC+02:00',
    offset: -120,
    country: 'Athens (Greece), Cairo (Egypt)'
  },
  {
    name: 'UTC+03:00',
    offset: -180,
    country: 'Riyadh (Saudi Arabia), Nairobi (Kenya)'
  },
  {
    name: 'UTC+03:30',
    offset: -210,
    country: 'Tehran (Iran)'
  },
  {
    name: 'UTC+04:00',
    offset: -240,
    country: 'Dubai (UAE), Baku (Azerbaijan)'
  },
  {
    name: 'UTC+04:30',
    offset: -270,
    country: 'Kabul (Afghanistan)'
  },
  {
    name: 'UTC+05:00',
    offset: -300,
    country: 'Islamabad (Pakistan), Tashkent (Uzbekistan)'
  },
  {
    name: 'UTC+05:30',
    offset: -330,
    country: 'New Delhi (India), Colombo (Sri Lanka)'
  },
  {
    name: 'UTC+05:45',
    offset: -345,
    country: 'Kathmandu (Nepal)'
  },
  {
    name: 'UTC+06:00',
    offset: -360,
    country: 'Dhaka (Bangladesh), Thimphu (Bhutan)'
  },
  {
    name: 'UTC+06:30',
    offset: -390,
    country: 'Yangon (Myanmar)'
  },
  {
    name: 'UTC+07:00',
    offset: -420,
    country: 'Bangkok (Thailand), Jakarta (Indonesia)'
  },
  {
    name: 'UTC+08:00',
    offset: -480,
    country: 'Beijing (China), Singapore'
  },
  {
    name: 'UTC+09:00',
    offset: -540,
    country: 'Tokyo (Japan), Seoul (South Korea)'
  },
  {
    name: 'UTC+09:30',
    offset: -570,
    country: 'Adelaide (Australia)'
  },
  {
    name: 'UTC+10:00',
    offset: -600,
    country: 'Sydney (Australia), Port Moresby (Papua New Guinea)'
  },
  {
    name: 'UTC+11:00',
    offset: -660,
    country: 'Honiara (Solomon Islands), Nouméa (New Caledonia)'
  },
  {
    name: 'UTC+12:00',
    offset: -720,
    country: 'Wellington (New Zealand), Suva (Fiji)'
  },
  {
    name: 'UTC+12:45',
    offset: -765,
    country: 'Chatham Islands (New Zealand)'
  },
  {
    name: 'UTC+13:00',
    offset: -780,
    country: 'Nukuʻalofa (Tonga), Apia (Samoa, DST)'
  },
  {
    name: 'UTC+14:00',
    offset: -840,
    country: 'Kiritima\'ti (Line Islands, Kiribati)'
  }
]
