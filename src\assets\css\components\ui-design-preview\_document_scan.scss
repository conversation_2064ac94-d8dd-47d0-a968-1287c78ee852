.bg {
    height: 100%;
    /* padding-bottom: 100px; */
}

.step-box {
    width: max-content;
    padding: 11px 16px;
    border-radius: 42px;
    background: rgba(var(--color-background-14), 0.05);
    margin-top: 16px;
}

.icon {
    width: 18px;
    height: 18px;
    background: var(--color-text-4);
    border-radius: 4px;
    font-size: 12px;
    color: var(--vt-c-white-other);
}

.c1 {
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: rgba(var(--vt-c-primary-1-rgb), 1);
    margin-left: 8px;
}

.padd {
    padding-bottom: 58px;
}

.t1 {
    font-weight: 700;
    font-size: 22px;
    line-height: 32px;
    color: rgb(var(--color-text-6));
    margin-top: 16px;
}

.c2 {
    margin-top: 8px;
    font-size: 16px;
    line-height: 22px;
    color: var(--color-text-7);
}

.img {
    /* max-width: 100%;  */
    height: 230px;
    object-fit: contain;
    margin: 30px auto 24px auto;
    display: block;
}

.list {
    padding-bottom: 58px;
    box-sizing: border-box;
}

.list-item:not(:last-child) {
    margin-bottom: 12px;
}

.icon1 {
    width: 20px;
    height: 20px;
    border-radius: 6px;
    background: rgba(var(--vt-c-primary-1-rgb), 0.8);
    font-size: 12px;
    color: var(--vt-c-primary-3);
    margin-right: 6px;
}

.c3 {
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    color: rgba(var(--vt-c-white-other-3-rgb), 0.8);
}

.scan-success {
    padding-bottom: 200px;
    box-sizing: border-box;
}

.scan-result {
    width: 100%;
    padding: 24px 16px;
    box-sizing: border-box;
    background: rgba(var(--color-background-14), 0.05);
    border-radius: 20px;
    margin-top: 24px;

}

.scan-result li:not(:last-child) {
    margin-bottom: 16px;
}

.scan-result-error {
    border: 1px solid var(--color-text-12);
}

.scan-result-error_toast {
    color: var(--color-text-12);
    margin-top: 8px;
    font-size: 12px;
    line-height: 16px;
}

.t2 {
    font-size: 14px;
    line-height: 20px;
    color: rgba(var(--color-text-6), 0.8);
    margin-right: 10px;
}

.c4 {
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    color: rgb(var(--color-text-6));
    text-align: right;
}

.footer-box {
    width: 100%;
    position: fixed;
    bottom: 0;
}

.protocol {
    width: 100%;
    background: var(--color-background-15);
    padding: 8px 24px;
    box-sizing: border-box;
}

.checkbox {
    width: 100%;
}

.checkbox.animation {
    animation: checkboxAnimation 0.9s cubic-bezier(.36, .07, .19, .97);
}

.c5 {
    margin-left: 6px;
    font-size: 12px;
    line-height: 16px;
    max-width: calc(100% - 20px);
    color: var(--color-text-9);
    opacity: 0.8;
}

.c5 a {
    color: var(--color-text-4);
    text-decoration: none;
}

.box {
    padding: 16px 24px 16px 24px;
    background: var(--color-background);
    padding-bottom: calc(16px + constant(safe-area-inset-bottom));
    padding-bottom: calc(16px + env(safe-area-inset-bottom));
}

.btn1 {
    width: 155px;
    height: 48px;
    background: rgba(var(--color-background-14), 0.05);
    border-radius: 48px;
    border: none;
    color: rgba(var(--vt-c-primary-1-rgb), 0.8);
    font-weight: 500;
    font-size: 16px;
    line-height: 48px;
    text-align: center;
    margin-right: 16px;
}

.btn1:disabled {
    opacity: 0.2;
}

.btn2 {
    background: var(--color-text-4);
    color: var(--vt-c-white-other);
}

.failed-img {
    display: block;
    width: 255px;
    margin: 20px auto;
}

.center {
    text-align: center;
}


@media screen and (max-width: 360px) {
    .img {
        width: 100%;
    }
}