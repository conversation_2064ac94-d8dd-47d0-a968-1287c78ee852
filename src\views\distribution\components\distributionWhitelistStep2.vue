<template>
  <div class="mui-fl-btw">
    <div v-if="!onlyRead" class="mui-fl-1" style="margin-right: 40px; width: 100%;">
      <div class="t1 mgt16">Create Token Distribution</div>
      <div class="mui-fl-btw">
        <div class="leftBlock mui-fl-1" style="margin-right: 24px;">
          <div>
            <div class="t2">Token</div>
            <div class="t3">{{detail.tokenSymbol}}({{ detail.tokenContractAddress | formatPubKey }})</div>
          </div>
          <div>
            <div class="t2">Current Network</div>
            <div class="mui-fl-vert">
              <img style="width: 24px;height: 24px;margin-right: 4px;" src="@/assets/img/networkicon/moca.svg" alt="">
              <div class="t3">{{ detail.tokenNetwork }}</div>
            </div>
          </div>
          <div>
            <div class="t2">Start Date</div>
            <m-date-picker
              type="datetime"
              @focus="startFocus"
              @blur="time.startState = false"
              :class="['sty4-date-editor', time.startState && 'sty4-date-editor-active', time.errorStartState && 'sty4-date-editor-error']"
              prefix-icon="mico-date"
              v-model="time.start"
              align="right"
              range-separator="-"
              :clearable="false"
              :editable="false"
              placeholder="Start Date"
              popper-class="sty2-date-popper"
              :picker-options="startPickerOptions"
              :default-time="startDefaultTime"
            >
            </m-date-picker>
            <div v-if="time.errorStartState" class="errorText">
              Please Select Date
            </div>
          </div>
        </div>
        <div class="leftBlock mui-fl-1">
          <div>
            <div class="t2">Distribution Type</div>
            <div class="t3">{{ detail.distributionType }}</div>
          </div>
          <div>
            <div class="t2">Distribution Amount</div>
            <div class="t3">{{ detail.amount | toFormat }}</div>
          </div>
          <div>
            <div class="t2">End Date</div>
            <m-date-picker
              type="datetime"
              @focus="endFocus"
              @blur="time.endState = false"
              popper-class="sty2-date-popper"
              :class="['sty4-date-editor', time.endState && 'sty4-date-editor-active', time.errorEndState && 'sty4-date-editor-error']"
              prefix-icon="mico-date"
              v-model="time.end"
              align="right"
              range-separator="-"
              :clearable="false"
              :editable="false"
              placeholder="End Date"
              :picker-options="endPickerOptions()"
              :default-time="endDefaultTime"
            >
            </m-date-picker>
            <div v-if="time.errorEndState" class="errorText">
              Please Select Date
            </div>
          </div>
        </div>
      </div>
      <div class="t2">Upload address by CSV</div>
      <div class="t4 mui-fl-btw">
        <div>Please upload an XLS, XLSX, CSV, or JSON file in the format below.</div>
        <div class="download" @click="downloadFile">Download Template</div>
      </div>
      <div class="mui-fl-hori ">
        <div class="miniTable">
          <m-table class="sty5-table" :data="tableData" border>
            <m-table-column prop="date" label="Wallet Address">
            </m-table-column>
            <m-table-column prop="name" label="Amount">
            </m-table-column>
          </m-table>
        </div>
      </div>
      <m-upload :class="['sty1-upload', errorListState && 'sty1-upload-error']" drag accept=".xls,.xlsx,.csv,.json" ref="myupload"
        action="" :show-file-list="false" :auto-upload="false" multiple :on-change="handleExcelChange">
        <div>
          <i class="mico-upload"></i>
          <div class="t6">Drag & drop files or Browse</div>
          <div class="t7">Supports xls, xlsx, CSV, JSON files. Max 5MB</div>
        </div>
      </m-upload>
      <!-- <m-button class="sty2-button" @click="downloadFile">download</m-button> -->
    </div>
    <div :class="['mui-fl-1', 'tableBox', 'mgt16', onlyRead && 'onlyRead']" :style="{ position: !onlyRead ? 'relative' : whitelist.length && loading && !onlyRead ? 'relative' : 'revert-layer'}">
      <ul class="table">
        <div class="mui-fl-btw title">
          <div>Wallet Address</div>
          <div>Amount</div>
          <i v-if="!onlyRead" class="mico-creat" @click="showData = true"></i>
        </div>
        <div class="addData mui-fl-btw" v-if="showData">
          <m-input v-model="addData.address" class="sty4-input sty4-input-width" placeholder="Enter wallet address"></m-input>
          <m-input type=Number v-model="addData.anount" class="sty4-input" placeholder="Enter amount"></m-input>
          <svg ref="deletes" @click="showData = false" class="delete" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M9.82692 6L9.59615 12M6.40385 12L6.17308 6M12.8184 3.86038C13.0464 3.89481 13.2736 3.93165 13.5 3.97086M12.8184 3.86038L12.1065 13.115C12.0464 13.8965 11.3948 14.5 10.611 14.5H5.38905C4.60524 14.5 3.95358 13.8965 3.89346 13.115L3.18157 3.86038M12.8184 3.86038C12.0542 3.74496 11.281 3.65657 10.5 3.59622M2.5 3.97086C2.72638 3.93165 2.95358 3.89481 3.18157 3.86038M3.18157 3.86038C3.94585 3.74496 4.719 3.65657 5.5 3.59622M10.5 3.59622V2.98546C10.5 2.19922 9.8929 1.54282 9.10706 1.51768C8.73948 1.50592 8.37043 1.5 8 1.5C7.62957 1.5 7.26052 1.50592 6.89294 1.51768C6.1071 1.54282 5.5 2.19922 5.5 2.98546V3.59622M10.5 3.59622C9.67504 3.53247 8.84131 3.5 8 3.5C7.15869 3.5 6.32496 3.53247 5.5 3.59622" stroke="#33585C" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <div @click="addNewData" :style="{opacity: !addData.address || !addData.anount ? 0.5 : 1}" class="mui-fl-vert addBut">Add</div>
        </div>
        <li v-for="(data, index) of whitelist" :key="index" class="mui-fl-btw">
          <div class="walletAddress">{{ data['Wallet Address'] }}</div>
          <div>{{ data.Amount | toFormat }}</div>
          <svg v-if="!onlyRead" ref="deletes" @click="deleteData(index, data)" class="delete" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <path d="M9.82692 6L9.59615 12M6.40385 12L6.17308 6M12.8184 3.86038C13.0464 3.89481 13.2736 3.93165 13.5 3.97086M12.8184 3.86038L12.1065 13.115C12.0464 13.8965 11.3948 14.5 10.611 14.5H5.38905C4.60524 14.5 3.95358 13.8965 3.89346 13.115L3.18157 3.86038M12.8184 3.86038C12.0542 3.74496 11.281 3.65657 10.5 3.59622M2.5 3.97086C2.72638 3.93165 2.95358 3.89481 3.18157 3.86038M3.18157 3.86038C3.94585 3.74496 4.719 3.65657 5.5 3.59622M10.5 3.59622V2.98546C10.5 2.19922 9.8929 1.54282 9.10706 1.51768C8.73948 1.50592 8.37043 1.5 8 1.5C7.62957 1.5 7.26052 1.50592 6.89294 1.51768C6.1071 1.54282 5.5 2.19922 5.5 2.98546V3.59622M10.5 3.59622C9.67504 3.53247 8.84131 3.5 8 3.5C7.15869 3.5 6.32496 3.53247 5.5 3.59622" stroke="#33585C" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </li>
        <div class="mui-fl-hori" :style="{margin: clearWatch ? '12px' : '0'}" ref="loading">
          <div class="loading" v-if="clearWatch"></div>
        </div>
      </ul>
      <div v-if="!whitelist.length && !showData" class="mui-fl-col mui-fl-central noData">
        <img src="../../../assets/img/no-table-data.png" alt="">
        <div class="t8" :style="{color: errorListState ? '#EE6969' : '#809799'}">Please enter or upload the recipient information on the left side.</div>
      </div>
    </div>
  </div>
</template>

<script>
import FileSaver from 'file-saver'
import { formatGMTDate } from '@/utils/filters'
import { read, utils, write } from 'xlsx'
import { isNumber } from 'lodash'
export default {
  props: {
    onlyRead: {
      type: Boolean,
      required: false
    }
  },
  data () {
    return {
      startDefaultTime: '',
      endDefaultTime: '',
      time: {
        start: '',
        end: '',
        startState: false,
        endState: false,
        errorStartState: false,
        errorEndState: false
      },
      showData: false,
      postFileError: false,
      clientWidth: 0,
      addData: {
        address: '',
        anount: ''
      },
      whitelist: [],
      errorListState: false,
      nextPage: 0,
      ob: null,
      loading: false,
      clearWatch: true,
      tableData: [{
        date: '',
        name: ''
      }, {
        date: '',
        name: ''
      }],
      startStopTime: false
    }
  },
  computed: {
    detail () {
      return this.$store.state.distribution.detail
    },
    step () {
      return this.$store.state.distribution.detail.step
    },
    startPickerOptions () {
      const startDate = new Date()

      startDate.setDate(startDate.getDate() - 1)

      const startTime = startDate.getTime()
      return {
        disabledDate (time) {
          return time.getTime() < startTime
        }
      }
    }
  },
  watch: {
    'whitelist.length' (val) {
      if (val === 100) {
        setTimeout(() => {
          this.ob = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting && !this.loading && this.clearWatch) {
                this.getwhiteList()
              }
            })
          }, {
            root: null, // null = 视口为监听对象
            threshold: 0.1 // 元素在视口中占据的比例 0-1之间
          })
          this.ob.observe(this.$refs.loading)
        })
      }
    }
  },
  created () {
  },
  mounted () {
    this.$nextTick(() => {
      this.getwhiteList()
      if (this.step > 2) {
        this.time.start = this.detail.startDate
        this.time.end = this.detail.endDate
      }
      document.addEventListener('keydown', this.addkeydown)
    })
  },
  methods: {
    startFocus () {
      if (!this.time.start) {
        this.startDefaultTime =
          (new Date().getHours() + 2) + ':' +
          new Date().getMinutes().toString() + ':' +
          new Date().getSeconds().toString()
      }
      this.time.startState = true
      this.time.errorStartState = false
    },
    endFocus () {
      if (this.time.start) {
        this.endDefaultTime =
          (new Date(this.time.start).getHours()) + ':' +
          new Date(this.time.start).getMinutes().toString() + ':' +
          new Date(this.time.start).getSeconds().toString()
      }
      this.time.endState = true
      this.time.errorEndState = false
    },
    addkeydown (e) {
      if (e.code === 'Delete' && this.$refs.deletes) {
        this.$refs.deletes.forEach((item, index) => {
          if (window.getComputedStyle(item).display === 'block') {
            this.deleteData(index, this.whitelist[index])
          }
        })
      }
    },
    async getwhiteList () {
      this.loading = true
      const rp = await this.$api.request('disctrbution.getTdRecipient', { planId: this.detail.planId, page: this.nextPage }, {}, 'distribution')
      this.loading = false
      if (!rp.listTotal) {
        this.whitelist = []
      }
      const list = []
      if (!rp['[]']) {
        this.clearWatch = false
        return
      }
      rp['[]'].forEach(x => {
        list.push({
          'Wallet Address': x.TdRecipient.wallet_address,
          Amount: Number(x.TdRecipient.amount),
          recipient_id: x.TdRecipient.recipient_id
        })
      })
      this.whitelist = this.whitelist.concat(list)
      this.nextPage += 1
      if (list.length !== 100) {
        this.clearWatch = false
        // this.ob.disconnect()
      }
      this.clearWatch = list.length === 100
    },
    endPickerOptions () {
      const startTime = this.time.start
      return {
        disabledDate (time) {
          if (startTime) {
            return time.getTime() < new Date(startTime).getTime() + 3600000
          }
          return time.getTime()
        }
      }
    },
    async addNewData () {
      if (!await this.$store.dispatch('checkCoin', this.addData.address)) {
        this.$message({
          type: 'error',
          message: 'This wallet address is invalid. Please check and enter a valid address.'
        })
      } else if (!this.addData.address) {
        this.$message({
          type: 'error',
          message: 'Wallet address is missing in one or more rows. Please complete all address fields.'
        })
      } else if (this.addData.anount === '' || !Number(this.addData.anount)) {
        this.$message({
          type: 'error',
          message: 'The amount entered is invalid. Please check and enter a valid value.'
        })
      } else {
        const rp = await this.$api.request('disctrbution.createTdRecipient', { length: 1 }, {}, 'distribution')
        const postTdRecipient = await this.$store.dispatch('postTdRecipient', {
          data: {
            TdRecipient: {
              recipient_id: rp['[]'][0].recipientId,
              plan_id: this.detail.planId,
              wallet_address: this.addData.address,
              batch_order: this.addData.Recipients,
              amount: this.addData.anount
            }
          },
          list: this.whitelist
        })
        if (!postTdRecipient) {
          this.$message({
            type: 'error',
            message: 'wallet address is repeat!'
          })
          return
        } else if (rp.code === 20) {
          this.$message({
            type: 'error',
            message: 'add new data error!'
          })
          return
        }
        this.showData = false
        this.whitelist.unshift({
          'Wallet Address': this.addData.address,
          Amount: this.addData.anount,
          recipient_id: rp['[]'][0].recipientId
        })
        this.addData = {
          address: '',
          anount: ''
        }
      }
    },
    downloadFile () {
      const handData = [['Wallet Address', 'Amount'],
        ['******************************************', '2100'],
        ['******************************************', '2100']
      ]
      const ws = utils.aoa_to_sheet(handData)
      const wb = utils.book_new()
      utils.book_append_sheet(wb, ws, 'Sheet1')
      wb.Sheets.Sheet1['!cols'] = [
        { wpx: 300 }, { wpx: 80 }
      ]
      const wbout = write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
      try {
        FileSaver.saveAs(new Blob([wbout], { type: 'application/octet-stream;charset=utf-8' }), 'RecipientsTemplate.xlsx')
      } catch (e) {
        if (typeof e !== 'undefined') {
          console.log(e, wbout)
        }
      }
    },
    async deleteData (index, data) {
      const rp = await this.$api.request('disctrbution.deleteTdRecipient', { recipientId: data.recipient_id }, {}, 'distribution')
      if (rp.code !== 200) return
      this.whitelist.splice(index, 1)
    },
    toCamelCase (str) {
      return str.replace(/_([a-z])/g, (match, p1) => p1.toUpperCase())
    },
    // getChangedFields (obj1, obj2) {
    //   const changed = {}
    //   Object.keys(obj1).forEach(key => {
    //     if (obj1[key] !== obj2[key] && obj2[key]) {
    //       changed[this.toCamelCase(key)] = obj2[key]
    //     }
    //   })
    //   return changed
    // },
    async checkForm () {
      if (!this.time.start) {
        this.time.errorStartState = true
      }
      if (!this.time.end) {
        this.time.errorEndState = true
      }
      if (!this.whitelist.length) {
        this.errorListState = true
      }
      if (!this.whitelist.length || !this.time.end || !this.time.start) {
        return false
      }
      // if (new Date(this.time.end).getTime() < (new Date(this.time.start).getTime() + 3600000)) {
      //   this.$message({
      //     type: 'error',
      //     message: 'The end date should be set later than one day after the start date!'
      //   })
      //   return false
      // }
      // if (new Date(this.time.start).getTime() < (Date.now() + (2000 * 60))) {
      //   this.$message({
      //     type: 'error',
      //     message: 'The start date should be set later than two hour!'
      //   })
      //   return false
      // }
      return this.putDistributionInfo()
    },
    async putDistributionInfo () {
      // const form = {
      //   startDate: formatGMTDate(this.time.start),
      //   endDate: formatGMTDate(this.time.end)
      // }
      const diff = {}
      if (this.step === 2) {
        diff.step = 3
      }
      if (this.detail.startDate) {
        if (new Date(this.time.start).getTime() !== new Date(this.detail.startDate).getTime()) {
          diff.startDate = formatGMTDate(this.time.start)
        }
        if (new Date(this.time.end).getTime() !== new Date(this.detail.endDate).getTime()) {
          diff.endDate = formatGMTDate(this.time.end)
        }
        if (new Date(this.time.start).getTime() > new Date(this.time.end).getTime()) {
          this.$message({
            type: 'error',
            message: 'Start date cannot be later than end date!'
          })
          return false
        }
      } else {
        diff.startDate = formatGMTDate(this.time.start)
        diff.endDate = formatGMTDate(this.time.end)
      }
      if (!Object.keys(diff).length) {
        return true
      }
      return await this.$store.dispatch('putDistributionInfo', diff)
    },
    handleExcelChange (file) {
      const raw = file.raw
      if (raw) {
        if (raw.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || raw.type === 'application/vnd.ms-excel' || raw.type === 'text/csv' || raw.type === 'application/json') {
          if (raw.size > 5 * 1024 * 1024) {
            this.$message({
              type: 'error',
              message: 'The file size exceeds the limit of 5MB, please upload again!'
            })
            return
          }
          this.readFile(raw)
        } else {
          this.$message({
            type: 'error',
            message: 'The file format is incorrect, please upload again!'
          })
        }
      } else {
        this.$message({
          type: 'error',
          message: 'Please upload a file!'
        })
      }
    },
    readFile (raw) {
      const reader = new FileReader()
      reader.onload = async (e) => {
        try {
          const data = e.target.result
          let datas = []
          if (raw.type === 'application/json') {
            datas = JSON.parse(data)
          } else {
            const workbook = read(data, {
              type: 'binary'
            })
            const sheet = workbook.Sheets[[workbook.SheetNames[0]]]

            const sheetObj = utils.sheet_to_json(sheet, { raw: false })

            datas = [...sheetObj]
          }

          // if (datas.find(x => !x['Wallet Address']) || datas.find(x => !x.Amount)) {
          //   throw Error
          // }
          if (datas.length > 50000) {
            this.$message({
              type: 'warning',
              message: 'Addresses exceed the 50,000-entry limit. Please reduce your list and try again.'
            })
            return
          }
          this.errorListState = false
          this.nextPage = 0
          this.clearWatch = false
          const formData = new FormData()
          formData.append('files', raw)
          let clearRp = { code: 80000000 }
          this.postFileError = false
          this.$message.closeAll()
          if (!this.checkFile(datas)) return
          if (this.whitelist.length) {
            clearRp = await this.$api.request('disctrbution.cleanupWhitelist', { planId: this.detail.planId }, {}, 'cleanupWhitelist')
          }
          if (clearRp.code !== 80000000) return
          const rp = await this.$api.request('disctrbution.postFile', { file: formData, planId: this.detail.planId, dataList: datas }, {}, 'distributionFile')
          if (rp.code === 80000000) {
            this.whitelist = []
            this.getwhiteList()
            this.postFileError = false
          } else {
            this.postFileError = true
            this.getwhiteList()
            if (!this.checkFile(datas)) return
          }
        } catch (error) {
          console.log(error)
          this.nextPage = 0
          if (this.$refs.myupload) this.$refs.myupload.clearFiles()
          this.$message.warning('The data format is incorrect, please modify and upload again')
          return false
        }
      }
      reader.readAsBinaryString(raw)
    },
    checkFile (whitelist) {
      const sumWithInitial = whitelist.map(x => {
        return x.Amount
      }).reduce(
        (x, y) => Number(x) + Number(y),
        0
      )
      whitelist.filter(x => console.log(x.Amount))
      // whitelist.filter(x => console.log(typeof x.Amount))?
      const permission = `
        <div class="mui-fl-vert title">
          <i class="mico-warning"></i>
          <div>Notice</div>
        </div>
        <div class="content ${sumWithInitial > this.detail.amount && 'block'}">The total amount entered exceeds the allowed limit. Please adjust the values.</div>
        <div class="content ${whitelist.filter(x => !x.Amount).length && 'block'}">Amount is missing in one or more rows. Please fill in all amount fields.</div>
        <div class="content ${whitelist.filter(x => x.Amount <= 0 || !isNumber(Number(x.Amount))).length && 'block'}">The amount entered is invalid. Please check and enter a valid value.</div>
        <div class="content ${whitelist.filter(x => !x['Wallet Address']).length && 'block'}">Wallet address is missing in one or more rows. Please complete all address fields.</div>
        <div class="content ${whitelist.length > 50000 && 'block'}">Addresses exceed the 50,000-entry limit. Please reduce your list and try again.</div>
        <div class="content ${this.postFileError && 'block'}">This wallet address is invalid. Please check and enter a valid address.</div>
      `
      if (
        sumWithInitial > this.detail.amount || whitelist.filter(x => !x.Amount).length ||
        whitelist.filter(x => x.Amount <= 0 || !isNumber(Number(x.Amount))).length || whitelist.filter(x => !x['Wallet Address']).length ||
        whitelist.length > 50000 || this.postFileError
      ) {
        this.$message({
          dangerouslyUseHTMLString: true,
          duration: 0,
          offset: 32,
          center: true,
          customClass: 'sty1-message sty5-message sty6-message',
          type: 'success',
          iconClass: 'mico',
          showClose: true,
          message: permission
        })
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/tokenDistribution/_distributionWhitelistStep2.scss" scoped></style>
