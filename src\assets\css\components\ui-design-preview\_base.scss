:root {
  --vt-c-primary-1: #005563;
  --vt-c-primary-1-rgb: 0, 85, 99;
  --vt-c-primary-2: #CCDDE0;
  --vt-c-primary-3: #F2F7F7;
  
  --vt-c-primary-1-disabled: #CCDDE0;

  --vt-c-text-1: #000000;
  /* dark */
  /* --vt-c-text-1: #FFFFFF; */

  --vt-c-text-2: #666666;
  --vt-c-text-3: #999999;
  --vt-c-text-4: #CCCCCC;
  --vt-c-text-5: #6699A1;
  --vt-c-text-6: #000000;

  --vt-c-secondary-1: #FFFFFF;
  --vt-c-secondary-2: #808080;
  --vt-c-secondary-3: #CCCCCC;
  --vt-c-secondary-4: #E6E6E6;
  --vt-c-secondary-5: #F5F5F5;
  /* dark */
  /* --vt-c-secondary-5: #262626; */

  --vt-c-tertiary-red: #EE6969;
  --vt-c-tertiary-blue: #64ABFF;
  --vt-c-tertiary-yellow: #FFE28E;
  --vt-c-tertiary-green: #88F0DA;

  --vt-c-warning: #ee6969;
  --vt-c-warning-rgb: 238, 105, 105;
  --vt-c-success: #88F0DA;
  --vt-c-success-rgb: 136, 240, 218;
  --vt-c-progressing: #64ABFF;
  --vt-c-progressing-rgb: 100, 171, 255;
  --vt-c-toast: #FFE28E;
  --vt-c-toast-rgb: 255, 226, 142;

  --vt-c-white: #002E33;
  --vt-c-white-soft: #345D62;
  --vt-c-white-mute: #7B9395;
  --vt-c-white-bg: #fff;
  /* dark */
  /* --vt-c-white-bg: #141414; */
  --vt-c-white-gradient: linear-gradient(180deg, #DCECFF 0%, #E3F1FF 47.25%, #FEFFFF 86%);
  --vt-c-white-gradient-2: linear-gradient(180deg, rgba(217, 246, 239, 0.8) 0%, rgba(217, 246, 239, 0.2) 100%), #FFFFFF;
  --vt-c-white-mask-rgb: 0,0,0;
  --vt-c-white-other: #fff;
  --vt-c-white-other-2: #D9D9D9;
  --vt-c-white-other-3: #000;
  --vt-c-white-other-3-rgb: 0,0,0;
  --vt-c-white-primary-1-rgb: 0, 85, 99;
  
  --vt-c-black: #002E33;
  --vt-c-black-soft: #345D62;
  --vt-c-black-mute: #7B9395;
  --vt-c-black-bg: #141414;
  --vt-c-black-gradient: linear-gradient(180deg, #DCECFF 0%, #E3F1FF 47.25%, #FEFFFF 86%);
  --vt-c-black-other: #fff;
  --vt-c-black-primary-1-opacity: 0, 85, 99;


  --vt-c-divider-white-1: rgba(0, 46, 51, 0.1);
  --vt-c-divider-white-2: #CEDBDB;

  --vt-c-divider-black-1: rgba(0, 46, 51, 0.1);

  --vt-ff-harmony: 'HarmonyOS Sans', 'HarmonyOS Sans WF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  --vt-ff-circular: 'Circular Std', 'Circular Std WF', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* semantic color variables for this project */
:root {
  --color-theme: var(--vt-c-primary-1);
  --color-theme-rgb-1: var(--vt-c-primary-1-rgb), 0.05;
  --color-theme-rgb-2: var(--vt-c-primary-1-rgb), 0.10;
  --color-theme-rgb-3: var(--vt-c-primary-1-rgb), 0.50;
  --color-theme-rgb-4: var(--vt-c-primary-1-rgb), 0.80;
  --color-theme-rgb-5: var(--vt-c-primary-1-rgb), 0.50;
  --color-theme-rgb-6: var(--vt-c-primary-1-rgb), 0.60;


  --color-background: var(--vt-c-white-bg);
  --color-background-2: var(--vt-c-white-gradient);
  --color-backgroune-3: var(--vt-c-white-other);
  --color-background-4: var(--vt-c-white-other-3-rgb);
  --color-background-5: var(--vt-c-white-primary-1-rgb);
  --color-background-6: var(--vt-c-tertiary-green);
  --color-background-7: var(--vt-c-tertiary-blue);
  --color-background-8: var(--vt-c-secondary-4);
  --color-background-9: var(--vt-c-primary-2);
  --color-background-10: var(--vt-c-secondary-5);
  --color-background-11: var(--vt-c-primary-1-disabled);
  --color-background-12: var(--vt-c-white-gradient-2);
  --color-background-13: var(--vt-c-white-other-2);
  --color-background-14: var(--vt-c-primary-1-rgb);
  --color-background-15: var(--vt-c-primary-3);
  --color-background-16: var(--vt-c-secondary-1);
  --color-background-mask: var(--vt-c-white-mask-rgb);

  /* --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute); */

  --color-border: var(--vt-c-divider-black-1);
  --color-border-1: var(--vt-c-secondary-3);
  --color-border-2: var(--vt-c-divider-white-2);
  --color-border-3: var(--vt-c-primary-1-rgb);
  --color-border-4: var(--vt-c-white-other);
  /* --color-border-hover: var(--vt-c-divider-light-1); */

  --color-text-1: var(--vt-c-black);
  --color-text-2: var(--vt-c-black-soft);
  --color-text-3: var(--vt-c-black-mute);
  --color-text-4: var(--vt-c-primary-1);
  --color-text-5: var(--vt-c-white-bg);
  --color-text-6: var(--vt-c-white-other-3-rgb);
  --color-text-7: var(--vt-c-text-2);
  --color-text-8: var(--vt-c-secondary-1);
  --color-text-9: var(--vt-c-text-1);
  --color-text-10: var(--vt-c-secondary-2);
  --color-text-11: var(--vt-c-text-3);
  --color-text-12: var(--vt-c-tertiary-red);
  --color-text-13: var(--vt-c-tertiary-yellow);
  --color-text-14: var(--vt-c-tertiary-green);
  --color-text-15: var(--vt-c-text-6);
  --color-text-16: var(--vt-c-white-other);
  --color-text-17: var(--vt-c-text-6), 0.80
  /* --section-gap: 160px; */
}


@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black-bg);
    --color-background-2: var(--vt-c-black-gradient);
    --color-backgroune-3: var(--vt-c-black-other);
    --color-background-4: var(--vt-c-white-other-3-rgb);
    --color-border: var(--vt-c-divider-white-1);
    --color-text-1: var(--vt-c-white);
    --color-text-2: var(--vt-c-white-soft);
    --color-text-3: var(--vt-c-white-mute);
  }
}


.zkme-widget {
  pointer-events: none;
  user-select: none;
  .sty1-lab {
    text-align: center;
  }
  
  .sty1-title {
    font-weight: 700;
    font-size: 20px;
    line-height: 140%;
    text-align: center;
    color: var(--color-text-9);
    padding-top: 12px;
    position: relative;
  }
  
  .sty1-title-flow {
    margin-left: 6px
  }

  .sty1-gp {
    width: 100px;
  }
  
  .sty1-gp-powered {
    width: 100%;
    font-size: 10px;
    line-height: 20px;
    padding: 20px;
    color: var(--color-text-9);
    box-sizing: border-box;
  }

  .sty1-gp-powered.dark{
    color: #FFFFFF;
  }
  .sty1-gp-powered img {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }

  .sty1-wrapper {
    height: 100%;
    background: var(--color-background-2);
    padding: 0 32px;
    /* padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom); */
  }
  
  .sty1-cell {
    position: relative;
    /* top: 50%;
    left: 50%;
    margin: -350px 0 0 -255px; */
    width: 100%;
    height: 100%;
    max-width: 510px;
    max-height: 700px;
    box-sizing: border-box;
    padding: 60px 55px 0 55px;
    border-radius: 16px;
    /* border: 1px solid red; */
    /* pointer-events: none; */
  }
  
  .sty1-cell .close-icon {
    cursor: pointer;
    position: absolute;
    top: 18px;
    right: 20px;
    z-index: 111;
  }
  .sty1-cell .close-icon::after {
    font-family: "iconfont";
    content: "\e735";
    font-size: 24px;
    line-height: 24px;
    color: rgba(0, 0, 0, 0.5);
  }
  
  .sty1-cell.no-padd {
    padding: 0;
  }
  .sty1-cell.no-padd::after {
    display: none;
  }
  
  .sty3-cell {
    margin-top: 24px;
  }
  
  .sty1-header {
    height: 60px;
    width: 100%;
    font-weight: 700;
    font-size: 18px;
    color: var(--color-text-9);
    background: var(--color-background);
    position: sticky;
    top: 0;
    left: 0;
  }
  
  .header-back {
    font-size: 24px;
    color: var(--color-text-9);
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
  
  .qr-cell {
    margin: 42px auto 24px auto;
  }
  .qr-cell2 {
    margin: 36px auto 20px auto
  }

  .sty1-cell.no-padd {
    padding: 0;
  }

  .sty1-cell.no-padd::after {
    display: none;
  }

  .sty3-cell {
    margin-top: 24px;
  }

  .sty1-footer {
    width: 100%;
    padding: 16px 60px 16px 60px;
    box-sizing: border-box;
    position: absolute;
    left: 0;
    bottom: 15px;
    background: var(--color-background);
    padding-bottom: calc(16px + constant(safe-area-inset-bottom));
    padding-bottom: calc(16px + env(safe-area-inset-bottom));
  }


  // Button
  .el-button {
    height: 36px !important;
    -webkit-tap-highlight-color: transparent;
  }
  .el-button.el-button--primary, .el-button--primary {
    color: var(--color-text-8);
    background-color: var(--color-theme);
    border-color: var(--color-theme);
  }
  .el-button.el-button--primary:focus,
  .el-button.el-button--primary:hover,
  .el-button.el-button--primary:active {
    background-color: var(--color-theme);
    border-color: var(--color-theme);
  }
  .el-button.el-button--primary:disabled,
  .el-button.el-button--primary.is-disabled,
  .el-button.el-button--primary.is-disabled:focus,
  .el-button.el-button--primary.is-disabled:hover {
    /* background-color: var(--color-background-11); */
    background-color: rgba(var(--color-theme-rgb-3));
    border: none;
  }
  
  .el-button--large {
    height: 48px !important;
  }
  .el-button--large.is-round {
    border-radius: 29px !important;
  }
  
  .el-button.width-1 {
    width: 160px;
  }
  
  .el-button.width-2 {
    width: 260px;
  }
  
  .el-button.width-3 {
    width: 100%;
  }
  
  .el-button.width-4 {
    width: 155px;
  }
  
  .el-button.sty1-button {
    background-color: var(--color-background-10);
    color: var(--color-text-9);
    font-size: 16px;
    border: none;
  }
  .el-button.sty1-button:focus,
  .el-button.sty1-button:hover,
  .el-button.sty1-button:active {
    background-color: var(--color-background-10);
    color: var(--color-text-9);
  }
  
  .el-button.sty2-button {
    background-color: var(--color-background-15);
    color: var(--color-text-9);
    font-size: 16px;
    border: none;
  }

  // Checkbox
  .el-checkbox:last-of-type {
    margin-right: 0;
  }

  .el-checkbox.sty1 {
    height: auto;
    white-space: pre-wrap;
    -webkit-tap-highlight-color: transparent;
    font-weight: 400;
  }
  .el-checkbox.sty1 .el-checkbox__label {
    font-size: 12px;
    line-height: 16px;
    letter-spacing: -1.01px;
    padding-left: 12px;
  }
  .el-checkbox.sty1 .el-checkbox__input .el-checkbox__inner {
    border-radius: 50%;
    border: 1.2px solid var(--color-theme);
    padding: 1px;
    transform: none;
    transition: none;
    background-color:var(--color-background-15);
    /* background-color: transparent; */
  }
  
  .el-checkbox.sty1 .el-checkbox__input.is-checked .el-checkbox__inner::after {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border: none;
    background-color: var(--color-background-16);
    border-radius: 50%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    transform: none;
  }
  
  .el-checkbox.sty1 .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: var(--color-theme);
    border-color: var(--color-theme);
  }
  
  .el-checkbox.sty2 .el-checkbox__input .el-checkbox__inner {
    background-color: transparent;
  }
  
  .el-checkbox.sty2 .el-checkbox__label {
    padding-left: 0;
  }
  
  .el-checkbox.sty2 .el-checkbox__input .el-checkbox__inner {
    border: 1.2px solid var(--color-background-11);
  }
  
  .el-checkbox.sty2 .el-checkbox__input.is-checked .el-checkbox__inner{
    border-color: var(--color-theme);
    background-color: var(--color-theme);
  }
  
  .el-checkbox.sty2 .el-checkbox__input.is-checked .el-checkbox__inner::after {
    background-color: var(--color-background-15);
  }
}

.zkme-widget.dark {
  .popup-select {
    color: #fff;
  }
}

.zkme-widget.web {
  width: 510px;
  height: 700px;
}

.zkme-widget.mobile {
  width: 375px;
  height: 812px;

  .sty1-gp-powered {
    padding: 8px;
  }

  .sty1-cell {
    max-width: 375px;
    max-height: 812px;
    padding: 40px 24px 0;
  }
  .sty1-cell .close-icon {
    width: 24px;
    height: 24px;
    background-color: #F6F6F6;
    border-radius: 50%;
    top: 16px;
    right: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .sty1-cell .close-icon::after {
    font-size: 20px;
    line-height: 20px;
    font-weight: 550;
  }
}