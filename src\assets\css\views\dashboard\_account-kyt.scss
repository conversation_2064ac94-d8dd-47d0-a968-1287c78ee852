.account {
  margin-top: 24px;
  border-radius: 12px;
  border: 1px solid rgba(169, 225, 211, 0.60);
  background: rgba(132, 182, 184, 0.12);
  padding: 26px 32px;
}
.account li .t1 {
  color: #33585C;
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
}
.account li .t2 {
  margin-left: 12px;
  padding: 8px 20px;
  border-radius: 38px;
  color: #002E33;
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
  background-color: #fff;
}

.account li p i {
  font-size: 24px;
  margin-right: 6px;
  color: #738C8F;
  margin-right: 6px;
}
.account li .t3 {
  color: #33585C;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.account li .t4 {
  margin-left: 4px;
  color: #33585C;
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
}
.account .download {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #fff;
  font-size: 20px;
}
.kyt-price-text {
  position: absolute;
  top: 0;
  white-space: nowrap;
}
.kyt-price-box {
  margin-right: 24px;
  height: 40px;
  padding: 8px;
  border-radius: 7px;
  background-color: #F7F7F7;
}
.kyt-price-analysis-box {
  white-space: nowrap;
  margin-top: 30px;
}
.kyt-opacity {
  opacity: 0;
}
.kyt-analysis-text-mg {
  margin-bottom: 8px;
}

.kyt-title-mg-top40 {
  margin-top: 40px;
}
.kyt-title-mg-top24 {
  margin-top: 24px;
}

.kyt-transaction-actions {
  position: absolute;
  top: 50%;;
  left: 50%;
  transform: translate(-50%, -50%);
  img {
    width: 27px;
    height: 27px;
  }
  .title {
    font-weight: 450;
    font-size: 14px;
    line-height: 17px;
    color: #33585C;
    opacity: .6;
    margin-top: 6px;
    text-align: center;
  }
}
.kyt-segmentation {
  margin: 0 8px;
  color: #F0F0F0;
}
.kyt-wallectAddress {
  margin: 8px 0 36px;
}
.kyt-wallet-token {
  height: 64px;
  margin-bottom: 16px;
}
.kyt-tokenList {
  width: 24px;
  height: 24px;
  margin-top: 8px;
  cursor: pointer;
  transform: scale(1.230769230769231);
  margin-right: 18px;
}
.kyt-content-analysis-text-mg4 {
  .kyt-content-analysis-text {
    margin-bottom: 4px;
  }
}
.kyt-content-analysis-text-mg32 {
  margin-right: 32px;
}

.tokenImgsty {
  width: 60px;
  height: 60px;
  position: absolute;
  left: 0;
  top: 0;
}
.chainImg {
  transform: scale(3);
  position: relative;
  top: 16px;
  left: 16px;
  z-index: 10;
}
.kyt-Profile-text-height{
  width: 100%;
  position: relative;
  >div {
    width: 100%;
  }
}
.detail {
  // border-bottom: 1px solid #EE6969;
  cursor: pointer;
  position: relative;
  text-decoration: underline;
}
.kyt-labels-box {
  width: auto;
  height: auto;
  padding: 12px 8px;
  margin:0 16px 16px 0;
  // white-space: nowrap;
  // height: 22px;
}
.no-data {
  height: 100%;
  div {
    font-weight: 450;
    font-size: 14px;
    line-height: 18px;
    color: #B3C0C2;
  }
  // position: absolute;
  // top: 50%;
  // left: 50%;
  // transform: translate(-50%, -50%);
  img {
    margin-bottom: 16px;
    width: 128px;
    height: 128px;
  }
}
.kyt-coming-text {
  margin: 40px;
}
.kyt-incoming-pilot {
  width: 9px;
  height: 9px;
  margin-right: 4px;
  border-radius: 50%;
}
.kyt-title-text, .kyt-analysis-token-text {
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
  color: #002E33;
}
.kyt-analysis-token-text {
  font-size: 20px;
}
.kyt-bor {
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #F0F0F0;
  margin-right: 24px;
  margin-top: 16px;
  height: calc(100% - 72px);
  position: relative;
}
.kyt-title-mg-right0 {
  margin-right: 0;
}
.kyt-wallet-text, .kyt-token-title-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #002E33;
}
.kyt-token-title-text {
  font-size: 16px;
}
.kyt-chain-text {
  font-weight: 500;
  font-size: 18px;
  line-height: 25px;
  color: #738C8F;
}
// .kyt-token-text {
//   font-weight: 700;
//   font-size: 16px;
//   line-height: 22px;
//   color:#002E33;
// }
.mico-copy-cp {
  cursor: pointer;
}
.mico-help-linear, .mico-copy, .mico-tag {
  margin-left: 4px;
  font-size: 20px;
}
.mico-tag {
  margin: 0 4px 0 0;
}
.e-chart-radar {
  width: 380px;
  height: 199px;
}
.moderateb {
  border-radius: 12px;
  border: 1px solid rgba(169, 225, 211, 0.6);
  background: linear-gradient(0deg, rgba(132, 182, 184, 0.12), rgba(132, 182, 184, 0.12));
  height: 72px;
  position: relative;
  .moderate {
    border-radius: 48px;
    color: #005563;
    padding: 2px 21px;
    margin: -2px -1px 0;
  }
  .moderatenb {
    margin-top: 7px;
  }
}
.moderate-Low {
  background: #A9E1D3;
}
.moderate-Severe {
  background: #FEA491;
}
.moderate-High {
  background: #FFE28E;
}
.moderate-Moderate {
  background: #64ABFF;
}
.eChart {
  width: 235px;
  height: 235px;
  margin: 24px 0;
}
.kyt-content-title-text, .kyt-content-analysis-text, .kyt-address-analysis-text, .kyt-score-text {
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color:#738C8F;
}
.kyt-score-text {
  line-height: 20px;
  color: #005563;
}
.kyt-score-price-text {
  font-weight: 700;
  font-size: 28px;
  line-height: 36px;
  color: #005563;
}
.kyt-content-text {
  font-weight: 700;
  font-size: 16px;
  line-height: 22px;
  color: #002E33;
  white-space: nowrap;
}
.kyt-warn-text {
  border-radius: 8px;
  background: rgba(238, 105, 105, 0.06);
  padding: 12px;
  margin-top: 18px;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  color: #EE6969;
  .mico-warning {
    margin-right: 4px;
  }
}
.kyt-analysis-text {
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #002E33;
}

.kyt-content-analysis-text {
  line-height: 20px;
  white-space: nowrap;
}

.kyt-analysis-price-text {
  font-weight: 700;
  font-size: 14px;
  line-height: 18px;
  color: #002E33;
}

.kyt-address-analysis-text {
  font-size: 12px;
  line-height: 16px;
}

.txn-graph{
  margin-top: 40px;
  .no-data {
    min-height: 400px;
  }
}

.txn-graph-loading-or-empty{
  width: 100%;
  height: 1020px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  background: #f2f7f7;
  padding: 24px;
  margin-top: 16px;
}

.loading-txt {
  color: #005563;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  margin-top: 8px;
}