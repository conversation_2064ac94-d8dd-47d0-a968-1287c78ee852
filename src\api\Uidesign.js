/**
 * https://git.bitkinetic.com/zkme/zkme-admin-nest/src/branch/dev_widget_design/src/base/controllers/mchInfo.controller.ts
 */
export const widgetDesignConfig = () => {
  return {
    method: 'get',
    url: '/api/mchInfo/widgetDesignConfig',
    params: {}
  }
}
/**
 * https://git.bitkinetic.com/zkme/zkme-admin-nest/src/branch/dev_widget_design/src/base/controllers/mchInfo.controller.ts
 */
export const resetWidgetDesign = () => {
  return {
    method: 'post',
    url: '/api/mchInfo/resetWidgetDesign',
    params: {}
  }
}
/**
 * https://git.bitkinetic.com/zkme/zkme-admin-nest/src/branch/dev_widget_design/src/base/controllers/mchInfo.controller.ts
 */
export const updateWidgetDesign = ({ lightColor1, lightColor2, lightColor3, darkColor1, darkColor2, darkColor3, mode, customCss }) => {
  return {
    method: 'post',
    url: '/api/mchInfo/updateWidgetDesign',
    data: {
      lightColor1,
      lightColor2,
      lightColor3,
      darkColor1,
      darkColor2,
      darkColor3,
      mode,
      customCss
    }
  }
}
