<template>
  <div>
    <div class="mui-fl-btw top_function">
      <m-button @click="recordedinp = '', ($emit('Results', 'Create Program', '', tableData.length))"><i class="mico-creat"></i> Create Program</m-button>
      <MuiInput @enter="search" placeholder='Search for program name' v-model="recordedinp" class="inp" :closeflg="true" :searchflg="true" @search="search" @clear="clear" />
    </div>
    <div class="mui-fl-vert">
      <m-checkbox class="extendcheckbox" v-model="checked1" @change="handleCurrentChange()">Pin current apply</m-checkbox>
      <!-- <m-checkbox class="extendcheckbox" :disabled="!checked1" v-model="checked2" @change="handleCurrentChange()">Hide unapplied</m-checkbox> -->
      <div class="redivision"></div>
      <span>

        <m-dropdown v-if="!tagindex.val" trigger="click" @command="handleCommand">
          <m-button class="dropdownbut recorded_but mui-fl-vert">
            <span>Filter Highest level</span>
            <i class="mico-fold"></i>
          </m-button>
          <m-dropdown-menu slot="dropdown" class="extendlist recorded_list">
            <m-dropdown-item v-for="(i, d) of kycLevel" :key="d" :command="i">{{i.levelName}}</m-dropdown-item>
          </m-dropdown-menu>
        </m-dropdown>
        <m-tag
          v-else
          class="recorded_tag"
          :disable-transitions="true"
          @click="tagclose"
          >
          <div class="mui-fl-vert">
            <div>{{tagindex.val}}</div>
            <i class="mico-close"></i>
          </div>
        </m-tag>
      </span>
    </div>
    <m-divider class="divider"></m-divider>

    <div class="recorded_table-wrap mui-fl-col mui-fl-btw">
      <m-table
        :ref="'table'"
        :class="['recorded_table', (checked1 || checked2) && 'table_topping']"
        :data="tableData"
        @cell-click="expandDetails"
        @cell-mouse-enter="cellMouseEnter"
        @cell-mouse-leave="cellMouseLeave"
        :row-class-name=rowClassname
        :cell-class-name=cellClassname
        @sort-change="sortchange"
        :default-sort = "{prop: 'lastUpdateTime', order: 'descending'}"
      >
        <m-table-column prop="date" label="Program Name" min-width="110px">
          <template #default="{ row }">
            <m-popover
              :offset="10"
              popper-class="NotRecord_popover Record_popover"
              :disabled="(row.offsetWidth + 1) < programNameWidth"
              placement="top-start"
              trigger="hover"
              :content="row.programName">
              <div slot="reference" :ref="'programName'">
                <span :ref="'programText'" class="spanprogramName">
                  {{row.programName}}
                </span>
              </div>
            </m-popover>
          </template>
        </m-table-column>

        <m-table-column sortable="custom" prop="highestLevel" label="Highest Level" min-width="140px">
          <template #header>
            <span>Highest Level</span>
            <span class="caret-wrapper">
              <i class=" mico-sort-up"></i>
              <i class=" mico-sort-dow"></i>
            </span>
          </template>
        </m-table-column>

        <m-table-column sortable="custom" prop="items" label="Items" min-width="100px">
          <template #header>
            <span>Items</span>
            <span class="caret-wrapper">
              <i class=" mico-sort-up"></i>
              <i class=" mico-sort-dow"></i>
            </span>
          </template>
        </m-table-column>

        <m-table-column prop="status" label="Status" min-width="100px">
          <template #default="{ row }">
            <div :class="['mui-fl-vert', row.statusdate.name.trim() + 1]">
              <div class="tableballcolor ball"></div>
              <div class="row_status">{{row.statusdate.name}}</div>
            </div>
          </template>
        </m-table-column>

        <m-table-column sortable="custom"  prop="lastUpdateTime" label="Last update time" min-width="165px">
          <template #header>
            <span>Last Update Time</span>
            <span class="caret-wrapper">
              <i class=" mico-sort-up"></i>
              <i class=" mico-sort-dow"></i>
            </span>
          </template>
          <!-- <template #default="{ row }">
            <m-popover
              popper-class="NotRecord_popover Record_popover"
              placement="top-start"
              trigger="hover"
              :offset="20"
              :content="(row.statusdate.name === 'Apply ' ? 'Applied ' : row.statusdate.name) + row.statusdate.timetip + row.lastUpdateTime">
              <div slot="reference">{{row.lastUpdateTime}}</div>
            </m-popover>
          </template> -->
        </m-table-column>

        <m-table-column sortable="custom" prop="auditedUser" label="Audited User" min-width="135px">
          <template #header>
            <span>Audited User</span>
            <span class="caret-wrapper">
              <i class=" mico-sort-up"></i>
              <i class=" mico-sort-dow"></i>
            </span>
          </template>
          <template #default="{ row }">
            {{Number(row.auditedUser) || '--'}}
          </template>
        </m-table-column>

        <m-table-column prop="kycProgramId" label="Program No." v-if="!isCollapse" min-width="130px">
          <template #default="{ row }">
            <span class="kycProgramId">{{row.kycProgramId || '--'}}
              <div v-if="!row.copystyle && row.program_no" @click.stop="copy(row)" :class="['program_copy', (checked1 || checked2) && 'table_Copy']">Copy</div>
              <div v-if="row.copystyle && row.program_no" :class="['program_copy', 'Copied', (checked1 || checked2) && 'table_Copy']">Copied</div>
            </span>
          </template>
        </m-table-column>
      </m-table>

      <div v-show="!dataflag" class="no-table-data mui-fl-col mui-fl-vert">
        <img src="~@/assets/img/no-table-data.png" alt="">
        <p>No Record</p>
      </div>

      <div class="mui-fl-end">
        <m-pagination
          hide-on-single-page
          v-show="tableData.length"
          class="sty1-pagination sty3-cell"
          @current-change="handleCurrentChange"
          :current-page.sync="page.page"
          layout="prev, pager, next"
          :page-size="10"
          :total="page.total">
        </m-pagination>
      </div>
    </div>

    <m-drawer :modal="false" @close="openModified = false" :visible.sync="drawer" :title="detailsList.programName" class="recorded_drawer" width="486">
      <div class="mui-flex program drawer_margin">
        <span>Program No. </span>
        <span>{{detailsList.kycProgramId}}</span>
        <div class="m" style="height: 14px"></div>
        <span v-if="!drawerCopy" class="l" @click="copy(detailsList.kycProgramId)">Copy</span>
        <span v-else class="l cop">Copied</span>
      </div>

      <div v-for="(i, d) of detailsList.kycList" :key="d">
        <div class="Sybil" v-if="!i.status">
          <m-divider class="Sybil" content-position="left" v-if="i.adminKycPropertyBoList.map(x => x.value.filter(f => f.isSelect).length).reduce((x, y) => x + y)">
            {{i.levelName}} - {{i.levelDescription}}
          </m-divider>
        </div>
        <div v-for="(f, d) of i.adminKycPropertyBoList" :key="d">
          <div class="drawer_margin lv0" v-if="(i.levelName === 'Lvl.0' && f.value.filter(x => x.isSelect).length)">
            {{f.kycVerifyProperty}}
          </div>

          <div v-if="(i.levelName === 'Lvl.1' && f.value.filter(x => x.isSelect).length)">
            <div class="Simplelist mui-flex drawer_margin">
              <div class="personal recorded_personal">
                <div class="l" :ref="'kycVerifyProperty'" :style="{minWidth: drawer ? Math.max(...lv1offsetWidth) + 'px' : '0px'}">{{f.kycVerifyProperty}}</div>
              </div>
              <div class="mui-fl-wrap recorded_information">
                <div v-for="(t, x) of f.value" :key="x">
                  <div :style="{marginBottom: f.value.filter(x => x.isSelect).length === 3 && x !== 2 ? '4px' : ''}" class="r" v-if="t.isSelect">
                    <div>{{t.symbol}} {{t.value}}
                      <span :style="{margin: f.kycVerifyProperty === 'ID document' ? '0 16px' : '0 8px'}" class="redivision" v-if="((f.value.filter(x => x.isSelect).length) - x - 1 >= 1)"></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="drawer_margin" style="margin: 0 20px 0 24px;"><m-button class="recorded_button" style="font-weight: 500;" v-if="(detailsList.status === 1)" @click="$emit('fentry', detailsList, true), recordedinp = ''">Get a copy</m-button></div>
      <div class="drawer_margin" style="margin: 0 20px 0 24px;"><m-button class="recorded_button" style="font-weight: 500;" v-if="(detailsList.status === 2)" @click="$emit('fentry', detailsList, false), recordedinp = ''">Modify program</m-button></div>

      <div class="drawer_records drawer_margin" v-if="detailsList.recordsList?.length">Records</div>

      <div class="drawer_margin drawer_step">
        <div v-for="(i, d) of detailsList.recordsList" :key="d">
          <!-- {{ detailsList.recordsList.filter(x => x.status === 1).length }}
          {{ i.status }} -->
          <div
            v-if="
              (detailsList.recordsList.filter(x => x.status === 1).length && i.status !== 3) ||
              i.status === -1 || i.status === 2 || i.status === 4 ||
              (!detailsList.recordsList.filter(x => x.status === 1).length && i.status === 3)
              "
            :class="['mui-flex', 'mui-fl-vert', i.status === -1 ? 'Expired' : i.status === 1 ? 'Apply' : 'Created']">
            <div class="boolball" v-if="i.status !== 3 || ((i.status === 3) && i.createTime === detailsList.recordsList.filter(x => x.status === 3)[0].createTime)"></div>
            <span class="drawer_text_hand" v-if="(i.status === -1)">Expired </span>
            <span class="drawer_text_hand" v-if="(i.status === 1)">Applied </span>
            <span class="drawer_text_hand" v-if="(i.status === 2)">Created </span>
            <span class="drawer_text_hand" v-if="(i.status === 3) && i.createTime === detailsList.recordsList.filter(x => x.status === 3)[0].createTime" >Last modified </span>
            <span class="drawer_text_hand" v-if="(i.status === 4)">Expired </span>
            <span class="drawer_text" v-if="i.status !== 3 || i.createTime === detailsList.recordsList.filter(x => x.status === 3)[0].createTime">
              {{(i.status === 1 ? 'from ' : 'at ')}}
              {{i.status !== 3 ? i.createTime : detailsList.recordsList.filter(x => x.status === 3)[0].createTime}}
            </span>
          </div>
          <!-- textma // 样式 -->
          <div
            :class="['drawer_division', 'mui-fl-vert',
            i.createTime === detailsList.recordsList.filter(x => x.status === 3)[0]?.createTime && 'textma']"
            v-if="(
              ((detailsList.recordsList.length - 1) - d) && i.status !== 3 && i.status === 1 && !detailsList.recordsList.filter(x => x.status === 3).length) ||
              i.createTime === detailsList.recordsList.filter(x => x.status === 3)[0]?.createTime
            ">
            <span class="text" v-if="i.createTime === detailsList.recordsList.filter(x => x.status === 3)[0]?.createTime">
              <span @click="openModified = !openModified">
                Modified {{detailsList.recordsList.filter(x => x.status === 3).length}} times <i :class="['mico-fold', openModified && 'mico-fold-rotate']"></i>
              </span>
              <div v-if="openModified" style="cursor: default;">
                <div class="foldtext" v-for="(i, d) of detailsList.recordsList.filter(x => x.status === 3)" :key="d">
                  Modified at {{i.createTime}}
                </div>
              </div>
            </span>
          </div>
        </div>
      </div>

      <!-- <div class="drawer_records drawer_margin">Bill</div>
      <div class="drawer_margin">付费记录 咱不知道有啥</div> -->

      <div class="mui-fl-end drawer_margin drawer_end">
        <!-- <m-button v-if="(detailsList.status === 1)" class="recorded_button cancel_button" >Cancel service</m-button> -->
        <m-button v-if="(detailsList.status === 2)" style="font-size: 16px;" class="recorded_button cancel_button" @click="updateStatus('Delete', -1)">Delete program</m-button>
        <m-popover
          v-if="(detailsList.status === 2)"
          popper-class="NotRecord_popover FN"
          placement="top"
          width="98"
          trigger="hover"
          content="Free for limit time">
          <!-- <m-button class="recorded_button cancel_button freebut" slot="reference">Free Now!</m-button> -->
          <m-button style="font-size: 16px;" v-if="(detailsList.status === 2)" class="recorded_button cancel_button freebut" slot="reference" @click="updateStatus('Apply', 1)">Free Now!</m-button>
        </m-popover>
        <!-- <m-button v-if="(detailsList.status === 2)" class="recorded_button cancel_button freebut" slot="reference" @click="updateStatus('Apply', 1)">Apply</m-button> -->
      </div>
    </m-drawer>

    <KycWarning :model="model" :warningtip="warningtip" @Leave="Leave"></KycWarning>

    <m-dialog title="Apply a new program" :visible.sync="dialogTableVisible" class="recdialog">
      <div class="dialog_title">Already have a program in application, confirm to cancel the service and apply the new program?</div>
      <m-table :data="tableDatas" class="dialog_table" :border="true" empty-text="Not Recorded">
        <m-table-column prop="date">
          <template slot="header">
            <div>Before</div>
            <div>Lvl.0</div>
          </template>
        </m-table-column>
        <m-table-column prop="age">
          <template slot="header">
            <div>After</div>
            <div>Lvl.1</div>
          </template>
        </m-table-column>
      </m-table>
      <div class="mui-fl-end recorded_drawer">
        <m-button class="recorded_button dialog_button_cancel" @click="dialogTableVisible = false">Cancel</m-button>
        <m-button class="recorded_button dialog_button_confirm" @click="dialogTableVisible = false">Confirm</m-button>
      </div>
    </m-dialog>

  </div>
</template>
<script>
import KycWarning from '@/components/kyc_warning/KycWarning.vue'
import MuiInput from '@/components/mui-input/MuiInput.vue'

export default {
  components: { KycWarning, MuiInput },
  props: {
    update: {
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      parameter: {
        time: '',
        auditedUser: '',
        highestLevel: '',
        items: ''
      },
      dataflag: true,
      operationdata: {},
      model: 'Recorded' || 'program',
      drawerCopy: false,
      isCollapse: false,
      recordedinp: '',
      copystyle: false,
      drawer: false,
      checked1: true,
      checked2: false,
      warningtip: false,
      dialogTableVisible: false,
      lastUpdateTime: 'descending',
      kycLevel: [],
      detailsList: {},
      tableDatas: [],
      tableData: [],
      tagindex: {
        val: '',
        id: ''
      },
      page: {
        page: 1,
        size: 10
      },
      // temchecked1: false,
      // temchecked2: false,
      initialization: {},
      openModified: false,
      programNameWidth: '',
      lv1offsetWidth: []
    }
  },
  watch: {
    // 分页
    async '$route.query' (val) {
      await this.queryUserKycList()
      this.tableData.length && await this.pushprogramName()
      // if (Object.entries(val || {}).toString() === Object.entries(this.initialization || {}).toString() || !Object.entries(val || {}).toString()) {
      //   this.$emit('retrieval', false)
      // } else {
      //   this.$emit('retrieval', true)
      // }
    }
  },
  async created () {
    if (localStorage.getItem('beforeunload')) {
      this.handleCurrentChange()
    }
    await this.getAllList()
    await this.queryUserKycList()
    this.tableData.length && this.pushprogramName()
    if (this.update.state) {
      this.expandDetails(this.update)
    }
  },
  async mounted () {
    this.onWinResize()
    window.addEventListener('resize', this.onWinResize)
    window.addEventListener('beforeunload', this.beforeunload)
  },
  methods: {
    beforeunload () {
      localStorage.setItem('beforeunload', '1')
    },
    pushprogramName () {
      this.programNameWidth = this.programNameWidth || this.$refs.programName?.offsetWidth
      const spanprogramName = [...document.getElementsByClassName('spanprogramName')]
      if (this.tableData.length && spanprogramName.length) {
        this.tableData.forEach(v => {
          spanprogramName.forEach(x => {
            if (v.programName === x.textContent.trim()) {
              this.$set(v, 'offsetWidth', x.offsetWidth)
            }
          })
        })
      }
    },
    onWinResize () {
      this.isCollapse = document.body.clientWidth <= 1000
      setTimeout(() => {
        if (this.$refs.programName) {
          this.programNameWidth = this.$refs.programName?.offsetWidth
        }
      }, 200)
    },
    headercellclassname ({ column }) {
      // if (column.label === 'Last update time') {
      //   column.order = this.lastUpdateTime
      // }
    },
    sortchange ({ column, prop, order }) {
      this.parameter = {
        time: '',
        auditedUser: '',
        highestLevel: '',
        items: ''
      }
      if (order) {
        this.lastUpdateTime = order
      } else {
        this.lastUpdateTime = 'ascending'
        column.order = this.lastUpdateTime
      }
      switch (prop) {
        case 'lastUpdateTime':
          this.parameter.time = this.lastUpdateTime === 'ascending' ? 1 : 0
          break
        case 'highestLevel':
          this.parameter.highestLevel = this.lastUpdateTime === 'ascending' ? 1 : 0
          break
        case 'items':
          this.parameter.items = this.lastUpdateTime === 'ascending' ? 1 : 0
          break
        case 'auditedUser':
          this.parameter.auditedUser = this.lastUpdateTime === 'ascending' ? 1 : 0
          break
      }
      this.handleCurrentChange()
    },
    async getAllList () {
      const rp = await this.$api.request('kyc.getAllList')
      if (rp.code === 80000000) {
        this.kycLevel = rp.data
      }
    },
    handleCurrentChange (val) {
      if (typeof val === 'number') {
        this.page.page = val
        val = false
      }
      // if (val) {
      //   this.lastUpdateTime = 'descending'
      //   this.$refs.table.columns.forEach(v => {
      //     v.order = ''
      //     if (v.label === 'Last update time') {
      //       v.order = 'descending'
      //     }
      //   })
      // }
      this.initialization = val
      const route = {
        page: !val ? this.page.page : 1,
        time: !val ? this.parameter.time : 0,
        auditedUser: !val ? this.parameter.auditedUser : '',
        highestLevel: !val ? this.parameter.highestLevel : '',
        items: !val ? this.parameter.items : '',
        val: !val ? this.tagindex.val : '',
        id: !val ? this.tagindex.id : '',
        checked1: !val ? this.checked1 : true,
        checked2: !val ? this.checked2 : false,
        recordedinp: !val ? this.recordedinp : ''
      }
      this.$router.push({ query: route })
    },

    async queryUserKycList () {
      this.page.page = Number(this.$route.query.page) || 1
      this.page.size = Number(this.$route.query.size) || 10
      this.checked1 = this.$route.query.checked1 === 'true' || this.checked1 || false
      this.checked2 = this.$route.query.checked2 === 'true' || false
      this.recordedinp = this.$route.query.recordedinp || ''
      this.parameter.time = this.$route.query.time || 0
      this.parameter.auditedUser = this.$route.query.auditedUser || ''
      this.parameter.highestLevel = this.$route.query.highestLevel || ''
      this.parameter.items = this.$route.query.items || ''
      this.tagindex.val = this.$route.query.val || ''
      this.tagindex.id = this.$route.query.id || ''
      const data = {
        auditedUser: this.parameter.auditedUser,
        filterHighestLevel: this.tagindex.id,
        hideUnapplied: this.checked2 ? 1 : 0,
        highestLevel: this.parameter.highestLevel,
        items: this.parameter.items,
        pageReq: this.page,
        pinCurrentApply: this.checked1 ? 1 : 0,
        programName: this.recordedinp,
        time: this.parameter.time
      }
      const rp = await this.$api.request('kyc.queryUserKycList', data)
      if (rp.code === 80000000) {
        if ((!rp.data.list || !rp.data.list.length) && !this.recordedinp && !this.tagindex.id) {
          this.$emit('modify', true)
          return
        }
        if (!rp.data.list || !rp.data.list.length) {
          this.dataflag = false
        } else {
          this.dataflag = true
        }
        rp.data.list.forEach(v => {
          v.statusdate = this.statuscf(v.status)
        })
        if (!rp.data.list.length) {
          this.$emit('retrieval', true, 'No Results')
        }
        localStorage.setItem('beforeunload', '')
        this.tableData = rp.data.list
        this.page = rp.data.page
      }
    },
    statuscf (val) {
      const vlaue = {
        name: '',
        timetip: 'at '
      }
      switch (val) {
        case '-1':
          vlaue.name = 'Delete '
          break
        case '1':
          vlaue.name = 'Apply '
          vlaue.timetip = 'from '
          break
        case '2':
          vlaue.name = 'Created '
          break
        case '3':
          vlaue.name = 'Update '
          break
        case '4':
          vlaue.name = 'Expired '
          break
        default:
          break
      }
      return vlaue
    },
    clear () {
      this.recordedinp = ''
      this.handleCurrentChange()
      this.$emit('retrieval', false)
    },
    search () {
      if (!this.recordedinp) {
        return
      }
      this.$emit('retrieval', true, 'Search Results')
      this.handleCurrentChange()
    },
    tagclose () {
      this.tagindex.val = ''
      this.tagindex.id = ''
      if (!this.recordedinp) {
        this.$emit('retrieval', false)
        this.handleCurrentChange()
      } else {
        this.search()
      }
    },
    handleCommand (val) {
      this.tagindex.val = val.levelName
      this.tagindex.id = val.id
      this.handleCurrentChange()
    },
    cellClassname (row) {
      if (row.column.label === 'Program Name' && row.column.property === 'date') {
        return 'cellClassname'
      }
    },
    rowClassname (row) {
      return this.statuscf(row.row.status).name
    },
    cellMouseEnter (row, column) {
      if (column.label === 'Program No.') {
        this.$set(row, 'program_no', true)
      }
    },
    cellMouseLeave (val, column) {
      if (column.label === 'Program No.') {
        val.program_no = false
      }
    },
    copy (row) {
      const elInput = document.createElement('input')
      elInput.value = typeof row === 'object' ? row.kycProgramId : row
      document.body.appendChild(elInput)
      elInput.select()
      document.execCommand('Copy')
      elInput.remove()
      if (typeof row === 'object') {
        this.$set(row, 'copystyle', true)
        setTimeout(() => {
          row.copystyle = false
        }, 500)
      } else {
        this.drawerCopy = true
        setTimeout(() => {
          this.drawerCopy = false
        }, 500)
      }
    },
    async Leave (val) {
      this.warningtip = false
      if (val === 'Leave') {
        const rp = await this.$api.request('kyc.updateStatus', this.operationdata)
        if (rp.code === 80000000) {
          this.drawer = false
          this.queryUserKycList()
        }
      }
    },
    async expandDetails (row) {
      const rp = await this.$api.request('kyc.queryKycInfo', { id: row.id, category: 2 })
      if (rp.code === 80000000) {
        this.drawer = true
        this.detailsList = rp.data
        this.lv1offsetWidth = []
        this.detailsList.kycList.forEach(v => {
          if (v.levelName === 'Lvl.1') {
            v.adminKycPropertyBoList.forEach((x, i) => {
              if (x.value.find(s => s.isSelect)) {
                setTimeout(() => {
                  this.lv1offsetWidth.push(this.$refs.kycVerifyProperty[i]?.offsetWidth)
                }, 0)
              }
            })
          }
        })
      }
    },
    updateStatus (tip, status) {
      if (tip) {
        this.warningtip = true
      }
      this.model = tip
      this.operationdata = {
        id: this.detailsList.id,
        status: status
      }
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/views/kycpage/_recorded.scss" Scoped></style>
