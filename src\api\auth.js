
/**
 * 登录
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/%E8%AE%A4%E8%AF%81%E7%AE%A1%E7%90%86/validateUsingPOST
 */
export const login = payload => {
  return {
    method: 'post',
    url: '/api/auth/validate',
    data: {
      payload
    }
  }
}

// /**
//  * 验证密码
//  * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/%E5%BD%93%E5%89%8D%E7%99%BB%E5%BD%95%E7%AE%A1%E7%90%86/verifyPwdUsingPOST
//  */
export const verifyPwd = payload => {
  return {
    method: 'post',
    url: '/api/account/verifyPwd',
    data: {
      payload
    }
  }
}
// /**
//  * 退出登录
//  * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/%E5%BD%93%E5%89%8D%E7%99%BB%E5%BD%95%E7%AE%A1%E7%90%86/logoutUsingPOST
//  */
export const logout = () => {
  return {
    method: 'post',
    url: '/api/current/logout'
  }
}

/**
 * 邮箱验证
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/%E8%AE%A4%E8%AF%81%E7%AE%A1%E7%90%86/forgetPwdUsingPOST
 */
export const email = loginEmail => {
  return {
    method: 'post',
    url: '/api/account/forgetPwd',
    data: {
      loginEmail
    }
  }
}
/**
 * 邮箱验证
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/%E8%AE%A4%E8%AF%81%E7%AE%A1%E7%90%86/forgetPwdUsingPOST
 */
export const pwdVerifyLink = validationCode => {
  return {
    method: 'post',
    url: '/api/account/forgetPwd/verifyLink',
    data: {
      validationCode
    }
  }
}

/**
 * 修改密码
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/%E5%BD%93%E5%89%8D%E7%99%BB%E5%BD%95%E7%AE%A1%E7%90%86/modifyPwdUsingPOST
 */
export const modifyPwd = payload => {
  return {
    method: 'post',
    url: '/api/account/modifyPwd',
    data: {
      payload
    }
  }
}
/**
 * 重置密码
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/%E8%AE%A4%E8%AF%81%E7%AE%A1%E7%90%86/forgetPwdResetUsingPOST
 */
export const reset = payload => {
  return {
    method: 'post',
    url: '/api/account/forgetPwd/reset',
    data: {
      payload
    }
  }
}

/**
 * 发送验证邮件
 */
export const sendVerifyEmail = ({ name, company, website, email, token }) => {
  return {
    method: 'post',
    url: '/api/account/sendVerifyEmail',
    data: {
      name,
      company,
      website,
      email,
      token
    }
  }
}
/**
 * 验证validation code
 */
export const verifyLink = validationCode => {
  return {
    method: 'post',
    url: '/api/account/verifyLink',
    data: {
      validationCode
    }
  }
}
/**
 * 发送验证邮件
 */
export const register = payload => {
  return {
    method: 'post',
    url: '/api/account/register',
    data: {
      payload
    }
  }
}

/**
 * 重置密码
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/%E8%AE%A4%E8%AF%81%E7%AE%A1%E7%90%86/forgetPwdResetUsingPOST
 */
export const detail = () => {
  return {
    method: 'get',
    url: '/api/mchInfo/info'
  }
}
