<template>
  <div :class="['zkme-widget', themeMode === 'dark' && 'dark', previewMode === 'web' && step < 3 ? 'web' : 'mobile']">
    <div class="sty1-cell bg mui-fl-col mui-fl-btw">
      <VerifyIdentityStepOne v-show="step === 1" />
      <VerifyIdentityStepTwo v-show="step === 2" />
      <ProofOfIdentity v-show="step === 3" :themeMode="themeMode" :themeColor1="themeMode !== 'dark' ? colorsOp[0] : darkColorsOp[0]"
        :themeColor2="themeMode !== 'dark' ? colorsOp[1] : darkColorsOp[1]" />
      <CitizenshipComponent :themeColor1="themeMode !== 'dark' ? colorsOp[0] : darkColorsOp[0]" v-show="step === 4" />
      <DocumentScan :themeMode="themeMode" :themeColor1="themeMode !== 'dark' ? colorsOp[0] : darkColorsOp[0]"
        :themeColor2="themeMode !== 'dark' ? colorsOp[1] : darkColorsOp[1]"
        :themeColor3="themeMode !== 'dark' ? colorsOp[2] : darkColorsOp[2]" v-show="step === 5" />
      <FacialRecognition :themeMode="themeMode" :themeColor1="themeMode !== 'dark' ? colorsOp[0] : darkColorsOp[0]"
        :themeColor2="themeMode !== 'dark' ? colorsOp[1] : darkColorsOp[1]"
        :themeColor3="themeMode !== 'dark' ? colorsOp[2] : darkColorsOp[2]" v-show="step === 6" />
      <PoweredComponent :dark-mode="themeMode === 'dark'" v-show="step < 3" />
    </div>
  </div>
</template>
<script>
import { ProofOfIdentity, PoweredComponent, VerifyIdentityStepOne, VerifyIdentityStepTwo, CitizenshipComponent, DocumentScan, FacialRecognition } from '@/components/ui-design-preview'
import postcss from 'postcss'
import nested from 'postcss-nested'
import debounce from 'lodash/debounce'
export default {
  name: 'PreviewContent',
  components: { ProofOfIdentity, PoweredComponent, VerifyIdentityStepOne, VerifyIdentityStepTwo, CitizenshipComponent, DocumentScan, FacialRecognition },
  props: {
    step: {
      type: Number,
      required: true,
      default: () => {
        return 1
      }
    },
    previewMode: {
      type: String,
      required: true,
      default: () => {
        return 'web'
      }
    },
    propsColorsOp: {
      type: Array,
      required: true,
      default: () => {
        return []
      }
    },
    cssTextarea: {
      type: String,
      required: true,
      default: () => {
        return ''
      }
    },
    themeMode: {
      type: String,
      required: true,
      default: () => {
        return 'light'
      }
    }
  },
  data () {
    return {
      firstListen: false,
      colorsOp: [],
      darkColorsOp: []
    }
  },
  watch: {
    propsColorsOp (val) {
      this.colorsOp = !val || !val[0].length ? ['#005563', '#CCDDE0', '#F2F7F7'] : val[0]
      this.darkColorsOp = !val || !val[1].length ? ['#2E8A9B', '#22535C', '#172D32'] : val[1]
    },
    colorsOp () {
      if (!this.firstListen) {
        this.firstListen = true
        this.generateCss()
      }
    },
    themeMode () {
      if (!this.firstListen) {
        this.firstListen = true
        this.generateCss()
      }
    },
    cssTextarea: {
      handler: debounce(function () {
        if (!this.firstListen) {
          this.firstListen = true
          this.generateCss()
        }
      }, 1000),
      immediate: false
    }
  },
  created () {
    this.colorsOp = ['#005563', '#CCDDE0', '#F2F7F7']
    this.darkColorsOp = ['#2E8A9B', '#22535C', '#172D32']
  },
  methods: {
    async generateCss () {
      const existingStyleEle = document.getElementById('dynamic-widget-preview')
      if (existingStyleEle) {
        existingStyleEle.parentNode.removeChild(existingStyleEle)
      }
      let themeCss = ''
      let css = '.zkme-widget{'
      css += '.bg{background:var(--color-background)}'
      switch (this.themeMode) {
        case 'dark':
          if (this.darkColorsOp.length) {
            css += `:root{
              --vt-c-primary-1: ${this.darkColorsOp[0]};
              --vt-c-primary-1-rgb: ${this.hexToRgb(this.darkColorsOp[0])};
              --vt-c-primary-2: ${this.darkColorsOp[1]};
              --vt-c-primary-3: ${this.darkColorsOp[2]};
            }`
          }
          break
        case 'light':
        default:
          if (this.colorsOp.length) {
            css += `:root{
              --vt-c-primary-1: ${this.colorsOp[0]};
              --vt-c-primary-1-rgb: ${this.hexToRgb(this.colorsOp[0])};
              --vt-c-primary-2: ${this.colorsOp[1]};
              --vt-c-primary-3: ${this.colorsOp[2]};
            }`
          }
          break
      }

      if (this.themeMode === 'dark') {
        css += `
        :root{
    --vt-c-white-bg: #141414;
    --vt-c-secondary-5: #262626;
    --vt-c-text-1: #FFFFFF;
    
    --vt-c-white-other-3: #fff;
    --vt-c-white-other-3-rgb: 255,255,255;
    --vt-c-secondary-5: #262626;
    --vt-c-white-other: #000;

    --vt-c-warning: #ee6969;
    --vt-c-warning-rgb: 238, 105, 105;
    
    --vt-c-success: #1B292C;
    --vt-c-success-rgb: 27, 41, 44;
    --vt-c-progressing: #0F1B2A;
    --color-text-17: rgba(255, 255, 255, 0.8);
    --vt-c-tertiary-blue: #071627;
    --vt-c-tertiary-green: #1D2526;
        }`
      } else {
        css += `:root{
          --vt-c-text-1: #000000;
          --vt-c-white-bg: #fff;
          --vt-c-black-bg: #fff;
        }`
      }
      themeCss = `${css}}`
      css += this.cssTextarea ? `${this.cssTextarea}` : ''
      css += '}'
      const styleElement = document.createElement('style')
      styleElement.setAttribute('id', 'dynamic-widget-preview')
      css = css.replace(/[\r\n]/g, '')
      try {
        const result = await postcss([nested])
          .process(css)
          .async()
        const cssCode = result.css
        styleElement.innerHTML = cssCode.replaceAll('.zkme-widget :root', ':root')
        document.head.appendChild(styleElement)
        this.firstListen = false
      } catch (err) {
        this.firstListen = false
        const result = await postcss([nested])
          .process(themeCss)
          .async()
        const cssCode = result.css
        styleElement.innerHTML = cssCode.replaceAll('.zkme-widget :root', ':root')
        document.head.appendChild(styleElement)
        console.log(err)
      }
    },
    hexToRgb (hexString) {
      if (!hexString) {
        return ''
      }

      let hex = ''
      if (hexString.startsWith('#')) {
        hex = hexString.substring(1)
      }

      if (hex.length !== 6) {
        return
      }

      const r = parseInt(hex.substring(0, 2), 16)
      const g = parseInt(hex.substring(2, 4), 16)
      const b = parseInt(hex.substring(4, 6), 16)

      return `${r}, ${g}, ${b}`
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/ui-design-preview/_base.scss"/>
<style lang="scss" src="@/assets/css/components/ui-design-preview/_powered_component.scss" scoped />
