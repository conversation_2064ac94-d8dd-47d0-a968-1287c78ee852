<template>
  <div class="mui-fl-col mui-fl-btw">
    <div class="mui-fl-btw mui-fl-vert" style="margin-top: 28px;">
      <div class="t1 mui-fl-vert">
        <img style="width: 48px; height: 48px;" src="@/assets/img/networkicon/moca.svg" alt="">
        {{ detail.tokenSymbol }}
        <span class="t2">
          {{ detail.tokenContractAddress }}
          <i class="mico-copy" @click="copy(detail.tokenContractAddress)"></i>
        </span>
      </div>
      <div class="t3Block mui-fl-central">
        <span>{{ detail.claimPageUrl | abbreviateUrl(12, 16) }}</span>
        <span class="t3" @click="copy(detail.claimPageUrl)">Copy claim url</span>
      </div>
    </div>
    <div class="mui-fl-btw mui-fl-vert t6">
      <div>
        Airdrop Duration:
        {{ detail.startDate | formatTimestampToUTC8(detail.displayTimezone) }} -
        {{ detail.endDate | formatTimestampToUTC8(detail.displayTimezone) }}
      </div>
      <div style="color: #33585C;">Admin Address: 0x1dE0cD36c1FFD962A0470B97bD0cdA4E3DE12F2b</div>
    </div>
    <ul class="mui-fl-vert blockBox">
      <li v-for="(data, index) of tokenBlock" :key="index" class="block mui-fl-vert_start">
        <div>
          <img :src="data.img" alt="">
        </div>
        <div>
          <div class="t4 mui-fl-central">
            {{ data.name }}
            <m-tooltip popper-class="sty1-tooltip" placement="top">
              <template slot="content">
                <div v-html="data.message"></div>
              </template>
              <i class="mico-help-linear"></i>
            </m-tooltip>
          </div>
          <div class="t5">{{ data.quantity }}</div>
        </div>
      </li>
    </ul>
    <div class="mui-fl-btw operateButton">
      <m-button class="sty2-button sty7-button">Withdraw Token</m-button>
      <div>
        <m-button class="sty2-button sty7-button sty8-button" @click="dialogDeploy = true, deployContracts=false">Edit Distribution</m-button>
        <m-button class="sty2-button sty7-button sty9-button" @click="dialogDeploy = true, deployContracts=true">Delete Distribution</m-button>
      </div>
    </div>
    <div class='e-chart-radar' id='polyline'></div>
    <m-dialog
      :visible.sync="dialogDeploy"
      @close="dialogClose"
      custom-class="sty6-dialog"
      width="510px">
      <div v-if="!deployContracts">
        <div class="t7 mgb-20">Reminder</div>
        <div class="t8">
          To continue, please connect using your Admin<br/>
          address:
        </div>
        <div class="t9">{{ $store.state.wallet.connectedAddress }}</div>
        <div class="t8">Connect this wallet to proceed with the next steps.</div>
      </div>
      <div v-else>
        <div class="t7 mgt-20 mgb-20">
          Are you sure you want to delete<br/>
          this distribution？
        </div>
        <div class="t8 mgb-12">
          This action cannot be undone.
        </div>
        <div class="t8 mgb-20">
          All data related to this distribution will be<br/>
          permanently deleted, including claim pages, user<br/>
          progress, and analytics.
        </div>
      </div>
      <span slot="footer" class="mui-fl-btw">
        <m-button class="sty2-button sty12-button" @click="dialogDeploy = false, deployFlag = false">Cancel</m-button>
        <m-button v-if="!deployContracts" class="sty2-button sty12-button" style="background: #005563; color: #FFFFFF; margin-left: 24px;" type="primary" @click="deploy">
          <div v-if="deployFlag" class="mui-fl-central">
            <div class="loader loader2"></div>
          </div>
          <div v-else>Connect Wallet</div>
        </m-button>
        <!-- <m-button v-if="deployContracts" class="sty2-button sty12-button">
          <a :href=txHash target="_blank" style="color: #002E33;">View Transaction</a>
        </m-button> -->
        <m-button v-if="deployContracts" class="sty2-button sty9-button" style="width: 100%; color: #FFFFFF; margin-left: 24px;" type="primary" @click="dialogClose">
          Delete
        </m-button>
      </span>
    </m-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { toFormat } from '@/utils/filters'

export default {
  data () {
    return {
      dialogDeploy: false,
      deployContracts: false,
      deployFlag: false
    }
  },
  computed: {
    detail () {
      return this.$store.state.distribution.detail
    },
    tokenBlock () {
      const detail = this.$store.state.distribution.detail
      return [
        {
          name: 'Token Distributed Amount',
          quantity: toFormat(detail.amount),
          img: require('@/assets/img/distribution/analyze1.png'),
          message: 'The total number of tokens<br/> allocated for distribution.'
        },
        {
          name: 'Token Claimed Amount',
          quantity: Number(detail.claimedAmount) ? toFormat(detail.claimedAmount) : '--',
          img: require('@/assets/img/distribution/analyze2.png'),
          message: 'The total number of tokens already claimed,<br/> along with the claim progress percentage.'
        },
        {
          name: 'Distributed Address',
          quantity: toFormat(detail.totalRecipients),
          img: require('@/assets/img/distribution/analyze3.png'),
          message: 'The total number of eligible addresses.'
        },
        {
          name: 'Claimed Address',
          quantity: Number(detail.claimedAddresses) ? toFormat(detail.claimedAddresses) : '--',
          img: require('@/assets/img/distribution/analyze4.png'),
          message: 'The number of addresses that<br/> have successfully claimed.'
        }
      ]
    }
  },
  watch: {
  },
  created () {
  },
  mounted () {
    this.$nextTick(() => {
      const polyline = document.getElementById('polyline')
      this.polylineChart = echarts.init(polyline)
      this.polylineChartFun()
    })
  },
  methods: {
    deploy () {

    },
    dialogClose () {

    },
    copy (index) {
      const text = index
      const elInput = document.createElement('input')
      elInput.value = text
      document.body.appendChild(elInput)
      elInput.select()
      document.execCommand('Copy')
      elInput.remove()
      this.$message({
        message: 'Copied',
        iconClass: 'mico-lightTip',
        customClass: 'sty1-message',
        duration: 3000,
        offset: 32,
        center: true
      })
    },
    async polylineChartFun (dataTime) {
      dataTime = [1, 2, 3, 4, 5]
      const chartDom = document.getElementById('polyline')
      const myChart = echarts.init(chartDom)

      // const result = this.transformData(dataTime)
      const result = {
        transformedData: [
          [
            '2025-05-14',
            10
          ],
          [
            '2025-05-15',
            30
          ],
          [
            '2025-05-16',
            82
          ],
          [
            '2025-05-19',
            60
          ],
          [
            '2025-05-22',
            5
          ],
          [
            '2025-05-23',
            4
          ],
          [
            '2025-05-24',
            4
          ],
          [
            '2025-05-25',
            4
          ],
          [
            '2025-05-26',
            4
          ],
          [
            '2025-05-27',
            4
          ],
          [
            '2025-05-28',
            4
          ],
          [
            '2025-05-29',
            4
          ],
          [
            '2025-05-30',
            4
          ],
          [
            '2025-05-31',
            4
          ],
          [
            '2025-06-01',
            4
          ],
          [
            '2025-06-02',
            4
          ],
          [
            '2025-06-02',
            4
          ],
          [
            '2025-06-03',
            4
          ],
          [
            '2025-06-04',
            4
          ],
          [
            '2025-06-05',
            4
          ],
          [
            '2025-06-06',
            4
          ]
        ]
        // maxDay: 100,
        // minDay: 1
      }
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            return params[0].data[0] + '<br />' +
            `<div style="background: #005563;width: 8px;height:8px;line-height: 8px;border-radius: 50%;"><div>
              <div style="margin: 10px 0px 10px 20px">${params[0].data[1]}</div>`
          },
          textStyle: {
            align: 'left'
          }
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            formatter: 0,
            color: 'rgba(0, 0, 0, 0.7)'
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'background: rgba(0, 0, 26);'
            }
          },
          axisTick: {
            lineStyle: {
              color: 'none'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        dataZoom: [
          {
            type: 'inside',
            show: false
          }
        ],
        yAxis: {
          type: 'value',
          // min: 0,
          // max: result.maxDay <= 5 ? 5 : result.maxDay,
          axisLabel: {
            formatter: 0,
            color: 'rgba(0, 0, 0, 0.7)'
          },
          axisLine: {
            lineStyle: {
              type: 'dashed',
              color: 'none'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          },
          axisTick: {
            lineStyle: {
              color: 'none'
            }
          }
        },
        series: [
          {
            type: 'line',
            // clip: true,
            symbol: 'circle',
            symbolSize: 7.5,
            showSymbol: true,
            smooth: true,
            data: result.transformedData,
            width: '100%',
            lineStyle: {
              width: 2,
              color: 'rgba(0, 85, 99, 0.2)',
              shadowColor: '#00556300',
              shadowBlur: 8,
              shadowOffsetY: 7
            },
            itemStyle: {
              color: '#005563',
              borderColor: 'rgba(0, 85, 99, 0.1)',
              borderWidth: 10
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: '#00556329'
                },
                {
                  offset: 1,
                  color: '#00556300'
                }
              ])
            }
          }
        ]
      }
      myChart.setOption(option)
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/tokenDistribution/_distributionAnalyze.scss" scoped></style>
