<template>
    <div class="sty1-gp-powered mui-fl-central" :class="darkMode && 'dark'">
        <img :src="darkMode ? img1 : img2" alt="">
        <p>Powered by zkMe</p>
    </div>
</template>

<script>
export default {
  name: 'PoweredComponent',
  props: {
    darkMode: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      img1: require('@/assets/img/popup/logo1-dark.png'),
      img2: require('@/assets/img/popup/logo1.png')
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/ui-design-preview/_powered_component.scss"></style>
