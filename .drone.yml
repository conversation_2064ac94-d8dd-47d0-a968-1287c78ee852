kind: pipeline
name: build
type: docker
steps:
  # 开发测试环境 docker 镜像构建
  - name: docker-build-dev
    image: plugins/docker
    pull: if-not-exists
    settings:
      registry: hub.bitkinetic.com
      repo: hub.bitkinetic.com/zkme/zkme-web
      tags: alpha-${DRONE_BUILD_NUMBER}
      purge: true
      username:
        from_secret: docker-hub-username
      password:
        from_secret: docker-hub-password
    when:
      ref:
        - refs/heads/branch_dev
        - refs/heads/branch_test
  # 预发布生产 docker 镜像构建
  - name: docker-build-pd
    image: plugins/docker
    pull: if-not-exists
    settings:
      registry: hub.bitkinetic.com
      repo: hub.bitkinetic.com/zkme/zkme-web
      tags: ${DRONE_TAG}
      purge: true
      username:
        from_secret: docker-hub-username
      password:
        from_secret: docker-hub-password
    when:
      ref:
        - refs/tags/*
  #开发环境自动部署
  - name: deploy-to-k8s-dev
    image: hub.bitkinetic.com/public/drone-helm:v6
    pull: IfNotPresent
    environment:
      KUBECTL_CONFIG:
        from_secret: kube-config
      REPO_USER:
        from_secret: docker-hub-username
      REPO_PWD:
        from_secret: docker-hub-password
    commands:
      - /run.sh
      - helm repo add --username=$REPO_USER --password=$REPO_PWD dn https://charts.bitkinetic.com/
      - ls -lah
      - helm upgrade dev-zkme-web-static dn/static -n zkme-dev -f .values.yml --set image.tag=alpha-${DRONE_BUILD_NUMBER}
    when:
      ref:
        - refs/heads/branch_dev
  #测试环境自动部署
  - name: deploy-to-k8s-testing
    image: hub.bitkinetic.com/public/drone-helm:v6
    pull: IfNotPresent
    environment:
      KUBECTL_CONFIG:
        from_secret: kube-config
      REPO_USER:
        from_secret: docker-hub-username
      REPO_PWD:
        from_secret: docker-hub-password
    commands:
      - /run.sh
      - helm repo add --username=$REPO_USER --password=$REPO_PWD dn https://charts.bitkinetic.com/
      - ls -lah
      - helm upgrade test-zkme-web-static dn/static -n zkme-test -f .testing.values.yml --set image.tag=alpha-${DRONE_BUILD_NUMBER}
    when:
      ref:
        - refs/heads/branch_test
  - name: apply push to online
    image: hub.bitkinetic.com/public/deploy:v1
    pull: if-not-exists
    environment:
      DEPLOY_AUTH_KEY:
        from_secret: deploy-auth-key
      DEPLOY_SERVER:
        from_secret: deploy-base-url
      OBJECT: zkme
    commands:
      - app call -d
    when:
      ref:
        - refs/tags/*
  - name: notify
    image: hub.bitkinetic.com/public/deploy:v1
    pull: if-not-exists
    environment:
      DEPLOY_AUTH_KEY:
        from_secret: deploy-auth-key
      DEPLOY_SERVER:
        from_secret: deploy-base-url
    commands:
      - app call -b
    when:
      ref:
        - refs/tags/*
        - refs/heads/*
  - name: sonar scanner
    image: h.bitkinetic.com/public/sanner:v5-8.4.0
    pull: if-not-exists
    volumes:
      - name: scanner-cache
        path: /opt/sonar-scanner/.sonar/cache
    environment:
      SONAR_TOKEN:
        from_secret: sonarqube-token
      SONAR_HOST_URL:
        from_secret: sonarqube-url
    commands:
      - dependency-check.sh -n -s $PWD -f HTML -o dcr.html --disablePnpmAudit --disableYarnAudit
      - entrypoint.sh -Dsonar.scm.provider=git -Dsonar.projectKey=zkme-web -Dsonar.dependencyCheck.htmlReportPath=$PWD/dcr.html -Dsonar.exclusions=**/reports/*,dcr.*
    when:
      ref:
        - refs/heads/branch_test
        - refs/tags/*
trigger:
  ref:
    include:
      - refs/heads/branch_dev
      - refs/heads/branch_test
      - refs/tags/*
  event:
    - push
    - tag
volumes:
  - name: scanner-cache
    host:
      path: /var/lib/java/scanner-cache
---
kind: secret
name: sonarqube-token
get:
  path: drone-sonarqube
  name: token
---
kind: secret
name: sonarqube-url
get:
  path: drone-sonarqube
  name: sonarqube-url
---
kind: secret
name: docker-hub-username
get:
  path: docker-secret-hub.bitkinetic.com
  name: username
---
kind: secret
name: docker-hub-password
get:
  path: docker-secret-hub.bitkinetic.com
  name: password
---
kind: secret
name: kube-config
get:
  path: drone-helm-secret
  name: config
---
kind: secret
name: deploy-auth-key
get:
  path: drone-deploy-auth-key
  name: auth-key
---
kind: secret
name: deploy-base-url
get:
  path: drone-deploy-auth-key
  name: deploy-base-url
