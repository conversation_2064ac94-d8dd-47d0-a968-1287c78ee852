/**
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/API/api/kyt-controller/address_labelsUsingGET
 */
export const addressLabels = ({ walletAddress, coin }) => {
  return {
    method: 'get',
    url: '/api/kyt/address_labels',
    params: {
      coin,
      walletAddress
    }
  }
}
/**
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/API/api/kyt-controller/address_overviewUsingGET
 */
export const addressOverview = ({ walletAddress, coin }) => {
  return {
    method: 'get',
    url: '/api/kyt/address_overview',
    params: {
      walletAddress,
      coin
    }
  }
}
/**
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/API/api/kyt-controller/risk_scoreUsingGET
 */
export const riskscore = ({ walletAddress, coin }) => {
  return {
    method: 'get',
    url: '/api/kyt/risk_score',
    params: {
      walletAddress,
      coin
    }
  }
}
/**
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/API/api/kyt-controller/transactions_investigationUsingGET
 */
export const transactionsInvestigation = ({ walletAddress, coin, page, startTimestamp, endTimestamp }) => {
  return {
    method: 'get',
    url: '/api/kyt/transactions_investigation',
    params: {
      walletAddress,
      coin,
      page,
      start_timestamp: startTimestamp,
      end_timestamp: endTimestamp
    }
  }
}
/**
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/API/api/kyt-controller/address_actionUsingGET
 */
export const addressAction = ({ walletAddress, coin }) => {
  return {
    method: 'get',
    url: '/api/kyt/address_action',
    params: {
      walletAddress,
      coin
    }
  }
}
/**
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/API/api/kyt-controller/address_traceUsingGET
 */
export const addressTrace = ({ walletAddress, coin }) => {
  return {
    method: 'get',
    url: '/api/kyt/address_trace',
    params: {
      walletAddress,
      coin
    }
  }
}

export const txnsDetail = ({ from, to, address, hashList, decimals, chain, symbol }) => {
  return {
    method: 'post',
    url: '/api/kyt/txnsDetail',
    data: {
      from,
      to,
      address,
      hashList,
      decimals,
      chain,
      symbol
    }
  }
}
/**
 * tokensList
 */
export const tokens = ({ chain, address }) => {
  return {
    method: 'post',
    url: '/api/kyt/tokens',
    data: {
      chain,
      address
    }
  }
}
