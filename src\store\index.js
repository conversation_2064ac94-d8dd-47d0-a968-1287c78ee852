import Vue from 'vue'
import Vuex from 'vuex'
import kyc from './modules/kyc'
import common from './modules/common'
import auth from './modules/auth'
import zkKyc from './modules/zkKyc'
import wallet from './modules/wallet'
import distribution from './modules/distribution'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
  },
  mutations: {
  },
  actions: {
  },
  modules: {
    kyc,
    common,
    auth,
    zkKyc,
    wallet,
    distribution
  }
})
