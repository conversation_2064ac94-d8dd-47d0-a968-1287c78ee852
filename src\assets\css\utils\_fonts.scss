@font-face {
  font-family: "Circular Std";
  font-weight: normal;
  src: url('~@/assets/css/fonts/CircularStd-fonts/CircularStd-Book.otf') format('truetype');
}

@font-face {
  font-family: "Circular Std WF";
  font-weight: 500;
  src: url('~@/assets/css/fonts/CircularStd-fonts/CircularStd-Medium.otf') format('truetype');
}

@font-face {
  font-family: "Circular Std WF bold";
  font-weight: bold;
  src: url('~@/assets/css/fonts/CircularStd-fonts/CircularStd-Bold.otf') format('truetype');
}


@font-face {
  font-family: "HarmonyOS_Sans";
  font-weight: normal;
  src: url('~@/assets/css/fonts/HarmonyOS_Sans/HarmonyOS_Sans_Regular.ttf') format('truetype');
}

@font-face {
  font-family: "HarmonyOS_Sans";
  font-weight: 500;
  src: url('~@/assets/css/fonts/HarmonyOS_Sans/HarmonyOS_Sans_Medium.ttf') format('truetype');
}

@font-face {
  font-family: "HarmonyOS_Sans";
  font-weight: bold;
  src: url('~@/assets/css/fonts/HarmonyOS_Sans/HarmonyOS_Sans_Bold.ttf') format('truetype');
}
