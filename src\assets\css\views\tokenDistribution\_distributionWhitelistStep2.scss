.t1 {
  color: #002E33;
  font-size: 28px;
  font-weight: 500;
}
.leftBlock {
  display: grid;
  grid-gap: 20px;
  margin: 24px 0;
  .t2 {
    margin-bottom: 4px;
  }
}
.t2 {
  color: #809799;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}
.t3 {
  color: #002E33;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.t4 {
  color: #002E33;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  margin: 4px 0 12px;
}
.t5 {
  color: #809799;
  font-size: 12px;
  font-weight: 500;
}
.t6 {
  color: #002E33;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  margin: 6px 0;
}
.t7 {
  color: #B3C0C2;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}
.t8 {
  color: #809799;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}
.miniTable {
  width: 70%;
  border-radius: 12px;
}
.tableBox {
  height: calc(100vh - 250px);
  overflow: scroll;
  border-radius: 12px;
  border: 1px solid #F0F0F0;
  background: #F7F7F7;
  // width: 100%;
  img {
    width: 144px;
    height: 144px;
  }
}
.noData {
  position: absolute;
  width: 100%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.mgt16 {
  margin-top: 16px;
}
.mico-upload {
  font-size: 24px;
}
.errorText {
  color: #EE6969;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin-top: 4px;
}
.sty1-upload ::v-deep {
  border-radius: 16px;
  margin-top: 12px;
  .el-upload--text {
    width: 100%;
  }
  .el-upload-dragger {
    background: rgba(132, 182, 184, 0.12);
    border: 1px dashed rgba(0, 46, 51, 0.10);
    display: grid;
    align-items: center;
    width: 100%;
    overflow: scroll;
    i {
      font-size: 24px;
    }
  }
  &.sty1-upload-error {
    .el-upload-dragger {
      border-color: #EE6969;
    }
  }
}
.download {
  border-radius: 58px;
  background: rgba(132, 182, 184, 0.12);
  color: #002E33;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  padding: 8px 16px;
  cursor: pointer;
}

.table {
  width: fit-content;
  width: 100%;
  .title {
    > :first-child {
      min-width: 380px;
      margin-right: 16px;
    }
    width: calc(100% - 96px);
    padding: 12px 48px;
    color: #002E33;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    position: relative;
    .mico-creat {
      position: absolute;
      top: 14px;
      left: 14px;
      border-radius: 4px;
      background: rgba(132, 182, 184, 0.12);
      padding: 2px;
      cursor: pointer;
    }
  }
  .walletAddress {
    margin-right: 16px;
  }
  .addData {
    width: calc(100% - 96px);
    padding: 12px 48px;
    position: relative;
    background: rgba(132, 182, 184, 0.08);
    .addBut {
      position: absolute;
      right: 0;
      padding: 4px 9px;
      border-radius: 48px;
      background: #005563;
      color: #FFF;
      font-size: 12px;
      font-weight: 400;
      line-height: 16px;
      cursor: pointer;
    }
    &:hover {
      background: rgba(0, 85, 99, 0.04);
      .delete {
        display: block;
      }
    }
    .delete {
      display: none;
      position: absolute;
      top: 15px;
      left: 16px;
      cursor: pointer;
    }
  }
  li {
    width: calc(100% - 96px);
    padding: 12px 48px;
    color: #33585C;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    position: relative;
    &:hover {
      background: rgba(0, 85, 99, 0.04);
      .delete {
        display: block;
      }
    }
    .delete {
      display: none;
      position: absolute;
      top: 15px;
      left: 16px;
      cursor: pointer;
    }
  }
}
.onlyRead {
  position: absolute;
  top: 0;
  height: calc(100% - 34px);
  width: 50%;
}
.loading {
  width: 11px;
  padding: 2px;
  aspect-ratio: 1;
  border-radius: 50%;
  background: #005563;
  margin: 2px 9px 0;
  --_m: 
    conic-gradient(#0000 10%,#000),
    linear-gradient(#000 0 0) content-box;
  -webkit-mask: var(--_m);
          mask: var(--_m);
  -webkit-mask-composite: source-out;
          mask-composite: subtract;
  animation: loading 1s infinite linear;
}
@keyframes loading {
  to{transform: rotate(1turn)}
}
