export default {
  _format: 'hh-sol-artifact-1',
  contractName: 'ZKMEMultiProjectDistributor',
  sourceName: 'contracts/ZKMEMultiProjectDistributor.sol',
  abi: [
    {
      inputs: [],
      name: 'ECDSAInvalidSignature',
      type: 'error'
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'length',
          type: 'uint256'
        }
      ],
      name: 'ECDSAInvalidSignatureLength',
      type: 'error'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 's',
          type: 'bytes32'
        }
      ],
      name: 'ECDSAInvalidSignatureS',
      type: 'error'
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'user',
          type: 'address'
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'token',
          type: 'address'
        },
        {
          indexed: false,
          internalType: 'uint256',
          name: 'amount',
          type: 'uint256'
        }
      ],
      name: 'Claimed',
      type: 'event'
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: false,
          internalType: 'uint8',
          name: 'version',
          type: 'uint8'
        }
      ],
      name: 'Initialized',
      type: 'event'
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'owner',
          type: 'address'
        }
      ],
      name: 'ProjectCreated',
      type: 'event'
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'token',
          type: 'address'
        },
        {
          indexed: false,
          internalType: 'uint256',
          name: 'total',
          type: 'uint256'
        },
        {
          indexed: false,
          internalType: 'uint256',
          name: 'count',
          type: 'uint256'
        }
      ],
      name: 'RecipientsSet',
      type: 'event'
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'bytes32',
          name: 'role',
          type: 'bytes32'
        },
        {
          indexed: true,
          internalType: 'bytes32',
          name: 'previousAdminRole',
          type: 'bytes32'
        },
        {
          indexed: true,
          internalType: 'bytes32',
          name: 'newAdminRole',
          type: 'bytes32'
        }
      ],
      name: 'RoleAdminChanged',
      type: 'event'
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'bytes32',
          name: 'role',
          type: 'bytes32'
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'account',
          type: 'address'
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'sender',
          type: 'address'
        }
      ],
      name: 'RoleGranted',
      type: 'event'
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'bytes32',
          name: 'role',
          type: 'bytes32'
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'account',
          type: 'address'
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'sender',
          type: 'address'
        }
      ],
      name: 'RoleRevoked',
      type: 'event'
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'token',
          type: 'address'
        },
        {
          indexed: false,
          internalType: 'uint256',
          name: 'amount',
          type: 'uint256'
        }
      ],
      name: 'TokenDeposited',
      type: 'event'
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'token',
          type: 'address'
        }
      ],
      name: 'Withdraw',
      type: 'event'
    },
    {
      inputs: [],
      name: 'ADMIN_ROLE',
      outputs: [
        {
          internalType: 'bytes32',
          name: '',
          type: 'bytes32'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [],
      name: 'DEFAULT_ADMIN_ROLE',
      outputs: [
        {
          internalType: 'bytes32',
          name: '',
          type: 'bytes32'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [],
      name: 'PROJECT_ROLE',
      outputs: [
        {
          internalType: 'bytes32',
          name: '',
          type: 'bytes32'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'token',
          type: 'address'
        },
        {
          internalType: 'bytes32[]',
          name: 'proof',
          type: 'bytes32[]'
        },
        {
          internalType: 'bytes',
          name: 'signature',
          type: 'bytes'
        },
        {
          internalType: 'bytes32[]',
          name: 'depositProof',
          type: 'bytes32[]'
        },
        {
          internalType: 'uint256',
          name: 'amount',
          type: 'uint256'
        },
        {
          internalType: 'uint256',
          name: 'nonce',
          type: 'uint256'
        }
      ],
      name: 'claim',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: '',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: '',
          type: 'address'
        },
        {
          internalType: 'address',
          name: '',
          type: 'address'
        }
      ],
      name: 'claimed',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'projectOwner',
          type: 'address'
        },
        {
          internalType: 'uint256',
          name: 'startTimestamp',
          type: 'uint256'
        },
        {
          internalType: 'uint256',
          name: 'endTimestamp',
          type: 'uint256'
        }
      ],
      name: 'createProject',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'token',
          type: 'address'
        }
      ],
      name: 'deposit',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        }
      ],
      name: 'depositNative',
      outputs: [],
      stateMutability: 'payable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        }
      ],
      name: 'getProjectDetail',
      outputs: [
        {
          components: [
            {
              internalType: 'address',
              name: 'owner',
              type: 'address'
            },
            {
              internalType: 'uint256',
              name: 'startTimestamp',
              type: 'uint256'
            },
            {
              internalType: 'uint256',
              name: 'endTimestamp',
              type: 'uint256'
            }
          ],
          internalType: 'struct ZKMEMultiProjectDistributor.ProjectDetail',
          name: '',
          type: 'tuple'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        }
      ],
      name: 'getProjectOwner',
      outputs: [
        {
          internalType: 'address',
          name: '',
          type: 'address'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'role',
          type: 'bytes32'
        }
      ],
      name: 'getRoleAdmin',
      outputs: [
        {
          internalType: 'bytes32',
          name: '',
          type: 'bytes32'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'role',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'account',
          type: 'address'
        }
      ],
      name: 'grantRole',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'token',
          type: 'address'
        },
        {
          internalType: 'address',
          name: 'user',
          type: 'address'
        }
      ],
      name: 'hasClaimed',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'role',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'account',
          type: 'address'
        }
      ],
      name: 'hasRole',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: 'admin',
          type: 'address'
        }
      ],
      name: 'initialize',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'role',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'account',
          type: 'address'
        }
      ],
      name: 'renounceRole',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'role',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'account',
          type: 'address'
        }
      ],
      name: 'revokeRole',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'token',
          type: 'address'
        },
        {
          internalType: 'bytes32',
          name: 'root',
          type: 'bytes32'
        },
        {
          internalType: 'uint256',
          name: 'total',
          type: 'uint256'
        }
      ],
      name: 'setMerkleDepositRoot',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'token',
          type: 'address'
        },
        {
          internalType: 'bytes32',
          name: 'root',
          type: 'bytes32'
        }
      ],
      name: 'setMerkleRoot',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: 'signer',
          type: 'address'
        },
        {
          internalType: 'bool',
          name: 'enabled',
          type: 'bool'
        }
      ],
      name: 'setSigner',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: '',
          type: 'address'
        }
      ],
      name: 'signerMap',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes4',
          name: 'interfaceId',
          type: 'bytes4'
        }
      ],
      name: 'supportsInterface',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'root',
          type: 'bytes32'
        },
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'addr',
          type: 'address'
        },
        {
          internalType: 'uint256',
          name: 'nonce',
          type: 'uint256'
        },
        {
          internalType: 'uint256',
          name: 'amount',
          type: 'uint256'
        },
        {
          internalType: 'bytes',
          name: 'signature',
          type: 'bytes'
        }
      ],
      name: 'testESDSACheck',
      outputs: [
        {
          internalType: 'address',
          name: '',
          type: 'address'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32[]',
          name: 'proof',
          type: 'bytes32[]'
        },
        {
          internalType: 'address',
          name: 'addr',
          type: 'address'
        },
        {
          internalType: 'bytes32',
          name: 'root',
          type: 'bytes32'
        }
      ],
      name: 'testMerkel',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32[]',
          name: 'proof',
          type: 'bytes32[]'
        },
        {
          internalType: 'address',
          name: 'addr',
          type: 'address'
        },
        {
          internalType: 'bytes32',
          name: 'root',
          type: 'bytes32'
        },
        {
          internalType: 'uint256',
          name: 'amount',
          type: 'uint256'
        }
      ],
      name: 'testMerkelDeposit',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: '',
          type: 'address'
        }
      ],
      name: 'tokenWhiteMap',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: '',
          type: 'address'
        },
        {
          internalType: 'uint256',
          name: '',
          type: 'uint256'
        }
      ],
      name: 'usedNonces',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool'
        }
      ],
      stateMutability: 'view',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: 'token',
          type: 'address'
        }
      ],
      name: 'whiteListToken',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    },
    {
      inputs: [
        {
          internalType: 'bytes32',
          name: 'projectId',
          type: 'bytes32'
        },
        {
          internalType: 'address',
          name: 'token',
          type: 'address'
        }
      ],
      name: 'withdraw',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function'
    }
  ],
  bytecode: '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',
  deployedBytecode: '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',
  linkReferences: {},
  deployedLinkReferences: {}
}
