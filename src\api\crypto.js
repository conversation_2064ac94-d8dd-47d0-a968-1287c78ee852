import { enc, AES, mode, pad, algo } from 'crypto-js'
// import { Message } from 'element-ui'

const JAVA_API_AES_KEY = 'WpVog9P8NveQLEJYE2cnjg=='

export function encryptWithEbc (plaintext) {
  const encrypted = AES.encrypt(
    enc.Utf8.parse(plaintext),
    enc.Utf8.parse(JAVA_API_AES_KEY),
    {
      mode: mode.ECB,
      padding: pad.Pkcs7
    })
  return encrypted
}

export async function calcSignature (content, timestamp) {
  const hasher = algo.SHA256.create()

  if (typeof content === 'string') {
    hasher.update(enc.Utf8.parse(content))
  } else if (Object.prototype.toString.call(content) === '[object ArrayBuffer]') {
    const text = Buffer.from(content).toString()
    hasher.update(enc.Utf8.parse(text))
  } else if (Object.prototype.toString.call(content) === '[object Blob]') {
    const contentBuffer = Buffer.from(await content.arrayBuffer())
    const fileBuffer = contentBuffer.subarray(29, -33)
    const resultBuffer = Buffer.concat([
      Buffer.from('--\r\n'),
      fileBuffer,
      Buffer.from('\r\n----\r\n')
    ])
    const text = resultBuffer.toString()
    hasher.update(enc.Utf8.parse(text))
  } else {
    hasher.update(content.ciphertext)
  }
  const firstStepDigest = hasher.finalize().toString()
  const secondStepStr = firstStepDigest + '_' + timestamp
  const thirdStepResults = encryptWithEbc(secondStepStr)

  const hasher2 = algo.SHA256.create()
  hasher2.update(thirdStepResults.ciphertext)

  return hasher2.finalize().toString()
}

export async function assertResponse (data, headers, cfg) {
  if (cfg.responseType === 'blob') {
    return new Blob([data])
  }
  const datas = Buffer.from(data).toString()
  // const serverSignature = headers.get('X-Signature') || ''
  // const serverTimestamp = headers.get('X-Timestamp') || ''
  // const clientSignature = await calcSignature(data, serverTimestamp)
  // if (serverSignature !== clientSignature) {
  //   const errorMsg = 'Current network is insecure. Please switch networks and try again.'
  //   Message({
  //     message: errorMsg,
  //     type: 'error',
  //     duration: 3000
  //   })
  //   return {
  //     code: 80001000,
  //     msg: errorMsg
  //   }
  // }
  return JSON.parse(datas)
}
