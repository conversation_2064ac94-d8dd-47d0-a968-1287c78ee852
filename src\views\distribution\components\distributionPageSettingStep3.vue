<template>
  <div class="mui-fl-hori">
    <div :style="{marginRight: onlyRead ? '40px' : '0'}" id="boxNft">
      <div class="t1 mgt16">Create Token Distribution</div>
      <div class="mui-fl-vert">
        <div class="leftBlock mui-fl-1" style="margin-right: 24px">
          <div>
            <div class="t2">Token</div>
            <div class="t3">{{detail.tokenSymbol}}({{ detail.tokenContractAddress | formatPubKey }})</div>
          </div>
          <div>
            <div class="t2">Current Network</div>
            <div class="mui-fl-vert">
              <img style="width: 24px;height: 24px;margin-right: 4px;" src="@/assets/img/networkicon/moca.svg" alt="">
              <div class="t3">{{ detail.tokenNetwork }}</div>
            </div>
          </div>
          <div>
            <div class="t2">Start Date</div>
            <m-date-picker
              type="datetime"
              style="pointer-events: none;"
              :class="['sty4-date-editor']"
              prefix-icon="mico-date"
              v-model="detail.startDate"
              align="right"
              range-separator="-"
              :clearable="false"
              :editable="false"
              placeholder="Start Date"
              popper-class="sty2-date-popper"
            >
            </m-date-picker>
          </div>
        </div>
        <div class="leftBlock mui-fl-1">
          <div>
            <div class="t2">Distribution Type</div>
            <div class="t3">{{ detail.distributionType }}</div>
          </div>
          <div>
            <div class="t2">Distribution Amount</div>
            <div class="t3">{{ detail.amount | toFormat }}</div>
          </div>
          <div>
            <div class="t2">End Date</div>
            <m-date-picker
              style="pointer-events: none;"
              type="datetime"
              popper-class="sty2-date-popper"
              :class="['sty4-date-editor']"
              prefix-icon="mico-date"
              v-model="detail.endDate"
              align="right"
              range-separator="-"
              :clearable="false"
              :editable="false"
              placeholder="End Date"
            >
            </m-date-picker>
          </div>
        </div>
      </div>
      <div class="t2">Poster</div>
      <m-upload
        :style="{ pointerEvents: onlyRead ? 'none' : '' }"
        :class="['sty1-upload', errorImageUrlState && 'errorUpload']"
        drag
        accept=".jpg,.png,.jpeg"
        ref="myupload"
        action="#"
        :show-file-list="false"
        :auto-upload="false"
        multiple
        :on-change="fileCheck"
      >
        <div v-if="!imageUrl">
          <i class="mico-upload"></i>
          <div class="t6">Drag or Click here to upload poster</div>
          <div class="t7">Supports JPG, JPEG, PNG file: Max 5MB</div>
        </div>
        <div v-else class="uploadImg">
          <img :src="imageUrl" alt="">
        </div>
      </m-upload>
      <div
      >
        <div class="t2">Description</div>
        <div class="textareaBox">
          <textarea
            @focus="form.errorDescriptionState = false"
            v-model="form.description"
            placeholder="Description will be displayed to users when they claim their token."
            :class="['ui_textarea', form.description?.length > 500 && 'errorTextarea', form.errorDescriptionState && 'errorTextarea']"
            cols="30"
            rows="10"
            :style="{ width: onlyRead ? '100%' : '820px', pointerEvents: onlyRead ? 'none' : '' }"
          ></textarea>
          <div :class="['wordCount', form.description?.length > 500 && 'errorWordCount']">{{ form.description?.length }}/500</div>
        </div>
        <div v-if="form.description?.length > 500" class="errorText1">
          More than 500 characters.
        </div>

        <div class="mui-fl-btw">
          <div class="t4">Claim Page URL</div>
          <div class="mui-fl-central">
            <div class="t5">Enable Custom URL</div>
            <m-switch
              :style="{ pointerEvents: onlyRead ? 'none' : '' }"
              v-model="switchButton"
              active-color="#005563"
              inactive-color="#E6EDED"
              @change="ageSwitchchange"
            >
            </m-switch>
          </div>
        </div>
          <m-input
            :style="{ pointerEvents: onlyRead ? 'none' : '' }"
            v-if="switchButton"
            ref="pageUrl"
            @focus="form.errorPageUrlState = false; form.errorPageUrlState2 = false"
            v-model="form.pageUrl"
            :class="['sty4-input', 'sty4-input-sty1', (form.errorPageUrlState || form.errorPageUrlState2) && 'sty4-input-sty1-error']"
            placeholder="Enter your custom claim page URL"
          ></m-input>
          <div v-else class="pageUrl">{{ pageUrl }}</div>
          <div v-if="form.errorPageUrlState || form.errorPageUrlState2" class="errorPageUrl">
            {{ form.errorPageUrlState ? 'Please enter your custom claim page URL' : '' }}
            {{ form.errorPageUrlState2 ? 'Invalid URL format. Please enter a valid link starting with http:// or https://.' : '' }}
          </div>
      </div>
      <div class="mui-fl-col mui-fl-vert">
        <div class="t9">The Claim Page will be accessible via the custom URL you provide above.</div>
        <div class="mui-fl-vert">
          <div class="t9" style="margin-top: 4px;">To enable proper integration, please follow the steps in the</div>&nbsp;
          <a class="t10" href="" target="_blank">Token Distribution Custom Claim Integration Guide.</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { fileCheck } from '../../../utils/filters'
export default {
  props: {
    onlyRead: {
      type: Boolean,
      required: false
    }
  },
  data () {
    return {
      time: {
        start: new Date().getTime(),
        end: new Date().getTime()
      },
      imageUrl: '',
      formData: null,
      errorImageUrlState: false,
      switchButton: false,
      errorDescriptionLength: false,
      pageUrl: `${process.env.VUE_APP_ZKME_IDENTITY_URL}${this.$store.state.distribution.detail.planId}`,
      form: {
        description: '',
        pageUrl: '',
        errorDescriptionState: false,
        errorPageUrlState: false,
        errorPageUrlState2: false
      },
      rules: {
        description: [this.rulesInputChange('写名字'), this.rulesInputBlur('')],
        pageUrl: [this.rulesInputChange('Please enter your custom claim page URL'), this.rulesInputBlur('')]
      }
    }
  },
  computed: {
    detail () {
      return this.$store.state.distribution.detail
    },
    step () {
      return this.$store.state.distribution.detail.step
    }
  },
  watch: {
    imageUrl (val) {
      if (val) {
        this.errorImageUrlState = false
      }
    },
    switchButton (val) {
      if (val) {
        this.$nextTick(() => {
          // this.$refs.pageUrl.focus()
        })
      } else {
        this.form.errorPageUrlState = false
        this.form.errorPageUrlState2 = false
      }
    }
  },
  created () {},
  mounted () {
    this.$nextTick(() => {
      if (this.step > 3) {
        this.form.description = this.detail.description
        this.form.pageUrl = this.detail.claimPageUrl
        this.imageUrl = this.detail.symbolUrl
        this.switchButton = (this.pageUrl !== this.detail.claimPageUrl)
      }
    })
  },
  methods: {
    async checkForm () {
      if (!this.switchButton) {
        this.form.pageUrl = this.pageUrl
      }
      if (!this.form.description) {
        this.form.errorDescriptionState = true
      }
      if (!this.form.pageUrl) {
        this.form.errorPageUrlState = true
      } else {
        const urlPattern = /^(https?:\/\/)/
        const value = this.form.pageUrl.trim()
        console.log(!urlPattern.test(value))
        if (value && !urlPattern.test(value)) {
          this.form.errorPageUrlState2 = true
          return false
        } else if (value) {
          try {
            // eslint-disable-next-line no-new
            new URL(value)
          } catch (e) {
            this.form.errorPageUrlState2 = true
            return false
          }
        }
      }
      if (!this.imageUrl) {
        this.errorImageUrlState = true
      }
      if (!this.form.description || !this.form.pageUrl || !this.imageUrl || this.form.description?.length > 500) {
        return false
      }
      await this.putDistributionInfo()
      return true
    },
    async postimg () {
      const rp = await this.$api.request('disctrbution.fileUpload', this.formData, {}, 'distributionImg')
      const parameter = {
        filePath: rp.data.filePath,
        expireHours: 24 * 100
      }
      const url = await this.$api.request('disctrbution.getFileUrl', parameter, {}, 'distributionImgUrl')
      return url.data.downloadUrl
    },
    toCamelCase (str) {
      return str.replace(/_([a-z])/g, (match, p1) => p1.toUpperCase())
    },
    getChangedFields (obj1, obj2) {
      const changed = {}
      Object.keys(obj1).forEach(key => {
        if (obj1[key] !== obj2[key] && obj2[key]) {
          changed[this.toCamelCase(key)] = obj2[key]
        }
      })
      return changed
    },
    async putDistributionInfo () {
      let symbolUrl = ''
      if (this.formData) {
        symbolUrl = await this.postimg()
      }
      const form = {
        description: this.form.description,
        symbolUrl: symbolUrl,
        claimPageUrl: this.form.pageUrl
      }
      if (this.step === 3) {
        form.step = 4
      }
      const diff = this.detail.description ? this.getChangedFields(this.detail, form) : form
      if (!Object.keys(diff).length) {
        return true
      }
      return await this.$store.dispatch('putDistributionInfo', diff)
    },
    rulesInputChange (errorText) {
      return {
        required: true,
        trigger: 'change',
        validator: (rule, value, callbak) => {
          if (value) {
            callbak()
          } else {
            callbak(errorText)
          }
        }
      }
    },
    rulesInputBlur () {
      return {
        required: true,
        trigger: 'blur',
        validator: (rule, value, callbak) => {
          if (value.length >= 50) {
            callbak('Please enter a minimum of 50 characters.')
          } else {
            callbak()
          }
        }
      }
    },
    async fileCheck (file) {
      const { fileSize } = fileCheck(file, 0)
      const type = ['jpg', 'png', 'jpeg']
      if (!type.includes(file.raw.type.split('/')[1])) {
        this.$message.error('Unsupported file format. Please upload a JPG, JPEG, or PNG image.')
        return
      }
      if (fileSize > 5) {
        this.$message.error('File too large. Please upload an image under 5MB.')
      } else {
        this.imageUrl = URL.createObjectURL(file.raw)
        const formData = new FormData()
        formData.append('file', file.raw)
        this.formData = formData
      }
    },
    ageSwitchchange () {}
  }
}
</script>

<style lang="scss" src="@/assets/css/views/tokenDistribution/_distributionPageSettingStep3.scss" scoped></style>
