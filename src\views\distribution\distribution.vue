<template>
  <div class="pg-dashboard" style="height: calc(100% - 96px); ">
    <div>
      <div class="mui-fl-vert mui-fl-btw">
        <div class="pg-title1 mui-fl-vert mui-shr-0">
          <svg xmlns="http://www.w3.org/2000/svg" style="cursor: pointer;" width="24" height="24" viewBox="0 0 24 24" fill="none" @click="onBack">
            <path d="M9.81171 6.32812C14.1522 6.32823 17.6711 9.847 17.6711 14.1875C17.671 18.5279 14.1521 22.0468 9.81171 22.0469C5.47136 22.0468 1.95244 18.5279 1.95233 14.1875C1.95233 9.847 5.47129 6.32823 9.81171 6.32812ZM10.175 11.708C9.93922 11.6314 9.68516 11.6314 9.4494 11.708C9.18142 11.7952 8.94842 12.0274 8.48358 12.4922L8.11737 12.8584C7.65266 13.3231 7.42027 13.5562 7.33319 13.8242C7.25672 14.0599 7.25664 14.3142 7.33319 14.5498C7.42034 14.8177 7.6529 15.0502 8.11737 15.5146L8.48358 15.8809C8.94846 16.3457 9.18139 16.5789 9.4494 16.666C9.68506 16.7425 9.93932 16.7425 10.175 16.666C10.4431 16.5789 10.6759 16.3458 11.1408 15.8809L11.507 15.5146C11.9715 15.0501 12.204 14.8177 12.2912 14.5498C12.3678 14.3141 12.3677 14.0599 12.2912 13.8242C12.2041 13.5562 11.9718 13.3231 11.507 12.8584L11.1408 12.4922C10.6759 12.0273 10.443 11.7951 10.175 11.708ZM15.4123 3C18.6494 3 21.2736 5.62427 21.2736 8.86133C21.2736 10.7685 20.3619 12.4619 18.9514 13.5322C18.9441 9.0112 15.4085 5.31903 10.9504 5.06152C12.0254 3.80035 13.6253 3.00008 15.4123 3Z" fill="#738C8F"/>
            <path d="M11.5066 12.8584L11.1406 12.4924C10.6756 12.0275 10.4431 11.795 10.175 11.7079C9.9392 11.6312 9.68518 11.6312 9.44936 11.7079C9.18127 11.795 8.94878 12.0275 8.48381 12.4924L8.11781 12.8584C7.65283 13.3234 7.42034 13.5559 7.33324 13.824C7.25662 14.0598 7.25662 14.3138 7.33324 14.5497C7.42034 14.8177 7.65283 15.0502 8.11781 15.5152L8.48381 15.8812C8.94878 16.3462 9.18127 16.5787 9.44936 16.6658C9.68518 16.7424 9.9392 16.7424 10.175 16.6658C10.4431 16.5787 10.6756 16.3462 11.1406 15.8812L11.5066 15.5152C11.9715 15.0502 12.204 14.8177 12.2911 14.5497C12.3678 14.3138 12.3678 14.0598 12.2911 13.824C12.204 13.5559 11.9715 13.3234 11.5066 12.8584Z" fill="#B3C0C2"/>
          </svg>
          <!-- <i class="mcico-active-dashboard" style="cursor: pointer;" @click="onBack"></i> -->
          / Plans
          <span class="guideline mui-fl-vert">
            Token Distribution Guideline
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M2.55001 3.3002C2.30148 3.3002 2.10001 3.50167 2.10001 3.7502V8.8502C2.10001 9.09872 2.30148 9.3002 2.55001 9.3002H7.65001C7.89854 9.3002 8.10001 9.09872 8.10001 8.8502V6.4502C8.10001 6.20167 8.30148 6.0002 8.55001 6.0002C8.79854 6.0002 9.00001 6.20167 9.00001 6.4502V8.8502C9.00001 9.59578 8.3956 10.2002 7.65001 10.2002H2.55001C1.80443 10.2002 1.20001 9.59578 1.20001 8.8502V3.7502C1.20001 3.00461 1.80443 2.4002 2.55001 2.4002H5.55001C5.79854 2.4002 6.00001 2.60167 6.00001 2.8502C6.00001 3.09872 5.79854 3.3002 5.55001 3.3002H2.55001Z" fill="#002E33"/>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M3.71632 7.65211C3.88306 7.8364 4.16763 7.85063 4.35192 7.68389L9.90001 2.66419V4.3502C9.90001 4.59872 10.1015 4.8002 10.35 4.8002C10.5985 4.8002 10.8 4.59872 10.8 4.3502V1.6502C10.8 1.40167 10.5985 1.2002 10.35 1.2002H7.65001C7.40148 1.2002 7.20001 1.40167 7.20001 1.6502C7.20001 1.89872 7.40148 2.1002 7.65001 2.1002H9.18192L3.7481 7.0165C3.56381 7.18325 3.54958 7.46781 3.71632 7.65211Z" fill="#002E33"/>
            </svg>
          </span>
        </div>
        <div class="mui-fl-vert">
          <m-button v-if="!jumpStep && listLength" class="sty2-button sty7-button" @click="onjumpStep('create')">Create Token Distribution Plan</m-button>
          <m-button v-if="jumpStep && detail.planId" class="sty2-button sty7-button sty9-button" @click="deletePlan">Delete Draft</m-button>
          <m-popover popper-class="sty8-popper" :offset="-60" placement="bottom" width="200" trigger="click" v-model="visible" transition="ease-in-out">
            <m-button v-if="address" class="disconnect" type="text" @click="disconnect">
              Disconnect
            </m-button>
            <m-button class="connectWallet" slot="reference" @click="connectWallet">
              {{ address || 'Connect wallet' }}
            </m-button>
          </m-popover>
          <m-popover :key="'levelPopover'" placement="bottom-start" popper-class="sty1-popper sty2-popper sty9-popper"
            trigger="manual" v-model="showNetworkPopover" :offset="-60" transition="ease-in-out">
            <div class="selectNetwork">
              <div class="t1">Select network</div>
              <ul class="chainList">
                <li v-for="(data, index) of formNetWork" :key="index" class="chain mui-fl-vert mui-fl-btw" @click="changeChain(data)">
                  <div class="mui-fl-vert">
                    <img style="width: 20px; height: 20px;" :src="data.icon" alt="">
                    {{ data.value }}
                  </div>
                  <svg v-if="chain === data.value && !loading" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.33333 8.22222L6.97436 10L10.6667 6M14 8C14 11.3137 11.3137 14 8 14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2C11.3137 2 14 4.68629 14 8Z" stroke="#005563" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <div v-if="loadingName === data.value && loading" class="loader"></div>
                </li>
              </ul>
            </div>
            <!-- <m-cascader-panel v-model="cascaderValue" :props="{ expandTrigger: 'hover' }" :options="formNetWork"
              class="sty1-cascader sty2-cascader" @change="handleCascaderChange">
              <template #default="{ data }">
                {{ data.value }}
              </template>
            </m-cascader-panel> -->
            <div id="dropdown" slot="reference" class="dropdown mui-fl-btw mui-fl-vert" @click="showNetworkPopover = !showNetworkPopover">
              <div class="mui-fl-vert">
                <svg v-if="!chain" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                  fill="none">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58172 12.4183 0 8 0C3.58172 0 0 3.58172 0 8C0 12.4183 3.58172 16 8 16ZM6.28033 5.21967C5.98744 4.92678 5.51256 4.92678 5.21967 5.21967C4.92678 5.51256 4.92678 5.98744 5.21967 6.28033L6.93934 8L5.21967 9.71967C4.92678 10.0126 4.92678 10.4874 5.21967 10.7803C5.51256 11.0732 5.98744 11.0732 6.28033 10.7803L8 9.06066L9.71967 10.7803C10.0126 11.0732 10.4874 11.0732 10.7803 10.7803C11.0732 10.4874 11.0732 10.0126 10.7803 9.71967L9.06066 8L10.7803 6.28033C11.0732 5.98744 11.0732 5.51256 10.7803 5.21967C10.4874 4.92678 10.0126 4.92678 9.71967 5.21967L8 6.93934L6.28033 5.21967Z"
                    fill="#EE6969" />
                </svg>
                <img style="width: 20px; height: 20px;" v-else :src="formNetWork.find(x => x.value === chain).icon" alt="">
                <span class="chainText">{{ chain || 'Unsupported Network' }}</span>
              </div>
              <i class="mico-arrow-bottom"></i>
            </div>
          </m-popover>
        </div>
      </div>
    </div>
    <DistributionList v-if="!jumpStep" @onjumpStep="onjumpStep" @getlength="getlength" ref="distributionList"></DistributionList>
    <DistributionStep v-else @onBack="onBack" @back="jumpStep = false" @onjumpStep=onjumpStep @connectWallet="connectWallet"></DistributionStep>
    <ConnectWalletDialog :connectDialogOpened=connectDialogOpened @close="connectDialogOpened = false" />
  </div>
</template>

<script>
import ConnectWalletDialog from '@/components/connect-wallet/ConnectWalletDialog.vue'
import DistributionList from './components/distributionList.vue'
import DistributionStep from './distributionStep.vue'
import { formatPubKey } from '../../utils/filters'
export default {
  components: { ConnectWalletDialog, DistributionList, DistributionStep },
  data () {
    return {
      jumpStep: false,
      loading: false,
      loadingName: '',
      connectDialogOpened: false,
      visible: false,
      cascaderValue: false,
      showNetworkPopover: false,
      chainName: '',
      listLength: false
    }
  },
  computed: {
    address () {
      return formatPubKey(this.$store.state.wallet.connectedAddress, 4, 6)
    },
    detail () {
      return this.$store.state.distribution.detail
    },
    distributionStatus () {
      return this.$store.state.distribution.distributionStatus
    },
    chain () {
      const walletChainId = this.$store.state.wallet.walletChainId
      let name = ''
      if (walletChainId) {
        name = this.formNetWork.find(x => x.chainID === walletChainId)?.value
      }
      return name
    },
    formNetWork () {
      return [
        // {
        //   value: 'Polygon',
        //   label: '1',
        //   icon: require('@/assets/img/networkicon/polygon.svg'),
        //   chainID: 137
        // },
        // {
        //   value: 'Base Testnet',
        //   label: '2',
        //   icon: require('@/assets/img/networkicon/base.svg'),
        //   chainID: 8453
        // },
        // {
        //   value: 'Arbitrum',
        //   label: '3',
        //   icon: require('@/assets/img/networkicon/arbitrum.svg'),
        //   chainID: 42161
        // },
        {
          value: 'Moca',
          label: '4',
          icon: require('@/assets/img/networkicon/moca.svg'),
          chainID: 5151
        }
        // {
        //   value: 'Kaia',
        //   label: '4',
        //   icon: require('@/assets/img/networkicon/kaia.svg'),
        //   chainID: 8217
        // },
        // {
        //   value: 'BNB Smart Chain',
        //   label: '5',
        //   icon: require('@/assets/img/networkicon/bnb.svg'),
        //   chainID: 56
        // }
      ]
    }
  },
  filters: {
  },
  watch: {
    address (val) {
      if (val) {
        this.connectDialogOpened = false
      } else {
        this.visible = false
      }
    },
    distributionStatus (val) {
      if (val) {
        this.onBack()
      }
    }
  },
  mounted () {
    document.addEventListener('click', this.handleDocumentClick)
  },
  beforeDestroy () {
    document.removeEventListener('click', this.handleDocumentClick)
  },
  methods: {
    deletePlan () {
      this.$api.request('disctrbution.deleteTdTokenDistributionPlan', { planId: this.detail.planId }, {}, 'distribution').then(res => {
        if (res.code === 200) {
          this.$store.dispatch('clearDetail')
          this.jumpStep = false
        } else {
          this.$message.error('Delete failed.')
        }
      })
      console.log(this.detail)
    },
    handleDocumentClick (e) {
      if (e?.target.id === 'dropdown') {
        return
      }
      this.showNetworkPopover = false
    },
    getlength (val) {
      this.listLength = val
    },
    onBack () {
      this.jumpStep = false
      this.$store.commit('SET_DISTRIBUTUION_STATUS', false)
      this.$nextTick(() => {
        if (this.$refs.distributionList) {
          this.$refs.distributionList.closePageSwitching()
        }
      })
    },
    async onjumpStep (val) {
      if (val) {
        this.$store.dispatch('clearDetail')
      }
      if (!this.address) {
        this.connectWallet()
      } else {
        this.jumpStep = !this.jumpStep
      }
    },
    async changeChain (data) {
      if (data.value === this.chain) return
      this.loading = true
      this.loadingName = data.value
      try {
        await this.$store.dispatch('switchEthereumChain', '0x' + data.chainID.toString(16))
      } catch (error) {
        console.log('error', error)
      }
      this.loading = false
      this.showNetworkPopover = false
    },
    switchEthereumChain () {
      this.$store.dispatch('switchEthereumChain')
    },
    close () {
      this.dialogOpened = false
    },
    disconnect () {
      this.visible = false
      this.$store.dispatch('disconnect')
    },
    connectWallet () {
      if (this.address) return
      this.visible = true
      this.connectDialogOpened = true
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/tokenDistribution/_distribution.scss" scoped></style>
