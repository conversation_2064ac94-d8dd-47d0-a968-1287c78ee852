"use strict";(self["webpackChunkzkme_web"]=self["webpackChunkzkme_web"]||[]).push([[978],{66978:function(t,e,s){s.d(e,{A:function(){return y}});var i=function(){var t=this,e=t._self._c;return e("div",[e("m-drawer",{staticClass:"recorded_drawer",attrs:{modal:!1,visible:t.drawerVisible},on:{close:t.close},scopedSlots:t._u([{key:"title",fn:function(){return[t.detail.kycProgramId?e("div",{staticClass:"drawer-title"},[t._v(" "+t._s(t.detail.programName)+" "),t.isOldKycProgram||t.type?t._e():e("i",{staticClass:"mico-edit icon-edit",on:{click:t.toEdit}})]):t._e()]},proxy:!0}])},[t.detail.kycProgramId?t._e():e("div",{staticClass:"txn-graph-loading-or-empty mui-fl-central",staticStyle:{width:"630px",height:"100%"}},[e("div",{staticClass:"mui-fl-col"},[e("LoadingLottie",{attrs:{size:"40px"}}),e("p",{staticClass:"loading-txt"},[t._v("Loading...")])],1)]),t.detail.kycProgramId?e("div",{staticClass:"mui-flex program drawer_margin"},[e("span",[t._v("Program No. ")]),e("span",[t._v(t._s(t.detail.kycProgramId))]),e("div",{staticClass:"m",staticStyle:{height:"14px"}}),t.drawerCopy?e("span",{staticClass:"l cop"},[t._v("Copied")]):e("span",{staticClass:"l",on:{click:function(e){return t.copy(t.detail.kycProgramId)}}},[t._v("Copy")])]):t._e(),t.detail.kycList?e("kyc-detail",{ref:"detail",attrs:{list:t.detail.kycList,"select-country":t.selectCountry,"select-geolocation":t.selectGeolocation,kycInfo:t.category}}):t._e(),Object.keys(t.wallet).length&&t.detail.kycProgramId?e("div",[e("div",{staticClass:"con-text"},[t._v("Account address")]),e("div",{staticClass:"igTexts mbs"},[t._v("This account is utilized for invoking the zkMe smart contract and storing authorization information from your users.")]),e("div",{staticClass:"mb mui-flex edAddress"},[e("div",{staticClass:"addressWl"},t._l(t.wallet,(function(s,i){return e("div",{key:i,staticClass:"addresscor mui-flex"},["evm"===i?e("div",{staticClass:"igTexts addressHand"},[t._v("EVM address")]):t._e(),"sei"===i?e("div",{staticClass:"igTexts addressHand"},[t._v("Cosmos address (sei)")]):t._e(),"neutron"===i?e("div",{staticClass:"igTexts addressHand"},[t._v("Cosmos address (neutron)")]):t._e(),"aptos"===i?e("div",{staticClass:"igTexts addressHand"},[t._v("Aptos address")]):t._e(),"ton"===i?e("div",{staticClass:"igTexts addressHand"},[t._v("Ton address")]):t._e(),e("div",{staticClass:"igTexts addresscor"},[t._v(" "+t._s(s.address)+" "),e("m-popover",{attrs:{placement:"top",width:"64",trigger:"manual","close-delay":1e3,value:s.flag,"popper-class":"NotRecord_popover WhiteList",content:"Copied!"}},[e("i",{staticClass:"mico-copy igcur",attrs:{slot:"reference"},on:{click:function(e){return t.copyAddress(s)}},slot:"reference"})])],1)])})),0)])]):t._e(),t.detail.recordsList?e("div",{staticClass:"steps-box"},[t.detail.recordsList?.length?e("div",{staticClass:"drawer_records drawer_margin"},[t._v("Records")]):t._e(),e("div",{staticClass:"drawer_margin drawer_step"},t._l(t.detail.recordsList,(function(s,i){return e("div",{key:i},[t.filterRecordsList(1).length&&3!==s.status||-1===s.status||2===s.status||4===s.status||5===s.status||!t.filterRecordsList(1).length&&3===s.status?e("div",{class:["mui-flex","mui-fl-vert",t.status[3===s.status?2:s.status]?.toLowerCase()]},[3!==s.status||3===s.status&&t.getModifyStatus(s)?e("div",{staticClass:"boolball"}):t._e(),3===s.status&&t.getModifyStatus(s)?e("span",{staticClass:"drawer_text_hand"},[t._v(" "+t._s(t.status[s.status])+" ")]):3!==s.status?e("span",{staticClass:"drawer_text_hand"},[t._v(t._s(t.status[s.status]))]):t._e(),3!==s.status||t.getModifyStatus(s)?e("span",{staticClass:"drawer_text"},[t._v(" "+t._s(1===s.status?"from ":"at ")+" "+t._s(3!==s.status?s.createTime:t.modifyCreateTime)+" ")]):t._e()]):t._e(),t.detail.recordsList.length-1-i&&3!==s.status&&(1===s.status||5===s.status||4===s.status)&&!t.filterRecordsList(3).length||s.createTime===t.modifyCreateTime?e("div",{class:["drawer_division","mui-fl-vert",s.createTime===t.modifyCreateTime&&"textma"]},[s.createTime===t.modifyCreateTime?e("span",{staticClass:"text"},[e("span",{on:{click:function(e){t.openModified=!t.openModified}}},[t._v(" Modified "+t._s(t.filterRecordsList(3).length)+" times "),e("i",{class:["mico-fold",t.openModified&&"mico-fold-rotate"]})]),t.openModified?e("div",{staticStyle:{cursor:"default"}},t._l(t.filterRecordsList(3),(function(s,i){return e("div",{key:i,staticClass:"foldtext"},[t._v(" Modified at "+t._s(s.createTime)+" ")])})),0):t._e()]):t._e()]):t._e()])})),0)]):t._e(),t.type?t._e():e("div",{staticClass:"mui-fl-end footer-box"},[2===t.detail.status||3===t.detail.status?[e("m-button",{staticClass:"sty3-button fs-14",on:{click:t.toDelete}},[t._v("Delete")]),t.isOldKycProgram?t._e():e("m-button",{staticClass:"sty4-button fs-14",on:{click:t.modifyProgram}},[t._v("Modify program")]),t.isOldKycProgram?t._e():e("m-button",{staticClass:"sty2-button fs-14",attrs:{loading:t.applyLoading},on:{click:t.applyProgram}},[t._v("Apply program")])]:t._e(),1===t.detail.status||4===t.detail.status||5===t.detail.status?[e("m-button",{staticClass:"sty4-button fs-14 width-2",on:{click:t.duplicate}},[t._v("Duplicate")]),1===t.detail.status||5===t.detail.status?e("m-button",{staticClass:"sty3-button fs-14 width-1",on:{click:t.cancelService}},[t._v("Cancel program")]):t._e()]:t._e()],2)],1),e("KycWarning",{attrs:{model:t.model,warningtip:t.warningtip},on:{Leave:t.Leave}}),e("m-dialog",{staticClass:"sty2-dialog dialog",attrs:{visible:t.editNameVisible,"close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(e){t.editNameVisible=e}}},[[e("div",{staticClass:"leavetip_title"},[t._v(" Modify program name ")]),e("div",{staticClass:"program-name"},[t._v("Program Name")]),e("div",[e("MuiInput",{staticClass:"inp",attrs:{closeflg:!0,error:t.dialogwarning},on:{input:t.input},model:{value:t.dialoginp,callback:function(e){t.dialoginp=e},expression:"dialoginp"}})],1),t.dialogwarning&&t.dialoginp?e("div",{staticClass:"dialog_warning"},[t._v("Numbers or letters only, no more than 50 characters. ")]):t.dialogwarning&&!t.dialoginp?e("div",{staticClass:"dialog_warning"},[t._v("Please enter your program name.")]):t._e()],e("div",{staticClass:"dialog_button warin_button mui-fl-end"},[e("m-button",{staticClass:"kycSave",on:{click:t.cancel}},[t._v("Cancel")]),e("m-button",{staticClass:"kycCancel Apply",attrs:{loading:t.applyLoading},on:{click:t.confirm}},[t._v("Confirm")])],1)],2)],1)},a=[],o=(s(44114),s(17642),s(58004),s(33853),s(45876),s(32475),s(15024),s(31698),s(98992),s(54520),s(72577),s(3949),s(81454),s(84082)),l=o.A,r=s(4746),d=s(31214),n=s(11417),c=s(48544),u=s(5332),m={components:{KycDetail:r.A,KycWarning:l,MuiInput:n.A,LoadingLottie:u.A},props:{drawerVisible:{type:Boolean,default:!1},id:{type:Number,required:!0},createTime:{type:Number,required:!1},hasAppliedProgram:{type:Boolean,default:!1},type:{type:String,default:""}},data(){return{netWorkType:c.Xr,netWork:[],detail:{},initDetail:{},drawerCopy:!1,openModified:!1,warningtip:!1,model:"Recorded",msgTxt:"",operationData:{},editNameVisible:!1,dialogwarning:!1,Flagaddress:!1,FlagseiAddress:!1,FlagneutronAddress:!1,FlagaptosAddress:!1,wallet:{},dialogType:"",dialoginp:"",category:1,form:{countryIds:[],id:"",ids:"",programName:"",status:2,userId:"",category:""},applyLoading:!1,status:{"-1":"Expired",1:"Applied",2:"Created",3:"Last modified",4:"Expired",5:"Pending"}}},computed:{isOldKycProgram(){return!!this.createTime&&this.createTime<new Date("2023-09-28 18:00:00").getTime()},modifyCreateTime(){return this.detail?.recordsList.filter((t=>3===t.status))[0]?.createTime||""},kycMode(){return this.$store.state.kyc.category},kycLevel(){return localStorage.getItem("zkmeAdminUser")&&JSON.parse(localStorage.getItem("zkmeAdminUser")).level||this.$store.state.auth.user.level}},watch:{id(){this.id&&this.queryKycInfo()}},mounted(){this.queryKycInfo()},methods:{moveAndModify(t,e){t=t.map((t=>({...t,ids:t.id})));const s=[...t],i=s.pop();s.splice(e,0,i),s[e].ids=e;for(let a=e;a<s.length;a++)s[a].ids=a+1;return s},async queryKycInfo(){if(this.id)try{const t=await this.$api.request("kyc.queryKycInfo",{id:this.id,category:this.kycMode});if(8e7===t.code){this.category=this.kycMode,this.netWork=[];const e=[];let s=[],i=!0;t.data.kycList&&t.data.kycList.forEach((t=>{let a="",o=t.adminKycPropertyBoList;if(1===t.id)a="Network",o=t.adminKycPropertyBoList.map((t=>{const e=t.value.filter((t=>t.isSelect&&"Ethereum Goerli Testnet"!==t.value));s=s.concat(e.map((t=>t.id)));const i=t.value.filter((t=>t.isSelect)).map((t=>c.Xr[t.value]));return i.length&&this.netWork.push(...i),{...t,value:e}})),2===this.kycLevel&&(i=!1);else if(2===t.id){a="Passable requirements";const t=o[0].value.filter((t=>t.isSelect)),e=o[1].value.filter((t=>t.isSelect));s=s.concat(t.map((t=>t.id))),s=s.concat(e.map((t=>t.id))),o=o.map((t=>({...t,disabled:2===t.status||1===t.status,status:!0,value:"Age"===t.kycVerifyProperty?t.value.map((t=>({...t,isSelect:t.isSelect??"18"===t.value}))):t.value})))}else if(3===t.id)a="AML Screening",o=o.map((t=>(t.value[0].isSelect&&s.push(t.value[0].id),3===this.kycMode&&(s=[]),{...t,disabled:2===t.status||1===t.status,status:!0,id:t.value[0].id}))),i=t.adminKycPropertyBoList.filter((t=>t.value[0].isSelect)).length;else if(4===t.id)a="Uniqueness check",o=o.map((t=>(t.value[0].isSelect&&s.push(t.value[0].id),3===this.kycMode&&(s=[]),{...t,disabled:2===t.status||1===t.status,status:!0,id:t.value[0].id}))),i=t.adminKycPropertyBoList.filter((t=>t.value[0].isSelect)).length;else if(5===t.id){const e=t.adminKycPropertyBoList[0].value.filter((t=>t.isSelect));s=s.concat(e.map((t=>t.id))),a="Location Check",o=o.map((t=>({...t,disabled:2===t.status||1===t.status,status:!0,value:t.value})))}e.push({title:a,adminKycPropertyBoList:o,status:t.status,id:t.id,exhibit:i}),i=!0})),this.getIntegrationList(t.data);const a=[],o=[],l=[],r=[];t.data.countryInfoBoList&&(t.data.countryInfoBoList=this.moveAndModify(t.data.countryInfoBoList,118),t.data.countryInfoBoList.forEach((t=>{const e=t.regularName.substring(0,1);if(t.isSelect&&!this.kycMode){a.find((t=>t.letter===e))||a.push({letter:e,childrens:[]});const s=a.find((t=>t.letter===e)),i=s.childrens;!t.regularName.startsWith(e)||i.find((e=>e.regularName===t.regularName))&&i.length||i.push({...t})}else{o.find((t=>t.letter===e))||o.push({letter:e,childrens:[]});const s=o.find((t=>t.letter===e)),i=s.childrens;!t.regularName.startsWith(e)||i.find((e=>e.regularName===t.regularName))&&i.length||i.push({...t})}})),this.$store.commit("SET_SELECTCOUNTRIES",t.data.countryInfoBoList.filter((t=>t.isSelect)).length),this.$store.commit("SET_COUNTRIESLENGTH",t.data.countryInfoBoList.length)),t.data.locationInfoBoList&&(t.data.locationInfoBoList=this.moveAndModify(t.data.locationInfoBoList,118),t.data.locationInfoBoList.forEach((t=>{const e=t.regularName.substring(0,1);l.find((t=>t.letter===e))||l.push({letter:e,childrens:[]});const s=l.find((t=>t.letter===e)),i=s.childrens;!t.regularName.startsWith(e)||i.find((e=>e.regularName===t.regularName))&&i.length||i.push({...t})})),this.$store.commit("SET_GEOSELECTCOUNTRIES",t.data.locationInfoBoList.filter((t=>t.isSelect)).length));const d=t.data.countryInfoBoList?.map((t=>({...t,id:t.id,isSelect:t.isSelect,supportDocuments:t.supportDocumentList}))),n=t.data.locationInfoBoList?.filter((t=>t.isSelect)).map((t=>t.id));this.form={countryBoList:d,ids:s,status:t.data.status,id:t.data.id,userId:(new Date).getFullYear(),category:this.$store.state.kyc.category,locationIds:n};const u=a.length?a:o,m=a.length?o:[],p=l.length?l:r,h=l.length?r:[];this.selectCountry={avaliableList:u,unavailableList:m},this.selectGeolocation={avaliableList:p,unavailableList:h},this.detail={...t.data,kycList:e},this.initDetail={...t.data,countryInfoBoList:this.isOldKycProgram&&!t.data.countryInfoBoList.find((t=>t.isSelect))?t.data.countryInfoBoList.map((t=>({...t,isSelect:!0}))):t.data.countryInfoBoList}}}catch(t){console.log("queryKycInfo error: ",t)}},async getIntegrationList(t){this.wallet={};const e=[...new Set(this.netWork)],s=e.length?[...new Set(this.netWork)]:["EVM","Aptos","Neutron","SEI","TON"];for(const i of s)switch(i){case"EVM":if(!t.walletAddressEvm)continue;this.wallet.evm={address:t.walletAddressEvm,flag:!1};continue;case"Aptos":if(!t.walletAddressAptos)continue;this.wallet.aptos={address:t.walletAddressAptos,flag:!1};continue;case"Neutron":if(!t.walletAddressNeutron)continue;this.wallet.neutron={address:t.walletAddressNeutron,flag:!1};continue;case"SEI":if(!t.walletAddressSei)continue;this.wallet.sei={address:t.walletAddressSei,flag:!1};continue;case"TON":if(!t.walletAddressTon)continue;this.wallet.ton={address:t.walletAddressTon,flag:!1};continue}},copyAddress(t,e){const s=document.createElement("input");document.body.appendChild(s),s.value=t.address,s.select(),document.execCommand("Copy"),document.body.removeChild(s),this.wallet=Object.assign({},this.wallet),t.flag=!0;let i=null;i=setTimeout((()=>{t.flag=!1,clearTimeout(i)}),1e3)},getModifyStatus(t){return!!this.detail.recordsList&&t.createTime===this.detail.recordsList.filter((t=>3===t.status))[0].createTime},filterRecordsList(t){return this.detail?.recordsList.filter((e=>e.status===t))||[]},close(){3===this.kycMode&&this.selectGeolocation?.avaliableList.length?this.selectGeolocation.avaliableList.length&&this.$refs.detail.selectOption(this.selectGeolocation.avaliableList[0].childrens[0],5):this.selectGeolocation?.avaliableList.length&&(this.selectCountry.avaliableList.length&&this.$refs.detail.selectOption(this.selectCountry.avaliableList[0].childrens[0],2),this.selectGeolocation.avaliableList.length&&this.$refs.detail.selectOption(this.selectGeolocation.avaliableList[0].childrens[0],5)),this.$emit("close"),this.detail={}},async Leave(t){if(this.warningtip=!1,"Leave"===t){const t=await this.$api.request("kyc.updateStatus",this.operationdata);8e7===t.code&&(this.$message({customClass:"kycmessage",type:"success",iconClass:"mcico-success",message:this.msgTxt}),this.$emit("close"),this.$emit("update"))}},toDelete(){this.warningtip=!0,this.model="Delete",this.msgTxt="Delete success!",this.operationdata={id:this.detail.id,status:-1}},cancelService(){this.warningtip=!0,this.model="Cancel",this.msgTxt="Service canceled",this.operationdata={id:this.detail.id,status:4}},modifyProgram(){this.$store.commit(d.tq,this.initDetail),this.$store.commit(d.wh,"Modify program"),this.$router.push({path:`/zk-kyc/zkkyc-form?mode=${this.$store.state.kyc.category}`})},duplicate(){this.$store.commit(d.oN,this.initDetail),this.$store.commit(d.wh,"Create program"),this.$router.push({path:`/zk-kyc/zkkyc-form?mode=${this.$store.state.kyc.category}`})},applyProgram(){this.operationdata={id:this.detail.id,status:1},this.applyLoading=!0,this.applyConfirm()},async applyConfirm(){const t=await this.$api.request("kyc.updateStatus",this.operationdata);8e7===t.code&&(this.editNameVisible=!1,this.applyLoading=!1,this.$emit("close"),this.$emit("update"))},input(){const t=/^[0-9a-zA-Z ]*$/;this.dialoginp.length>50||!this.dialoginp.length||!t.test(this.dialoginp)?this.dialogwarning=!0:this.dialogwarning=!1},toEdit(){this.dialoginp=this.detail.programName,this.dialogType="edit",this.editNameVisible=!0},cancel(){this.editNameVisible=!1,setTimeout((()=>{this.dialoginp="",this.dialogwarning=!1}),100)},async confirm(){if("apply"===this.dialogType)return this.applyLoading=!0,void await this.applyConfirm();if(this.input(),this.dialogwarning)return;const t=await this.$api.request("kyc.updateKycProgram",{...this.form,programName:this.dialoginp});8e7===t.code&&(this.editNameVisible=!1,this.$message({customClass:"kycmessage",type:"success",iconClass:"mcico-success",message:"Save Success!"}),this.$emit("close"),this.$emit("update"))},copy(t){const e=document.createElement("input");e.value="object"===typeof t?t.kycProgramId:t,document.body.appendChild(e),e.select(),document.execCommand("Copy"),e.remove(),"object"===typeof t?(this.$set(t,"copystyle",!0),setTimeout((()=>{t.copystyle=!1}),500)):(this.drawerCopy=!0,setTimeout((()=>{this.drawerCopy=!1}),500))}}},p=m,h=s(81656),g=(0,h.A)(p,i,a,!1,null,"18088059",null),y=g.exports}}]);