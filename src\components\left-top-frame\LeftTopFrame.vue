<template>
  <div class="mui-container mui-flex">
    <div class="mui-aside mui-fl-col mui-shr-0 mui-fl-btw">
      <div class="logo-header mui-shr-0 mui-fl-vert">
        <div class="logo">
          <span />
        </div>
        <p class="business">Business</p>
      </div>
      <div class="scrollbox mui-fl-1">
        <m-menu
          class="sty1-menu"
          text-color="#738C8F"
          active-text-color="#002E33"
          background-color="#E6EDED"
          router
          :collapse="isCollapse"
          :default-active="activeName"
        >
          <template v-for="lv1 of finalNav">
            <!-- id 属性给测试自动化使用 -->
            <!-- 一级导航 -->
            <m-submenu v-if="lv1.child" :key="lv1.id" :index="lv1.id" class="sty1-submenu">
              <template slot="title">
                <div class="mui-fl-vert">
                  <i :class="activeLV1Menu === lv1.name ? lv1.activeIcon : lv1.icon"></i>
                  <span :id="'nv_' + lv1.power">{{ lv1.name }}</span>
                </div>
              </template>

              <template v-for="lv2 of lv1.child">
                <!-- 二级导航 -->
                <m-submenu v-if="lv2.child" :key="lv2.id" :index="lv2.id">
                  <template slot="title">
                    <i :class="activeLV1Menu === lv1.name ? lv1.activeIcon : lv1.icon"></i>
                    <span :id="'nv_' + lv2.power">{{ lv2.name }}</span>
                  </template>
                </m-submenu>
                <m-menu-item v-else :key="lv2.id" :index="lv2.path" :id="'nv_' + lv2.power" @click="financewidth('导航菜单')">
                <i :class="lv2.icon"></i>
                <span slot="title">{{ lv2.name }}</span>
              </m-menu-item>
              </template>
            </m-submenu>

            <m-menu-item v-else :key="lv1.id" :index="lv1.path" :id="'nv_' + lv1.power" class="mui-fl-vert">
              <i :class="activeLV1Menu === lv1.name ? lv1.activeIcon : lv1.icon"></i>
              <span slot="title">{{ lv1.name }}</span>
            </m-menu-item>
          </template>
        </m-menu>
      </div>
      <div class="setup mui-shr-0 mui-fl-vert">
        <!-- <i class="taplight mico-settings" @click="handleTip" /> -->
        <a v-if="$route.name === 'Uidesign'" href="https://docs.zk.me/zkme-dochub/verify-with-zkme-protocol/integration-guide/customize-widget-ui" target="_blank"><i class="taplight mico-help" /></a>
        <a v-else href="https://docs.zk.me/zkme-dochub/verify-with-zkme-protocol/integration-checklist/set-up-your-zkme-dashboard" target="_blank"><i class="taplight mico-help" /></a>
      </div>
    </div>

    <div class="mui-content mui-fl-col mui-fl-1">
      <div class="mui-header mui-fl-end">
        <div class="mui-fl-vert">
          <div class="mui-fl-vert">
            <m-popover
              popper-class="sty3-popper"
              placement="bottom-end"
              width="180"
              trigger="hover">
              <ul class="user-set">
                <li class="taplight2" @click="$router.push({ name: 'ResetPassword' })">Reset Password</li>
                <li class="taplight2" @click="logout">Log Out</li>
              </ul>
              <div slot="reference" class="mui-fl-vert">
                <img src="@/assets/img/default.png">
                <p>{{ tokenName }}</p>
                <i class="mico-arrow-bottom"></i>
              </div>
            </m-popover>
            <!-- <i class="mico-arrow-bottom" /> -->
          </div>
        </div>
      </div>
      <router-view class="sty2-cell" />
    </div>
  </div>
</template>

<script>
import navCfg from './nav.config'

export default {
  name: 'LeftTopFrame',
  data () {
    return {
      navCfg: navCfg(),
      finalNav: null,
      routes: [],
      widthflag: false,
      connecting: false,
      isCollapse: document.body.clientWidth <= 1200
    }
  },
  computed: {
    tokenName () {
      return this.$store.state.auth.user?.name || ''
    },
    activeLV1Menu () {
      const cfg = this.generateNav()
      let asd = ''
      cfg.finalNav.forEach(x => {
        if (x.child) {
          x.child.forEach(y => {
            if (y.path === this.activeName) {
              asd = x.name
            }
          })
        } else {
          if (x.path === this.activeName) {
            asd = x.name
          }
        }
      })
      return asd
    },
    // 查找当前的页面路由是否在 routes 中有匹配，匹配到的话返回该项作为导航的激活项（高亮）
    activeName () {
      return this.routes.find(x => {
        if (x === '/') {
          if (this.$route.path === '/') {
            return x
          }
        } else if (this.$route.path.indexOf(x) === 0) {
          // 该判断条件是为了满足进入详情页之类的页面也能使导航高亮
          return x
        }
      })
    },
    powers () {
      // return this.$store.state.auth.powers
      return []
    },
    isCollapse0 () {
      return document.body.clientWidth
    }
    // user () { return this.$store.state.auth.user },
    // isCollapse: {
    //   get () {
    //     // return this.$store.state.foundation.prefer.navCollap
    //     // return false
    //     return true
    //   },
    //   set (val) {
    //     this.$store.commit('UPDATE_PREFER', { navCollap: val })
    //   }
    // },
  },
  mounted () {
    const cfg = this.generateNav()
    if (cfg) {
      this.finalNav = cfg.finalNav
      this.routes = cfg.routes
    }
    this.onWinResize()
    const cancalDebounce = this.debounce(this.onWinResize, 150)

    window.addEventListener('resize', cancalDebounce)
  },
  created () {
    this.financewidth()
  },
  watch: {
    '$route.query' () {
      this.financewidth()
    }
  },
  // async beforeRouteLeave (to, from, next) {
  //   if (to.name === 'Login' && this.$store.state.auth.user) {
  //     const rp = await this.$store.dispatch('logout')
  //     if (rp.code === 80000000) {
  //       next()
  //     }
  //     this.$api.defaults.errorResponseHandler = err => {
  //       if (err.request?.status === 401) {
  //         next()
  //       }
  //     }
  //   } else {
  //     next()
  //   }
  // },
  methods: {
    async logout () {
      this.$store.dispatch('logout')
      this.$router.push('/login')
    },
    onWinResize () {
      this.isCollapse = document.body.clientWidth <= 1200
    },

    debounce (fn, delay) {
      let timer
      return () => {
        if (timer) {
          clearTimeout(timer)
        }
        timer = setTimeout(() => {
          fn()
        }, delay)
      }
    },

    financewidth () {
      if (this.$route.path === '/distributionList') {
        this.$store.commit('SET_DISTRIBUTUION_STATUS', true)
      }
      if (this.$route.path === '/finance/payment' || this.$route.path === '/finance/examine') {
        this.widthflag = true
      } else {
        this.widthflag = false
      }
    },
    // 根据服务端返回的权限数据与 nav.config 匹配生成导航
    generateNav () {
      const routes = []
      let id = 0
      // 即使 this.powers 为空也要给一个默认值 {}，
      // 以便下面遍历如期运行，至少会生成无需权限的导航项目
      // const pws = this.$store.state.auth.powers || {}
      const pws = {}

      const deepReduce = (arr) => {
        return arr.reduce((x, y) => {
          if (y.power === 'dashboard_read' && !pws[y.power]) {
            y.any = 1
            y.path = '/welcome'
          }
          if (pws[y.power] || y.any) { // 先匹配一级导航，有权限再进入该导航的 child 属性
            y.id = id + '' // 增加 id 属性以便给 v-for 设置 key 和 menu-item 的 index
            id++
            y.path && routes.push(y.path) // 生产路由表，用于计算 activeName
            y.child && (y.child = deepReduce(y.child)) // 递归生成子路由
            x.push(y) // 累加有权限的一级导航
          }
          return x
        }, [])
      }

      const finalNav = deepReduce(this.navCfg)

      return { finalNav, routes }
    },
    handleTip () {
      this.$message({
        message: 'Coming soon',
        iconClass: 'mico-lightTip',
        customClass: 'sty1-message',
        duration: 3000,
        offset: 32,
        center: true
      })
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/components/_left_top_frame.scss"></style>
