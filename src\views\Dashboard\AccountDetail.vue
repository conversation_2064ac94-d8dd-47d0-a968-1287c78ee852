<template>
  <div class="pg-account-detail" id="pg-account-detail">
    <div class="sty1-bread-crumb mui-fl-vert mui-shr-0">
      <i class="mcico-dashboard taplight2" @click="$router.back()"></i>
      <span>/</span>
      Applicant Data
    </div>
    <a ref="aLink" style="display: none;"></a>

    <ul class="account">
      <div class="mui-fl-btw">
        <li class="mui-fl-vert">
          <p class="t1">{{ kycText === 2 ? 'zkMe account' : 'Account'}}</p>
          <m-popover placement="bottom" trigger="hover" :popper-class="accountWidth ? 'sty4-popper' : 'sty4-popper sty3-popper-fail'">
            <div>{{ RP && RP.email }}</div>
            <span slot="reference">
              <p class="t2" ref="account">{{ RP && RP.email }}</p>
            </span>
          </m-popover>
        </li>
        <!-- <li class="download mui-fl-central taplight marg-l10">
          <m-popover popper-class="sty3-popper" placement="bottom-end" width="180" trigger="hover">
            <template slot="reference">
              <i class="icon-download mico-information" />
            </template>
            <div class="information_select">
              <div @click="reqState = 'form'">Submit data recovery</div>
              <div @click="reqState = 'list'">Submission records</div>
            </div>
          </m-popover>
        </li> -->
        <!-- <li v-if="level !== 2" class="mui-fl-vert">
          <p class="t3 mui-fl-vert">
            <i class="mico-APIKey"></i>
            Bound wallet:
          </p>
          <p class="t4">{{ RP && RP.walletAddress | formatPubKey }}</p>
        </li> -->
        <li v-if="level === 2 && RP?.boundingDetails[0]?.authorizationInfo && !isNoMint" class="mui-fl-vert">
          <p class="t3 mui-fl-vert">
            <i class="mico-APIKey"></i>
            Authorization Info:
          </p>
          <p class="t4">
            <m-popover placement="bottom" :width="540" trigger="click" popper-class="sty4-popper">
              <div class="mui-fl-end copy mui-fl-vert" @click.stop="copyTxt(RP?.boundingDetails[0]?.authorizationInfo)">
                <div>Copy</div>
                <i class="mico-copy taplight"></i>
              </div>
              <div class="mui-fl-vert authorization-info-view-content">
                <ul>
                  {
                  <li style="margin-left: 6px;"
                    v-for="(value, key) in JSON.parse(RP?.boundingDetails[0]?.authorizationInfo)" :key="key">
                    <template v-if="(typeof value) === 'string'">
                      <p>"{{ key }}": "{{ value }}"</p>
                    </template>
                    <template v-if="(typeof value) === 'object'">
                      <div>
                        <p>"{{ key }}": {</p>
                        <div v-for="(val, k) in value" :key="k">
                          <p style="margin-left: 6px;">"{{ k }}": "{{ val }}"</p>
                        </div>}
                      </div>
                    </template>
                  </li>
                  }
                </ul>
              </div>
              <span style="cursor: pointer;" slot="reference">view</span>
            </m-popover>
          </p>
        </li>
        <li v-if="level === 2 && RP?.boundingDetails[0]?.userIds?.length" class="mui-fl-vert">
          <p class="t3 mui-fl-vert">
            <i class="mico-AppID"></i>
            User ID:
          </p>
          <p class="t4 mui-fl-central">
            {{ RP?.boundingDetails[0]?.userIds.length ? formatPubKey(RP?.boundingDetails[0]?.userIds[0]) : '-' }}
            <i style="margin: 0 4px;" @click.stop="copyTxt(userId)" class="mico-copy boundCopy"></i>
            <m-tooltip v-if="kycText === 2 && RP?.boundingDetails[0]?.userIds.length > 1" class="sty3-tooltip"
              placement="bottom" popper-class="account-detail-tootip">
              <template slot="content">
                <div class="row" v-for="(data, index) of RP?.boundingDetails[0]?.userIds.slice(1)" :key="index">
                  {{ data | formatPubKey }}
                  <i @click.stop="copyTxt(data)" class="mico-copy moreCopy"></i>
                </div>
                <!-- <i @click.stop="copyTxt(RP?.boundingDetails[0]?.walletAddress)" class="mico-copy boundCopy"></i> -->
              </template>
              <div class="more mui-fl-vert">
                <!-- <i @click.stop="copyTxt(userId)" class="mico-copy boundCopy"></i> -->
                <span>More</span>
              </div>
            </m-tooltip>
          </p>
          <!-- <i @click.stop="copyTxt(RP?.boundingDetails[0]?.walletAddress)" class="mico-copy boundCopy"></i> -->
        </li>
        <div class="mui-flex">
          <li class="download mui-fl-central taplight" @click="downloadPdf">
            <i class="mico-download"></i>
          </li>
          <li class="download mui-fl-central taplight marg-l10" :style="{cursor: submitCheckFail !== 6 ? 'no-drop' : 'pointer'}">
            <m-popover :popper-class="submitCheckFail !== 6 ? 'sty3-popper sty3-popper-fail' : ' sty3-popper'" placement="bottom-end" width="180" trigger="hover">
              <template slot="reference">
                <i class="icon-download mico-information" />
              </template>
              <div class="information_select" v-if="!submitCheckFail !== 6 ">
                <div @click="reqState = 'form'">Submit data recovery</div>
                <div @click="reqState = 'list'">Submission records</div>
              </div>
            </m-popover>
          </li>
        </div>
      </div>
      <m-collapse v-if="kycText !== 2 && RP?.boundingDetails.length" class="sty3-collapse"
        :class="[(RP?.boundingDetails.length <= 1 || kycText === 2) && 'isDisabled']">
        <m-collapse-item>
          <template slot="title">
            <div class="mui-fl-btw collapseReport" style="width: 80%;">
              <div v-if="kycText !== 2" class="mui-fl-1 mui-fl-vert"><i class="mcico-network"></i>Network:&nbsp; <span>{{
                RP?.boundingDetails[0]?.network || '-' }}</span></div>
              <div v-if="!kycText" class="mui-fl-1 mui-fl-vert"><i class="mico-AppID"></i>SBT ID:&nbsp; <span>{{
                RP?.boundingDetails[0]?.tokenId || '-' }}</span></div>
              <div class="mui-fl-1 mui-fl-vert" style="text-wrap: nowrap;">
                <i class="mico-APIKey"></i>
                Bound wallet:&nbsp;
                <span :style="{cursor: checkChain(RP?.boundingDetails[0]) ? 'default' : 'pointer' }" @click.stop="toKYT(RP?.boundingDetails[0])">
                  {{ RP?.boundingDetails[0]?.walletAddress ? formatPubKey(RP?.boundingDetails[0]?.walletAddress) : '-' }}
                </span>
                <i @click.stop="copyTxt(RP?.boundingDetails[0]?.walletAddress)" class="mico-copy boundCopy"></i>
              </div>
            </div>
          </template>
          <template v-if="kycText !== 2">
            <div style="width: 80%;" v-for="(data, index) of RP?.boundingDetails" :key="index">
              <div v-if="index" class="mui-fl-btw collapseReport compress">
                <div class="mui-fl-1 mui-fl-vert"><i class="mcico-network"></i>Network:&nbsp; <span>{{ data.network
                }}</span></div>
                <div v-if="!kycText" class="mui-fl-1 mui-fl-vert"><i class="mico-AppID"></i>ZIS ID:&nbsp; <span>{{
                  data.tokenId || '-'
                }}</span></div>
                <div class="mui-fl-1 mui-fl-vert"><i class="mico-APIKey"></i>Bound wallet:&nbsp;
                <span :style="{cursor: checkChain(data) ? 'default' : 'pointer' }" class="toKYT" @click.stop="toKYT(data)">{{
                  data.walletAddress | formatPubKey }}
                  <i @click.stop="copyTxt(data.walletAddress)" class="mico-copy boundCopy"></i>
                  </span>
                </div>
              </div>
            </div>
          </template>
        </m-collapse-item>
      </m-collapse>
    </ul>
    <RequestInformation :state="reqState" :selectAccount="{ 1: [{ zkmeId: zkmeId, kycProgramId: kycProgramId }] }"
      @restState="reqState = ''"></RequestInformation>

    <div class="setting-items">
      <p class="title">Client settings</p>
      <ul class="client-settings sty1 mui-fl-vert">
        <li class="mui-fl-vert">
          <i class="a-icon a-icon-checked"></i>
          <p>Minimum age: {{ RP && RP.minimumAge }}</p>
        </li>
        <li class="mui-fl-vert" v-if="RP?.availableCitizenship">
          <i class="a-icon a-icon-checked"></i>
          <p>Available citizenship</p>
        </li>
        <li class="mui-fl-vert" v-if="RP?.availableCountries">
          <i class="a-icon a-icon-checked"></i>
          <p>Available countries/regions</p>
        </li>
        <li class="mui-fl-vert" v-if="RP?.amlChecking">
          <i class="a-icon a-icon-checked"></i>
          <p>AML Checking</p>
        </li>
        <li class="mui-fl-vert" v-if="RP?.availableUniqueness">
          <i class="a-icon a-icon-checked"></i>
          <p>Uniqueness check</p>
        </li>
      </ul>
    </div>

    <div class="setting-items">
      <p class="title">Regulations</p>
      <ul class="regulations sty1 mui-fl-vert mui-fl-btw">
        <li v-for="(item, index) in regulations" :key="index">
          <p class="mui-fl-vert regulationsIcon" v-if="!item.checking">
            <m-tooltip placement="bottom" :popper-class="item.status?.toString() === 'false' && item.name === 'Supported document' ? 'showAuthDocument account-detail-tootip' : 'account-detail-tootip authDocument'">
              <template slot="content">
                <div class="row">
                  The current document submitted by the user does not meet the required criteria, please be patient while waiting for the user to re-upload the document.
                </div>
              </template>
              <p class="mui-fl-vert regulationsIcon" v-if="!item.checking">
                <i v-if="item.status?.toString() === 'true'" class="a-icon mcico-success2"></i>
                <i v-else-if="item.status?.toString() === 'false' && item.name === 'Supported document'" class="a-icon mcico-supportedTip"></i>
                <i v-else-if="item.status?.toString() === 'false'" class="a-icon a-icon-error"></i>
                <i v-else-if="item.status?.toString() === 'null'" class="a-icon mcico-wait"></i>
                {{ item.name }}
              </p>
            </m-tooltip>
          </p>
        </li>
      </ul>
    </div>

    <div class="setting-items">
      <p class="title">Key findings</p>
      <ul class="key-findings sty2 mui-fl-wrap">
        <li class="mui-shr-0" v-for="(item, index) of keyfindings" :key="index">
          <p class="t1" v-if="!item.status">{{ item.title }}</p>
          <div v-if="!item.status" class="mui-fl-wrap key-findings-items" :class="[item?.style && 'mui-fl-col key-findings-items-error']">
            <div v-for="(itm, idx) of item.list" :key="idx">
              <div class="mui-fl-vert">
                <i v-if="(typeof itm.status !== 'boolean')" class="a-icon mcico-wait" />
                <i v-else class="a-icon" :class="[!itm.status ? 'a-icon-error' : 'a-icon-success']" />
                <span>{{ itm.name }}</span>
              </div>
              <ul class="mui-fl-col exceptionData" v-if="itm.data?.length && !itm.status">
                <li v-for="(data, index) of itm.data" :key="index">
                  <div class="title">
                    {{ data.title }}
                  </div>
                  <div class="detail">
                    {{ data.detail }}
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </li>
      </ul>
    </div>

    <div class="setting-items" id="events">
      <p class="title">Events</p>
      <ul class="sty1 events-items">
        <li v-for="(item, index) of RP && eventList" :key="index" :class="{
          'eventTime-bg': !item.eventTime && index <= lastNoTime,
          }">
          <div class="mui-fl-vert mui-fl-btw">
            <div>
              <p class="event-name">{{ item.eventCode }}</p>
            </div>
            <div v-if="item.eventTime" class="event-applicant mui-fl-vert">
              <p>Last Updated: &nbsp;</p>
              <p class="value">{{ item.eventTime | formatGMTDate }} (GMT+8)</p>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { formatPubKey } from '@/utils/filters'
import axios from 'axios'
import RequestInformation from '@/components/request-information/RequestInformation.vue'

export default {
  components: { RequestInformation },

  data () {
    return {
      lastNoTime: -1,
      formatPubKey: formatPubKey,
      regulations: [],
      keyfindings: [],
      eventList: [],
      blockchainId: '',
      userWalletAddress: '',
      popBindingId: '',
      zkmeId: '',
      userId: '',
      kycProgramId: '',
      RP: null,
      reqState: '',
      keyComArr: ['Sanctions', 'Adverse media', 'Politically exposed persons (PEP)'],
      downloadLink: '',
      submitCheckFail: 1,
      accountWidth: false,
      userStep: [
        'KYC Passed', 'OnChain Minted', 'SBT Minted',
        'ZKP Generated', 'Liveness Checked', 'OCR Passed', 'Verification Started']
    }
  },
  computed: {
    ...mapState({
      level: ({ auth }) => auth.user && auth.user.level,
      isNoMint: ({ auth }) => auth.user && auth.user.isNoMint
    }),
    kycText () {
      return (localStorage.getItem('zkmeAdminUser') && JSON.parse(localStorage.getItem('zkmeAdminUser')).level) || this.$store.state.auth.user.level
      // switch (Number(level)) {
      //   case 0:
      //     levelTitle = 'Advance'
      //     break
      //   case 1:
      //     levelTitle = 'Standard'
      //     break
      //   case 2:
      //     levelTitle = 'Simple'
      //     break
      // }
      // return level
    }
  },
  created () {
    // this.submitCheckFail = this.$route.query.status !== 'AUTHORIZED'
    this.blockchainId = this.$route.query.blockchainId
    this.userWalletAddress = this.$route.query.userWalletAddress
    this.zkmeId = this.$route.query.zkmeId
    this.userId = this.$route.query.userId
    this.kycProgramId = this.$route.query.kycProgramId
    this.account = this.$route.query.account
    this.popBindingId = this.$route.query.popBindingId
    this.getDetail()
  },
  methods: {
    checkChain (row) {
      if (row.network.startsWith('sei') || row.network.startsWith('neutron') || row.network.startsWith('Aptos')) {
        return true
      } else {
        return false
      }
    },
    findLastNoTimeIndex () {
      for (let i = 0; i < this.eventList.length; i++) {
        if (this.eventList[i].eventTime) {
          return i
        }
      }
      return -1
    },
    async getDetail () {
      const rp = await this.$api.request('dashboard.getDetail', {
        kycProgramId: this.kycProgramId,
        zkmeId: this.zkmeId
      })
      if (rp.code === ********) {
        this.RP = rp.data
        this.submitCheckFail = rp.data.dashBoardKycStatus
        if (this.level !== 0) {
          this.userStep = this.userStep.filter(status => status !== 'OnChain Minted')
        }
        this.userStep.forEach(v => {
          this.eventList.push({
            eventCode: v,
            eventTime: rp.data.eventList.find(item => item.eventTitle === v)?.eventTime
          })
        })
        if (this.isNoMint) {
          this.eventList = this.eventList.filter(item => item.eventCode !== 'OnChain Minted' && item.eventCode !== 'SBT Minted')
        }
        this.lastNoTime = this.findLastNoTimeIndex()
        this.regulations.push(
          {
            name: 'Supported document',
            status: typeof rp.data.supportedDocument !== 'boolean' ? 'null' : rp.data.supportedDocument
          },
          {
            name: 'Facial check',
            status: typeof rp.data.facialCheck !== 'boolean' ? 'null' : rp.data.facialCheck
          },
          {
            name: 'Citizenship',
            status: typeof rp.data.regulationsCitizenship !== 'boolean' ? 'null' : rp.data.regulationsCitizenship
          },
          {
            name: 'Age',
            status: typeof rp.data.regulationsAge !== 'boolean' ? 'null' : rp.data.regulationsAge
          },
          {
            name: 'Uniqueness check',
            status: typeof rp.data.isDocumentUniqueness !== 'boolean' ? 'null' : rp.data.isDocumentUniqueness,
            checking: !this.RP?.availableUniqueness
          },
          {
            name: 'Risk level: unknown',
            status: ([6, -1].includes(this.submitCheckFail)) ? true : 'null',
            checking: !this.RP?.amlChecking
          },
          {
            name: 'Location',
            status: typeof rp.data.location !== 'boolean' ? 'null' : rp.data.location,
            checking: !this.RP?.availableCountries
          }
        )
        this.keyfindings.push(
          {
            title: 'Document review check',
            list: [
              {
                name: 'Images quality',
                status: rp.data.imagesQuality
              },
              {
                name: 'Security features',
                status: rp.data.securityFeatures
              },
              {
                name: 'Template',
                status: rp.data.template
              },
              {
                name: 'Fonts',
                status: rp.data.fonts
              }
            ]
          },
          {
            title: 'Data validation',
            list: [
              {
                name: 'Document has not been recorded as expired, lost, stolen or compromised',
                status: rp.data.docEffective
              },
              {
                name: 'Document has not been found on our blocklist',
                status: rp.data.docAllowed
              },
              {
                name: 'Data comparison',
                status: rp.data.docComparison
              }
            ]
          },
          {
            title: 'Facial check',
            list: [
              {
                name: 'Face detection',
                status: rp.data.faceDetection
              },
              {
                name: 'Liveness check',
                status: rp.data.livenessCheck
              }
            ]
          },
          {
            title: 'Compromised persons/organizations',
            style: 'grid',
            status: !this.RP?.amlChecking,
            list: [
              {
                name: 'Sanctions, Warnings, and Fitness & Probity',
                status: rp.data.sanctions,
                data: rp.data.sanctionsDetail
              },
              {
                name: 'Politically exposed persons (PEP)',
                status: rp.data.pep,
                data: rp.data.pepDetail
              },
              {
                name: 'Adverse media',
                status: rp.data.adverseMedia,
                data: rp.data.adverseMediaDetail
              }
            ]
          })
        this.$nextTick(() => {
          this.accountWidth = this.$refs.account.clientWidth === 250
        })
      }
    },
    async copyTxt (text) {
      this.isCopyTxt = true
      if (navigator.clipboard && window.isSecureContext) {
        try {
          await navigator.clipboard.writeText(text)
          this.$message({
            message: 'Copied',
            iconClass: 'mico-lightTip',
            customClass: 'sty1-message',
            duration: 3000,
            offset: 32,
            center: true
          })
          return
        } catch (err) {
          console.error(err)
          return false
        }
      } else {
        const textarea = document.createElement('textarea')
        textarea.textContent = text
        textarea.style.position = 'fixed'
        document.body.appendChild(textarea)
        textarea.select()
        try {
          document.execCommand('copy')
          this.$message({
            message: 'Copied',
            iconClass: 'mico-lightTip',
            customClass: 'sty1-message',
            duration: 3000,
            offset: 32,
            center: true
          })
          return
        } catch (ex) {
          this.$message({
            message: 'Copy to clipboard failed.',
            iconClass: 'mico-lightTip',
            customClass: 'sty1-message',
            duration: 3000,
            offset: 32,
            center: true
          })
          return false
        } finally {
          document.body.removeChild(textarea)
        }
      }
    },
    async downloadPdf () {
      if (![-1, 6].includes(this.submitCheckFail)) {
        this.$message({
          message: 'The current user has not completed the KYC process.',
          iconClass: 'mcico-warn2',
          customClass: 'sty4-message supportDocuments',
          duration: 3000,
          offset: 32,
          center: true
        })
        return
      }
      const form = {
        kycProgramId: this.kycProgramId,
        blockchainId: this.RP.boundingDetails[0]?.blockchainId,
        userWalletAddress: this.userWalletAddress,
        zkmeId: this.zkmeId,
        account: this.account,
        popBindingId: this.popBindingId
      }
      let simpleForm
      if (this.kycText === 2) {
        delete form.userWalletAddress
        simpleForm = Object.assign(form, { account: this.RP.boundingDetails[0]?.userIds[0] })
      }
      const rp = await this.$api.request('dashboard.downloadKycReport', simpleForm || form)
      if (rp.code === ********) {
        const blobFile = await axios.get(rp.data.reportUrl, {
          responseType: 'blob'
        })
        const type = 'application/pdf'
        const blob = new Blob([blobFile.data], { type })
        const url = URL.createObjectURL(blob)
        this.$refs.aLink.href = url
        this.$refs.aLink.download = this.kycProgramId + '_' + 'KYCreport' + '_' + this.RP.email + '.pdf'
        this.$refs.aLink.click()
      }
    },
    toKYT (row) {
      if (this.checkChain(row)) {
        return
      }
      this.$router.push({
        path: '/dashboard/account-kyt',
        query: {
          blockchainId: row.blockchainId,
          userWalletAddress: row.walletAddress,
          zkmeId: this.zkmeId,
          kycProgramId: this.kycProgramId
        }
      })
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/dashboard/_account-detail.scss" scoped></style>
