/**
 * https://digitalpulse.larksuite.com/wiki/RjEVwANhCiZaAgkvk0zue3vNsWL
 */

export const mchInfo = () => {
  return {
    method: 'post',
    url: '/api/mchInfo/get',
    data: {}
  }
}

/**
 * https://digitalpulse.larksuite.com/wiki/RjEVwANhCiZaAgkvk0zue3vNsWL
 */
export const generate = () => {
  return {
    method: 'post',
    url: '/api/apikey/generate',
    data: {}
  }
}
/**
 * https://digitalpulse.larksuite.com/wiki/RjEVwANhCiZaAgkvk0zue3vNsWL
 */
export const updateUrls = urls => {
  return {
    method: 'post',
    url: '/api/mchInfo/updateUrls',
    data: {
      urls
    }
  }
}
/**
 * https://digitalpulse.larksuite.com/wiki/RjEVwANhCiZaAgkvk0zue3vNsWL
 */
export const updateMchConfig = ({ mchWallets, publicKey, randDigits }) => {
  return {
    method: 'post',
    url: '/api/mchInfo/updateMchConfig',
    data: {
      publicKey,
      randDigits
    }
  }
}
