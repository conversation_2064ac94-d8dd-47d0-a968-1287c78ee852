.pg-login {
  position: relative;
  width: 100%;
  height: 100vh;
  background: url('~@/assets/img/login-bg.png') no-repeat;
  background-size: cover;
  background-position: 50% 50%;
  position: relative;
}
.pg-login::after {
	content: '';
	width: 100%;
	height: 100%;
  background: linear-gradient(194deg, #E2F9F9 12.26%, #F0FCF8 99.84%);
	position: absolute;
	left: 0;
	top: 0;
	z-index: -1;
}

.login-left {
  position: relative;
  .c {
    box-sizing: border-box;
    padding: 64px 54px 62px;
    width: 450px;
    min-height: 546px;
    background-color: rgba(255, 255, 255, 0.55);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    border: 1px solid #fff;
    &.creat-account {
      height: 730px;
      min-height: 730px;
    }
    .t1, .t3 {
      font-weight: 700;
      font-size: 20px;
      line-height: 1.2;
      color: #002E33;
      margin-bottom: 24px;
    }
    .t1 {
      margin-bottom: 40px;
    }
    .t2 {
      font-size: 20px;
      line-height: 36px;
      margin-top: 16px;
    }
    .t3 {
      margin-bottom: 8px;
    }
    .t4, .t5, .t6 {
      color: #738C8F;
      font-weight: 400;
      font-size: 10px;
      line-height: 14px;
    }
    .t4 {
      font-size: 12px;
      line-height: 16px;
      width: 280px;
    }
    .protocol {
      width: auto;
      padding: 12px;
      border-radius: 12px;
      background-color: #fff;
      position: absolute;
      bottom: 50px;
      color: rgba(0, 85, 99, 0.8);
      // margin-top: 32px;
      // margin-bottom: 16px;
    }
    .t5 {
      color:#005563;
      text-decoration: underline;
      cursor: pointer;
    }
    .t6 {
      font-size: 12px;
      line-height: 20px;
      margin-bottom: 40px;
    }
    .btn {
      margin-bottom: 0;
    }
  }
}
.toplogo {
  position: absolute;
  top: 30px;
  left: 40px;
  .mcico-colorLogo {
    font-size: 30px;
  }
  .logo {
    width: 68px;
    height: 22px;
    background: url('~@/assets/img/logo4.png') no-repeat;
    background-size: 100%;
    margin: 0 12px 0 9px;
  }
  .top-logo-name {
    color: #005563;
    background-color: #8EDEE3;
    border-radius: 6px 6px 6px 0;
    font-weight: 700;
    font-size: 12px;
    line-height: 20px;
    padding: 0 16px;
    margin-top: 4px;
  }
}
.retrieve {
  font-weight: 400;
  font-size: 12px;
  line-height: 400;
  color: #005563;
}

// .login-right {
//   width: 100%;
//   height: 100vh;
//   background: url('~@/assets/img/login-bg.png') no-repeat;
//   background-size: cover;
//   background-position: 50% 50%;
// }

.mico-clock2 {
  font-size: 18px;
  margin-right: 7px;
  color: #33585C;
}

.timeClear {
  color: #33585C;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  margin-bottom: 32px;
}