.bg {
    overflow: hidden;
  }
  /* .ndContain {
    scrollbar-color: transparent transparent;
    scrollbar-track-color: transparent;
    -ms-scrollbar-track-color: transparent;
  }
  .ndContain::-webkit-scrollbar {
    width: 0;
    height: 0;
    background-color: transparent;
  }
  .ndContain::-webkit-scrollbar-thumb {
    background-color: transparent;
    background-clip: content-box;
    border: 0;
  }
  .ndContain::-webkit-scrollbar-corner {
    display: none;
  } */
  .step-box {
    width: max-content;
    padding: 11px 16px;
    border-radius: 42px;
    background: rgba(var(--color-background-14), 0.05);
    margin-top: 16px;
  }
  .icon {
    width: 18px;
    height: 18px;
    background: var(--vt-c-primary-1) !important;
    border-radius: 4px;
    font-size: 12px;
    color: var(--vt-c-white-other);
  }
  .c1 {
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: rgba(var(--vt-c-primary-1-rgb), 1);
    margin-left: 8px;
  }
  .t1 {
    font-weight: 700;
    font-size: 22px;
    line-height: 32px;
    color: rgb(var(--color-text-6));
    margin-top: 16px;
  }
  .c2 {
    margin-top: 8px;
    font-size: 14px;
    line-height: 20px;
    color: var(--color-text-7);
  }
  .country-box {
    margin-top: 36px;
  }
  
  .country-box span {
    width: calc(100% - 56px);
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .t2 {
    font-size: 14px;
    line-height: 140%;
    color: rgb(var(--color-text-6));
    margin-bottom: 8px;
  }
  .select {
    width: 100%;
    height: 48px;
    border: 1px solid rgba(var(--color-border-3), 0.2);
    padding: 12px 20px 12px 52px;
    box-sizing: border-box;
    border-radius: 40px;
    appearance: none;
    font-size: 14px;
    line-height: 20px;
    color: rgb(var(--color-text-6));
    pointer-events: all;
    background: var(--color-background);
  }
  .select-box {
    position: relative;
  }

  .popup-select {
    width: 100%;
    /* max-width: 327px; */
    height: 48px;
    padding: 14px 22px;
    box-sizing: border-box;
    border: 1.5px solid var(--vt-c-primary-2);
    border-radius: 40px;
  }
  .country {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
  .select:focus-visible {
    outline: none;
  }
  .no-country {
    width: 24px;
    height: 24px;
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
  }
  .placeholder {
    opacity: 0.4;
  }
  .option {
    max-width: 100%;
  }
  .option-logo {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 12px;
  }
  /* .btn {
    width: 255px;
    height: 48px;
    border: none;
    background: var(--color-text-4);
    color: var(--vt-c-white-other);
    font-weight: 500;
    font-size: 16px;
    line-height: 48px;
    text-align: center;
    border-radius: 48px;
  }
  .btn:disabled {
    opacity: 0.2;
  } */
  
  .select-documents {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: var(--color-background-mask);
    z-index: 10;
    transition: .3s background-color, 0s visibility .3s;
    visibility: hidden;
  }
  .select-documents.active {
    transition-delay: 0s;
    background: rgba(var(--color-background-mask), .5);
    visibility: visible;
  }
  .popup-box {
    width: 100%;
    padding: 24px 24px 16px 24px;
    box-sizing: border-box;
    background: var(--color-background);
    border-radius: 24px 24px 0px 0px;
    position: fixed;
    left: 0;
    bottom: 0;
  }
  
  .slide-enter-active {
    animation: top-change 0.3s;
  }
  .slide-leave-active {
    animation: top-change 0.3s reverse;
  }
  
  .bottom-popup-act {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 24px;
  }
  .bottom-popup-act::before {
    content: "";
    position: absolute;
    top: 3px;
    left: 50%;
    margin-left: -17px;
    width: 35px;
    height: 3px;
    border-radius: 2px;
    background: var(--color-background-13);
  }
  
  .t3 {
    font-weight: 700;
    font-size: 18px;
    line-height: 25px;
    color: rgb(var(--color-text-6));
    text-align: center;
    margin-bottom: 28px;
  }
  .documents-item {
    width: 100%;
    padding: 16px;
    border-radius: 12px;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    z-index: 1;
  }
  .documents-item:not(:first-child) {
    margin-top: 16px;
  }
  .documents-item::after {
    content: "";
    width: 151px;
    height: 106px;
    position: absolute;
    top: 0;
    right: 0;
    z-index: -1;
  }
  
  .documents-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    font-size: 20px;
    color: var(--vt-c-white-other);
    margin-bottom: 4px;
  }
  .popico-globe-alt {
    background: var(--vt-c-progressing);
  }
  .popico-square-user {
    background: var(--vt-c-success);
  }
  .popico-car-side {
    background: var(--vt-c-toast);
  }
  .t4 {
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    color: rgb(var(--color-text-6));
    margin-bottom: 2px;
    z-index: 2;
  }
  .c3 {
    font-size: 12px;
    line-height: 16px;
    color: rgb(var(--color-text-6));
    opacity: 0.4;
    z-index: 2;
  }
  @keyframes top-change {
    0% {
      transform: translateY(100%);
      -webkit-transform: translateY(100%);
    }
    100% {
      transform: translateY(0);
      -webkit-transform: translateY(0);
    }
  }
  