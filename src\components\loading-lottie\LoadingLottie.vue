<template>
    <div>
        <p id="ndLottie" :style="{ height: size }"></p>
    </div>
</template>
<script>
import lottie from 'lottie-web'
import loadingWhiteJson from '@/assets/animation-data/loading.json'
export default {
  name: 'LoadingLottie',
  props: {
    // loading颜色
    color: {
      required: false,
      default: 'rgb(0, 85, 99)',
      type: String
    },
    // loading大小
    size: {
      required: false,
      default: '40px',
      type: String
    }
  },
  mounted () {
    this.getLottie()
  },
  methods: {
    async getLottie () {
      lottie.destroy()
      try {
        const ndLottie = document.getElementById('ndLottie')
        const anim = lottie.loadAnimation({
          container: ndLottie,
          renderer: 'svg',
          loop: true,
          name: 'loading',
          autoplay: true,
          animationData: loadingWhiteJson
        })
        anim.play()

        const ndLottieEle = document.querySelector('#ndLottie')
        const ndPath = ndLottieEle.getElementsByTagName('path')
        if (ndPath) {
          for (var i = 0; i < ndPath.length; i++) {
            ndPath[i].style.fill = this.color
          }
        }
      } catch (err) {
        console.log(err)
      }
    }
  }
}
</script>
