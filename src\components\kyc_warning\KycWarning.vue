<template>
  <m-dialog :visible.sync="warningtip" class="sty2-dialog dialog" :show-close="false" :close-on-click-modal="false" :close-on-press-escape="false">
    <div class="leavetip_title"><i class="mico-warning"></i>
      <span v-if="model === 'Delete'">Confirm to delete the program?</span>
      <span v-else-if="model === 'Cancel'">Confirm to cancel the service?</span>
      <span v-else-if="model === 'Apply'">Confirm to apply the service?</span>
      <span v-else>Warning</span>
    </div>
    <div class="leavetip_text" v-if="model === 'Delete'">It cannot be recovered once deleted.</div>
    <div class="leavetip_text" v-else-if="model === 'Cancel'">Your users will not be able to KYC until a new program is applied again.</div>
    <div class="leavetip_text" v-else-if="model === 'Apply'">The program cannot be modified after applying, please check it carefully.</div>
    <div class="leavetip_text" v-else>There is unsaved solution. It will be lost if leaving the page.</div>
    <div class="dialog_button warin_button dialog_recorded_button mui-fl-end" v-if="model === 'Delete' || model === 'Cancel'">
      <m-button class="kycSave" @click="$emit('Leave', 'Back')">Back</m-button>
      <m-button :class="['kycCancel', model]" @click="$emit('Leave', 'Leave')">{{model === 'Delete' ? 'Delete' : 'Cancel service'}}</m-button>
    </div>
    <div class="dialog_button warin_button mui-fl-end" v-else>
      <m-button class="kycSave" @click="$emit('Leave', 'Back')">{{model === 'Stay' ? 'Leave' : 'Back'}}</m-button>
      <m-button class="kycCancel" @click="$emit('Leave', 'Leave')" :class="model">{{ model === 'Apply' ? 'Confirm' : model }}</m-button>
    </div>
  </m-dialog>
</template>

<script>
export default {
  props: {
    warningtip: {
      type: Boolean,
      default: false
    },
    model: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.leavetip_text {
  width: 340px;
  line-height: 18px;
  color: #002E33;
}
.leavetip_title {
  font-weight: 500;
  line-height: 22px;
  font-size: 18px;
  margin-bottom: 24px;
  color: #002E33;
}
.mico-warning {
  font-size: 20px;
  color: #EE6969;
  margin-right: 8px;
}
.dialog_button {
  .kycSave, .kycCancel {
    width: 109px;
    height: 36px;
    background-color: #F7F7F7;
    color: #33585C;
    border-radius: 26px;
    border: none;
    margin-top: 24px;
  }
  .kycCancel {
    background-color: #A9E1D3;
    color: #002E33;
    margin-left: 12px;
    margin: 24px 0 24px 12px;
  }
}
.warin_button {
  .kycSave, .kycCancel {
    width: auto !important;
  }
  .kycSave {
    padding: 0 36px !important;
  }
  .kycCancel {
    margin: 24px 0 24px 12px !important;
  }
  .Delete {
    padding: 0 30px;
  }
  .Apply, .Stay {
    padding: 0 36px;
  }
}
.dialog_recorded_button {
  .kycCancel {
    background: rgba(238, 105, 105, 0.06) !important;
    color: #EE6969  !important;
  }
}

</style>
