(self["webpackChunkzkme_web"]=self["webpackChunkzkme_web"]||[]).push([[163],{32163:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return Et}});var s=function(){var e=this,t=e._self._c;return t("div",{class:[(1===e.mode||0!==e.mode&&"dark"===e.themeMode)&&"modeDark","previewMode"]},[e._m(0),t("div",{staticClass:"sty2-cell ui_bg mui-fl-btw"},[t("div",{staticClass:"mui-fl-col"},[t("div",{staticClass:"ui_selector ui_mgt_14"},[t("div",{staticClass:"ui_text ui_mgb_16"},[e._v(" Set your theme color ")]),t("div",{staticClass:"mui-fl-wrap mui-fl-start",staticStyle:{"max-width":"396px"}},[e._l(0===e.mode||"dark"!==e.themeMode?e.colorsOp:e.darkColorsOp,(function(r,s){return t("div",{key:s,staticClass:"ui_color",on:{click:function(t){return e.selectColor(r,s)}}},[t("div",{staticClass:"ui_color_hand",style:{background:r[0]}}),t("div",{staticClass:"mui-flex"},[t("div",{staticClass:"ui_color_Lfoot",style:{background:r[1]}}),t("div",{staticClass:"ui_color_Rfoot",style:{background:r[2]}})]),t("i",{directives:[{name:"show",rawName:"v-show",value:e.indexColor===s,expression:"indexColor === index"}],staticClass:"mcico-ui-success"})])})),t("div",{staticClass:"ui_color ui_color_design mui-fl-central"},[t("i",{directives:[{name:"show",rawName:"v-show",value:"custom"===e.indexColor,expression:"indexColor === 'custom'"}],staticClass:"mcico-ui-success"}),t("i",{staticClass:"mico-drawing"}),t("div",{staticClass:"color_picker"},[t("m-color-picker",{ref:"colorPicker",attrs:{"popper-class":1===e.mode||0!==e.mode&&"dark"===e.themeMode?"sty2-ColorPicker":"sty1-ColorPicker","show-alpha":!1,predefine:e.predefineColors},on:{"active-change":e.activeChange},model:{value:e.colorPicker,callback:function(t){e.colorPicker=t},expression:"colorPicker"}})],1)])],2)]),t("div",{staticClass:"ui_selector ui_mgt_14 mui-fl-col ui_selector_collapse"},[t("m-collapse",{ref:"collapse",class:["sty1-collapse",(1===e.mode||0!==e.mode&&"dark"===e.themeMode)&&"sty2-collapse"]},[t("m-collapse-item",{attrs:{title:"Advance setting"}},[t("template",{slot:"title"},[t("div",{staticClass:"ui_text"},[e._v(" Advance setting "),t("m-tooltip",{attrs:{"popper-class":1===e.mode||0!==e.mode&&"dark"===e.themeMode?"sty2-tooltip":"sty1-tooltip",effect:"light",placement:"top"}},[t("template",{slot:"content"},[e._v(" If you need more advanced customization, you can "),t("br"),e._v(" edit CSS code to achieve more advanced UI"),t("br"),e._v(" configurations. ")]),t("i",{staticClass:"mico-help-linear"})],2)],1)]),t("textarea",{directives:[{name:"model",rawName:"v-model",value:e.cssTextarea,expression:"cssTextarea"}],staticClass:"ui_textarea",attrs:{placeholder:e.textareaPlaceholder,cols:"30",rows:"10"},domProps:{value:e.cssTextarea},on:{input:function(t){t.target.composing||(e.cssTextarea=t.target.value)}}})],2)],1)],1)]),t("div",{class:[(1===e.mode||0!==e.mode&&"dark"===e.themeMode)&&"dark","preview_con","mui-fl-col","mui-fl-central","mui-fl-1"]},[t("div",{staticClass:"mui-fl-vert ui_mode"},[t("div",{staticClass:"mode_choose mui-fl-btw mui-fl-vert"},[t("div",{class:["mui-fl-1","mui-fl-central","web"===e.previewMode&&"select"],on:{click:function(t){return e.selectPreviewMode("web")}}},[t("i",{staticClass:"mico-monitor-alt"}),e._v("PC")]),t("div",{class:["mui-fl-1","mui-fl-central","mobile"===e.previewMode&&"select"],on:{click:function(t){return e.selectPreviewMode("mobile")}}},[t("i",{staticClass:"mico-mobile-alt"}),e._v("Mobile")])]),1===e.mode||0!==e.mode&&"dark"===e.themeMode?t("i",{staticClass:"mcico-dark",on:{click:function(t){return e.selectMode(0)}}}):t("i",{staticClass:"mcico-light",on:{click:function(t){return e.selectMode(1)}}})]),t("div",{staticClass:"component_preview_con mui-fl-vert"},[t("button",{staticClass:"preview_btn mui-shr-0",class:e.preivewStep<=1&&"choose_color",on:{click:e.previousStep}},[t("i",{staticClass:"mico-fold chevron-left"})]),t("div",{staticClass:"preview_content"},[t("div",{staticClass:"cent-wrap mui-fl-central"},[t("div",{staticClass:"mui-fl-col mui-fl-btw"},[t("PreviewContent",{attrs:{cssTextarea:e.cssTextarea,themeMode:1===e.mode||0!==e.mode&&"dark"===e.themeMode?"dark":"light",step:e.preivewStep,previewMode:e.previewMode,propsColorsOp:e.selectColorData}}),e.preivewStep>=3&&"web"===e.previewMode?t("div",{staticClass:"custom-tooltip",class:1===e.mode||0!==e.mode&&"dark"===e.themeMode?"dark":"light",staticStyle:{position:"relative"}},[e._m(1)]):e._e()],1)])]),t("button",{staticClass:"preview_btn mui-shr-0",class:e.preivewStep>=6&&"choose_color",on:{click:e.nextStep}},[t("i",{staticClass:"mico-fold chevron-right"})])])])]),t("m-button",{class:["save","save_configure",!e.indexColor.toString()&&!e.cssTextarea&&"choose_color"],on:{click:e.configure}},[e._v("Save and configure")]),e.keepColor?t("m-button",{staticClass:"save save_configure reset",on:{click:e.reset}},[e._v("Reset")]):e._e(),t("m-dialog",{staticClass:"sty2-dialog dialog",attrs:{visible:e.warningtip,"close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(t){e.warningtip=t}}},[t("div",{staticClass:"leavetip_title"},[t("i",{staticClass:"mico-warning"}),t("span",[e._v("Confirm to reset the UI Design？")])]),t("div",{staticClass:"leavetip_text"},[e._v("Resetting will restore default colors, and the current configuration will not be saved. ")]),t("div",{staticClass:"dialog_button warin_button mui-fl-end"},[t("m-button",{staticClass:"kycSave",on:{click:function(t){e.warningtip=!1}}},[e._v("Cancel")]),t("m-button",{staticClass:"kycCancel",on:{click:function(t){e.confirmReset=!0,e.reset()}}},[e._v("Reset")])],1)])],1)},i=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"pg-title1 mui-fl-vert"},[t("i",{staticClass:"mcico-active-integration"}),e._v(" 🎨 UI Design ")])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"tooltip-content",attrs:{"aria-hidden":"false"}},[e._v(" This step requires users to "),t("br"),e._v("perform on their mobile"),t("br"),e._v(" device. "),t("div",{staticClass:"tooltip-arrow"})])}];r(44114),r(98992),r(3949),r(81454);function n(e,t){o(e)&&(e="100%");var r=a(e);return e=360===t?e:Math.min(t,Math.max(0,parseFloat(e))),r&&(e=parseInt(String(e*t),10)/100),Math.abs(e-t)<1e-6?1:(e=360===t?(e<0?e%t+t:e%t)/parseFloat(String(t)):e%t/parseFloat(String(t)),e)}function o(e){return"string"===typeof e&&-1!==e.indexOf(".")&&1===parseFloat(e)}function a(e){return"string"===typeof e&&-1!==e.indexOf("%")}function l(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function c(e){return e<=1?"".concat(100*Number(e),"%"):e}function u(e){return 1===e.length?"0"+e:String(e)}function h(e,t,r){return{r:255*n(e,255),g:255*n(t,255),b:255*n(r,255)}}function p(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+6*r*(t-e):r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function f(e,t,r){var s,i,o;if(e=n(e,360),t=n(t,100),r=n(r,100),0===t)i=r,o=r,s=r;else{var a=r<.5?r*(1+t):r+t-r*t,l=2*r-a;s=p(l,a,e+1/3),i=p(l,a,e),o=p(l,a,e-1/3)}return{r:255*s,g:255*i,b:255*o}}function d(e,t,r){e=n(e,255),t=n(t,255),r=n(r,255);var s=Math.max(e,t,r),i=Math.min(e,t,r),o=0,a=s,l=s-i,c=0===s?0:l/s;if(s===i)o=0;else{switch(s){case e:o=(t-r)/l+(t<r?6:0);break;case t:o=(r-e)/l+2;break;case r:o=(e-t)/l+4;break;default:break}o/=6}return{h:o,s:c,v:a}}function m(e,t,r){e=6*n(e,360),t=n(t,100),r=n(r,100);var s=Math.floor(e),i=e-s,o=r*(1-t),a=r*(1-i*t),l=r*(1-(1-i)*t),c=s%6,u=[r,a,o,o,l,r][c],h=[l,r,r,a,o,o][c],p=[o,o,l,r,r,a][c];return{r:255*u,g:255*h,b:255*p}}function g(e,t,r,s){var i=[u(Math.round(e).toString(16)),u(Math.round(t).toString(16)),u(Math.round(r).toString(16))];return s&&i[0].startsWith(i[0].charAt(1))&&i[1].startsWith(i[1].charAt(1))&&i[2].startsWith(i[2].charAt(1))?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function v(e){return y(e)/255}function y(e){return parseInt(e,16)}var w={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function b(e){var t={r:0,g:0,b:0},r=1,s=null,i=null,n=null,o=!1,a=!1;return"string"===typeof e&&(e=E(e)),"object"===typeof e&&(A(e.r)&&A(e.g)&&A(e.b)?(t=h(e.r,e.g,e.b),o=!0,a="%"===String(e.r).substr(-1)?"prgb":"rgb"):A(e.h)&&A(e.s)&&A(e.v)?(s=c(e.s),i=c(e.v),t=m(e.h,s,i),o=!0,a="hsv"):A(e.h)&&A(e.s)&&A(e.l)&&(s=c(e.s),n=c(e.l),t=f(e.h,s,n),o=!0,a="hsl"),Object.prototype.hasOwnProperty.call(e,"a")&&(r=e.a)),r=l(r),{ok:o,format:e.format||a,r:Math.min(255,Math.max(t.r,0)),g:Math.min(255,Math.max(t.g,0)),b:Math.min(255,Math.max(t.b,0)),a:r}}var C="[-\\+]?\\d+%?",k="[-\\+]?\\d*\\.\\d+%?",_="(?:".concat(k,")|(?:").concat(C,")"),S="[\\s|\\(]+(".concat(_,")[,|\\s]+(").concat(_,")[,|\\s]+(").concat(_,")\\s*\\)?"),x="[\\s|\\(]+(".concat(_,")[,|\\s]+(").concat(_,")[,|\\s]+(").concat(_,")[,|\\s]+(").concat(_,")\\s*\\)?"),O={CSS_UNIT:new RegExp(_),rgb:new RegExp("rgb"+S),rgba:new RegExp("rgba"+x),hsl:new RegExp("hsl"+S),hsla:new RegExp("hsla"+x),hsv:new RegExp("hsv"+S),hsva:new RegExp("hsva"+x),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function E(e){if(e=e.trim().toLowerCase(),0===e.length)return!1;var t=!1;if(w[e])e=w[e],t=!0;else if("transparent"===e)return{r:0,g:0,b:0,a:0,format:"name"};var r=O.rgb.exec(e);return r?{r:r[1],g:r[2],b:r[3]}:(r=O.rgba.exec(e),r?{r:r[1],g:r[2],b:r[3],a:r[4]}:(r=O.hsl.exec(e),r?{h:r[1],s:r[2],l:r[3]}:(r=O.hsla.exec(e),r?{h:r[1],s:r[2],l:r[3],a:r[4]}:(r=O.hsv.exec(e),r?{h:r[1],s:r[2],v:r[3]}:(r=O.hsva.exec(e),r?{h:r[1],s:r[2],v:r[3],a:r[4]}:(r=O.hex8.exec(e),r?{r:y(r[1]),g:y(r[2]),b:y(r[3]),a:v(r[4]),format:t?"name":"hex8"}:(r=O.hex6.exec(e),r?{r:y(r[1]),g:y(r[2]),b:y(r[3]),format:t?"name":"hex"}:(r=O.hex4.exec(e),r?{r:y(r[1]+r[1]),g:y(r[2]+r[2]),b:y(r[3]+r[3]),a:v(r[4]+r[4]),format:t?"name":"hex8"}:(r=O.hex3.exec(e),!!r&&{r:y(r[1]+r[1]),g:y(r[2]+r[2]),b:y(r[3]+r[3]),format:t?"name":"hex"})))))))))}function A(e){return Boolean(O.CSS_UNIT.exec(String(e)))}var T=2,P=.16,M=.05,I=.05,L=.15,F=5,D=4,R=[{index:7,opacity:.15},{index:6,opacity:.25},{index:5,opacity:.3},{index:5,opacity:.45},{index:5,opacity:.65},{index:5,opacity:.85},{index:4,opacity:.9},{index:3,opacity:.95},{index:2,opacity:.97},{index:1,opacity:.98}];function N(e){var t=e.r,r=e.g,s=e.b,i=d(t,r,s);return{h:360*i.h,s:i.s,v:i.v}}function j(e){var t=e.r,r=e.g,s=e.b;return"#".concat(g(t,r,s,!1))}function U(e,t,r){var s=r/100,i={r:(t.r-e.r)*s+e.r,g:(t.g-e.g)*s+e.g,b:(t.b-e.b)*s+e.b};return i}function B(e,t,r){var s;return s=Math.round(e.h)>=60&&Math.round(e.h)<=240?r?Math.round(e.h)-T*t:Math.round(e.h)+T*t:r?Math.round(e.h)+T*t:Math.round(e.h)-T*t,s<0?s+=360:s>=360&&(s-=360),s}function q(e,t,r){return 0===e.h&&0===e.s?e.s:(s=r?e.s-P*t:t===D?e.s+P:e.s+M*t,s>1&&(s=1),r&&t===F&&s>.1&&(s=.1),s<.06&&(s=.06),Number(s.toFixed(2)));var s}function z(e,t,r){var s;return s=r?e.v+I*t:e.v-L*t,s>1&&(s=1),Number(s.toFixed(2))}function V(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=[],s=b(e),i=F;i>0;i-=1){var n=N(s),o=j(b({h:B(n,i,!0),s:q(n,i,!0),v:z(n,i,!0)}));r.push(o)}r.push(j(s));for(var a=1;a<=D;a+=1){var l=N(s),c=j(b({h:B(l,a),s:q(l,a),v:z(l,a)}));r.push(c)}return"dark"===t.theme?R.map((function(e){var s=e.index,i=e.opacity,n=j(U(b(t.backgroundColor||"#141414"),b(r[s]),100*i));return n})):r}var W={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},G={},Y={};Object.keys(W).forEach((function(e){G[e]=V(W[e]),G[e].primary=G[e][5],Y[e]=V(W[e],{theme:"dark",backgroundColor:"#141414"}),Y[e].primary=Y[e][5]}));G.red,G.volcano,G.gold,G.orange,G.yellow,G.lime,G.green,G.cyan,G.blue,G.geekblue,G.purple,G.magenta,G.grey,G.grey;var Q=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mui-fl-col mui-fl-vert mui-fl-btw mui-fl-1"},[e._m(0),t("div",{staticClass:"bottom-act"},[t("ul",{staticClass:"dot mui-fl-central"},e._l(2,(function(e,r){return t("li",{key:r,class:{active:1===e}})})),0),t("m-button",{staticClass:"width-2 taplight",attrs:{type:"primary",size:"large",round:""}},[e._v("Continue")])],1)])},$=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"mui-fl-col mui-fl-vert"},[t("p",{staticClass:"sty1-title"},[e._v("Verify your Identity")]),t("img",{staticClass:"hero kyc",attrs:{src:r(66598),alt:"KYC Image"}}),t("p",{staticClass:"t2"},[e._v("Proof-of-Citizenship")]),t("p",{staticClass:"t1 mt8"},[e._v(" To establish your identity on web3 ecosystems and fulfill compliance requirements to use dApps services, your eligibility needs to be verified and preserved based on proof of citizenship. An Identity Soulbound Token will be generated and associated with your asset wallet. ")]),t("p",{staticClass:"t3"},[e._v("The verification process is designed to ensure your anonymity, no private data is shared with anyone.")])])}],Z={name:"VerifyIdentityStepOne"},K=Z,J=r(81656),H=(0,J.A)(K,Q,$,!1,null,"2c5ac622",null),X=H.exports,ee=function(){var e=this,t=e._self._c;return t("div",{staticClass:"mui-fl-col mui-fl-vert mui-fl-btw mui-fl-1"},[t("div",{staticClass:"mui-fl-col mui-fl-vert"},[t("p",{staticClass:"step2-p1"},[e._v(" We value your privacy, and have no intention to process or store any of your personal identifiable information (PII). ")]),t("p",{staticClass:"step2-p2"},[e._v(" We have partnered with zkMe in order to verify your credentials in zero-knowledge, meaning that we can prove your eligibility with: ")]),t("ul",{staticClass:"step2-ul"},e._l(e.kycStepList,(function(r,s){return t("li",{key:s,staticClass:"mui-fl-vert"},[t("i",{class:["mui-shr-0",r.i]}),t("div",{staticClass:"mui-fl-1"},[t("p",{staticClass:"t"},[e._v(e._s(r.t))]),t("p",{staticClass:"d"},[e._v(e._s(r.d))])])])})),0),t("div",{class:{"step2-protocol":1}},[t("m-checkbox",{staticClass:"sty1 mui-fl-vert"},[t("p",{staticClass:"pro-text"},[t("span",[e._v("By starting the verification, you agree that you are over 16 years old, have read, understood and accepted zkMe's ")]),e._v(" "),t("a",[e._v("Privacy Policy")]),e._v(" "),t("span",[e._v("and")]),e._v(" "),t("a",[e._v("User Agreement")]),t("span",[e._v(".")])])])],1)]),t("div",{staticClass:"bottom-act"},[t("ul",{staticClass:"dot mui-fl-central"},e._l(2,(function(e,r){return t("li",{key:r,class:{active:2===e}})})),0),t("m-button",{staticClass:"width-2 taplight",attrs:{type:"primary",size:"large",round:""}},[e._v("Start verification")])],1)])},te=[],re={name:"VerifyIdentityStepTwo",data(){return{kycStepList:[{i:"popico-zkme",t:"End-to-End Zero-Knowledge",d:"No personal data is shared with anyone (incl. zkMe)."},{i:"popico-selective",t:"Selective disclosure",d:"Only Yes/No answers to simple eligibility questions are generated and shared."},{i:"popico-self-sovereign",t:"Self-Sovereign",d:"Credential holders can control, ammend revoke eligibility proofs at any time."},{i:"popico-anonymous",t:"Anonymous until proven guilty",d:"Only in case a regulator initiates formal bad actor proceedings, will the proof of your eligibility be unlocked and shared."}]}}},se=re,ie=(0,J.A)(se,ee,te,!1,null,"3d4d265d",null),ne=ie.exports,oe=function(){var e=this,t=e._self._c;return t("svg",{staticStyle:{"margin-right":"6px"},attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("g",{attrs:{"clip-path":"url(#clip0_6328_11973)"}},[t("g",{attrs:{filter:"url(#filter0_d_6328_11973)"}},[t("path",{attrs:{d:"M1.80615 3.95552C1.77363 3.17522 2.28732 2.47698 3.04193 2.27575L5.20916 1.69782C7.0381 1.2101 8.96291 1.2101 10.7919 1.69782L12.9591 2.27575C13.7137 2.47698 14.2274 3.17522 14.1949 3.95552L14.0209 8.13066C13.9078 10.8442 12.16 13.2179 9.60226 14.1314L9.12163 14.3031C8.39664 14.562 7.60437 14.562 6.87938 14.3031L6.39876 14.1314C3.84106 13.2179 2.09318 10.8442 1.98011 8.13066L1.80615 3.95552Z",fill:"url(#paint0_linear_6328_11973)"}}),t("path",{attrs:{d:"M14.4206 8.14732L14.5945 3.97218C14.6348 3.0046 13.9979 2.13878 13.0621 1.88925L10.8949 1.31133C8.99845 0.8056 7.00256 0.8056 5.10609 1.31133L2.93887 1.88925C2.00315 2.13878 1.36618 3.0046 1.40649 3.97218L1.58046 8.14732C1.70031 11.0237 3.55307 13.5398 6.26422 14.5081L6.74485 14.6798C7.55684 14.9698 8.44417 14.9698 9.25616 14.6798L9.73679 14.5081C12.4479 13.5398 14.3007 11.0237 14.4206 8.14732Z",stroke:"light"===e.themeMode?"#ffffff":"#141414","stroke-width":"0.8"}})]),t("mask",{staticStyle:{"mask-type":"alpha"},attrs:{id:"mask0_6328_11973",maskUnits:"userSpaceOnUse",x:"1",y:"1",width:"14",height:"14"}},[t("path",{attrs:{d:"M1.80615 3.95552C1.77363 3.17522 2.28732 2.47698 3.04193 2.27575L5.20916 1.69782C7.0381 1.2101 8.96291 1.2101 10.7919 1.69782L12.9591 2.27575C13.7137 2.47698 14.2274 3.17522 14.1949 3.95552L14.0209 8.13066C13.9078 10.8442 12.16 13.2179 9.60226 14.1314L9.12163 14.3031C8.39664 14.562 7.60437 14.562 6.87938 14.3031L6.39876 14.1314C3.84106 13.2179 2.09318 10.8442 1.98011 8.13066L1.80615 3.95552Z",fill:"url(#paint1_linear_6328_11973)"}})]),t("g",{attrs:{mask:"url(#mask0_6328_11973)"}},[t("rect",{attrs:{x:"11.5",y:"-0.46875",width:"2.86948",height:"17",transform:"rotate(30 11.5 -0.46875)",fill:"url(#paint2_linear_6328_11973)","fill-opacity":"0.43"}})]),t("path",{attrs:{d:"M3.00118 4.72259C2.97494 4.09289 3.38948 3.52942 3.99844 3.36703L5.74736 2.90065C7.22329 2.50707 8.77658 2.50707 10.2525 2.90065L12.0014 3.36703C12.6104 3.52942 13.0249 4.09289 12.9987 4.72259L12.8583 8.09187C12.7671 10.2817 11.3565 12.1972 9.29253 12.9344L8.90467 13.0729C8.31961 13.2818 7.68026 13.2818 7.09521 13.0729L6.70735 12.9344C4.64332 12.1972 3.23281 10.2817 3.14156 8.09187L3.00118 4.72259Z",fill:e.themeColor2}}),t("mask",{staticStyle:{"mask-type":"alpha"},attrs:{id:"mask1_6328_11973",maskUnits:"userSpaceOnUse",x:"3",y:"2",width:"10",height:"12"}},[t("path",{attrs:{d:"M3.00118 4.72259C2.97494 4.09289 3.38948 3.52942 3.99844 3.36703L5.74736 2.90065C7.22329 2.50707 8.77658 2.50707 10.2525 2.90065L12.0014 3.36703C12.6104 3.52942 13.0249 4.09289 12.9987 4.72259L12.8583 8.09187C12.7671 10.2817 11.3565 12.1972 9.29253 12.9344L8.90467 13.0729C8.31961 13.2818 7.68026 13.2818 7.09521 13.0729L6.70735 12.9344C4.64332 12.1972 3.23281 10.2817 3.14156 8.09187L3.00118 4.72259Z",fill:"#956B2D"}})]),t("g",{attrs:{mask:"url(#mask1_6328_11973)"}},[t("rect",{attrs:{x:"1.33594",y:"1.78906",width:"6.66667",height:"12.5",fill:e.themeColor1}})]),t("path",{attrs:{d:"M5.60156 8L7.60156 10L10.8016 6",stroke:"white","stroke-width":"1.6","stroke-linecap":"round","stroke-linejoin":"round"}}),t("mask",{staticStyle:{"mask-type":"alpha"},attrs:{id:"mask2_6328_11973",maskUnits:"userSpaceOnUse",x:"3",y:"2",width:"10",height:"12"}},[t("path",{attrs:{d:"M3.00118 4.72259C2.97494 4.09289 3.38948 3.52942 3.99844 3.36703L5.74736 2.90065C7.22329 2.50707 8.77658 2.50707 10.2525 2.90065L12.0014 3.36703C12.6104 3.52942 13.0249 4.09289 12.9987 4.72259L12.8583 8.09187C12.7671 10.2817 11.3565 12.1972 9.29252 12.9344L8.90467 13.0729C8.31961 13.2818 7.68026 13.2818 7.09521 13.0729L6.70735 12.9344C4.64332 12.1972 3.23281 10.2817 3.14156 8.09187L3.00118 4.72259Z",fill:"#956B2D"}})]),t("g",{attrs:{mask:"url(#mask2_6328_11973)"}},[t("rect",{attrs:{x:"11.5",y:"-0.46875",width:"2.86948",height:"17",transform:"rotate(30 11.5 -0.46875)",fill:"url(#paint3_linear_6328_11973)","fill-opacity":"0.43"}})])]),t("defs",[t("filter",{attrs:{id:"filter0_d_6328_11973",x:"0.204639",y:"-0.267773",width:"15.5917",height:"16.3651",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB"}},[t("feFlood",{attrs:{"flood-opacity":"0",result:"BackgroundImageFix"}}),t("feColorMatrix",{attrs:{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}}),t("feOffset"),t("feGaussianBlur",{attrs:{stdDeviation:"0.4"}}),t("feComposite",{attrs:{in2:"hardAlpha",operator:"out"}}),t("feColorMatrix",{attrs:{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"}}),t("feBlend",{attrs:{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_6328_11973"}}),t("feBlend",{attrs:{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_6328_11973",result:"shape"}})],1),t("linearGradient",{attrs:{id:"paint0_linear_6328_11973",x1:"8.00051",y1:"1.33203",x2:"8.00051",y2:"14.4973",gradientUnits:"userSpaceOnUse"}},[t("stop",{attrs:{"stop-color":e.themeColor2}}),t("stop",{attrs:{offset:"1","stop-color":e.themeColor1}})],1),t("linearGradient",{attrs:{id:"paint1_linear_6328_11973",x1:"8.00051",y1:"1.33203",x2:"8.00051",y2:"14.4973",gradientUnits:"userSpaceOnUse"}},[t("stop",{attrs:{"stop-color":"#B07E33"}}),t("stop",{attrs:{offset:"1","stop-color":"#D08E2C"}})],1),t("linearGradient",{attrs:{id:"paint2_linear_6328_11973",x1:"12.9347",y1:"-0.46875",x2:"12.9347",y2:"16.5312",gradientUnits:"userSpaceOnUse"}},[t("stop",{attrs:{"stop-color":e.themeColor1}}),t("stop",{attrs:{offset:"1","stop-color":"white","stop-opacity":"0"}})],1),t("linearGradient",{attrs:{id:"paint3_linear_6328_11973",x1:"12.9347",y1:"-0.46875",x2:"12.9347",y2:"16.5312",gradientUnits:"userSpaceOnUse"}},[t("stop",{attrs:{"stop-color":e.themeColor1}}),t("stop",{attrs:{offset:"1","stop-color":"white","stop-opacity":"0"}})],1),t("clipPath",{attrs:{id:"clip0_6328_11973"}},[t("rect",{attrs:{width:"16",height:"16",fill:"white"}})])],1)])},ae=[],le={name:"WarnTipSvg",props:{themeColor1:{type:String,required:!0,default:()=>"#005563"},themeColor2:{type:String,required:!0,default:()=>"#CCDDE0"},themeMode:{type:String,required:!0,default:()=>"light"}}},ce=le,ue=(0,J.A)(ce,oe,ae,!1,null,null,null),he=ue.exports,pe=function(){var e=this,t=e._self._c;return t("div",{ref:"ndWrapper",staticClass:"bg mui-fl-col mui-fl-vert"},[t("h1",{staticClass:"t1"},[e._v(" Proof-of-Citizenship (PoC) ")]),t("div",{staticClass:"zkme",class:e.themeMode}),t("div",[t("p",{staticClass:"c1"},[e._v(" Super easy with the following 3-step process: ")]),t("ul",{staticClass:"list"},[e._l(e.list,(function(r,s){return t("li",{key:r.id},[t("ul",{staticClass:"mui-flex"},[t("li",{staticClass:"mui-fl-col mui-fl-vert list-item"},[t("i",{class:[r.icon,"icon","mui-fl-central"]}),t("div",{directives:[{name:"show",rawName:"v-show",value:s!==e.list.length-1,expression:"index !== list.length - 1"}],staticClass:"spacer"})]),t("li",{staticClass:"mui-fl-col title-box"},[t("p",{class:{title:1}},[e._v(e._s(r.title))]),t("p",{directives:[{name:"show",rawName:"v-show",value:r.cont,expression:"item.cont"}],class:{cont:1,"cont-finished":!r.isFinished}},[e._v(e._s(r.cont)+" ")])])])])})),t("div",{staticClass:"user-tips mui-fl-vert"},[t("WarnTipSvg",{attrs:{themeMode:e.themeMode,themeColor1:e.themeColor1,themeColor2:e.themeColor2}}),e._v(" Please note we will not save your facial information. ")],1)],2)]),t("div",{staticClass:"mui-fl-central sty1-footer btn-box"},[t("m-button",{staticClass:"width-3",attrs:{type:"primary",round:"",size:"large"}},[e._v("Verify now")])],1)])},fe=[],de={name:"ProofOfIdentity",components:{WarnTipSvg:he},props:{themeColor1:{type:String,required:!0,default:()=>"#005563"},themeColor2:{type:String,required:!0,default:()=>"#CCDDE0"},themeMode:{type:String,required:!0,default:()=>"light"}},data(){return{list:[{id:"1",icon:"popico-face-id",title:"Scan your ID by mobile phone",isFinished:!0,cont:""},{id:"2",icon:"popico-square-user",isFinished:!1,title:"Scan your face",cont:""},{id:"3",icon:"popico-wallet",isFinished:!1,title:"Get your PoC and SBT",cont:""}]}}},me=de,ge=(0,J.A)(me,pe,fe,!1,null,"52072f1d",null),ve=ge.exports,ye=function(){var e=this,t=e._self._c;return t("svg",{staticStyle:{"margin-right":"8px"},attrs:{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("mask",{staticStyle:{"mask-type":"alpha"},attrs:{id:"mask0_6205_3654",maskUnits:"userSpaceOnUse",x:"2",y:"2",width:"20",height:"20"}},[t("circle",{attrs:{cx:"12",cy:"12",r:"10",fill:"#64ABFF","fill-opacity":"0.2"}})]),t("g",{attrs:{mask:"url(#mask0_6205_3654)"}},[t("path",{attrs:{d:"M12 24.5C16.4183 24.5 20 22.7091 20 20.5C20 18.2909 16.4183 16.5 12 16.5C7.58172 16.5 4 18.2909 4 20.5C4 22.7091 7.58172 24.5 12 24.5Z",fill:e.themeColor1,"fill-opacity":"0.5"}})]),t("circle",{attrs:{cx:"12",cy:"12",r:"10",fill:e.themeColor1,"fill-opacity":"0.1"}}),t("path",{attrs:{d:"M12 20C13.6569 20 15 19.5523 15 19C15 18.4477 13.6569 18 12 18C10.3431 18 9 18.4477 9 19C9 19.5523 10.3431 20 12 20Z",fill:e.themeColor1,"fill-opacity":"0.1"}}),t("path",{attrs:{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M13.9104 15.1201C13.2327 15.7134 12.5543 16.3072 12 17C11.4457 16.3072 10.7673 15.7134 10.0895 15.1201C9.24481 14.3808 8.40109 13.6423 7.80002 12.7141C7.29387 11.9324 7 11.0005 7 10C7 7.23858 9.23858 5 12 5C14.7614 5 17 7.23858 17 10C17 11.0005 16.7061 11.9324 16.2 12.7141C15.5989 13.6423 14.7552 14.3808 13.9104 15.1201ZM12 12C13.1046 12 14 11.1046 14 10C14 8.89543 13.1046 8 12 8C10.8954 8 10 8.89543 10 10C10 11.1046 10.8954 12 12 12Z",fill:e.themeColor1,"fill-opacity":"0.5"}})])},we=[],be={name:"UsaCircularSvg",props:{themeColor1:{type:String,required:!0,default:()=>"#005563"}}},Ce=be,ke=(0,J.A)(Ce,ye,we,!1,null,null,null),_e=ke.exports,Se=function(){var e=this,t=e._self._c;return t("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("g",{attrs:{opacity:"0.5"}},[t("path",{attrs:{d:"M10.7124 6H5.28758C4.8837 6 4.68176 6 4.58826 6.07986C4.50712 6.14916 4.46406 6.25311 4.47243 6.35949C4.48208 6.48208 4.62487 6.62487 4.91046 6.91046L7.62288 9.62288C7.75488 9.75488 7.82088 9.82088 7.89699 9.84561C7.96394 9.86737 8.03606 9.86737 8.10301 9.84561C8.17912 9.82088 8.24512 9.75488 8.37712 9.62288L11.0895 6.91046C11.3751 6.62487 11.5179 6.48208 11.5276 6.35949C11.5359 6.25311 11.4929 6.14916 11.4117 6.07986C11.3182 6 11.1163 6 10.7124 6Z",fill:e.themeColor1,stroke:"themeColor1","stroke-width":"1.33333","stroke-linecap":"round","stroke-linejoin":"round"}})])])},xe=[],Oe={name:"CaretDownSvg",props:{themeColor1:{type:String,required:!0,default:()=>"#005563"}}},Ee=Oe,Ae=(0,J.A)(Ee,Se,xe,!1,null,null,null),Te=Ae.exports,Pe=function(){var e=this,t=e._self._c;return t("div",[t("div",{staticClass:"bg",on:{touchmove:function(e){e.preventDefault()}}},[t("div",{ref:"ndContain",staticClass:"ndContain",attrs:{id:"ndContain"},on:{touchmove:function(e){e.preventDefault()}}},[e._m(0),e._m(1),t("h2",{staticClass:"t1"},[e._v(" Select Citizenship ")]),t("p",{staticClass:"c2"},[e._v(" Please ensure your Citizenship matches your valid ID. Your privileges could change based on the selection. ")]),t("div",{staticClass:"country-box"},[t("div",{staticClass:"mui-fl-vert select-box popup-select"},[t("div",{staticClass:"mui-fl-vert mui-fl-1"},[t("UsaCircularSvg",{staticClass:"counrty",attrs:{themeColor1:e.themeColor1}}),t("span",{staticClass:"placeholder"},[e._v("Select Country")])],1),t("CaretDownSvg",{attrs:{themeColor1:e.themeColor1}})],1)]),t("div",{staticClass:"mui-fl-central sty1-footer"},[t("m-button",{staticClass:"width-3",attrs:{disabled:"",type:"primary",round:"",size:"large"}},[e._v("Continue")])],1)])])])},Me=[function(){var e=this,t=e._self._c;return t("header",{staticClass:"mui-fl-central sty1-header"},[t("i",{staticClass:"popico-back header-back"}),e._v(" Proof-of-Citizenship (PoC) ")])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"mui-fl-vert step-box"},[t("i",{staticClass:"popico-globe-alt mui-fl-central icon"}),t("p",{staticClass:"c1"},[e._v("Nationality (1/3)")])])}],Ie={name:"CitizenshipComponent",components:{UsaCircularSvg:_e,CaretDownSvg:Te},props:{themeColor1:{type:String,required:!0,default:()=>"#005563"}}},Le=Ie,Fe=(0,J.A)(Le,Pe,Me,!1,null,"33885feb",null),De=Fe.exports,Re=function(){var e=this,t=e._self._c;return t("svg",{attrs:{width:"293",height:"208",viewBox:"0 0 293 208",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("rect",{attrs:{x:"279",y:"14",width:"180",height:"265",rx:"30",transform:"rotate(90 279 14)",fill:e.themeColor3}}),t("path",{attrs:{d:"M279 153L279 164C279 180.569 265.569 194 249 194L44 194C27.4314 194 14 180.569 14 164L14 153L279 153Z",fill:e.themeColor2,"fill-opacity":"0.6"}}),t("path",{attrs:{d:"M36 6V6C19.4315 6 6 19.4315 6 36V36",stroke:e.themeColor1,"stroke-opacity":"0.8","stroke-width":"10","stroke-linecap":"round"}}),t("path",{attrs:{d:"M257 202V202C273.569 202 287 188.569 287 172V172",stroke:e.themeColor1,"stroke-opacity":"0.8","stroke-width":"10","stroke-linecap":"round"}}),t("path",{attrs:{d:"M257 6V6C273.569 6 287 19.4315 287 36V36",stroke:e.themeColor1,"stroke-opacity":"0.8","stroke-width":"10","stroke-linecap":"round"}}),t("path",{attrs:{d:"M36 202V202C19.4315 202 6 188.569 6 172V172",stroke:e.themeColor1,"stroke-opacity":"0.8","stroke-width":"10","stroke-linecap":"round"}}),t("rect",{attrs:{opacity:"0.5",x:"34",y:"32",width:"35",height:"8",rx:"4",fill:e.themeColor1}}),t("rect",{attrs:{opacity:"0.5",x:"224",y:"32",width:"35",height:"8",rx:"4",fill:e.themeColor1}}),t("rect",{attrs:{opacity:"0.5",x:"34",y:"167",width:"64",height:"8",rx:"4",fill:e.themeColor1}}),t("rect",{attrs:{opacity:"0.5",x:"235",y:"167",width:"24",height:"8",rx:"4",fill:e.themeColor1}}),t("rect",{attrs:{opacity:"0.2",x:"118",y:"58",width:"35",height:"8",rx:"4",fill:e.themeColor1}}),t("rect",{attrs:{opacity:"0.5",x:"118",y:"74",width:"88",height:"8",rx:"4",fill:e.themeColor1}}),t("rect",{attrs:{opacity:"0.2",x:"118",y:"96",width:"35",height:"8",rx:"4",fill:e.themeColor1}}),t("rect",{attrs:{opacity:"0.5",x:"118",y:"112",width:"142",height:"8",rx:"4",fill:e.themeColor1}}),t("rect",{attrs:{opacity:"0.5",x:"118",y:"128",width:"76",height:"8",rx:"4",fill:e.themeColor1}}),t("rect",{attrs:{x:"38",y:"60",width:"60",height:"73",rx:"8",fill:e.themeColor3,stroke:"dark"===e.themeMode?e.themeColor1:e.themeColor2,"stroke-width":"7"}}),t("circle",{attrs:{cx:"57.1444",cy:"91.1444",r:"3.14442",fill:e.themeColor1,"fill-opacity":"0.8"}}),t("path",{attrs:{d:"M78.7063 98.7813C78.9544 98.7813 79.1569 98.9827 79.1423 99.2303C78.8615 103.983 74.148 107.765 68.3746 107.765C62.6012 107.765 57.8878 103.983 57.607 99.2303C57.5924 98.9827 57.7949 98.7813 58.0429 98.7813L68.3746 98.7812L78.7063 98.7813Z",fill:e.themeColor1,"fill-opacity":"0.8"}}),t("circle",{attrs:{cx:"79.6054",cy:"91.1444",r:"3.14442",fill:e.themeColor1,"fill-opacity":"0.8"}})])},Ne=[],je={name:"DocumentSvg",props:{themeColor1:{type:String,required:!0,default:()=>"#005563"},themeColor2:{type:String,required:!0,default:()=>"#CCDDE0"},themeColor3:{type:String,required:!0,default:()=>"#E8F4F5"},themeMode:{type:String,required:!0,default:()=>"light"}}},Ue=je,Be=(0,J.A)(Ue,Re,Ne,!1,null,null,null),qe=Be.exports,ze=function(){var e=this,t=e._self._c;return t("div",{staticClass:"bg"},[e._m(0),e._m(1),t("div",{staticClass:"padd"},[t("h2",{staticClass:"t1"},[e._v("Scan your ID")]),t("p",{staticClass:"c2"},[e._v("Please have your document ready.")]),t("DocumentSvg",{staticClass:"img",attrs:{themeMode:e.themeMode,themeColor1:e.themeColor1,themeColor2:e.themeColor2,themeColor3:e.themeColor3}}),t("ul",{staticClass:"list"},e._l(e.list,(function(r){return t("li",{key:r.id,staticClass:"mui-fl-vert list-item"},[t("i",{class:[r.icon,"icon1","mui-fl-central"]}),t("p",{staticClass:"c3"},[e._v(e._s(r.title))])])})),0),t("div",{staticClass:"mui-fl-central sty1-footer"},[t("m-button",{staticClass:"width-3",attrs:{type:"primary",round:"",size:"large"}},[e._v("Continue")])],1)],1)])},Ve=[function(){var e=this,t=e._self._c;return t("header",{staticClass:"mui-fl-central sty1-header"},[t("i",{staticClass:"popico-back header-back"}),t("span",[e._v("Proof-of-Citizenship (PoC)")])])},function(){var e=this,t=e._self._c;return t("div",{staticClass:"mui-fl-vert step-box"},[t("i",{staticClass:"popico-credit-card-scan mui-fl-central icon"}),t("p",{staticClass:"c1"},[e._v("Government-issued ID (2/3)")])])}],We={name:"DocumentScan",components:{DocumentSvg:qe},props:{themeColor1:{type:String,required:!0,default:()=>"#005563"},themeColor2:{type:String,required:!0,default:()=>"#CCDDE0"},themeColor3:{type:String,required:!0,default:()=>"#E8F4F5"},themeMode:{type:String,required:!0,default:()=>"light"}},data(){return{list:[{id:1,icon:"popico-image-polaroid-user",title:"Use portrait orientation"},{id:2,icon:"popico-bolt",title:"Turn off the flash on your camera"},{id:3,icon:"popico-dial-low",title:"Use a dark background"},{id:4,icon:"popico-images-user",title:"Take the photo on a flat surface"}]}}},Ge=We,Ye=(0,J.A)(Ge,ze,Ve,!1,null,"059488ed",null),Qe=Ye.exports,$e=function(){var e=this,t=e._self._c;return t("svg",{attrs:{width:"266",height:"266",viewBox:"0 0 266 266",fill:"none",xmlns:"http://www.w3.org/2000/svg"}},[t("rect",{attrs:{x:"13",y:"13",width:"240",height:"240",rx:"36",fill:e.themeColor3}}),t("rect",{attrs:{x:"94.4316",y:"94.4326",width:"77.1351",height:"77.1351",rx:"38.5676",fill:e.themeColor3,stroke:"dark"===e.themeMode?e.themeColor1:e.themeColor2,"stroke-width":"7.58706"}}),t("rect",{attrs:{opacity:"0.4",x:"65",y:"65",width:"136",height:"136",rx:"68",stroke:e.themeColor2,"stroke-width":"10","stroke-linejoin":"round","stroke-dasharray":"1 6"}}),t("circle",{attrs:{cx:"118.361",cy:"126.117",r:"4.09896",fill:e.themeColor1}}),t("path",{attrs:{d:"M146.467 136.071C146.79 136.071 147.054 136.334 147.035 136.657C146.669 142.853 140.525 147.783 132.999 147.783C125.473 147.783 119.329 142.853 118.963 136.657C118.944 136.334 119.208 136.071 119.531 136.071L132.999 136.071L146.467 136.071Z",fill:e.themeColor1}}),t("circle",{attrs:{cx:"147.638",cy:"126.117",r:"4.09896",fill:e.themeColor1}}),t("path",{attrs:{d:"M41 5V5C21.1177 5 5 21.1177 5 41V41",stroke:e.themeColor1,"stroke-width":"10","stroke-linecap":"round"}}),t("path",{attrs:{d:"M225 261V261C244.882 261 261 244.882 261 225V225",stroke:e.themeColor1,"stroke-width":"10","stroke-linecap":"round"}}),t("path",{attrs:{d:"M225 5V5C244.882 5 261 21.1177 261 41V41",stroke:e.themeColor1,"stroke-width":"10","stroke-linecap":"round"}}),t("path",{attrs:{d:"M41 261V261C21.1177 261 5 244.882 5 225V225",stroke:e.themeColor1,"stroke-width":"10","stroke-linecap":"round"}})])},Ze=[],Ke={name:"FacialSvg",props:{themeColor1:{type:String,required:!0,default:()=>"#005563"},themeColor2:{type:String,required:!0,default:()=>"#CCDDE0"},themeColor3:{type:String,required:!0,default:()=>"#E8F4F5"},themeMode:{type:String,required:!0,default:()=>"light"}}},Je=Ke,He=(0,J.A)(Je,$e,Ze,!1,null,null,null),Xe=He.exports,et=function(){var e=this,t=e._self._c;return t("div",{staticClass:"bg"},[t("header",{staticClass:"mui-fl-central sty1-header"},[t("i",{staticClass:"popico-back header-back",on:{click:function(t){return e.$router.back()}}}),e._v(" Proof-of-Citizenship (PoC) ")]),e._m(0),t("h2",{staticClass:"t1"},[e._v("Facial Recognition")]),t("p",{staticClass:"c2"},[e._v(" We need to verify your face to make sure it's a real person. Please click the button below to continue. ")]),t("FacialSvg",{staticClass:"img",attrs:{themeMode:e.themeMode,themeColor1:e.themeColor1,themeColor2:e.themeColor2,themeColor3:e.themeColor3}}),t("div",{staticClass:"mui-fl-central sty1-footer"},[t("m-button",{staticClass:"width-3",attrs:{type:"primary",round:"",size:"large"}},[e._v("Continue")])],1)],1)},tt=[function(){var e=this,t=e._self._c;return t("div",{staticClass:"mui-fl-vert step-box"},[t("i",{staticClass:"popico-face-id mui-fl-central icon"}),t("p",{staticClass:"c1"},[e._v("Facial Recognition (3/3)")])])}],rt={name:"FacialRecognition",components:{FacialSvg:Xe},props:{themeColor1:{type:String,required:!0,default:()=>"#005563"},themeColor2:{type:String,required:!0,default:()=>"#CCDDE0"},themeColor3:{type:String,required:!0,default:()=>"#E8F4F5"},themeMode:{type:String,required:!0,default:()=>"light"}}},st=rt,it=(0,J.A)(st,et,tt,!1,null,"007478d8",null),nt=it.exports,ot=function(){var e=this,t=e._self._c;return t("div",{staticClass:"sty1-gp-powered mui-fl-central",class:e.darkMode&&"dark"},[t("img",{attrs:{src:e.darkMode?e.img1:e.img2,alt:""}}),t("p",[e._v("Powered by zkMe")])])},at=[],lt={name:"PoweredComponent",props:{darkMode:{type:Boolean,required:!0}},data(){return{img1:r(35006),img2:r(1939)}}},ct=lt,ut=(0,J.A)(ct,ot,at,!1,null,null,null),ht=ut.exports,pt=function(){var e=this,t=e._self._c;return t("div",{class:["zkme-widget","dark"===e.themeMode&&"dark","web"===e.previewMode&&e.step<3?"web":"mobile"]},[t("div",{staticClass:"sty1-cell bg mui-fl-col mui-fl-btw"},[t("VerifyIdentityStepOne",{directives:[{name:"show",rawName:"v-show",value:1===e.step,expression:"step === 1"}]}),t("VerifyIdentityStepTwo",{directives:[{name:"show",rawName:"v-show",value:2===e.step,expression:"step === 2"}]}),t("ProofOfIdentity",{directives:[{name:"show",rawName:"v-show",value:3===e.step,expression:"step === 3"}],attrs:{themeMode:e.themeMode,themeColor1:"dark"!==e.themeMode?e.colorsOp[0]:e.darkColorsOp[0],themeColor2:"dark"!==e.themeMode?e.colorsOp[1]:e.darkColorsOp[1]}}),t("CitizenshipComponent",{directives:[{name:"show",rawName:"v-show",value:4===e.step,expression:"step === 4"}],attrs:{themeColor1:"dark"!==e.themeMode?e.colorsOp[0]:e.darkColorsOp[0]}}),t("DocumentScan",{directives:[{name:"show",rawName:"v-show",value:5===e.step,expression:"step === 5"}],attrs:{themeMode:e.themeMode,themeColor1:"dark"!==e.themeMode?e.colorsOp[0]:e.darkColorsOp[0],themeColor2:"dark"!==e.themeMode?e.colorsOp[1]:e.darkColorsOp[1],themeColor3:"dark"!==e.themeMode?e.colorsOp[2]:e.darkColorsOp[2]}}),t("FacialRecognition",{directives:[{name:"show",rawName:"v-show",value:6===e.step,expression:"step === 6"}],attrs:{themeMode:e.themeMode,themeColor1:"dark"!==e.themeMode?e.colorsOp[0]:e.darkColorsOp[0],themeColor2:"dark"!==e.themeMode?e.colorsOp[1]:e.darkColorsOp[1],themeColor3:"dark"!==e.themeMode?e.colorsOp[2]:e.darkColorsOp[2]}}),t("PoweredComponent",{directives:[{name:"show",rawName:"v-show",value:e.step<3,expression:"step < 3"}],attrs:{"dark-mode":"dark"===e.themeMode}})],1)])},ft=[],dt=r(43818),mt=dt;dt.stringify,dt.fromJSON,dt.plugin,dt.parse,dt.list,dt.document,dt.comment,dt.atRule,dt.rule,dt.decl,dt.root,dt.CssSyntaxError,dt.Declaration,dt.Container,dt.Processor,dt.Document,dt.Comment,dt.Warning,dt.AtRule,dt.Result,dt.Input,dt.Rule,dt.Root,dt.Node;var gt=r(67697),vt=r.n(gt),yt=r(90896),wt=r.n(yt),bt={name:"PreviewContent",components:{ProofOfIdentity:ve,PoweredComponent:ht,VerifyIdentityStepOne:X,VerifyIdentityStepTwo:ne,CitizenshipComponent:De,DocumentScan:Qe,FacialRecognition:nt},props:{step:{type:Number,required:!0,default:()=>1},previewMode:{type:String,required:!0,default:()=>"web"},propsColorsOp:{type:Array,required:!0,default:()=>[]},cssTextarea:{type:String,required:!0,default:()=>""},themeMode:{type:String,required:!0,default:()=>"light"}},data(){return{firstListen:!1,colorsOp:[],darkColorsOp:[]}},watch:{propsColorsOp(e){this.colorsOp=e&&e[0].length?e[0]:["#005563","#CCDDE0","#F2F7F7"],this.darkColorsOp=e&&e[1].length?e[1]:["#2E8A9B","#22535C","#172D32"]},colorsOp(){this.firstListen||(this.firstListen=!0,this.generateCss())},themeMode(){this.firstListen||(this.firstListen=!0,this.generateCss())},cssTextarea:{handler:wt()((function(){this.firstListen||(this.firstListen=!0,this.generateCss())}),1e3),immediate:!1}},created(){this.colorsOp=["#005563","#CCDDE0","#F2F7F7"],this.darkColorsOp=["#2E8A9B","#22535C","#172D32"]},methods:{async generateCss(){const e=document.getElementById("dynamic-widget-preview");e&&e.parentNode.removeChild(e);let t="",r=".zkme-widget{";switch(r+=".bg{background:var(--color-background)}",this.themeMode){case"dark":this.darkColorsOp.length&&(r+=`:root{\n              --vt-c-primary-1: ${this.darkColorsOp[0]};\n              --vt-c-primary-1-rgb: ${this.hexToRgb(this.darkColorsOp[0])};\n              --vt-c-primary-2: ${this.darkColorsOp[1]};\n              --vt-c-primary-3: ${this.darkColorsOp[2]};\n            }`);break;case"light":default:this.colorsOp.length&&(r+=`:root{\n              --vt-c-primary-1: ${this.colorsOp[0]};\n              --vt-c-primary-1-rgb: ${this.hexToRgb(this.colorsOp[0])};\n              --vt-c-primary-2: ${this.colorsOp[1]};\n              --vt-c-primary-3: ${this.colorsOp[2]};\n            }`);break}"dark"===this.themeMode?r+="\n        :root{\n    --vt-c-white-bg: #141414;\n    --vt-c-secondary-5: #262626;\n    --vt-c-text-1: #FFFFFF;\n    \n    --vt-c-white-other-3: #fff;\n    --vt-c-white-other-3-rgb: 255,255,255;\n    --vt-c-secondary-5: #262626;\n    --vt-c-white-other: #000;\n\n    --vt-c-warning: #ee6969;\n    --vt-c-warning-rgb: 238, 105, 105;\n    \n    --vt-c-success: #1B292C;\n    --vt-c-success-rgb: 27, 41, 44;\n    --vt-c-progressing: #0F1B2A;\n    --color-text-17: rgba(255, 255, 255, 0.8);\n    --vt-c-tertiary-blue: #071627;\n    --vt-c-tertiary-green: #1D2526;\n        }":r+=":root{\n          --vt-c-text-1: #000000;\n          --vt-c-white-bg: #fff;\n          --vt-c-black-bg: #fff;\n        }",t=`${r}}`,r+=this.cssTextarea?`${this.cssTextarea}`:"",r+="}";const s=document.createElement("style");s.setAttribute("id","dynamic-widget-preview"),r=r.replace(/[\r\n]/g,"");try{const e=await mt([vt()]).process(r).async(),t=e.css;s.innerHTML=t.replaceAll(".zkme-widget :root",":root"),document.head.appendChild(s),this.firstListen=!1}catch(i){this.firstListen=!1;const e=await mt([vt()]).process(t).async(),r=e.css;s.innerHTML=r.replaceAll(".zkme-widget :root",":root"),document.head.appendChild(s),console.log(i)}},hexToRgb(e){if(!e)return"";let t="";if(e.startsWith("#")&&(t=e.substring(1)),6!==t.length)return;const r=parseInt(t.substring(0,2),16),s=parseInt(t.substring(2,4),16),i=parseInt(t.substring(4,6),16);return`${r}, ${s}, ${i}`}}},Ct=bt,kt=(0,J.A)(Ct,pt,ft,!1,null,"6cbd0620",null),_t=kt.exports,St={components:{PreviewContent:_t},data(){return{colors:["#0057FF","#0082FF","#00A8EF","#14BCD4","#00D29F","#00CB49","#C0E725","#E6C832","#FFB800","#df9143","#FF6800","#A65529","#FF339B","#EB35F7","#A14FFF","#5A46F9"],colorsOp:[],darkColorsOp:[],themeMode:"light",mode:2,changemode:"",preivewStep:1,previewMode:"web",colorPicker:null,predefineColors:["#FF6B00","#FFE500","#05FF00","#00F0FF","#00ced1","#EBEBE5","#EBEBEA","#EBEBEB","#EBEBEC"],hexColor:"",indexColor:"",selectColorData:[[],[]],tempColor:"",keepColor:!1,warningtip:!1,confirmReset:!1,localStorageColor:JSON.parse(localStorage.getItem("localStorageColor")),cssTextarea:"",textareaPlaceholder:" // Change background color \n:root {\n    --color-background: #fff;\n    --vt-c-white-bg: #fff;\n}"}},computed:{userName(){return this.$store.state.auth.user.name}},async created(){if(this.localStorageColor)for(const e in this.localStorageColor)this.userName===this.localStorageColor[e].userName&&(this.predefineColors=this.localStorageColor[e].colorList);this.classification(),this.widgetDesignConfig()},mounted(){this.$watch((()=>this.$refs.colorPicker?.$data.showPicker),(e=>{e||this.colorChange(this.tempColor,"push")})),this.getSystemMode(!0)},methods:{classification(){this.colors.forEach((e=>{const t=this.colorSelect(e);this.colorsOp.push([t[5],t[2],t[0]]);const r=this.colorSelect(e,"dark");this.darkColorsOp.push([r[5],r[3],r[1]])}))},async widgetDesignConfig(){const e=await this.$api.request("Uidesign.widgetDesignConfig",{},{},!0);if(8e7===e.code){if(e.data?.themeColor){let t="";this.changemode=1,this.colorsOp.forEach(((r,s)=>{r.includes(e.data?.themeColor)&&(t=s)})),this.selectColor(e.data?.themeColor,t),t.toString()?this.indexColor=t:(this.colorPicker=e.data?.themeColor,this.indexColor="custom"),this.keepColor=!0}else this.keepColor=!1;e.data?.customCss&&(this.cssTextarea=e.data?.customCss),this.mode="undefined"===typeof e.data?.mode?2:e.data?.mode}},colorSelect(e,t="default"){return V(e,{theme:t})},selectMode(e){this.changemode=e,this.mode=e},selectPreviewMode(e){this.previewMode=e},previousStep(){this.preivewStep<=1||(this.preivewStep=this.preivewStep-1)},nextStep(){this.preivewStep>=6||(this.preivewStep=this.preivewStep+1)},componentToHex(e){var t=e.toString(16);return 1===t.length?"0"+t:t},activeChange(e){this.tempColor=e,this.colorChange(e,"watch")},colorChange(e,t){var r=/rgb\((\d{1,3}), (\d{1,3}), (\d{1,3})\)/,s=r.exec(e);if(null!==s){const e="#"+this.componentToHex(Number(s[1]))+this.componentToHex(Number(s[2]))+this.componentToHex(Number(s[3]));if(this.hexColor=e,this.colorPicker=e,this.indexColor="custom",this.selectColor(this.colorPicker,"custom"),"watch"===t||this.predefineColors.includes(e.toUpperCase()))return;this.predefineColors.pop(),this.predefineColors.unshift(e.toUpperCase());const r=[];let i;r.push({userName:this.userName,colorList:this.predefineColors}),i=this.localStorageColor?r.concat(this.localStorageColor):r;const n=[...new Map(i.map((e=>[e.userName,e]))).values()];localStorage.setItem("localStorageColor",JSON.stringify(n))}},selectColor(e,t){if(this.indexColor=t,"#005563"!==e)if("number"===typeof t)this.selectColorData=[[this.colorsOp[t][0],this.colorsOp[t][1],this.colorsOp[t][2]],[this.darkColorsOp[t][0],this.darkColorsOp[t][1],this.darkColorsOp[t][2]]];else{const t=this.colorSelect(e),r=this.colorSelect(e,"dark");this.selectColorData=[[t[5],t[3],t[0]],[r[5],r[2],r[0]]]}else this.selectColorData=[[],[]]},async reset(){if(!this.confirmReset)return void(this.warningtip=!0);const e=await this.$api.request("Uidesign.resetWidgetDesign",{},{},!0);this.warningtip=!1,8e7===e.code&&(this.indexColor="",this.selectColorData=[[],[]],this.cssTextarea="",this.keepColor=!1,this.confirmReset=!1,this.mode=2,this.changemode="",this.$message({message:"Reset success!",iconClass:"mcico-success",customClass:"sty4-message",duration:3e3,offset:32,center:!0}))},async configure(){if(!this.indexColor.toString()&&!this.changemode.toString()&&!this.cssTextarea)return;let e;e=this.selectColorData[0].length?{lightColor1:this.selectColorData[0][0]||"",lightColor2:this.selectColorData[0][1]||"",lightColor3:this.selectColorData[0][2]||"",darkColor1:this.selectColorData[1][0]||"",darkColor2:this.selectColorData[1][1]||"",darkColor3:this.selectColorData[1][2]||"",mode:0,customCss:this.cssTextarea}:{lightColor1:"",lightColor2:"",lightColor3:"",darkColor1:"",darkColor2:"",darkColor3:"",mode:0,customCss:this.cssTextarea};const t=await this.$api.request("Uidesign.updateWidgetDesign",e,{},!0);8e7===t.code&&(this.keepColor=!0,this.widgetDesignConfig(),this.$message({message:"Save and configure success!",iconClass:"mcico-success",customClass:"sty4-message",duration:3e3,offset:32,center:!0}))},setThemeMode(e){this.themeMode=e},addThemeEventListener(){const e=window.matchMedia("(prefers-color-scheme: dark)"),t=window.matchMedia("(prefers-color-scheme: light)"),r=e=>{e.matches&&this.setThemeMode("dark")},s=e=>{e.matches&&this.setThemeMode("light")};e.addEventListener("change",r),t.addEventListener("change",s)},getSystemMode(e){const t=window.matchMedia("(prefers-color-scheme: dark)"),r=window.matchMedia("(prefers-color-scheme: light)");this.setThemeMode(r.matches?"light":t.matches&&"dark"),e&&this.addThemeEventListener()}},watch:{mode(){0===this.mode?this.setThemeMode("light"):1===this.mode?this.setThemeMode("dark"):this.getSystemMode(!1)}}},xt=St,Ot=(0,J.A)(xt,s,i,!1,null,"e7992d7c",null),Et=Ot.exports},75564:function(e){"use strict";
/*! https://mths.be/cssesc v3.0.0 by @mathias */var t={},r=t.hasOwnProperty,s=function(e,t){if(!e)return t;var s={};for(var i in t)s[i]=r.call(e,i)?e[i]:t[i];return s},i=/[ -,\.\/:-@\[-\^`\{-~]/,n=/[ -,\.\/:-@\[\]\^`\{-~]/,o=/(^|\\+)?(\\[A-F0-9]{1,6})\x20(?![a-fA-F0-9\x20])/g,a=function e(t,r){r=s(r,e.options),"single"!=r.quotes&&"double"!=r.quotes&&(r.quotes="single");var a="double"==r.quotes?'"':"'",l=r.isIdentifier,c=t.charAt(0),u="",h=0,p=t.length;while(h<p){var f=t.charAt(h++),d=f.charCodeAt(),m=void 0;if(d<32||d>126){if(d>=55296&&d<=56319&&h<p){var g=t.charCodeAt(h++);56320==(64512&g)?d=((1023&d)<<10)+(1023&g)+65536:h--}m="\\"+d.toString(16).toUpperCase()+" "}else m=r.escapeEverything?i.test(f)?"\\"+f:"\\"+d.toString(16).toUpperCase()+" ":/[\t\n\f\r\x0B]/.test(f)?"\\"+d.toString(16).toUpperCase()+" ":"\\"==f||!l&&('"'==f&&a==f||"'"==f&&a==f)||l&&n.test(f)?"\\"+f:f;u+=m}return l&&(/^-[-\d]/.test(u)?u="\\-"+u.slice(1):/\d/.test(c)&&(u="\\3"+c+" "+u.slice(1))),u=u.replace(o,(function(e,t,r){return t&&t.length%2?e:(t||"")+r})),!l&&r.wrap?a+u+a:u};a.options={escapeEverything:!1,isIdentifier:!1,quotes:"single",wrap:!1},a.version="3.0.0",e.exports=a},97874:function(e,t,r){var s=r(68606),i=s.Symbol;e.exports=i},99315:function(e,t,r){var s=r(97874),i=r(60038),n=r(85677),o="[object Null]",a="[object Undefined]",l=s?s.toStringTag:void 0;function c(e){return null==e?void 0===e?a:o:l&&l in Object(e)?i(e):n(e)}e.exports=c},64867:function(e,t,r){var s=r(45761),i=/^\s+/;function n(e){return e?e.slice(0,s(e)+1).replace(i,""):e}e.exports=n},86903:function(e,t,r){var s="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;e.exports=s},60038:function(e,t,r){var s=r(97874),i=Object.prototype,n=i.hasOwnProperty,o=i.toString,a=s?s.toStringTag:void 0;function l(e){var t=n.call(e,a),r=e[a];try{e[a]=void 0;var s=!0}catch(l){}var i=o.call(e);return s&&(t?e[a]=r:delete e[a]),i}e.exports=l},85677:function(e){var t=Object.prototype,r=t.toString;function s(e){return r.call(e)}e.exports=s},68606:function(e,t,r){var s=r(86903),i="object"==typeof self&&self&&self.Object===Object&&self,n=s||i||Function("return this")();e.exports=n},45761:function(e){var t=/\s/;function r(e){var r=e.length;while(r--&&t.test(e.charAt(r)));return r}e.exports=r},90896:function(e,t,r){var s=r(19692),i=r(21087),n=r(84243),o="Expected a function",a=Math.max,l=Math.min;function c(e,t,r){var c,u,h,p,f,d,m=0,g=!1,v=!1,y=!0;if("function"!=typeof e)throw new TypeError(o);function w(t){var r=c,s=u;return c=u=void 0,m=t,p=e.apply(s,r),p}function b(e){return m=e,f=setTimeout(_,t),g?w(e):p}function C(e){var r=e-d,s=e-m,i=t-r;return v?l(i,h-s):i}function k(e){var r=e-d,s=e-m;return void 0===d||r>=t||r<0||v&&s>=h}function _(){var e=i();if(k(e))return S(e);f=setTimeout(_,C(e))}function S(e){return f=void 0,y&&c?w(e):(c=u=void 0,p)}function x(){void 0!==f&&clearTimeout(f),m=0,c=d=u=f=void 0}function O(){return void 0===f?p:S(i())}function E(){var e=i(),r=k(e);if(c=arguments,u=this,d=e,r){if(void 0===f)return b(d);if(v)return clearTimeout(f),f=setTimeout(_,t),w(d)}return void 0===f&&(f=setTimeout(_,t)),p}return t=n(t)||0,s(r)&&(g=!!r.leading,v="maxWait"in r,h=v?a(n(r.maxWait)||0,t):h,y="trailing"in r?!!r.trailing:y),E.cancel=x,E.flush=O,E}e.exports=c},19692:function(e){function t(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}e.exports=t},85091:function(e){function t(e){return null!=e&&"object"==typeof e}e.exports=t},29299:function(e,t,r){var s=r(99315),i=r(85091),n="[object Symbol]";function o(e){return"symbol"==typeof e||i(e)&&s(e)==n}e.exports=o},21087:function(e,t,r){var s=r(68606),i=function(){return s.Date.now()};e.exports=i},84243:function(e,t,r){var s=r(64867),i=r(19692),n=r(29299),o=NaN,a=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,c=/^0o[0-7]+$/i,u=parseInt;function h(e){if("number"==typeof e)return e;if(n(e))return o;if(i(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=s(e);var r=l.test(e);return r||c.test(e)?u(e.slice(2),r?2:8):a.test(e)?o:+e}e.exports=h},67697:function(e,t,r){r(44114),r(98992),r(3949),r(81454);const{Rule:s,AtRule:i}=r(43818);let n=r(5033);function o(e,t){let r;try{n((e=>{r=e})).processSync(e)}catch(s){throw e.includes(":")?t?t.error("Missed semicolon"):s:t?t.error(s.message):s}return r.at(0)}function a(e,t){let r=!1;return e.each((e=>{if("nesting"===e.type){let s=t.clone({});"&"!==e.value?e.replaceWith(o(e.value.replace("&",s.toString()))):e.replaceWith(s),r=!0}else"nodes"in e&&e.nodes&&a(e,t)&&(r=!0)})),r}function l(e,t){let r=[];return e.selectors.forEach((s=>{let i=o(s,e);t.selectors.forEach((e=>{if(!e)return;let s=o(e,t),l=a(s,i);l||(s.prepend(n.combinator({value:" "})),s.prepend(i.clone({}))),r.push(s.toString())}))})),r}function c(e,t){let r=e.prev();t.after(e);while(r&&"comment"===r.type){let e=r.prev();t.after(r),r=e}return e}function u(e){return function t(r,s,i,n=i){let o=[];if(s.each((a=>{"rule"===a.type&&i?n&&(a.selectors=l(r,a)):"atrule"===a.type&&a.nodes?e[a.name]?t(r,a,n):!1!==s[g]&&o.push(a):o.push(a)})),i&&o.length){let e=r.clone({nodes:[]});for(let t of o)e.append(t);s.prepend(e)}}}function h(e,t,r){let i=new s({selector:e,nodes:[]});return i.append(t),r.after(i),i}function p(e,t){let r={};for(let s of e)r[s]=!0;if(t)for(let s of t)r[s.replace(/^@/,"")]=!0;return r}function f(e){e=e.trim();let t=e.match(/^\((.*)\)$/);if(!t)return{type:"basic",selector:e};let r=t[1].match(/^(with(?:out)?):(.+)$/);if(r){let e="with"===r[1],t=Object.fromEntries(r[2].trim().split(/\s+/).map((e=>[e,!0])));if(e&&t.all)return{type:"noop"};let s=e=>!!t[e];return t.all?s=()=>!0:e&&(s=e=>"all"!==e&&!t[e]),{type:"withrules",escapes:s}}return{type:"unknown"}}function d(e){let t=[],r=e.parent;while(r&&r instanceof i)t.push(r),r=r.parent;return t}function m(e){let t=e[v];if(t){let r,s,i,n,o=e.nodes,a=-1,l=d(e);if(l.forEach(((e,o)=>{if(t(e.name))r=e,a=o,i=n;else{let t=n;n=e.clone({nodes:[]}),t&&n.append(t),s=s||n}})),r)if(i){let e=s;e.append(o),r.after(i)}else r.after(o);else e.after(o);if(e.next()&&r){let t;l.slice(0,a+1).forEach(((r,s,i)=>{let n=t;t=r.clone({nodes:[]}),n&&t.append(n);let o=[],a=i[s-1]||e,l=a.next();while(l)o.push(l),l=l.next();t.append(o)})),t&&(i||o[o.length-1]).after(t)}}else e.after(e.nodes);e.remove()}const g=Symbol("rootRuleMergeSel"),v=Symbol("rootRuleEscapes");function y(e){let{params:t}=e,{type:r,selector:i,escapes:n}=f(t);if("unknown"===r)throw e.error(`Unknown @${e.name} parameter ${JSON.stringify(t)}`);if("basic"===r&&i){let t=new s({selector:i,nodes:e.nodes});e.removeAll(),e.append(t)}e[v]=n,e[g]=n?!n("all"):"noop"===r}const w=Symbol("hasRootRule");e.exports=(e={})=>{let t=p(["media","supports","layer","container"],e.bubble),r=u(t),s=p(["document","font-face","keyframes","-webkit-keyframes","-moz-keyframes"],e.unwrap),i=(e.rootRuleName||"at-root").replace(/^@/,""),n=e.preserveEmpty;return{postcssPlugin:"postcss-nested",Once(e){e.walkAtRules(i,(t=>{y(t),e[w]=!0}))},Rule(e){let o=!1,a=e,u=!1,p=[];e.each((n=>{"rule"===n.type?(p.length&&(a=h(e.selector,p,a),p=[]),u=!0,o=!0,n.selectors=l(e,n),a=c(n,a)):"atrule"===n.type?(p.length&&(a=h(e.selector,p,a),p=[]),n.name===i?(o=!0,r(e,n,!0,n[g]),a=c(n,a)):t[n.name]?(u=!0,o=!0,r(e,n,!0),a=c(n,a)):s[n.name]?(u=!0,o=!0,r(e,n,!1),a=c(n,a)):u&&p.push(n)):"decl"===n.type&&u&&p.push(n)})),p.length&&(a=h(e.selector,p,a)),o&&!0!==n&&(e.raws.semicolon=!0,0===e.nodes.length&&e.remove())},RootExit(e){e[w]&&(e.walkAtRules(i,m),e[w]=!1)}}},e.exports.postcss=!0},5033:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=a(r(90953)),i=o(r(91620));function n(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function o(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==typeof e&&"function"!==typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var s={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(s,o,a):s[o]=e[o]}return s["default"]=e,r&&r.set(e,s),s}function a(e){return e&&e.__esModule?e:{default:e}}var l=function(e){return new s["default"](e)};Object.assign(l,i),delete l.__esModule;var c=l;t["default"]=c,e.exports=t.default},90534:function(e,t,r){"use strict";r(44114),r(98992),r(23215),r(54520),r(3949),t.__esModule=!0,t["default"]=void 0;var s,i,n=S(r(84522)),o=S(r(65671)),a=S(r(21351)),l=S(r(86685)),c=S(r(17379)),u=S(r(44284)),h=S(r(24747)),p=S(r(38424)),f=_(r(86960)),d=S(r(97201)),m=S(r(30540)),g=S(r(66610)),v=S(r(57299)),y=_(r(72460)),w=_(r(49587)),b=_(r(26785)),C=r(85706);function k(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(k=function(e){return e?r:t})(e)}function _(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==typeof e&&"function"!==typeof e)return{default:e};var r=k(t);if(r&&r.has(e))return r.get(e);var s={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&Object.prototype.hasOwnProperty.call(e,n)){var o=i?Object.getOwnPropertyDescriptor(e,n):null;o&&(o.get||o.set)?Object.defineProperty(s,n,o):s[n]=e[n]}return s["default"]=e,r&&r.set(e,s),s}function S(e){return e&&e.__esModule?e:{default:e}}function x(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function O(e,t,r){return t&&x(e.prototype,t),r&&x(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}var E=(s={},s[w.space]=!0,s[w.cr]=!0,s[w.feed]=!0,s[w.newline]=!0,s[w.tab]=!0,s),A=Object.assign({},E,(i={},i[w.comment]=!0,i));function T(e){return{line:e[y.FIELDS.START_LINE],column:e[y.FIELDS.START_COL]}}function P(e){return{line:e[y.FIELDS.END_LINE],column:e[y.FIELDS.END_COL]}}function M(e,t,r,s){return{start:{line:e,column:t},end:{line:r,column:s}}}function I(e){return M(e[y.FIELDS.START_LINE],e[y.FIELDS.START_COL],e[y.FIELDS.END_LINE],e[y.FIELDS.END_COL])}function L(e,t){if(e)return M(e[y.FIELDS.START_LINE],e[y.FIELDS.START_COL],t[y.FIELDS.END_LINE],t[y.FIELDS.END_COL])}function F(e,t){var r=e[t];if("string"===typeof r)return-1!==r.indexOf("\\")&&((0,C.ensureObject)(e,"raws"),e[t]=(0,C.unesc)(r),void 0===e.raws[t]&&(e.raws[t]=r)),e}function D(e,t){var r=-1,s=[];while(-1!==(r=e.indexOf(t,r+1)))s.push(r);return s}function R(){var e=Array.prototype.concat.apply([],arguments);return e.filter((function(t,r){return r===e.indexOf(t)}))}var N=function(){function e(e,t){void 0===t&&(t={}),this.rule=e,this.options=Object.assign({lossy:!1,safe:!1},t),this.position=0,this.css="string"===typeof this.rule?this.rule:this.rule.selector,this.tokens=(0,y["default"])({css:this.css,error:this._errorGenerator(),safe:this.options.safe});var r=L(this.tokens[0],this.tokens[this.tokens.length-1]);this.root=new n["default"]({source:r}),this.root.errorGenerator=this._errorGenerator();var s=new o["default"]({source:{start:{line:1,column:1}}});this.root.append(s),this.current=s,this.loop()}var t=e.prototype;return t._errorGenerator=function(){var e=this;return function(t,r){return"string"===typeof e.rule?new Error(t):e.rule.error(t,r)}},t.attribute=function(){var e=[],t=this.currToken;this.position++;while(this.position<this.tokens.length&&this.currToken[y.FIELDS.TYPE]!==w.closeSquare)e.push(this.currToken),this.position++;if(this.currToken[y.FIELDS.TYPE]!==w.closeSquare)return this.expected("closing square bracket",this.currToken[y.FIELDS.START_POS]);var r=e.length,s={source:M(t[1],t[2],this.currToken[3],this.currToken[4]),sourceIndex:t[y.FIELDS.START_POS]};if(1===r&&!~[w.word].indexOf(e[0][y.FIELDS.TYPE]))return this.expected("attribute",e[0][y.FIELDS.START_POS]);var i=0,n="",o="",a=null,l=!1;while(i<r){var c=e[i],u=this.content(c),h=e[i+1];switch(c[y.FIELDS.TYPE]){case w.space:if(l=!0,this.options.lossy)break;if(a){(0,C.ensureObject)(s,"spaces",a);var p=s.spaces[a].after||"";s.spaces[a].after=p+u;var d=(0,C.getProp)(s,"raws","spaces",a,"after")||null;d&&(s.raws.spaces[a].after=d+u)}else n+=u,o+=u;break;case w.asterisk:if(h[y.FIELDS.TYPE]===w.equals)s.operator=u,a="operator";else if((!s.namespace||"namespace"===a&&!l)&&h){n&&((0,C.ensureObject)(s,"spaces","attribute"),s.spaces.attribute.before=n,n=""),o&&((0,C.ensureObject)(s,"raws","spaces","attribute"),s.raws.spaces.attribute.before=n,o=""),s.namespace=(s.namespace||"")+u;var m=(0,C.getProp)(s,"raws","namespace")||null;m&&(s.raws.namespace+=u),a="namespace"}l=!1;break;case w.dollar:if("value"===a){var g=(0,C.getProp)(s,"raws","value");s.value+="$",g&&(s.raws.value=g+"$");break}case w.caret:h[y.FIELDS.TYPE]===w.equals&&(s.operator=u,a="operator"),l=!1;break;case w.combinator:if("~"===u&&h[y.FIELDS.TYPE]===w.equals&&(s.operator=u,a="operator"),"|"!==u){l=!1;break}h[y.FIELDS.TYPE]===w.equals?(s.operator=u,a="operator"):s.namespace||s.attribute||(s.namespace=!0),l=!1;break;case w.word:if(h&&"|"===this.content(h)&&e[i+2]&&e[i+2][y.FIELDS.TYPE]!==w.equals&&!s.operator&&!s.namespace)s.namespace=u,a="namespace";else if(!s.attribute||"attribute"===a&&!l){n&&((0,C.ensureObject)(s,"spaces","attribute"),s.spaces.attribute.before=n,n=""),o&&((0,C.ensureObject)(s,"raws","spaces","attribute"),s.raws.spaces.attribute.before=o,o=""),s.attribute=(s.attribute||"")+u;var v=(0,C.getProp)(s,"raws","attribute")||null;v&&(s.raws.attribute+=u),a="attribute"}else if(!s.value&&""!==s.value||"value"===a&&!l&&!s.quoteMark){var b=(0,C.unesc)(u),k=(0,C.getProp)(s,"raws","value")||"",_=s.value||"";s.value=_+b,s.quoteMark=null,(b!==u||k)&&((0,C.ensureObject)(s,"raws"),s.raws.value=(k||_)+u),a="value"}else{var S="i"===u||"I"===u;!s.value&&""!==s.value||!s.quoteMark&&!l?(s.value||""===s.value)&&(a="value",s.value+=u,s.raws.value&&(s.raws.value+=u)):(s.insensitive=S,S&&"I"!==u||((0,C.ensureObject)(s,"raws"),s.raws.insensitiveFlag=u),a="insensitive",n&&((0,C.ensureObject)(s,"spaces","insensitive"),s.spaces.insensitive.before=n,n=""),o&&((0,C.ensureObject)(s,"raws","spaces","insensitive"),s.raws.spaces.insensitive.before=o,o=""))}l=!1;break;case w.str:if(!s.attribute||!s.operator)return this.error("Expected an attribute followed by an operator preceding the string.",{index:c[y.FIELDS.START_POS]});var x=(0,f.unescapeValue)(u),O=x.unescaped,E=x.quoteMark;s.value=O,s.quoteMark=E,a="value",(0,C.ensureObject)(s,"raws"),s.raws.value=u,l=!1;break;case w.equals:if(!s.attribute)return this.expected("attribute",c[y.FIELDS.START_POS],u);if(s.value)return this.error('Unexpected "=" found; an operator was already defined.',{index:c[y.FIELDS.START_POS]});s.operator=s.operator?s.operator+u:u,a="operator",l=!1;break;case w.comment:if(a)if(l||h&&h[y.FIELDS.TYPE]===w.space||"insensitive"===a){var A=(0,C.getProp)(s,"spaces",a,"after")||"",T=(0,C.getProp)(s,"raws","spaces",a,"after")||A;(0,C.ensureObject)(s,"raws","spaces",a),s.raws.spaces[a].after=T+u}else{var P=s[a]||"",I=(0,C.getProp)(s,"raws",a)||P;(0,C.ensureObject)(s,"raws"),s.raws[a]=I+u}else o+=u;break;default:return this.error('Unexpected "'+u+'" found.',{index:c[y.FIELDS.START_POS]})}i++}F(s,"attribute"),F(s,"namespace"),this.newNode(new f["default"](s)),this.position++},t.parseWhitespaceEquivalentTokens=function(e){e<0&&(e=this.tokens.length);var t=this.position,r=[],s="",i=void 0;do{if(E[this.currToken[y.FIELDS.TYPE]])this.options.lossy||(s+=this.content());else if(this.currToken[y.FIELDS.TYPE]===w.comment){var n={};s&&(n.before=s,s=""),i=new l["default"]({value:this.content(),source:I(this.currToken),sourceIndex:this.currToken[y.FIELDS.START_POS],spaces:n}),r.push(i)}}while(++this.position<e);if(s)if(i)i.spaces.after=s;else if(!this.options.lossy){var o=this.tokens[t],a=this.tokens[this.position-1];r.push(new h["default"]({value:"",source:M(o[y.FIELDS.START_LINE],o[y.FIELDS.START_COL],a[y.FIELDS.END_LINE],a[y.FIELDS.END_COL]),sourceIndex:o[y.FIELDS.START_POS],spaces:{before:s,after:""}}))}return r},t.convertWhitespaceNodesToSpace=function(e,t){var r=this;void 0===t&&(t=!1);var s="",i="";e.forEach((function(e){var n=r.lossySpace(e.spaces.before,t),o=r.lossySpace(e.rawSpaceBefore,t);s+=n+r.lossySpace(e.spaces.after,t&&0===n.length),i+=n+e.value+r.lossySpace(e.rawSpaceAfter,t&&0===o.length)})),i===s&&(i=void 0);var n={space:s,rawSpace:i};return n},t.isNamedCombinator=function(e){return void 0===e&&(e=this.position),this.tokens[e+0]&&this.tokens[e+0][y.FIELDS.TYPE]===w.slash&&this.tokens[e+1]&&this.tokens[e+1][y.FIELDS.TYPE]===w.word&&this.tokens[e+2]&&this.tokens[e+2][y.FIELDS.TYPE]===w.slash},t.namedCombinator=function(){if(this.isNamedCombinator()){var e=this.content(this.tokens[this.position+1]),t=(0,C.unesc)(e).toLowerCase(),r={};t!==e&&(r.value="/"+e+"/");var s=new m["default"]({value:"/"+t+"/",source:M(this.currToken[y.FIELDS.START_LINE],this.currToken[y.FIELDS.START_COL],this.tokens[this.position+2][y.FIELDS.END_LINE],this.tokens[this.position+2][y.FIELDS.END_COL]),sourceIndex:this.currToken[y.FIELDS.START_POS],raws:r});return this.position=this.position+3,s}this.unexpected()},t.combinator=function(){var e=this;if("|"===this.content())return this.namespace();var t=this.locateNextMeaningfulToken(this.position);if(!(t<0||this.tokens[t][y.FIELDS.TYPE]===w.comma)){var r,s=this.currToken,i=void 0;if(t>this.position&&(i=this.parseWhitespaceEquivalentTokens(t)),this.isNamedCombinator()?r=this.namedCombinator():this.currToken[y.FIELDS.TYPE]===w.combinator?(r=new m["default"]({value:this.content(),source:I(this.currToken),sourceIndex:this.currToken[y.FIELDS.START_POS]}),this.position++):E[this.currToken[y.FIELDS.TYPE]]||i||this.unexpected(),r){if(i){var n=this.convertWhitespaceNodesToSpace(i),o=n.space,a=n.rawSpace;r.spaces.before=o,r.rawSpaceBefore=a}}else{var l=this.convertWhitespaceNodesToSpace(i,!0),c=l.space,u=l.rawSpace;u||(u=c);var h={},p={spaces:{}};c.endsWith(" ")&&u.endsWith(" ")?(h.before=c.slice(0,c.length-1),p.spaces.before=u.slice(0,u.length-1)):c.startsWith(" ")&&u.startsWith(" ")?(h.after=c.slice(1),p.spaces.after=u.slice(1)):p.value=u,r=new m["default"]({value:" ",source:L(s,this.tokens[this.position-1]),sourceIndex:s[y.FIELDS.START_POS],spaces:h,raws:p})}return this.currToken&&this.currToken[y.FIELDS.TYPE]===w.space&&(r.spaces.after=this.optionalSpace(this.content()),this.position++),this.newNode(r)}var f=this.parseWhitespaceEquivalentTokens(t);if(f.length>0){var d=this.current.last;if(d){var g=this.convertWhitespaceNodesToSpace(f),v=g.space,b=g.rawSpace;void 0!==b&&(d.rawSpaceAfter+=b),d.spaces.after+=v}else f.forEach((function(t){return e.newNode(t)}))}},t.comma=function(){if(this.position===this.tokens.length-1)return this.root.trailingComma=!0,void this.position++;this.current._inferEndPosition();var e=new o["default"]({source:{start:T(this.tokens[this.position+1])}});this.current.parent.append(e),this.current=e,this.position++},t.comment=function(){var e=this.currToken;this.newNode(new l["default"]({value:this.content(),source:I(e),sourceIndex:e[y.FIELDS.START_POS]})),this.position++},t.error=function(e,t){throw this.root.error(e,t)},t.missingBackslash=function(){return this.error("Expected a backslash preceding the semicolon.",{index:this.currToken[y.FIELDS.START_POS]})},t.missingParenthesis=function(){return this.expected("opening parenthesis",this.currToken[y.FIELDS.START_POS])},t.missingSquareBracket=function(){return this.expected("opening square bracket",this.currToken[y.FIELDS.START_POS])},t.unexpected=function(){return this.error("Unexpected '"+this.content()+"'. Escaping special characters with \\ may help.",this.currToken[y.FIELDS.START_POS])},t.unexpectedPipe=function(){return this.error("Unexpected '|'.",this.currToken[y.FIELDS.START_POS])},t.namespace=function(){var e=this.prevToken&&this.content(this.prevToken)||!0;return this.nextToken[y.FIELDS.TYPE]===w.word?(this.position++,this.word(e)):this.nextToken[y.FIELDS.TYPE]===w.asterisk?(this.position++,this.universal(e)):void this.unexpectedPipe()},t.nesting=function(){if(this.nextToken){var e=this.content(this.nextToken);if("|"===e)return void this.position++}var t=this.currToken;this.newNode(new g["default"]({value:this.content(),source:I(t),sourceIndex:t[y.FIELDS.START_POS]})),this.position++},t.parentheses=function(){var e=this.current.last,t=1;if(this.position++,e&&e.type===b.PSEUDO){var r=new o["default"]({source:{start:T(this.tokens[this.position-1])}}),s=this.current;e.append(r),this.current=r;while(this.position<this.tokens.length&&t)this.currToken[y.FIELDS.TYPE]===w.openParenthesis&&t++,this.currToken[y.FIELDS.TYPE]===w.closeParenthesis&&t--,t?this.parse():(this.current.source.end=P(this.currToken),this.current.parent.source.end=P(this.currToken),this.position++);this.current=s}else{var i,n=this.currToken,a="(";while(this.position<this.tokens.length&&t)this.currToken[y.FIELDS.TYPE]===w.openParenthesis&&t++,this.currToken[y.FIELDS.TYPE]===w.closeParenthesis&&t--,i=this.currToken,a+=this.parseParenthesisToken(this.currToken),this.position++;e?e.appendToPropertyAndEscape("value",a,a):this.newNode(new h["default"]({value:a,source:M(n[y.FIELDS.START_LINE],n[y.FIELDS.START_COL],i[y.FIELDS.END_LINE],i[y.FIELDS.END_COL]),sourceIndex:n[y.FIELDS.START_POS]}))}if(t)return this.expected("closing parenthesis",this.currToken[y.FIELDS.START_POS])},t.pseudo=function(){var e=this,t="",r=this.currToken;while(this.currToken&&this.currToken[y.FIELDS.TYPE]===w.colon)t+=this.content(),this.position++;return this.currToken?this.currToken[y.FIELDS.TYPE]!==w.word?this.expected(["pseudo-class","pseudo-element"],this.currToken[y.FIELDS.START_POS]):void this.splitWord(!1,(function(s,i){t+=s,e.newNode(new p["default"]({value:t,source:L(r,e.currToken),sourceIndex:r[y.FIELDS.START_POS]})),i>1&&e.nextToken&&e.nextToken[y.FIELDS.TYPE]===w.openParenthesis&&e.error("Misplaced parenthesis.",{index:e.nextToken[y.FIELDS.START_POS]})})):this.expected(["pseudo-class","pseudo-element"],this.position-1)},t.space=function(){var e=this.content();0===this.position||this.prevToken[y.FIELDS.TYPE]===w.comma||this.prevToken[y.FIELDS.TYPE]===w.openParenthesis||this.current.nodes.every((function(e){return"comment"===e.type}))?(this.spaces=this.optionalSpace(e),this.position++):this.position===this.tokens.length-1||this.nextToken[y.FIELDS.TYPE]===w.comma||this.nextToken[y.FIELDS.TYPE]===w.closeParenthesis?(this.current.last.spaces.after=this.optionalSpace(e),this.position++):this.combinator()},t.string=function(){var e=this.currToken;this.newNode(new h["default"]({value:this.content(),source:I(e),sourceIndex:e[y.FIELDS.START_POS]})),this.position++},t.universal=function(e){var t=this.nextToken;if(t&&"|"===this.content(t))return this.position++,this.namespace();var r=this.currToken;this.newNode(new d["default"]({value:this.content(),source:I(r),sourceIndex:r[y.FIELDS.START_POS]}),e),this.position++},t.splitWord=function(e,t){var r=this,s=this.nextToken,i=this.content();while(s&&~[w.dollar,w.caret,w.equals,w.word].indexOf(s[y.FIELDS.TYPE])){this.position++;var n=this.content();if(i+=n,n.lastIndexOf("\\")===n.length-1){var o=this.nextToken;o&&o[y.FIELDS.TYPE]===w.space&&(i+=this.requiredSpace(this.content(o)),this.position++)}s=this.nextToken}var l=D(i,".").filter((function(e){var t="\\"===i[e-1],r=/^\d+\.\d+%$/.test(i);return!t&&!r})),h=D(i,"#").filter((function(e){return"\\"!==i[e-1]})),p=D(i,"#{");p.length&&(h=h.filter((function(e){return!~p.indexOf(e)})));var f=(0,v["default"])(R([0].concat(l,h)));f.forEach((function(s,n){var o,p=f[n+1]||i.length,d=i.slice(s,p);if(0===n&&t)return t.call(r,d,f.length);var m=r.currToken,g=m[y.FIELDS.START_POS]+f[n],v=M(m[1],m[2]+s,m[3],m[2]+(p-1));if(~l.indexOf(s)){var w={value:d.slice(1),source:v,sourceIndex:g};o=new a["default"](F(w,"value"))}else if(~h.indexOf(s)){var b={value:d.slice(1),source:v,sourceIndex:g};o=new c["default"](F(b,"value"))}else{var C={value:d,source:v,sourceIndex:g};F(C,"value"),o=new u["default"](C)}r.newNode(o,e),e=null})),this.position++},t.word=function(e){var t=this.nextToken;return t&&"|"===this.content(t)?(this.position++,this.namespace()):this.splitWord(e)},t.loop=function(){while(this.position<this.tokens.length)this.parse(!0);return this.current._inferEndPosition(),this.root},t.parse=function(e){switch(this.currToken[y.FIELDS.TYPE]){case w.space:this.space();break;case w.comment:this.comment();break;case w.openParenthesis:this.parentheses();break;case w.closeParenthesis:e&&this.missingParenthesis();break;case w.openSquare:this.attribute();break;case w.dollar:case w.caret:case w.equals:case w.word:this.word();break;case w.colon:this.pseudo();break;case w.comma:this.comma();break;case w.asterisk:this.universal();break;case w.ampersand:this.nesting();break;case w.slash:case w.combinator:this.combinator();break;case w.str:this.string();break;case w.closeSquare:this.missingSquareBracket();case w.semicolon:this.missingBackslash();default:this.unexpected()}},t.expected=function(e,t,r){if(Array.isArray(e)){var s=e.pop();e=e.join(", ")+" or "+s}var i=/^[aeiou]/.test(e[0])?"an":"a";return r?this.error("Expected "+i+" "+e+', found "'+r+'" instead.',{index:t}):this.error("Expected "+i+" "+e+".",{index:t})},t.requiredSpace=function(e){return this.options.lossy?" ":e},t.optionalSpace=function(e){return this.options.lossy?"":e},t.lossySpace=function(e,t){return this.options.lossy?t?" ":"":e},t.parseParenthesisToken=function(e){var t=this.content(e);return e[y.FIELDS.TYPE]===w.space?this.requiredSpace(t):t},t.newNode=function(e,t){return t&&(/^ +$/.test(t)&&(this.options.lossy||(this.spaces=(this.spaces||"")+t),t=!0),e.namespace=t,F(e,"namespace")),this.spaces&&(e.spaces.before=this.spaces,this.spaces=""),this.current.append(e)},t.content=function(e){return void 0===e&&(e=this.currToken),this.css.slice(e[y.FIELDS.START_POS],e[y.FIELDS.END_POS])},t.locateNextMeaningfulToken=function(e){void 0===e&&(e=this.position+1);var t=e;while(t<this.tokens.length){if(!A[this.tokens[t][y.FIELDS.TYPE]])return t;t++}return-1},O(e,[{key:"currToken",get:function(){return this.tokens[this.position]}},{key:"nextToken",get:function(){return this.tokens[this.position+1]}},{key:"prevToken",get:function(){return this.tokens[this.position-1]}}]),e}();t["default"]=N,e.exports=t.default},90953:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=i(r(90534));function i(e){return e&&e.__esModule?e:{default:e}}var n=function(){function e(e,t){this.func=e||function(){},this.funcRes=null,this.options=t}var t=e.prototype;return t._shouldUpdateSelector=function(e,t){void 0===t&&(t={});var r=Object.assign({},this.options,t);return!1!==r.updateSelector&&"string"!==typeof e},t._isLossy=function(e){void 0===e&&(e={});var t=Object.assign({},this.options,e);return!1===t.lossless},t._root=function(e,t){void 0===t&&(t={});var r=new s["default"](e,this._parseOptions(t));return r.root},t._parseOptions=function(e){return{lossy:this._isLossy(e)}},t._run=function(e,t){var r=this;return void 0===t&&(t={}),new Promise((function(s,i){try{var n=r._root(e,t);Promise.resolve(r.func(n)).then((function(s){var i=void 0;return r._shouldUpdateSelector(e,t)&&(i=n.toString(),e.selector=i),{transform:s,root:n,string:i}})).then(s,i)}catch(o){return void i(o)}}))},t._runSync=function(e,t){void 0===t&&(t={});var r=this._root(e,t),s=this.func(r);if(s&&"function"===typeof s.then)throw new Error("Selector processor returned a promise to a synchronous call.");var i=void 0;return t.updateSelector&&"string"!==typeof e&&(i=r.toString(),e.selector=i),{transform:s,root:r,string:i}},t.ast=function(e,t){return this._run(e,t).then((function(e){return e.root}))},t.astSync=function(e,t){return this._runSync(e,t).root},t.transform=function(e,t){return this._run(e,t).then((function(e){return e.transform}))},t.transformSync=function(e,t){return this._runSync(e,t).transform},t.process=function(e,t){return this._run(e,t).then((function(e){return e.string||e.root.toString()}))},t.processSync=function(e,t){var r=this._runSync(e,t);return r.string||r.root.toString()},e}();t["default"]=n,e.exports=t.default},86960:function(e,t,r){"use strict";r(44114),t.__esModule=!0,t["default"]=void 0,t.unescapeValue=y;var s,i=l(r(75564)),n=l(r(45690)),o=l(r(11875)),a=r(26785);function l(e){return e&&e.__esModule?e:{default:e}}function c(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function u(e,t,r){return t&&c(e.prototype,t),r&&c(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function h(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,p(e,t)}function p(e,t){return p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},p(e,t)}var f=r(23404),d=/^('|")([^]*)\1$/,m=f((function(){}),"Assigning an attribute a value containing characters that might need to be escaped is deprecated. Call attribute.setValue() instead."),g=f((function(){}),"Assigning attr.quoted is deprecated and has no effect. Assign to attr.quoteMark instead."),v=f((function(){}),"Constructing an Attribute selector with a value without specifying quoteMark is deprecated. Note: The value should be unescaped now.");function y(e){var t=!1,r=null,s=e,i=s.match(d);return i&&(r=i[1],s=i[2]),s=(0,n["default"])(s),s!==e&&(t=!0),{deprecatedUsage:t,unescaped:s,quoteMark:r}}function w(e){if(void 0!==e.quoteMark)return e;if(void 0===e.value)return e;v();var t=y(e.value),r=t.quoteMark,s=t.unescaped;return e.raws||(e.raws={}),void 0===e.raws.value&&(e.raws.value=e.value),e.value=s,e.quoteMark=r,e}var b=function(e){function t(t){var r;return void 0===t&&(t={}),r=e.call(this,w(t))||this,r.type=a.ATTRIBUTE,r.raws=r.raws||{},Object.defineProperty(r.raws,"unquoted",{get:f((function(){return r.value}),"attr.raws.unquoted is deprecated. Call attr.value instead."),set:f((function(){return r.value}),"Setting attr.raws.unquoted is deprecated and has no effect. attr.value is unescaped by default now.")}),r._constructed=!0,r}h(t,e);var r=t.prototype;return r.getQuotedValue=function(e){void 0===e&&(e={});var t=this._determineQuoteMark(e),r=C[t],s=(0,i["default"])(this._value,r);return s},r._determineQuoteMark=function(e){return e.smart?this.smartQuoteMark(e):this.preferredQuoteMark(e)},r.setValue=function(e,t){void 0===t&&(t={}),this._value=e,this._quoteMark=this._determineQuoteMark(t),this._syncRawValue()},r.smartQuoteMark=function(e){var r=this.value,s=r.replace(/[^']/g,"").length,n=r.replace(/[^"]/g,"").length;if(s+n===0){var o=(0,i["default"])(r,{isIdentifier:!0});if(o===r)return t.NO_QUOTE;var a=this.preferredQuoteMark(e);if(a===t.NO_QUOTE){var l=this.quoteMark||e.quoteMark||t.DOUBLE_QUOTE,c=C[l],u=(0,i["default"])(r,c);if(u.length<o.length)return l}return a}return n===s?this.preferredQuoteMark(e):n<s?t.DOUBLE_QUOTE:t.SINGLE_QUOTE},r.preferredQuoteMark=function(e){var r=e.preferCurrentQuoteMark?this.quoteMark:e.quoteMark;return void 0===r&&(r=e.preferCurrentQuoteMark?e.quoteMark:this.quoteMark),void 0===r&&(r=t.DOUBLE_QUOTE),r},r._syncRawValue=function(){var e=(0,i["default"])(this._value,C[this.quoteMark]);e===this._value?this.raws&&delete this.raws.value:this.raws.value=e},r._handleEscapes=function(e,t){if(this._constructed){var r=(0,i["default"])(t,{isIdentifier:!0});r!==t?this.raws[e]=r:delete this.raws[e]}},r._spacesFor=function(e){var t={before:"",after:""},r=this.spaces[e]||{},s=this.raws.spaces&&this.raws.spaces[e]||{};return Object.assign(t,r,s)},r._stringFor=function(e,t,r){void 0===t&&(t=e),void 0===r&&(r=k);var s=this._spacesFor(t);return r(this.stringifyProperty(e),s)},r.offsetOf=function(e){var t=1,r=this._spacesFor("attribute");if(t+=r.before.length,"namespace"===e||"ns"===e)return this.namespace?t:-1;if("attributeNS"===e)return t;if(t+=this.namespaceString.length,this.namespace&&(t+=1),"attribute"===e)return t;t+=this.stringifyProperty("attribute").length,t+=r.after.length;var s=this._spacesFor("operator");t+=s.before.length;var i=this.stringifyProperty("operator");if("operator"===e)return i?t:-1;t+=i.length,t+=s.after.length;var n=this._spacesFor("value");t+=n.before.length;var o=this.stringifyProperty("value");if("value"===e)return o?t:-1;t+=o.length,t+=n.after.length;var a=this._spacesFor("insensitive");return t+=a.before.length,"insensitive"===e&&this.insensitive?t:-1},r.toString=function(){var e=this,t=[this.rawSpaceBefore,"["];return t.push(this._stringFor("qualifiedAttribute","attribute")),this.operator&&(this.value||""===this.value)&&(t.push(this._stringFor("operator")),t.push(this._stringFor("value")),t.push(this._stringFor("insensitiveFlag","insensitive",(function(t,r){return!(t.length>0)||e.quoted||0!==r.before.length||e.spaces.value&&e.spaces.value.after||(r.before=" "),k(t,r)})))),t.push("]"),t.push(this.rawSpaceAfter),t.join("")},u(t,[{key:"quoted",get:function(){var e=this.quoteMark;return"'"===e||'"'===e},set:function(e){g()}},{key:"quoteMark",get:function(){return this._quoteMark},set:function(e){this._constructed?this._quoteMark!==e&&(this._quoteMark=e,this._syncRawValue()):this._quoteMark=e}},{key:"qualifiedAttribute",get:function(){return this.qualifiedName(this.raws.attribute||this.attribute)}},{key:"insensitiveFlag",get:function(){return this.insensitive?"i":""}},{key:"value",get:function(){return this._value},set:function(e){if(this._constructed){var t=y(e),r=t.deprecatedUsage,s=t.unescaped,i=t.quoteMark;if(r&&m(),s===this._value&&i===this._quoteMark)return;this._value=s,this._quoteMark=i,this._syncRawValue()}else this._value=e}},{key:"insensitive",get:function(){return this._insensitive},set:function(e){e||(this._insensitive=!1,!this.raws||"I"!==this.raws.insensitiveFlag&&"i"!==this.raws.insensitiveFlag||(this.raws.insensitiveFlag=void 0)),this._insensitive=e}},{key:"attribute",get:function(){return this._attribute},set:function(e){this._handleEscapes("attribute",e),this._attribute=e}}]),t}(o["default"]);t["default"]=b,b.NO_QUOTE=null,b.SINGLE_QUOTE="'",b.DOUBLE_QUOTE='"';var C=(s={"'":{quotes:"single",wrap:!0},'"':{quotes:"double",wrap:!0}},s[null]={isIdentifier:!0},s);function k(e,t){return""+t.before+e+t.after}},21351:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=a(r(75564)),i=r(85706),n=a(r(56402)),o=r(26785);function a(e){return e&&e.__esModule?e:{default:e}}function l(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function c(e,t,r){return t&&l(e.prototype,t),r&&l(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function u(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,h(e,t)}function h(e,t){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},h(e,t)}var p=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=o.CLASS,r._constructed=!0,r}u(t,e);var r=t.prototype;return r.valueToString=function(){return"."+e.prototype.valueToString.call(this)},c(t,[{key:"value",get:function(){return this._value},set:function(e){if(this._constructed){var t=(0,s["default"])(e,{isIdentifier:!0});t!==e?((0,i.ensureObject)(this,"raws"),this.raws.value=t):this.raws&&delete this.raws.value}this._value=e}}]),t}(n["default"]);t["default"]=p,e.exports=t.default},30540:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=n(r(56402)),i=r(26785);function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,a(e,t)}function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}var l=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=i.COMBINATOR,r}return o(t,e),t}(s["default"]);t["default"]=l,e.exports=t.default},86685:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=n(r(56402)),i=r(26785);function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,a(e,t)}function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}var l=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=i.COMMENT,r}return o(t,e),t}(s["default"]);t["default"]=l,e.exports=t.default},11247:function(e,t,r){"use strict";t.__esModule=!0,t.universal=t.tag=t.string=t.selector=t.root=t.pseudo=t.nesting=t.id=t.comment=t.combinator=t.className=t.attribute=void 0;var s=m(r(86960)),i=m(r(21351)),n=m(r(30540)),o=m(r(86685)),a=m(r(17379)),l=m(r(66610)),c=m(r(38424)),u=m(r(84522)),h=m(r(65671)),p=m(r(24747)),f=m(r(44284)),d=m(r(97201));function m(e){return e&&e.__esModule?e:{default:e}}var g=function(e){return new s["default"](e)};t.attribute=g;var v=function(e){return new i["default"](e)};t.className=v;var y=function(e){return new n["default"](e)};t.combinator=y;var w=function(e){return new o["default"](e)};t.comment=w;var b=function(e){return new a["default"](e)};t.id=b;var C=function(e){return new l["default"](e)};t.nesting=C;var k=function(e){return new c["default"](e)};t.pseudo=k;var _=function(e){return new u["default"](e)};t.root=_;var S=function(e){return new h["default"](e)};t.selector=S;var x=function(e){return new p["default"](e)};t.string=x;var O=function(e){return new f["default"](e)};t.tag=O;var E=function(e){return new d["default"](e)};t.universal=E},23259:function(e,t,r){"use strict";r(44114),r(98992),r(23215),r(54520),r(81454),r(8872),r(37550),t.__esModule=!0,t["default"]=void 0;var s=a(r(56402)),i=o(r(26785));function n(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function o(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==typeof e&&"function"!==typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var s={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(s,o,a):s[o]=e[o]}return s["default"]=e,r&&r.set(e,s),s}function a(e){return e&&e.__esModule?e:{default:e}}function l(e,t){var r="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(r)return(r=r.call(e)).next.bind(r);if(Array.isArray(e)||(r=c(e))||t&&e&&"number"===typeof e.length){r&&(e=r);var s=0;return function(){return s>=e.length?{done:!0}:{done:!1,value:e[s++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function c(e,t){if(e){if("string"===typeof e)return u(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(e,t):void 0}}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,s=new Array(t);r<t;r++)s[r]=e[r];return s}function h(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function p(e,t,r){return t&&h(e.prototype,t),r&&h(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function f(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,d(e,t)}function d(e,t){return d=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},d(e,t)}var m=function(e){function t(t){var r;return r=e.call(this,t)||this,r.nodes||(r.nodes=[]),r}f(t,e);var r=t.prototype;return r.append=function(e){return e.parent=this,this.nodes.push(e),this},r.prepend=function(e){return e.parent=this,this.nodes.unshift(e),this},r.at=function(e){return this.nodes[e]},r.index=function(e){return"number"===typeof e?e:this.nodes.indexOf(e)},r.removeChild=function(e){var t;for(var r in e=this.index(e),this.at(e).parent=void 0,this.nodes.splice(e,1),this.indexes)t=this.indexes[r],t>=e&&(this.indexes[r]=t-1);return this},r.removeAll=function(){for(var e,t=l(this.nodes);!(e=t()).done;){var r=e.value;r.parent=void 0}return this.nodes=[],this},r.empty=function(){return this.removeAll()},r.insertAfter=function(e,t){t.parent=this;var r,s=this.index(e);for(var i in this.nodes.splice(s+1,0,t),t.parent=this,this.indexes)r=this.indexes[i],s<=r&&(this.indexes[i]=r+1);return this},r.insertBefore=function(e,t){t.parent=this;var r,s=this.index(e);for(var i in this.nodes.splice(s,0,t),t.parent=this,this.indexes)r=this.indexes[i],r<=s&&(this.indexes[i]=r+1);return this},r._findChildAtPosition=function(e,t){var r=void 0;return this.each((function(s){if(s.atPosition){var i=s.atPosition(e,t);if(i)return r=i,!1}else if(s.isAtPosition(e,t))return r=s,!1})),r},r.atPosition=function(e,t){return this.isAtPosition(e,t)?this._findChildAtPosition(e,t)||this:void 0},r._inferEndPosition=function(){this.last&&this.last.source&&this.last.source.end&&(this.source=this.source||{},this.source.end=this.source.end||{},Object.assign(this.source.end,this.last.source.end))},r.each=function(e){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach++;var t=this.lastEach;if(this.indexes[t]=0,this.length){var r,s;while(this.indexes[t]<this.length){if(r=this.indexes[t],s=e(this.at(r),r),!1===s)break;this.indexes[t]+=1}return delete this.indexes[t],!1!==s&&void 0}},r.walk=function(e){return this.each((function(t,r){var s=e(t,r);if(!1!==s&&t.length&&(s=t.walk(e)),!1===s)return!1}))},r.walkAttributes=function(e){var t=this;return this.walk((function(r){if(r.type===i.ATTRIBUTE)return e.call(t,r)}))},r.walkClasses=function(e){var t=this;return this.walk((function(r){if(r.type===i.CLASS)return e.call(t,r)}))},r.walkCombinators=function(e){var t=this;return this.walk((function(r){if(r.type===i.COMBINATOR)return e.call(t,r)}))},r.walkComments=function(e){var t=this;return this.walk((function(r){if(r.type===i.COMMENT)return e.call(t,r)}))},r.walkIds=function(e){var t=this;return this.walk((function(r){if(r.type===i.ID)return e.call(t,r)}))},r.walkNesting=function(e){var t=this;return this.walk((function(r){if(r.type===i.NESTING)return e.call(t,r)}))},r.walkPseudos=function(e){var t=this;return this.walk((function(r){if(r.type===i.PSEUDO)return e.call(t,r)}))},r.walkTags=function(e){var t=this;return this.walk((function(r){if(r.type===i.TAG)return e.call(t,r)}))},r.walkUniversals=function(e){var t=this;return this.walk((function(r){if(r.type===i.UNIVERSAL)return e.call(t,r)}))},r.split=function(e){var t=this,r=[];return this.reduce((function(s,i,n){var o=e.call(t,i);return r.push(i),o?(s.push(r),r=[]):n===t.length-1&&s.push(r),s}),[])},r.map=function(e){return this.nodes.map(e)},r.reduce=function(e,t){return this.nodes.reduce(e,t)},r.every=function(e){return this.nodes.every(e)},r.some=function(e){return this.nodes.some(e)},r.filter=function(e){return this.nodes.filter(e)},r.sort=function(e){return this.nodes.sort(e)},r.toString=function(){return this.map(String).join("")},p(t,[{key:"first",get:function(){return this.at(0)}},{key:"last",get:function(){return this.at(this.length-1)}},{key:"length",get:function(){return this.nodes.length}}]),t}(s["default"]);t["default"]=m,e.exports=t.default},78056:function(e,t,r){"use strict";t.__esModule=!0,t.isComment=t.isCombinator=t.isClassName=t.isAttribute=void 0,t.isContainer=k,t.isIdentifier=void 0,t.isNamespace=_,t.isNesting=void 0,t.isNode=o,t.isPseudo=void 0,t.isPseudoClass=C,t.isPseudoElement=b,t.isUniversal=t.isTag=t.isString=t.isSelector=t.isRoot=void 0;var s,i=r(26785),n=(s={},s[i.ATTRIBUTE]=!0,s[i.CLASS]=!0,s[i.COMBINATOR]=!0,s[i.COMMENT]=!0,s[i.ID]=!0,s[i.NESTING]=!0,s[i.PSEUDO]=!0,s[i.ROOT]=!0,s[i.SELECTOR]=!0,s[i.STRING]=!0,s[i.TAG]=!0,s[i.UNIVERSAL]=!0,s);function o(e){return"object"===typeof e&&n[e.type]}function a(e,t){return o(t)&&t.type===e}var l=a.bind(null,i.ATTRIBUTE);t.isAttribute=l;var c=a.bind(null,i.CLASS);t.isClassName=c;var u=a.bind(null,i.COMBINATOR);t.isCombinator=u;var h=a.bind(null,i.COMMENT);t.isComment=h;var p=a.bind(null,i.ID);t.isIdentifier=p;var f=a.bind(null,i.NESTING);t.isNesting=f;var d=a.bind(null,i.PSEUDO);t.isPseudo=d;var m=a.bind(null,i.ROOT);t.isRoot=m;var g=a.bind(null,i.SELECTOR);t.isSelector=g;var v=a.bind(null,i.STRING);t.isString=v;var y=a.bind(null,i.TAG);t.isTag=y;var w=a.bind(null,i.UNIVERSAL);function b(e){return d(e)&&e.value&&(e.value.startsWith("::")||":before"===e.value.toLowerCase()||":after"===e.value.toLowerCase()||":first-letter"===e.value.toLowerCase()||":first-line"===e.value.toLowerCase())}function C(e){return d(e)&&!b(e)}function k(e){return!(!o(e)||!e.walk)}function _(e){return l(e)||y(e)}t.isUniversal=w},17379:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=n(r(56402)),i=r(26785);function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,a(e,t)}function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}var l=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=i.ID,r}o(t,e);var r=t.prototype;return r.valueToString=function(){return"#"+e.prototype.valueToString.call(this)},t}(s["default"]);t["default"]=l,e.exports=t.default},91620:function(e,t,r){"use strict";r(98992),r(3949),t.__esModule=!0;var s=r(26785);Object.keys(s).forEach((function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===s[e]||(t[e]=s[e]))}));var i=r(11247);Object.keys(i).forEach((function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===i[e]||(t[e]=i[e]))}));var n=r(78056);Object.keys(n).forEach((function(e){"default"!==e&&"__esModule"!==e&&(e in t&&t[e]===n[e]||(t[e]=n[e]))}))},11875:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=o(r(75564)),i=r(85706),n=o(r(56402));function o(e){return e&&e.__esModule?e:{default:e}}function a(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function l(e,t,r){return t&&a(e.prototype,t),r&&a(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function c(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,u(e,t)}function u(e,t){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},u(e,t)}var h=function(e){function t(){return e.apply(this,arguments)||this}c(t,e);var r=t.prototype;return r.qualifiedName=function(e){return this.namespace?this.namespaceString+"|"+e:e},r.valueToString=function(){return this.qualifiedName(e.prototype.valueToString.call(this))},l(t,[{key:"namespace",get:function(){return this._namespace},set:function(e){if(!0===e||"*"===e||"&"===e)return this._namespace=e,void(this.raws&&delete this.raws.namespace);var t=(0,s["default"])(e,{isIdentifier:!0});this._namespace=e,t!==e?((0,i.ensureObject)(this,"raws"),this.raws.namespace=t):this.raws&&delete this.raws.namespace}},{key:"ns",get:function(){return this._namespace},set:function(e){this.namespace=e}},{key:"namespaceString",get:function(){if(this.namespace){var e=this.stringifyProperty("namespace");return!0===e?"":e}return""}}]),t}(n["default"]);t["default"]=h,e.exports=t.default},66610:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=n(r(56402)),i=r(26785);function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,a(e,t)}function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}var l=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=i.NESTING,r.value="&",r}return o(t,e),t}(s["default"]);t["default"]=l,e.exports=t.default},56402:function(e,t,r){"use strict";r(81454),t.__esModule=!0,t["default"]=void 0;var s=r(85706);function i(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function n(e,t,r){return t&&i(e.prototype,t),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}var o=function e(t,r){if("object"!==typeof t||null===t)return t;var s=new t.constructor;for(var i in t)if(t.hasOwnProperty(i)){var n=t[i],o=typeof n;"parent"===i&&"object"===o?r&&(s[i]=r):s[i]=n instanceof Array?n.map((function(t){return e(t,s)})):e(n,s)}return s},a=function(){function e(e){void 0===e&&(e={}),Object.assign(this,e),this.spaces=this.spaces||{},this.spaces.before=this.spaces.before||"",this.spaces.after=this.spaces.after||""}var t=e.prototype;return t.remove=function(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this},t.replaceWith=function(){if(this.parent){for(var e in arguments)this.parent.insertBefore(this,arguments[e]);this.remove()}return this},t.next=function(){return this.parent.at(this.parent.index(this)+1)},t.prev=function(){return this.parent.at(this.parent.index(this)-1)},t.clone=function(e){void 0===e&&(e={});var t=o(this);for(var r in e)t[r]=e[r];return t},t.appendToPropertyAndEscape=function(e,t,r){this.raws||(this.raws={});var s=this[e],i=this.raws[e];this[e]=s+t,i||r!==t?this.raws[e]=(i||s)+r:delete this.raws[e]},t.setPropertyAndEscape=function(e,t,r){this.raws||(this.raws={}),this[e]=t,this.raws[e]=r},t.setPropertyWithoutEscape=function(e,t){this[e]=t,this.raws&&delete this.raws[e]},t.isAtPosition=function(e,t){if(this.source&&this.source.start&&this.source.end)return!(this.source.start.line>e)&&(!(this.source.end.line<e)&&(!(this.source.start.line===e&&this.source.start.column>t)&&!(this.source.end.line===e&&this.source.end.column<t)))},t.stringifyProperty=function(e){return this.raws&&this.raws[e]||this[e]},t.valueToString=function(){return String(this.stringifyProperty("value"))},t.toString=function(){return[this.rawSpaceBefore,this.valueToString(),this.rawSpaceAfter].join("")},n(e,[{key:"rawSpaceBefore",get:function(){var e=this.raws&&this.raws.spaces&&this.raws.spaces.before;return void 0===e&&(e=this.spaces&&this.spaces.before),e||""},set:function(e){(0,s.ensureObject)(this,"raws","spaces"),this.raws.spaces.before=e}},{key:"rawSpaceAfter",get:function(){var e=this.raws&&this.raws.spaces&&this.raws.spaces.after;return void 0===e&&(e=this.spaces.after),e||""},set:function(e){(0,s.ensureObject)(this,"raws","spaces"),this.raws.spaces.after=e}}]),e}();t["default"]=a,e.exports=t.default},38424:function(e,t,r){"use strict";r(81454),t.__esModule=!0,t["default"]=void 0;var s=n(r(23259)),i=r(26785);function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,a(e,t)}function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}var l=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=i.PSEUDO,r}o(t,e);var r=t.prototype;return r.toString=function(){var e=this.length?"("+this.map(String).join(",")+")":"";return[this.rawSpaceBefore,this.stringifyProperty("value"),e,this.rawSpaceAfter].join("")},t}(s["default"]);t["default"]=l,e.exports=t.default},84522:function(e,t,r){"use strict";r(44114),r(98992),r(8872),t.__esModule=!0,t["default"]=void 0;var s=n(r(23259)),i=r(26785);function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function a(e,t,r){return t&&o(e.prototype,t),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function l(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,c(e,t)}function c(e,t){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},c(e,t)}var u=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=i.ROOT,r}l(t,e);var r=t.prototype;return r.toString=function(){var e=this.reduce((function(e,t){return e.push(String(t)),e}),[]).join(",");return this.trailingComma?e+",":e},r.error=function(e,t){return this._error?this._error(e,t):new Error(e)},a(t,[{key:"errorGenerator",set:function(e){this._error=e}}]),t}(s["default"]);t["default"]=u,e.exports=t.default},65671:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=n(r(23259)),i=r(26785);function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,a(e,t)}function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}var l=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=i.SELECTOR,r}return o(t,e),t}(s["default"]);t["default"]=l,e.exports=t.default},24747:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=n(r(56402)),i=r(26785);function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,a(e,t)}function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}var l=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=i.STRING,r}return o(t,e),t}(s["default"]);t["default"]=l,e.exports=t.default},44284:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=n(r(11875)),i=r(26785);function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,a(e,t)}function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}var l=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=i.TAG,r}return o(t,e),t}(s["default"]);t["default"]=l,e.exports=t.default},26785:function(e,t){"use strict";t.__esModule=!0,t.UNIVERSAL=t.TAG=t.STRING=t.SELECTOR=t.ROOT=t.PSEUDO=t.NESTING=t.ID=t.COMMENT=t.COMBINATOR=t.CLASS=t.ATTRIBUTE=void 0;var r="tag";t.TAG=r;var s="string";t.STRING=s;var i="selector";t.SELECTOR=i;var n="root";t.ROOT=n;var o="pseudo";t.PSEUDO=o;var a="nesting";t.NESTING=a;var l="id";t.ID=l;var c="comment";t.COMMENT=c;var u="combinator";t.COMBINATOR=u;var h="class";t.CLASS=h;var p="attribute";t.ATTRIBUTE=p;var f="universal";t.UNIVERSAL=f},97201:function(e,t,r){"use strict";t.__esModule=!0,t["default"]=void 0;var s=n(r(11875)),i=r(26785);function n(e){return e&&e.__esModule?e:{default:e}}function o(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,a(e,t)}function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}var l=function(e){function t(t){var r;return r=e.call(this,t)||this,r.type=i.UNIVERSAL,r.value="*",r}return o(t,e),t}(s["default"]);t["default"]=l,e.exports=t.default},57299:function(e,t){"use strict";function r(e){return e.sort((function(e,t){return e-t}))}t.__esModule=!0,t["default"]=r,e.exports=t.default},49587:function(e,t){"use strict";t.__esModule=!0,t.word=t.tilde=t.tab=t.str=t.space=t.slash=t.singleQuote=t.semicolon=t.plus=t.pipe=t.openSquare=t.openParenthesis=t.newline=t.greaterThan=t.feed=t.equals=t.doubleQuote=t.dollar=t.cr=t.comment=t.comma=t.combinator=t.colon=t.closeSquare=t.closeParenthesis=t.caret=t.bang=t.backslash=t.at=t.asterisk=t.ampersand=void 0;var r=38;t.ampersand=r;var s=42;t.asterisk=s;var i=64;t.at=i;var n=44;t.comma=n;var o=58;t.colon=o;var a=59;t.semicolon=a;var l=40;t.openParenthesis=l;var c=41;t.closeParenthesis=c;var u=91;t.openSquare=u;var h=93;t.closeSquare=h;var p=36;t.dollar=p;var f=126;t.tilde=f;var d=94;t.caret=d;var m=43;t.plus=m;var g=61;t.equals=g;var v=124;t.pipe=v;var y=62;t.greaterThan=y;var w=32;t.space=w;var b=39;t.singleQuote=b;var C=34;t.doubleQuote=C;var k=47;t.slash=k;var _=33;t.bang=_;var S=92;t.backslash=S;var x=13;t.cr=x;var O=12;t.feed=O;var E=10;t.newline=E;var A=9;t.tab=A;var T=b;t.str=T;var P=-1;t.comment=P;var M=-2;t.word=M;var I=-3;t.combinator=I},72460:function(e,t,r){"use strict";r(44114),t.__esModule=!0,t.FIELDS=void 0,t["default"]=g;var s,i,n=a(r(49587));function o(e){if("function"!==typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}function a(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==typeof e&&"function"!==typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var s={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in e)if("default"!==n&&Object.prototype.hasOwnProperty.call(e,n)){var a=i?Object.getOwnPropertyDescriptor(e,n):null;a&&(a.get||a.set)?Object.defineProperty(s,n,a):s[n]=e[n]}return s["default"]=e,r&&r.set(e,s),s}for(var l=(s={},s[n.tab]=!0,s[n.newline]=!0,s[n.cr]=!0,s[n.feed]=!0,s),c=(i={},i[n.space]=!0,i[n.tab]=!0,i[n.newline]=!0,i[n.cr]=!0,i[n.feed]=!0,i[n.ampersand]=!0,i[n.asterisk]=!0,i[n.bang]=!0,i[n.comma]=!0,i[n.colon]=!0,i[n.semicolon]=!0,i[n.openParenthesis]=!0,i[n.closeParenthesis]=!0,i[n.openSquare]=!0,i[n.closeSquare]=!0,i[n.singleQuote]=!0,i[n.doubleQuote]=!0,i[n.plus]=!0,i[n.pipe]=!0,i[n.tilde]=!0,i[n.greaterThan]=!0,i[n.equals]=!0,i[n.dollar]=!0,i[n.caret]=!0,i[n.slash]=!0,i),u={},h="0123456789abcdefABCDEF",p=0;p<h.length;p++)u[h.charCodeAt(p)]=!0;function f(e,t){var r,s=t;do{if(r=e.charCodeAt(s),c[r])return s-1;r===n.backslash?s=d(e,s)+1:s++}while(s<e.length);return s-1}function d(e,t){var r=t,s=e.charCodeAt(r+1);if(l[s]);else if(u[s]){var i=0;do{r++,i++,s=e.charCodeAt(r+1)}while(u[s]&&i<6);i<6&&s===n.space&&r++}else r++;return r}var m={TYPE:0,START_LINE:1,START_COL:2,END_LINE:3,END_COL:4,START_POS:5,END_POS:6};function g(e){var t,r,s,i,o,a,l,c,u,h,p,d,m,g=[],v=e.css.valueOf(),y=v,w=y.length,b=-1,C=1,k=0,_=0;function S(t,r){if(!e.safe)throw e.error("Unclosed "+t,C,k-b,k);v+=r,u=v.length-1}while(k<w){switch(t=v.charCodeAt(k),t===n.newline&&(b=k,C+=1),t){case n.space:case n.tab:case n.newline:case n.cr:case n.feed:u=k;do{u+=1,t=v.charCodeAt(u),t===n.newline&&(b=u,C+=1)}while(t===n.space||t===n.newline||t===n.tab||t===n.cr||t===n.feed);m=n.space,i=C,s=u-b-1,_=u;break;case n.plus:case n.greaterThan:case n.tilde:case n.pipe:u=k;do{u+=1,t=v.charCodeAt(u)}while(t===n.plus||t===n.greaterThan||t===n.tilde||t===n.pipe);m=n.combinator,i=C,s=k-b,_=u;break;case n.asterisk:case n.ampersand:case n.bang:case n.comma:case n.equals:case n.dollar:case n.caret:case n.openSquare:case n.closeSquare:case n.colon:case n.semicolon:case n.openParenthesis:case n.closeParenthesis:u=k,m=t,i=C,s=k-b,_=u+1;break;case n.singleQuote:case n.doubleQuote:d=t===n.singleQuote?"'":'"',u=k;do{o=!1,u=v.indexOf(d,u+1),-1===u&&S("quote",d),a=u;while(v.charCodeAt(a-1)===n.backslash)a-=1,o=!o}while(o);m=n.str,i=C,s=k-b,_=u+1;break;default:t===n.slash&&v.charCodeAt(k+1)===n.asterisk?(u=v.indexOf("*/",k+2)+1,0===u&&S("comment","*/"),r=v.slice(k,u+1),c=r.split("\n"),l=c.length-1,l>0?(h=C+l,p=u-c[l].length):(h=C,p=b),m=n.comment,C=h,i=h,s=u-p):t===n.slash?(u=k,m=t,i=C,s=k-b,_=u+1):(u=f(v,k),m=n.word,i=C,s=u-b),_=u+1;break}g.push([m,C,k-b,i,s,k,_]),p&&(b=p,p=null),k=_}return g}t.FIELDS=m},44929:function(e,t){"use strict";function r(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];while(r.length>0){var i=r.shift();e[i]||(e[i]={}),e=e[i]}}t.__esModule=!0,t["default"]=r,e.exports=t.default},18895:function(e,t){"use strict";function r(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),s=1;s<t;s++)r[s-1]=arguments[s];while(r.length>0){var i=r.shift();if(!e[i])return;e=e[i]}return e}t.__esModule=!0,t["default"]=r,e.exports=t.default},85706:function(e,t,r){"use strict";t.__esModule=!0,t.unesc=t.stripComments=t.getProp=t.ensureObject=void 0;var s=a(r(45690));t.unesc=s["default"];var i=a(r(18895));t.getProp=i["default"];var n=a(r(44929));t.ensureObject=n["default"];var o=a(r(4150));function a(e){return e&&e.__esModule?e:{default:e}}t.stripComments=o["default"]},4150:function(e,t){"use strict";function r(e){var t="",r=e.indexOf("/*"),s=0;while(r>=0){t+=e.slice(s,r);var i=e.indexOf("*/",r+2);if(i<0)return t;s=i+2,r=e.indexOf("/*",s)}return t+=e.slice(s),t}t.__esModule=!0,t["default"]=r,e.exports=t.default},45690:function(e,t){"use strict";function r(e){for(var t=e.toLowerCase(),r="",s=!1,i=0;i<6&&void 0!==t[i];i++){var n=t.charCodeAt(i),o=n>=97&&n<=102||n>=48&&n<=57;if(s=32===n,!o)break;r+=t[i]}if(0!==r.length){var a=parseInt(r,16),l=a>=55296&&a<=57343;return l||0===a||a>1114111?["�",r.length+(s?1:0)]:[String.fromCodePoint(a),r.length+(s?1:0)]}}t.__esModule=!0,t["default"]=i;var s=/\\/;function i(e){var t=s.test(e);if(!t)return e;for(var i="",n=0;n<e.length;n++)if("\\"!==e[n])i+=e[n];else{var o=r(e.slice(n+1,n+7));if(void 0!==o){i+=o[0],n+=o[1];continue}if("\\"===e[n+1]){i+="\\",n++;continue}e.length===n+1&&(i+=e[n])}return i}e.exports=t.default},35241:function(e,t,r){"use strict";let s=r(29308);class i extends s{constructor(e){super(e),this.type="atrule"}append(...e){return this.proxyOf.nodes||(this.nodes=[]),super.append(...e)}prepend(...e){return this.proxyOf.nodes||(this.nodes=[]),super.prepend(...e)}}e.exports=i,i.default=i,s.registerAtRule(i)},88806:function(e,t,r){"use strict";let s=r(45867);class i extends s{constructor(e){super(e),this.type="comment"}}e.exports=i,i.default=i},29308:function(e,t,r){"use strict";r(44114),r(98992),r(23215),r(3949),r(81454),r(37550);let s,i,n,o,a=r(88806),l=r(59995),c=r(45867),{isClean:u,my:h}=r(57534);function p(e){return e.map((e=>(e.nodes&&(e.nodes=p(e.nodes)),delete e.source,e)))}function f(e){if(e[u]=!1,e.proxyOf.nodes)for(let t of e.proxyOf.nodes)f(t)}class d extends c{append(...e){for(let t of e){let e=this.normalize(t,this.last);for(let t of e)this.proxyOf.nodes.push(t)}return this.markDirty(),this}cleanRaws(e){if(super.cleanRaws(e),this.nodes)for(let t of this.nodes)t.cleanRaws(e)}each(e){if(!this.proxyOf.nodes)return;let t,r,s=this.getIterator();while(this.indexes[s]<this.proxyOf.nodes.length){if(t=this.indexes[s],r=e(this.proxyOf.nodes[t],t),!1===r)break;this.indexes[s]+=1}return delete this.indexes[s],r}every(e){return this.nodes.every(e)}getIterator(){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach+=1;let e=this.lastEach;return this.indexes[e]=0,e}getProxyProcessor(){return{get(e,t){return"proxyOf"===t?e:e[t]?"each"===t||"string"===typeof t&&t.startsWith("walk")?(...r)=>e[t](...r.map((e=>"function"===typeof e?(t,r)=>e(t.toProxy(),r):e))):"every"===t||"some"===t?r=>e[t](((e,...t)=>r(e.toProxy(),...t))):"root"===t?()=>e.root().toProxy():"nodes"===t?e.nodes.map((e=>e.toProxy())):"first"===t||"last"===t?e[t].toProxy():e[t]:e[t]},set(e,t,r){return e[t]===r||(e[t]=r,"name"!==t&&"params"!==t&&"selector"!==t||e.markDirty()),!0}}}index(e){return"number"===typeof e?e:(e.proxyOf&&(e=e.proxyOf),this.proxyOf.nodes.indexOf(e))}insertAfter(e,t){let r,s=this.index(e),i=this.normalize(t,this.proxyOf.nodes[s]).reverse();s=this.index(e);for(let n of i)this.proxyOf.nodes.splice(s+1,0,n);for(let n in this.indexes)r=this.indexes[n],s<r&&(this.indexes[n]=r+i.length);return this.markDirty(),this}insertBefore(e,t){let r,s=this.index(e),i=0===s&&"prepend",n=this.normalize(t,this.proxyOf.nodes[s],i).reverse();s=this.index(e);for(let o of n)this.proxyOf.nodes.splice(s,0,o);for(let o in this.indexes)r=this.indexes[o],s<=r&&(this.indexes[o]=r+n.length);return this.markDirty(),this}normalize(e,t){if("string"===typeof e)e=p(i(e).nodes);else if("undefined"===typeof e)e=[];else if(Array.isArray(e)){e=e.slice(0);for(let t of e)t.parent&&t.parent.removeChild(t,"ignore")}else if("root"===e.type&&"document"!==this.type){e=e.nodes.slice(0);for(let t of e)t.parent&&t.parent.removeChild(t,"ignore")}else if(e.type)e=[e];else if(e.prop){if("undefined"===typeof e.value)throw new Error("Value field is missed in node creation");"string"!==typeof e.value&&(e.value=String(e.value)),e=[new l(e)]}else if(e.selector||e.selectors)e=[new o(e)];else if(e.name)e=[new s(e)];else{if(!e.text)throw new Error("Unknown node type in node creation");e=[new a(e)]}let r=e.map((e=>(e[h]||d.rebuild(e),e=e.proxyOf,e.parent&&e.parent.removeChild(e),e[u]&&f(e),e.raws||(e.raws={}),"undefined"===typeof e.raws.before&&t&&"undefined"!==typeof t.raws.before&&(e.raws.before=t.raws.before.replace(/\S/g,"")),e.parent=this.proxyOf,e)));return r}prepend(...e){e=e.reverse();for(let t of e){let e=this.normalize(t,this.first,"prepend").reverse();for(let t of e)this.proxyOf.nodes.unshift(t);for(let t in this.indexes)this.indexes[t]=this.indexes[t]+e.length}return this.markDirty(),this}push(e){return e.parent=this,this.proxyOf.nodes.push(e),this}removeAll(){for(let e of this.proxyOf.nodes)e.parent=void 0;return this.proxyOf.nodes=[],this.markDirty(),this}removeChild(e){let t;e=this.index(e),this.proxyOf.nodes[e].parent=void 0,this.proxyOf.nodes.splice(e,1);for(let r in this.indexes)t=this.indexes[r],t>=e&&(this.indexes[r]=t-1);return this.markDirty(),this}replaceValues(e,t,r){return r||(r=t,t={}),this.walkDecls((s=>{t.props&&!t.props.includes(s.prop)||t.fast&&!s.value.includes(t.fast)||(s.value=s.value.replace(e,r))})),this.markDirty(),this}some(e){return this.nodes.some(e)}walk(e){return this.each(((t,r)=>{let s;try{s=e(t,r)}catch(i){throw t.addToError(i)}return!1!==s&&t.walk&&(s=t.walk(e)),s}))}walkAtRules(e,t){return t?e instanceof RegExp?this.walk(((r,s)=>{if("atrule"===r.type&&e.test(r.name))return t(r,s)})):this.walk(((r,s)=>{if("atrule"===r.type&&r.name===e)return t(r,s)})):(t=e,this.walk(((e,r)=>{if("atrule"===e.type)return t(e,r)})))}walkComments(e){return this.walk(((t,r)=>{if("comment"===t.type)return e(t,r)}))}walkDecls(e,t){return t?e instanceof RegExp?this.walk(((r,s)=>{if("decl"===r.type&&e.test(r.prop))return t(r,s)})):this.walk(((r,s)=>{if("decl"===r.type&&r.prop===e)return t(r,s)})):(t=e,this.walk(((e,r)=>{if("decl"===e.type)return t(e,r)})))}walkRules(e,t){return t?e instanceof RegExp?this.walk(((r,s)=>{if("rule"===r.type&&e.test(r.selector))return t(r,s)})):this.walk(((r,s)=>{if("rule"===r.type&&r.selector===e)return t(r,s)})):(t=e,this.walk(((e,r)=>{if("rule"===e.type)return t(e,r)})))}get first(){if(this.proxyOf.nodes)return this.proxyOf.nodes[0]}get last(){if(this.proxyOf.nodes)return this.proxyOf.nodes[this.proxyOf.nodes.length-1]}}d.registerParse=e=>{i=e},d.registerRule=e=>{o=e},d.registerAtRule=e=>{s=e},d.registerRoot=e=>{n=e},e.exports=d,d.default=d,d.rebuild=e=>{"atrule"===e.type?Object.setPrototypeOf(e,s.prototype):"rule"===e.type?Object.setPrototypeOf(e,o.prototype):"decl"===e.type?Object.setPrototypeOf(e,l.prototype):"comment"===e.type?Object.setPrototypeOf(e,a.prototype):"root"===e.type&&Object.setPrototypeOf(e,n.prototype),e[h]=!0,e.nodes&&e.nodes.forEach((e=>{d.rebuild(e)}))}},59877:function(e,t,r){"use strict";r(81454);let s=r(72615),i=r(49746);class n extends Error{constructor(e,t,r,s,i,o){super(e),this.name="CssSyntaxError",this.reason=e,i&&(this.file=i),s&&(this.source=s),o&&(this.plugin=o),"undefined"!==typeof t&&"undefined"!==typeof r&&("number"===typeof t?(this.line=t,this.column=r):(this.line=t.line,this.column=t.column,this.endLine=r.line,this.endColumn=r.column)),this.setMessage(),Error.captureStackTrace&&Error.captureStackTrace(this,n)}setMessage(){this.message=this.plugin?this.plugin+": ":"",this.message+=this.file?this.file:"<css input>","undefined"!==typeof this.line&&(this.message+=":"+this.line+":"+this.column),this.message+=": "+this.reason}showSourceCode(e){if(!this.source)return"";let t=this.source;null==e&&(e=s.isColorSupported);let r=e=>e,n=e=>e,o=e=>e;if(e){let{bold:e,gray:t,red:a}=s.createColors(!0);n=t=>e(a(t)),r=e=>t(e),i&&(o=e=>i(e))}let a=t.split(/\r?\n/),l=Math.max(this.line-3,0),c=Math.min(this.line+2,a.length),u=String(c).length;return a.slice(l,c).map(((e,t)=>{let s=l+1+t,i=" "+(" "+s).slice(-u)+" | ";if(s===this.line){if(e.length>160){let t=20,s=Math.max(0,this.column-t),a=Math.max(this.column+t,this.endColumn+t),l=e.slice(s,a),c=r(i.replace(/\d/g," "))+e.slice(0,Math.min(this.column-1,t-1)).replace(/[^\t]/g," ");return n(">")+r(i)+o(l)+"\n "+c+n("^")}let t=r(i.replace(/\d/g," "))+e.slice(0,this.column-1).replace(/[^\t]/g," ");return n(">")+r(i)+o(e)+"\n "+t+n("^")}return" "+r(i)+o(e)})).join("\n")}toString(){let e=this.showSourceCode();return e&&(e="\n\n"+e+"\n"),this.name+": "+this.message+e}}e.exports=n,n.default=n},59995:function(e,t,r){"use strict";let s=r(45867);class i extends s{constructor(e){e&&"undefined"!==typeof e.value&&"string"!==typeof e.value&&(e={...e,value:String(e.value)}),super(e),this.type="decl"}get variable(){return this.prop.startsWith("--")||"$"===this.prop[0]}}e.exports=i,i.default=i},10242:function(e,t,r){"use strict";let s,i,n=r(29308);class o extends n{constructor(e){super({type:"document",...e}),this.nodes||(this.nodes=[])}toResult(e={}){let t=new s(new i,this,e);return t.stringify()}}o.registerLazyResult=e=>{s=e},o.registerProcessor=e=>{i=e},e.exports=o,o.default=o},14709:function(e,t,r){"use strict";r(44114),r(81454);let s=r(35241),i=r(88806),n=r(59995),o=r(41779),a=r(70817),l=r(48663),c=r(55289);function u(e,t){if(Array.isArray(e))return e.map((e=>u(e)));let{inputs:r,...h}=e;if(r){t=[];for(let e of r){let r={...e,__proto__:o.prototype};r.map&&(r.map={...r.map,__proto__:a.prototype}),t.push(r)}}if(h.nodes&&(h.nodes=e.nodes.map((e=>u(e,t)))),h.source){let{inputId:e,...r}=h.source;h.source=r,null!=e&&(h.source.input=t[e])}if("root"===h.type)return new l(h);if("decl"===h.type)return new n(h);if("rule"===h.type)return new c(h);if("comment"===h.type)return new i(h);if("atrule"===h.type)return new s(h);throw new Error("Unknown node type: "+e.type)}e.exports=u,u.default=u},41779:function(e,t,r){"use strict";r(81454),r(14603),r(47566),r(98721);let{nanoid:s}=r(95042),{isAbsolute:i,resolve:n}=r(197),{SourceMapConsumer:o,SourceMapGenerator:a}=r(21866),{fileURLToPath:l,pathToFileURL:c}=r(52739),u=r(59877),h=r(70817),p=r(49746),f=Symbol("fromOffsetCache"),d=Boolean(o&&a),m=Boolean(n&&i);class g{constructor(e,t={}){if(null===e||"undefined"===typeof e||"object"===typeof e&&!e.toString)throw new Error(`PostCSS received ${e} instead of CSS string`);if(this.css=e.toString(),"\ufeff"===this.css[0]||"￾"===this.css[0]?(this.hasBOM=!0,this.css=this.css.slice(1)):this.hasBOM=!1,t.from&&(!m||/^\w+:\/\//.test(t.from)||i(t.from)?this.file=t.from:this.file=n(t.from)),m&&d){let e=new h(this.css,t);if(e.text){this.map=e;let t=e.consumer().file;!this.file&&t&&(this.file=this.mapResolve(t))}}this.file||(this.id="<input css "+s(6)+">"),this.map&&(this.map.file=this.from)}error(e,t,r,s={}){let i,n,o;if(t&&"object"===typeof t){let e=t,s=r;if("number"===typeof e.offset){let s=this.fromOffset(e.offset);t=s.line,r=s.col}else t=e.line,r=e.column;if("number"===typeof s.offset){let e=this.fromOffset(s.offset);n=e.line,i=e.col}else n=s.line,i=s.column}else if(!r){let e=this.fromOffset(t);t=e.line,r=e.col}let a=this.origin(t,r,n,i);return o=a?new u(e,void 0===a.endLine?a.line:{column:a.column,line:a.line},void 0===a.endLine?a.column:{column:a.endColumn,line:a.endLine},a.source,a.file,s.plugin):new u(e,void 0===n?t:{column:r,line:t},void 0===n?r:{column:i,line:n},this.css,this.file,s.plugin),o.input={column:r,endColumn:i,endLine:n,line:t,source:this.css},this.file&&(c&&(o.input.url=c(this.file).toString()),o.input.file=this.file),o}fromOffset(e){let t,r;if(this[f])r=this[f];else{let e=this.css.split("\n");r=new Array(e.length);let t=0;for(let s=0,i=e.length;s<i;s++)r[s]=t,t+=e[s].length+1;this[f]=r}t=r[r.length-1];let s=0;if(e>=t)s=r.length-1;else{let t,i=r.length-2;while(s<i)if(t=s+(i-s>>1),e<r[t])i=t-1;else{if(!(e>=r[t+1])){s=t;break}s=t+1}}return{col:e-r[s]+1,line:s+1}}mapResolve(e){return/^\w+:\/\//.test(e)?e:n(this.map.consumer().sourceRoot||this.map.root||".",e)}origin(e,t,r,s){if(!this.map)return!1;let n,o,a=this.map.consumer(),u=a.originalPositionFor({column:t,line:e});if(!u.source)return!1;"number"===typeof r&&(n=a.originalPositionFor({column:s,line:r})),o=i(u.source)?c(u.source):new URL(u.source,this.map.consumer().sourceRoot||c(this.map.mapFile));let h={column:u.column,endColumn:n&&n.column,endLine:n&&n.line,line:u.line,url:o.toString()};if("file:"===o.protocol){if(!l)throw new Error("file: protocol is not available in this PostCSS build");h.file=l(o)}let p=a.sourceContentFor(u.source);return p&&(h.source=p),h}toJSON(){let e={};for(let t of["hasBOM","css","file","id"])null!=this[t]&&(e[t]=this[t]);return this.map&&(e.map={...this.map},e.map.consumerCache&&(e.map.consumerCache=void 0)),e}get from(){return this.file||this.id}}e.exports=g,g.default=g,p&&p.registerInput&&p.registerInput(g)},96723:function(e,t,r){"use strict";r(44114),r(98992),r(3949),r(81454);let s=r(29308),i=r(10242),n=r(52961),o=r(84468),a=r(50234),l=r(48663),c=r(92458),{isClean:u,my:h}=r(57534);r(14273);const p={atrule:"AtRule",comment:"Comment",decl:"Declaration",document:"Document",root:"Root",rule:"Rule"},f={AtRule:!0,AtRuleExit:!0,Comment:!0,CommentExit:!0,Declaration:!0,DeclarationExit:!0,Document:!0,DocumentExit:!0,Once:!0,OnceExit:!0,postcssPlugin:!0,prepare:!0,Root:!0,RootExit:!0,Rule:!0,RuleExit:!0},d={Once:!0,postcssPlugin:!0,prepare:!0},m=0;function g(e){return"object"===typeof e&&"function"===typeof e.then}function v(e){let t=!1,r=p[e.type];return"decl"===e.type?t=e.prop.toLowerCase():"atrule"===e.type&&(t=e.name.toLowerCase()),t&&e.append?[r,r+"-"+t,m,r+"Exit",r+"Exit-"+t]:t?[r,r+"-"+t,r+"Exit",r+"Exit-"+t]:e.append?[r,m,r+"Exit"]:[r,r+"Exit"]}function y(e){let t;return t="document"===e.type?["Document",m,"DocumentExit"]:"root"===e.type?["Root",m,"RootExit"]:v(e),{eventIndex:0,events:t,iterator:0,node:e,visitorIndex:0,visitors:[]}}function w(e){return e[u]=!1,e.nodes&&e.nodes.forEach((e=>w(e))),e}let b={};class C{constructor(e,t,r){let i;if(this.stringified=!1,this.processed=!1,"object"!==typeof t||null===t||"root"!==t.type&&"document"!==t.type)if(t instanceof C||t instanceof a)i=w(t.root),t.map&&("undefined"===typeof r.map&&(r.map={}),r.map.inline||(r.map.inline=!1),r.map.prev=t.map);else{let e=o;r.syntax&&(e=r.syntax.parse),r.parser&&(e=r.parser),e.parse&&(e=e.parse);try{i=e(t,r)}catch(n){this.processed=!0,this.error=n}i&&!i[h]&&s.rebuild(i)}else i=w(t);this.result=new a(e,i,r),this.helpers={...b,postcss:b,result:this.result},this.plugins=this.processor.plugins.map((e=>"object"===typeof e&&e.prepare?{...e,...e.prepare(this.result)}:e))}async(){return this.error?Promise.reject(this.error):this.processed?Promise.resolve(this.result):(this.processing||(this.processing=this.runAsync()),this.processing)}catch(e){return this.async().catch(e)}finally(e){return this.async().then(e,e)}getAsyncError(){throw new Error("Use process(css).then(cb) to work with async plugins")}handleError(e,t){let r=this.result.lastPlugin;try{t&&t.addToError(e),this.error=e,"CssSyntaxError"!==e.name||e.plugin?r.postcssVersion:(e.plugin=r.postcssPlugin,e.setMessage())}catch(s){console&&console.error&&console.error(s)}return e}prepareVisitors(){this.listeners={};let e=(e,t,r)=>{this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push([e,r])};for(let t of this.plugins)if("object"===typeof t)for(let r in t){if(!f[r]&&/^[A-Z]/.test(r))throw new Error(`Unknown event ${r} in ${t.postcssPlugin}. Try to update PostCSS (${this.processor.version} now).`);if(!d[r])if("object"===typeof t[r])for(let s in t[r])e(t,"*"===s?r:r+"-"+s.toLowerCase(),t[r][s]);else"function"===typeof t[r]&&e(t,r,t[r])}this.hasListener=Object.keys(this.listeners).length>0}async runAsync(){this.plugin=0;for(let r=0;r<this.plugins.length;r++){let t=this.plugins[r],s=this.runOnRoot(t);if(g(s))try{await s}catch(e){throw this.handleError(e)}}if(this.prepareVisitors(),this.hasListener){let e=this.result.root;while(!e[u]){e[u]=!0;let r=[y(e)];while(r.length>0){let e=this.visitTick(r);if(g(e))try{await e}catch(t){let e=r[r.length-1].node;throw this.handleError(t,e)}}}if(this.listeners.OnceExit)for(let[r,s]of this.listeners.OnceExit){this.result.lastPlugin=r;try{if("document"===e.type){let t=e.nodes.map((e=>s(e,this.helpers)));await Promise.all(t)}else await s(e,this.helpers)}catch(t){throw this.handleError(t)}}}return this.processed=!0,this.stringify()}runOnRoot(e){this.result.lastPlugin=e;try{if("object"===typeof e&&e.Once){if("document"===this.result.root.type){let t=this.result.root.nodes.map((t=>e.Once(t,this.helpers)));return g(t[0])?Promise.all(t):t}return e.Once(this.result.root,this.helpers)}if("function"===typeof e)return e(this.result.root,this.result)}catch(t){throw this.handleError(t)}}stringify(){if(this.error)throw this.error;if(this.stringified)return this.result;this.stringified=!0,this.sync();let e=this.result.opts,t=c;e.syntax&&(t=e.syntax.stringify),e.stringifier&&(t=e.stringifier),t.stringify&&(t=t.stringify);let r=new n(t,this.result.root,this.result.opts),s=r.generate();return this.result.css=s[0],this.result.map=s[1],this.result}sync(){if(this.error)throw this.error;if(this.processed)return this.result;if(this.processed=!0,this.processing)throw this.getAsyncError();for(let e of this.plugins){let t=this.runOnRoot(e);if(g(t))throw this.getAsyncError()}if(this.prepareVisitors(),this.hasListener){let e=this.result.root;while(!e[u])e[u]=!0,this.walkSync(e);if(this.listeners.OnceExit)if("document"===e.type)for(let t of e.nodes)this.visitSync(this.listeners.OnceExit,t);else this.visitSync(this.listeners.OnceExit,e)}return this.result}then(e,t){return this.async().then(e,t)}toString(){return this.css}visitSync(e,t){for(let[s,i]of e){let e;this.result.lastPlugin=s;try{e=i(t,this.helpers)}catch(r){throw this.handleError(r,t.proxyOf)}if("root"!==t.type&&"document"!==t.type&&!t.parent)return!0;if(g(e))throw this.getAsyncError()}}visitTick(e){let t=e[e.length-1],{node:r,visitors:s}=t;if("root"!==r.type&&"document"!==r.type&&!r.parent)return void e.pop();if(s.length>0&&t.visitorIndex<s.length){let[e,i]=s[t.visitorIndex];t.visitorIndex+=1,t.visitorIndex===s.length&&(t.visitors=[],t.visitorIndex=0),this.result.lastPlugin=e;try{return i(r.toProxy(),this.helpers)}catch(n){throw this.handleError(n,r)}}if(0!==t.iterator){let s,i=t.iterator;while(s=r.nodes[r.indexes[i]])if(r.indexes[i]+=1,!s[u])return s[u]=!0,void e.push(y(s));t.iterator=0,delete r.indexes[i]}let i=t.events;while(t.eventIndex<i.length){let e=i[t.eventIndex];if(t.eventIndex+=1,e===m)return void(r.nodes&&r.nodes.length&&(r[u]=!0,t.iterator=r.getIterator()));if(this.listeners[e])return void(t.visitors=this.listeners[e])}e.pop()}walkSync(e){e[u]=!0;let t=v(e);for(let r of t)if(r===m)e.nodes&&e.each((e=>{e[u]||this.walkSync(e)}));else{let t=this.listeners[r];if(t&&this.visitSync(t,e.toProxy()))return}}warnings(){return this.sync().warnings()}get content(){return this.stringify().content}get css(){return this.stringify().css}get map(){return this.stringify().map}get messages(){return this.sync().messages}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){return this.sync().root}get[Symbol.toStringTag](){return"LazyResult"}}C.registerPostcss=e=>{b=e},e.exports=C,C.default=C,l.registerLazyResult(C),i.registerLazyResult(C)},37583:function(e,t,r){"use strict";r(44114);let s={comma(e){return s.split(e,[","],!0)},space(e){let t=[" ","\n","\t"];return s.split(e,t)},split(e,t,r){let s=[],i="",n=!1,o=0,a=!1,l="",c=!1;for(let u of e)c?c=!1:"\\"===u?c=!0:a?u===l&&(a=!1):'"'===u||"'"===u?(a=!0,l=u):"("===u?o+=1:")"===u?o>0&&(o-=1):0===o&&t.includes(u)&&(n=!0),n?(""!==i&&s.push(i.trim()),i="",n=!1):i+=u;return(r||""!==i)&&s.push(i.trim()),s}};e.exports=s,s.default=s},52961:function(e,t,r){"use strict";var s=r(90712)["Buffer"];r(44114),r(98992),r(81454),r(37550),r(64979);let{dirname:i,relative:n,resolve:o,sep:a}=r(197),{SourceMapConsumer:l,SourceMapGenerator:c}=r(21866),{pathToFileURL:u}=r(52739),h=r(41779),p=Boolean(l&&c),f=Boolean(i&&o&&n&&a);class d{constructor(e,t,r,s){this.stringify=e,this.mapOpts=r.map||{},this.root=t,this.opts=r,this.css=s,this.originalCSS=s,this.usesFileUrls=!this.mapOpts.from&&this.mapOpts.absolute,this.memoizedFileURLs=new Map,this.memoizedPaths=new Map,this.memoizedURLs=new Map}addAnnotation(){let e;e=this.isInline()?"data:application/json;base64,"+this.toBase64(this.map.toString()):"string"===typeof this.mapOpts.annotation?this.mapOpts.annotation:"function"===typeof this.mapOpts.annotation?this.mapOpts.annotation(this.opts.to,this.root):this.outputFile()+".map";let t="\n";this.css.includes("\r\n")&&(t="\r\n"),this.css+=t+"/*# sourceMappingURL="+e+" */"}applyPrevMaps(){for(let e of this.previous()){let t,r=this.toUrl(this.path(e.file)),s=e.root||i(e.file);!1===this.mapOpts.sourcesContent?(t=new l(e.text),t.sourcesContent&&(t.sourcesContent=null)):t=e.consumer(),this.map.applySourceMap(t,r,this.toUrl(this.path(s)))}}clearAnnotation(){if(!1!==this.mapOpts.annotation)if(this.root){let e;for(let t=this.root.nodes.length-1;t>=0;t--)e=this.root.nodes[t],"comment"===e.type&&e.text.startsWith("# sourceMappingURL=")&&this.root.removeChild(t)}else this.css&&(this.css=this.css.replace(/\n*\/\*#[\S\s]*?\*\/$/gm,""))}generate(){if(this.clearAnnotation(),f&&p&&this.isMap())return this.generateMap();{let e="";return this.stringify(this.root,(t=>{e+=t})),[e]}}generateMap(){if(this.root)this.generateString();else if(1===this.previous().length){let e=this.previous()[0].consumer();e.file=this.outputFile(),this.map=c.fromSourceMap(e,{ignoreInvalidMapping:!0})}else this.map=new c({file:this.outputFile(),ignoreInvalidMapping:!0}),this.map.addMapping({generated:{column:0,line:1},original:{column:0,line:1},source:this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>"});return this.isSourcesContent()&&this.setSourcesContent(),this.root&&this.previous().length>0&&this.applyPrevMaps(),this.isAnnotation()&&this.addAnnotation(),this.isInline()?[this.css]:[this.css,this.map]}generateString(){this.css="",this.map=new c({file:this.outputFile(),ignoreInvalidMapping:!0});let e,t,r=1,s=1,i="<no source>",n={generated:{column:0,line:0},original:{column:0,line:0},source:""};this.stringify(this.root,((o,a,l)=>{if(this.css+=o,a&&"end"!==l&&(n.generated.line=r,n.generated.column=s-1,a.source&&a.source.start?(n.source=this.sourcePath(a),n.original.line=a.source.start.line,n.original.column=a.source.start.column-1,this.map.addMapping(n)):(n.source=i,n.original.line=1,n.original.column=0,this.map.addMapping(n))),t=o.match(/\n/g),t?(r+=t.length,e=o.lastIndexOf("\n"),s=o.length-e):s+=o.length,a&&"start"!==l){let e=a.parent||{raws:{}},t="decl"===a.type||"atrule"===a.type&&!a.nodes;t&&a===e.last&&!e.raws.semicolon||(a.source&&a.source.end?(n.source=this.sourcePath(a),n.original.line=a.source.end.line,n.original.column=a.source.end.column-1,n.generated.line=r,n.generated.column=s-2,this.map.addMapping(n)):(n.source=i,n.original.line=1,n.original.column=0,n.generated.line=r,n.generated.column=s-1,this.map.addMapping(n)))}}))}isAnnotation(){return!!this.isInline()||("undefined"!==typeof this.mapOpts.annotation?this.mapOpts.annotation:!this.previous().length||this.previous().some((e=>e.annotation)))}isInline(){if("undefined"!==typeof this.mapOpts.inline)return this.mapOpts.inline;let e=this.mapOpts.annotation;return("undefined"===typeof e||!0===e)&&(!this.previous().length||this.previous().some((e=>e.inline)))}isMap(){return"undefined"!==typeof this.opts.map?!!this.opts.map:this.previous().length>0}isSourcesContent(){return"undefined"!==typeof this.mapOpts.sourcesContent?this.mapOpts.sourcesContent:!this.previous().length||this.previous().some((e=>e.withContent()))}outputFile(){return this.opts.to?this.path(this.opts.to):this.opts.from?this.path(this.opts.from):"to.css"}path(e){if(this.mapOpts.absolute)return e;if(60===e.charCodeAt(0))return e;if(/^\w+:\/\//.test(e))return e;let t=this.memoizedPaths.get(e);if(t)return t;let r=this.opts.to?i(this.opts.to):".";"string"===typeof this.mapOpts.annotation&&(r=i(o(r,this.mapOpts.annotation)));let s=n(r,e);return this.memoizedPaths.set(e,s),s}previous(){if(!this.previousMaps)if(this.previousMaps=[],this.root)this.root.walk((e=>{if(e.source&&e.source.input.map){let t=e.source.input.map;this.previousMaps.includes(t)||this.previousMaps.push(t)}}));else{let e=new h(this.originalCSS,this.opts);e.map&&this.previousMaps.push(e.map)}return this.previousMaps}setSourcesContent(){let e={};if(this.root)this.root.walk((t=>{if(t.source){let r=t.source.input.from;if(r&&!e[r]){e[r]=!0;let s=this.usesFileUrls?this.toFileUrl(r):this.toUrl(this.path(r));this.map.setSourceContent(s,t.source.input.css)}}}));else if(this.css){let e=this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>";this.map.setSourceContent(e,this.css)}}sourcePath(e){return this.mapOpts.from?this.toUrl(this.mapOpts.from):this.usesFileUrls?this.toFileUrl(e.source.input.from):this.toUrl(this.path(e.source.input.from))}toBase64(e){return s?s.from(e).toString("base64"):window.btoa(unescape(encodeURIComponent(e)))}toFileUrl(e){let t=this.memoizedFileURLs.get(e);if(t)return t;if(u){let t=u(e).toString();return this.memoizedFileURLs.set(e,t),t}throw new Error("`map.absolute` option is not available in this PostCSS build")}toUrl(e){let t=this.memoizedURLs.get(e);if(t)return t;"\\"===a&&(e=e.replace(/\\/g,"/"));let r=encodeURI(e).replace(/[#?]/g,encodeURIComponent);return this.memoizedURLs.set(e,r),r}}e.exports=d},85512:function(e,t,r){"use strict";r(81454);let s=r(52961),i=r(84468);const n=r(50234);let o=r(92458);r(14273);class a{constructor(e,t,r){let i;t=t.toString(),this.stringified=!1,this._processor=e,this._css=t,this._opts=r,this._map=void 0;let a=o;this.result=new n(this._processor,i,this._opts),this.result.css=t;let l=this;Object.defineProperty(this.result,"root",{get(){return l.root}});let c=new s(a,i,this._opts,t);if(c.isMap()){let[e,t]=c.generate();e&&(this.result.css=e),t&&(this.result.map=t)}else c.clearAnnotation(),this.result.css=c.css}async(){return this.error?Promise.reject(this.error):Promise.resolve(this.result)}catch(e){return this.async().catch(e)}finally(e){return this.async().then(e,e)}sync(){if(this.error)throw this.error;return this.result}then(e,t){return this.async().then(e,t)}toString(){return this._css}warnings(){return[]}get content(){return this.result.css}get css(){return this.result.css}get map(){return this.result.map}get messages(){return[]}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){if(this._root)return this._root;let e,t=i;try{e=t(this._css,this._opts)}catch(r){this.error=r}if(this.error)throw this.error;return this._root=e,e}get[Symbol.toStringTag](){return"NoWorkResult"}}e.exports=a,a.default=a},45867:function(e,t,r){"use strict";r(81454);let s=r(59877),i=r(72661),n=r(92458),{isClean:o,my:a}=r(57534);function l(e,t){let r=new e.constructor;for(let s in e){if(!Object.prototype.hasOwnProperty.call(e,s))continue;if("proxyCache"===s)continue;let i=e[s],n=typeof i;"parent"===s&&"object"===n?t&&(r[s]=t):"source"===s?r[s]=i:Array.isArray(i)?r[s]=i.map((e=>l(e,r))):("object"===n&&null!==i&&(i=l(i)),r[s]=i)}return r}function c(e,t){if(t&&"undefined"!==typeof t.offset)return t.offset;let r=1,s=1,i=0;for(let n=0;n<e.length;n++){if(s===t.line&&r===t.column){i=n;break}"\n"===e[n]?(r=1,s+=1):r+=1}return i}class u{constructor(e={}){this.raws={},this[o]=!1,this[a]=!0;for(let t in e)if("nodes"===t){this.nodes=[];for(let r of e[t])"function"===typeof r.clone?this.append(r.clone()):this.append(r)}else this[t]=e[t]}addToError(e){if(e.postcssNode=this,e.stack&&this.source&&/\n\s{4}at /.test(e.stack)){let t=this.source;e.stack=e.stack.replace(/\n\s{4}at /,`$&${t.input.from}:${t.start.line}:${t.start.column}$&`)}return e}after(e){return this.parent.insertAfter(this,e),this}assign(e={}){for(let t in e)this[t]=e[t];return this}before(e){return this.parent.insertBefore(this,e),this}cleanRaws(e){delete this.raws.before,delete this.raws.after,e||delete this.raws.between}clone(e={}){let t=l(this);for(let r in e)t[r]=e[r];return t}cloneAfter(e={}){let t=this.clone(e);return this.parent.insertAfter(this,t),t}cloneBefore(e={}){let t=this.clone(e);return this.parent.insertBefore(this,t),t}error(e,t={}){if(this.source){let{end:r,start:s}=this.rangeBy(t);return this.source.input.error(e,{column:s.column,line:s.line},{column:r.column,line:r.line},t)}return new s(e)}getProxyProcessor(){return{get(e,t){return"proxyOf"===t?e:"root"===t?()=>e.root().toProxy():e[t]},set(e,t,r){return e[t]===r||(e[t]=r,"prop"!==t&&"value"!==t&&"name"!==t&&"params"!==t&&"important"!==t&&"text"!==t||e.markDirty()),!0}}}markClean(){this[o]=!0}markDirty(){if(this[o]){this[o]=!1;let e=this;while(e=e.parent)e[o]=!1}}next(){if(!this.parent)return;let e=this.parent.index(this);return this.parent.nodes[e+1]}positionBy(e){let t=this.source.start;if(e.index)t=this.positionInside(e.index);else if(e.word){let r=this.source.input.css.slice(c(this.source.input.css,this.source.start),c(this.source.input.css,this.source.end)),s=r.indexOf(e.word);-1!==s&&(t=this.positionInside(s))}return t}positionInside(e){let t=this.source.start.column,r=this.source.start.line,s=c(this.source.input.css,this.source.start),i=s+e;for(let n=s;n<i;n++)"\n"===this.source.input.css[n]?(t=1,r+=1):t+=1;return{column:t,line:r}}prev(){if(!this.parent)return;let e=this.parent.index(this);return this.parent.nodes[e-1]}rangeBy(e){let t={column:this.source.start.column,line:this.source.start.line},r=this.source.end?{column:this.source.end.column+1,line:this.source.end.line}:{column:t.column+1,line:t.line};if(e.word){let s=this.source.input.css.slice(c(this.source.input.css,this.source.start),c(this.source.input.css,this.source.end)),i=s.indexOf(e.word);-1!==i&&(t=this.positionInside(i),r=this.positionInside(i+e.word.length))}else e.start?t={column:e.start.column,line:e.start.line}:e.index&&(t=this.positionInside(e.index)),e.end?r={column:e.end.column,line:e.end.line}:"number"===typeof e.endIndex?r=this.positionInside(e.endIndex):e.index&&(r=this.positionInside(e.index+1));return(r.line<t.line||r.line===t.line&&r.column<=t.column)&&(r={column:t.column+1,line:t.line}),{end:r,start:t}}raw(e,t){let r=new i;return r.raw(this,e,t)}remove(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this}replaceWith(...e){if(this.parent){let t=this,r=!1;for(let s of e)s===this?r=!0:r?(this.parent.insertAfter(t,s),t=s):this.parent.insertBefore(t,s);r||this.remove()}return this}root(){let e=this;while(e.parent&&"document"!==e.parent.type)e=e.parent;return e}toJSON(e,t){let r={},s=null==t;t=t||new Map;let i=0;for(let n in this){if(!Object.prototype.hasOwnProperty.call(this,n))continue;if("parent"===n||"proxyCache"===n)continue;let e=this[n];if(Array.isArray(e))r[n]=e.map((e=>"object"===typeof e&&e.toJSON?e.toJSON(null,t):e));else if("object"===typeof e&&e.toJSON)r[n]=e.toJSON(null,t);else if("source"===n){let s=t.get(e.input);null==s&&(s=i,t.set(e.input,i),i++),r[n]={end:e.end,inputId:s,start:e.start}}else r[n]=e}return s&&(r.inputs=[...t.keys()].map((e=>e.toJSON()))),r}toProxy(){return this.proxyCache||(this.proxyCache=new Proxy(this,this.getProxyProcessor())),this.proxyCache}toString(e=n){e.stringify&&(e=e.stringify);let t="";return e(this,(e=>{t+=e})),t}warn(e,t,r){let s={node:this};for(let i in r)s[i]=r[i];return e.warn(t,s)}get proxyOf(){return this}}e.exports=u,u.default=u},84468:function(e,t,r){"use strict";let s=r(29308),i=r(41779),n=r(62548);function o(e,t){let r=new i(e,t),s=new n(r);try{s.parse()}catch(o){throw o}return s.root}e.exports=o,o.default=o,s.registerParse(o)},62548:function(e,t,r){"use strict";r(44114),r(98992),r(81454),r(8872),r(37550);let s=r(35241),i=r(88806),n=r(59995),o=r(48663),a=r(55289),l=r(80078);const c={empty:!0,space:!0};function u(e){for(let t=e.length-1;t>=0;t--){let r=e[t],s=r[3]||r[2];if(s)return s}}class h{constructor(e){this.input=e,this.root=new o,this.current=this.root,this.spaces="",this.semicolon=!1,this.createTokenizer(),this.root.source={input:e,start:{column:1,line:1,offset:0}}}atrule(e){let t,r,i,n=new s;n.name=e[1].slice(1),""===n.name&&this.unnamedAtrule(n,e),this.init(n,e[2]);let o=!1,a=!1,l=[],c=[];while(!this.tokenizer.endOfFile()){if(e=this.tokenizer.nextToken(),t=e[0],"("===t||"["===t?c.push("("===t?")":"]"):"{"===t&&c.length>0?c.push("}"):t===c[c.length-1]&&c.pop(),0===c.length){if(";"===t){n.source.end=this.getPosition(e[2]),n.source.end.offset++,this.semicolon=!0;break}if("{"===t){a=!0;break}if("}"===t){if(l.length>0){i=l.length-1,r=l[i];while(r&&"space"===r[0])r=l[--i];r&&(n.source.end=this.getPosition(r[3]||r[2]),n.source.end.offset++)}this.end(e);break}l.push(e)}else l.push(e);if(this.tokenizer.endOfFile()){o=!0;break}}n.raws.between=this.spacesAndCommentsFromEnd(l),l.length?(n.raws.afterName=this.spacesAndCommentsFromStart(l),this.raw(n,"params",l),o&&(e=l[l.length-1],n.source.end=this.getPosition(e[3]||e[2]),n.source.end.offset++,this.spaces=n.raws.between,n.raws.between="")):(n.raws.afterName="",n.params=""),a&&(n.nodes=[],this.current=n)}checkMissedSemicolon(e){let t=this.colon(e);if(!1===t)return;let r,s=0;for(let i=t-1;i>=0;i--)if(r=e[i],"space"!==r[0]&&(s+=1,2===s))break;throw this.input.error("Missed semicolon","word"===r[0]?r[3]+1:r[2])}colon(e){let t,r,s,i=0;for(let[n,o]of e.entries()){if(r=o,s=r[0],"("===s&&(i+=1),")"===s&&(i-=1),0===i&&":"===s){if(t){if("word"===t[0]&&"progid"===t[1])continue;return n}this.doubleColon(r)}t=r}return!1}comment(e){let t=new i;this.init(t,e[2]),t.source.end=this.getPosition(e[3]||e[2]),t.source.end.offset++;let r=e[1].slice(2,-2);if(/^\s*$/.test(r))t.text="",t.raws.left=r,t.raws.right="";else{let e=r.match(/^(\s*)([^]*\S)(\s*)$/);t.text=e[2],t.raws.left=e[1],t.raws.right=e[3]}}createTokenizer(){this.tokenizer=l(this.input)}decl(e,t){let r=new n;this.init(r,e[0][2]);let s,i=e[e.length-1];";"===i[0]&&(this.semicolon=!0,e.pop()),r.source.end=this.getPosition(i[3]||i[2]||u(e)),r.source.end.offset++;while("word"!==e[0][0])1===e.length&&this.unknownWord(e),r.raws.before+=e.shift()[1];r.source.start=this.getPosition(e[0][2]),r.prop="";while(e.length){let t=e[0][0];if(":"===t||"space"===t||"comment"===t)break;r.prop+=e.shift()[1]}r.raws.between="";while(e.length){if(s=e.shift(),":"===s[0]){r.raws.between+=s[1];break}"word"===s[0]&&/\w/.test(s[1])&&this.unknownWord([s]),r.raws.between+=s[1]}"_"!==r.prop[0]&&"*"!==r.prop[0]||(r.raws.before+=r.prop[0],r.prop=r.prop.slice(1));let o,a=[];while(e.length){if(o=e[0][0],"space"!==o&&"comment"!==o)break;a.push(e.shift())}this.precheckMissedSemicolon(e);for(let n=e.length-1;n>=0;n--){if(s=e[n],"!important"===s[1].toLowerCase()){r.important=!0;let t=this.stringFrom(e,n);t=this.spacesFromEnd(e)+t," !important"!==t&&(r.raws.important=t);break}if("important"===s[1].toLowerCase()){let t=e.slice(0),s="";for(let e=n;e>0;e--){let r=t[e][0];if(s.trim().startsWith("!")&&"space"!==r)break;s=t.pop()[1]+s}s.trim().startsWith("!")&&(r.important=!0,r.raws.important=s,e=t)}if("space"!==s[0]&&"comment"!==s[0])break}let l=e.some((e=>"space"!==e[0]&&"comment"!==e[0]));l&&(r.raws.between+=a.map((e=>e[1])).join(""),a=[]),this.raw(r,"value",a.concat(e),t),r.value.includes(":")&&!t&&this.checkMissedSemicolon(e)}doubleColon(e){throw this.input.error("Double colon",{offset:e[2]},{offset:e[2]+e[1].length})}emptyRule(e){let t=new a;this.init(t,e[2]),t.selector="",t.raws.between="",this.current=t}end(e){this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.semicolon=!1,this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.spaces="",this.current.parent?(this.current.source.end=this.getPosition(e[2]),this.current.source.end.offset++,this.current=this.current.parent):this.unexpectedClose(e)}endFile(){this.current.parent&&this.unclosedBlock(),this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.root.source.end=this.getPosition(this.tokenizer.position())}freeSemicolon(e){if(this.spaces+=e[1],this.current.nodes){let e=this.current.nodes[this.current.nodes.length-1];e&&"rule"===e.type&&!e.raws.ownSemicolon&&(e.raws.ownSemicolon=this.spaces,this.spaces="")}}getPosition(e){let t=this.input.fromOffset(e);return{column:t.col,line:t.line,offset:e}}init(e,t){this.current.push(e),e.source={input:this.input,start:this.getPosition(t)},e.raws.before=this.spaces,this.spaces="","comment"!==e.type&&(this.semicolon=!1)}other(e){let t=!1,r=null,s=!1,i=null,n=[],o=e[1].startsWith("--"),a=[],l=e;while(l){if(r=l[0],a.push(l),"("===r||"["===r)i||(i=l),n.push("("===r?")":"]");else if(o&&s&&"{"===r)i||(i=l),n.push("}");else if(0===n.length){if(";"===r){if(s)return void this.decl(a,o);break}if("{"===r)return void this.rule(a);if("}"===r){this.tokenizer.back(a.pop()),t=!0;break}":"===r&&(s=!0)}else r===n[n.length-1]&&(n.pop(),0===n.length&&(i=null));l=this.tokenizer.nextToken()}if(this.tokenizer.endOfFile()&&(t=!0),n.length>0&&this.unclosedBracket(i),t&&s){if(!o)while(a.length){if(l=a[a.length-1][0],"space"!==l&&"comment"!==l)break;this.tokenizer.back(a.pop())}this.decl(a,o)}else this.unknownWord(a)}parse(){let e;while(!this.tokenizer.endOfFile())switch(e=this.tokenizer.nextToken(),e[0]){case"space":this.spaces+=e[1];break;case";":this.freeSemicolon(e);break;case"}":this.end(e);break;case"comment":this.comment(e);break;case"at-word":this.atrule(e);break;case"{":this.emptyRule(e);break;default:this.other(e);break}this.endFile()}precheckMissedSemicolon(){}raw(e,t,r,s){let i,n,o,a,l=r.length,u="",h=!0;for(let p=0;p<l;p+=1)i=r[p],n=i[0],"space"!==n||p!==l-1||s?"comment"===n?(a=r[p-1]?r[p-1][0]:"empty",o=r[p+1]?r[p+1][0]:"empty",c[a]||c[o]||","===u.slice(-1)?h=!1:u+=i[1]):u+=i[1]:h=!1;if(!h){let s=r.reduce(((e,t)=>e+t[1]),"");e.raws[t]={raw:s,value:u}}e[t]=u}rule(e){e.pop();let t=new a;this.init(t,e[0][2]),t.raws.between=this.spacesAndCommentsFromEnd(e),this.raw(t,"selector",e),this.current=t}spacesAndCommentsFromEnd(e){let t,r="";while(e.length){if(t=e[e.length-1][0],"space"!==t&&"comment"!==t)break;r=e.pop()[1]+r}return r}spacesAndCommentsFromStart(e){let t,r="";while(e.length){if(t=e[0][0],"space"!==t&&"comment"!==t)break;r+=e.shift()[1]}return r}spacesFromEnd(e){let t,r="";while(e.length){if(t=e[e.length-1][0],"space"!==t)break;r=e.pop()[1]+r}return r}stringFrom(e,t){let r="";for(let s=t;s<e.length;s++)r+=e[s][1];return e.splice(t,e.length-t),r}unclosedBlock(){let e=this.current.source.start;throw this.input.error("Unclosed block",e.line,e.column)}unclosedBracket(e){throw this.input.error("Unclosed bracket",{offset:e[2]},{offset:e[2]+1})}unexpectedClose(e){throw this.input.error("Unexpected }",{offset:e[2]},{offset:e[2]+1})}unknownWord(e){throw this.input.error("Unknown word",{offset:e[0][2]},{offset:e[0][2]+e[0][1].length})}unnamedAtrule(e,t){throw this.input.error("At-rule without name",{offset:t[2]},{offset:t[2]+t[1].length})}}e.exports=h},43818:function(e,t,r){"use strict";let s=r(35241),i=r(88806),n=r(29308),o=r(59877),a=r(59995),l=r(10242),c=r(14709),u=r(41779),h=r(96723),p=r(37583),f=r(45867),d=r(84468),m=r(35195),g=r(50234),v=r(48663),y=r(55289),w=r(92458),b=r(31651);function C(...e){return 1===e.length&&Array.isArray(e[0])&&(e=e[0]),new m(e)}C.plugin=function(e,t){let r,s=!1;function i(...r){console&&console.warn&&!s&&(s=!0,console.warn(e+": postcss.plugin was deprecated. Migration guide:\nhttps://evilmartians.com/chronicles/postcss-8-plugin-migration"),{NODE_ENV:"production",VUE_APP_GTAG:"G-10WWTRZY00",VUE_APP_RECAPTCHA_TOKEN:"6Lc190gqAAAAANSORGBkadmouKFe5GpHGYn9-glK",VUE_APP_ZKME_API_URL:"https://test-agw.zk.me/zkseradmin",VUE_APP_ZKME_DECRYPT_URL:"https://test-dpa.zk.me",VUE_APP_ZKME_DISTRIBUTION_IMG_URL:"https://dev-agw.zk.me/token/file",VUE_APP_ZKME_DISTRIBUTION_URL:"https://dev-agw.zk.me/token/apijson",VUE_APP_ZKME_IDENTITY_URL:"https://test-app.zk.me/claim/",VUE_APP_ZKME_NEW_API_URL:"https://test-agw.zk.me/nest",VUE_APP_ZKME_RECIPIENT_URL:"https://dev-agw.zk.me/token/recipient",BASE_URL:"/"}.LANG&&{NODE_ENV:"production",VUE_APP_GTAG:"G-10WWTRZY00",VUE_APP_RECAPTCHA_TOKEN:"6Lc190gqAAAAANSORGBkadmouKFe5GpHGYn9-glK",VUE_APP_ZKME_API_URL:"https://test-agw.zk.me/zkseradmin",VUE_APP_ZKME_DECRYPT_URL:"https://test-dpa.zk.me",VUE_APP_ZKME_DISTRIBUTION_IMG_URL:"https://dev-agw.zk.me/token/file",VUE_APP_ZKME_DISTRIBUTION_URL:"https://dev-agw.zk.me/token/apijson",VUE_APP_ZKME_IDENTITY_URL:"https://test-app.zk.me/claim/",VUE_APP_ZKME_NEW_API_URL:"https://test-agw.zk.me/nest",VUE_APP_ZKME_RECIPIENT_URL:"https://dev-agw.zk.me/token/recipient",BASE_URL:"/"}.LANG.startsWith("cn")&&console.warn(e+": 里面 postcss.plugin 被弃用. 迁移指南:\nhttps://www.w3ctech.com/topic/2226"));let i=t(...r);return i.postcssPlugin=e,i.postcssVersion=(new m).version,i}return Object.defineProperty(i,"postcss",{get(){return r||(r=i()),r}}),i.process=function(e,t,r){return C([i(r)]).process(e,t)},i},C.stringify=w,C.parse=d,C.fromJSON=c,C.list=p,C.comment=e=>new i(e),C.atRule=e=>new s(e),C.decl=e=>new a(e),C.rule=e=>new y(e),C.root=e=>new v(e),C.document=e=>new l(e),C.CssSyntaxError=o,C.Declaration=a,C.Container=n,C.Processor=m,C.Document=l,C.Comment=i,C.Warning=b,C.AtRule=s,C.Result=g,C.Input=u,C.Rule=y,C.Root=v,C.Node=f,h.registerPostcss(C),e.exports=C,C.default=C},70817:function(e,t,r){"use strict";var s=r(90712)["Buffer"];r(81454),r(64979);let{existsSync:i,readFileSync:n}=r(19977),{dirname:o,join:a}=r(197),{SourceMapConsumer:l,SourceMapGenerator:c}=r(21866);function u(e){return s?s.from(e,"base64").toString():window.atob(e)}class h{constructor(e,t){if(!1===t.map)return;this.loadAnnotation(e),this.inline=this.startWith(this.annotation,"data:");let r=t.map?t.map.prev:void 0,s=this.loadMap(t.from,r);!this.mapFile&&t.from&&(this.mapFile=t.from),this.mapFile&&(this.root=o(this.mapFile)),s&&(this.text=s)}consumer(){return this.consumerCache||(this.consumerCache=new l(this.text)),this.consumerCache}decodeInline(e){let t=/^data:application\/json;charset=utf-?8;base64,/,r=/^data:application\/json;base64,/,s=/^data:application\/json;charset=utf-?8,/,i=/^data:application\/json,/,n=e.match(s)||e.match(i);if(n)return decodeURIComponent(e.substr(n[0].length));let o=e.match(t)||e.match(r);if(o)return u(e.substr(o[0].length));let a=e.match(/data:application\/json;([^,]+),/)[1];throw new Error("Unsupported source map encoding "+a)}getAnnotationURL(e){return e.replace(/^\/\*\s*# sourceMappingURL=/,"").trim()}isMap(e){return"object"===typeof e&&("string"===typeof e.mappings||"string"===typeof e._mappings||Array.isArray(e.sections))}loadAnnotation(e){let t=e.match(/\/\*\s*# sourceMappingURL=/g);if(!t)return;let r=e.lastIndexOf(t.pop()),s=e.indexOf("*/",r);r>-1&&s>-1&&(this.annotation=this.getAnnotationURL(e.substring(r,s)))}loadFile(e){if(this.root=o(e),i(e))return this.mapFile=e,n(e,"utf-8").toString().trim()}loadMap(e,t){if(!1===t)return!1;if(t){if("string"===typeof t)return t;if("function"!==typeof t){if(t instanceof l)return c.fromSourceMap(t).toString();if(t instanceof c)return t.toString();if(this.isMap(t))return JSON.stringify(t);throw new Error("Unsupported previous source map format: "+t.toString())}{let r=t(e);if(r){let e=this.loadFile(r);if(!e)throw new Error("Unable to load previous source map: "+r.toString());return e}}}else{if(this.inline)return this.decodeInline(this.annotation);if(this.annotation){let t=this.annotation;return e&&(t=a(o(e),t)),this.loadFile(t)}}}startWith(e,t){return!!e&&e.substr(0,t.length)===t}withContent(){return!!(this.consumer().sourcesContent&&this.consumer().sourcesContent.length>0)}}e.exports=h,h.default=h},35195:function(e,t,r){"use strict";r(44114);let s=r(10242),i=r(96723),n=r(85512),o=r(48663);class a{constructor(e=[]){this.version="8.4.49",this.plugins=this.normalize(e)}normalize(e){let t=[];for(let r of e)if(!0===r.postcss?r=r():r.postcss&&(r=r.postcss),"object"===typeof r&&Array.isArray(r.plugins))t=t.concat(r.plugins);else if("object"===typeof r&&r.postcssPlugin)t.push(r);else if("function"===typeof r)t.push(r);else{if("object"!==typeof r||!r.parse&&!r.stringify)throw new Error(r+" is not a PostCSS plugin")}return t}process(e,t={}){return this.plugins.length||t.parser||t.stringifier||t.syntax?new i(this,e,t):new n(this,e,t)}use(e){return this.plugins=this.plugins.concat(this.normalize([e])),this}}e.exports=a,a.default=a,o.registerProcessor(a),s.registerProcessor(a)},50234:function(e,t,r){"use strict";r(44114),r(98992),r(54520),r(81454);let s=r(31651);class i{constructor(e,t,r){this.processor=e,this.messages=[],this.root=t,this.opts=r,this.css=void 0,this.map=void 0}toString(){return this.css}warn(e,t={}){t.plugin||this.lastPlugin&&this.lastPlugin.postcssPlugin&&(t.plugin=this.lastPlugin.postcssPlugin);let r=new s(e,t);return this.messages.push(r),r}warnings(){return this.messages.filter((e=>"warning"===e.type))}get content(){return this.css}}e.exports=i,i.default=i},48663:function(e,t,r){"use strict";let s,i,n=r(29308);class o extends n{constructor(e){super(e),this.type="root",this.nodes||(this.nodes=[])}normalize(e,t,r){let s=super.normalize(e);if(t)if("prepend"===r)this.nodes.length>1?t.raws.before=this.nodes[1].raws.before:delete t.raws.before;else if(this.first!==t)for(let i of s)i.raws.before=t.raws.before;return s}removeChild(e,t){let r=this.index(e);return!t&&0===r&&this.nodes.length>1&&(this.nodes[1].raws.before=this.nodes[r].raws.before),super.removeChild(e)}toResult(e={}){let t=new s(new i,this,e);return t.stringify()}}o.registerLazyResult=e=>{s=e},o.registerProcessor=e=>{i=e},e.exports=o,o.default=o,n.registerRoot(o)},55289:function(e,t,r){"use strict";let s=r(29308),i=r(37583);class n extends s{constructor(e){super(e),this.type="rule",this.nodes||(this.nodes=[])}get selectors(){return i.comma(this.selector)}set selectors(e){let t=this.selector?this.selector.match(/,\s*/):null,r=t?t[0]:","+this.raw("between","beforeOpen");this.selector=e.join(r)}}e.exports=n,n.default=n,s.registerRule(n)},72661:function(e){"use strict";const t={after:"\n",beforeClose:"\n",beforeComment:"\n",beforeDecl:"\n",beforeOpen:" ",beforeRule:"\n",colon:": ",commentLeft:" ",commentRight:" ",emptyBody:"",indent:"    ",semicolon:!1};function r(e){return e[0].toUpperCase()+e.slice(1)}class s{constructor(e){this.builder=e}atrule(e,t){let r="@"+e.name,s=e.params?this.rawValue(e,"params"):"";if("undefined"!==typeof e.raws.afterName?r+=e.raws.afterName:s&&(r+=" "),e.nodes)this.block(e,r+s);else{let i=(e.raws.between||"")+(t?";":"");this.builder(r+s+i,e)}}beforeAfter(e,t){let r;r="decl"===e.type?this.raw(e,null,"beforeDecl"):"comment"===e.type?this.raw(e,null,"beforeComment"):"before"===t?this.raw(e,null,"beforeRule"):this.raw(e,null,"beforeClose");let s=e.parent,i=0;while(s&&"root"!==s.type)i+=1,s=s.parent;if(r.includes("\n")){let t=this.raw(e,null,"indent");if(t.length)for(let e=0;e<i;e++)r+=t}return r}block(e,t){let r,s=this.raw(e,"between","beforeOpen");this.builder(t+s+"{",e,"start"),e.nodes&&e.nodes.length?(this.body(e),r=this.raw(e,"after")):r=this.raw(e,"after","emptyBody"),r&&this.builder(r),this.builder("}",e,"end")}body(e){let t=e.nodes.length-1;while(t>0){if("comment"!==e.nodes[t].type)break;t-=1}let r=this.raw(e,"semicolon");for(let s=0;s<e.nodes.length;s++){let i=e.nodes[s],n=this.raw(i,"before");n&&this.builder(n),this.stringify(i,t!==s||r)}}comment(e){let t=this.raw(e,"left","commentLeft"),r=this.raw(e,"right","commentRight");this.builder("/*"+t+e.text+r+"*/",e)}decl(e,t){let r=this.raw(e,"between","colon"),s=e.prop+r+this.rawValue(e,"value");e.important&&(s+=e.raws.important||" !important"),t&&(s+=";"),this.builder(s,e)}document(e){this.body(e)}raw(e,s,i){let n;if(i||(i=s),s&&(n=e.raws[s],"undefined"!==typeof n))return n;let o=e.parent;if("before"===i){if(!o||"root"===o.type&&o.first===e)return"";if(o&&"document"===o.type)return""}if(!o)return t[i];let a=e.root();if(a.rawCache||(a.rawCache={}),"undefined"!==typeof a.rawCache[i])return a.rawCache[i];if("before"===i||"after"===i)return this.beforeAfter(e,i);{let t="raw"+r(i);this[t]?n=this[t](a,e):a.walk((e=>{if(n=e.raws[s],"undefined"!==typeof n)return!1}))}return"undefined"===typeof n&&(n=t[i]),a.rawCache[i]=n,n}rawBeforeClose(e){let t;return e.walk((e=>{if(e.nodes&&e.nodes.length>0&&"undefined"!==typeof e.raws.after)return t=e.raws.after,t.includes("\n")&&(t=t.replace(/[^\n]+$/,"")),!1})),t&&(t=t.replace(/\S/g,"")),t}rawBeforeComment(e,t){let r;return e.walkComments((e=>{if("undefined"!==typeof e.raws.before)return r=e.raws.before,r.includes("\n")&&(r=r.replace(/[^\n]+$/,"")),!1})),"undefined"===typeof r?r=this.raw(t,null,"beforeDecl"):r&&(r=r.replace(/\S/g,"")),r}rawBeforeDecl(e,t){let r;return e.walkDecls((e=>{if("undefined"!==typeof e.raws.before)return r=e.raws.before,r.includes("\n")&&(r=r.replace(/[^\n]+$/,"")),!1})),"undefined"===typeof r?r=this.raw(t,null,"beforeRule"):r&&(r=r.replace(/\S/g,"")),r}rawBeforeOpen(e){let t;return e.walk((e=>{if("decl"!==e.type&&(t=e.raws.between,"undefined"!==typeof t))return!1})),t}rawBeforeRule(e){let t;return e.walk((r=>{if(r.nodes&&(r.parent!==e||e.first!==r)&&"undefined"!==typeof r.raws.before)return t=r.raws.before,t.includes("\n")&&(t=t.replace(/[^\n]+$/,"")),!1})),t&&(t=t.replace(/\S/g,"")),t}rawColon(e){let t;return e.walkDecls((e=>{if("undefined"!==typeof e.raws.between)return t=e.raws.between.replace(/[^\s:]/g,""),!1})),t}rawEmptyBody(e){let t;return e.walk((e=>{if(e.nodes&&0===e.nodes.length&&(t=e.raws.after,"undefined"!==typeof t))return!1})),t}rawIndent(e){if(e.raws.indent)return e.raws.indent;let t;return e.walk((r=>{let s=r.parent;if(s&&s!==e&&s.parent&&s.parent===e&&"undefined"!==typeof r.raws.before){let e=r.raws.before.split("\n");return t=e[e.length-1],t=t.replace(/\S/g,""),!1}})),t}rawSemicolon(e){let t;return e.walk((e=>{if(e.nodes&&e.nodes.length&&"decl"===e.last.type&&(t=e.raws.semicolon,"undefined"!==typeof t))return!1})),t}rawValue(e,t){let r=e[t],s=e.raws[t];return s&&s.value===r?s.raw:r}root(e){this.body(e),e.raws.after&&this.builder(e.raws.after)}rule(e){this.block(e,this.rawValue(e,"selector")),e.raws.ownSemicolon&&this.builder(e.raws.ownSemicolon,e,"end")}stringify(e,t){if(!this[e.type])throw new Error("Unknown AST node type "+e.type+". Maybe you need to change PostCSS stringifier.");this[e.type](e,t)}}e.exports=s,s.default=s},92458:function(e,t,r){"use strict";let s=r(72661);function i(e,t){let r=new s(t);r.stringify(e)}e.exports=i,i.default=i},57534:function(e){"use strict";e.exports.isClean=Symbol("isClean"),e.exports.my=Symbol("my")},80078:function(e,t,r){"use strict";r(44114);const s="'".charCodeAt(0),i='"'.charCodeAt(0),n="\\".charCodeAt(0),o="/".charCodeAt(0),a="\n".charCodeAt(0),l=" ".charCodeAt(0),c="\f".charCodeAt(0),u="\t".charCodeAt(0),h="\r".charCodeAt(0),p="[".charCodeAt(0),f="]".charCodeAt(0),d="(".charCodeAt(0),m=")".charCodeAt(0),g="{".charCodeAt(0),v="}".charCodeAt(0),y=";".charCodeAt(0),w="*".charCodeAt(0),b=":".charCodeAt(0),C="@".charCodeAt(0),k=/[\t\n\f\r "#'()/;[\\\]{}]/g,_=/[\t\n\f\r !"#'():;@[\\\]{}]|\/(?=\*)/g,S=/.[\r\n"'(/\\]/,x=/[\da-f]/i;e.exports=function(e,t={}){let r,O,E,A,T,P,M,I,L,F,D=e.css.valueOf(),R=t.ignoreErrors,N=D.length,j=0,U=[],B=[];function q(){return j}function z(t){throw e.error("Unclosed "+t,j)}function V(){return 0===B.length&&j>=N}function W(e){if(B.length)return B.pop();if(j>=N)return;let t=!!e&&e.ignoreUnclosed;switch(r=D.charCodeAt(j),r){case a:case l:case u:case h:case c:A=j;do{A+=1,r=D.charCodeAt(A)}while(r===l||r===a||r===u||r===h||r===c);P=["space",D.slice(j,A)],j=A-1;break;case p:case f:case g:case v:case b:case y:case m:{let e=String.fromCharCode(r);P=[e,e,j];break}case d:if(F=U.length?U.pop()[1]:"",L=D.charCodeAt(j+1),"url"===F&&L!==s&&L!==i&&L!==l&&L!==a&&L!==u&&L!==c&&L!==h){A=j;do{if(M=!1,A=D.indexOf(")",A+1),-1===A){if(R||t){A=j;break}z("bracket")}I=A;while(D.charCodeAt(I-1)===n)I-=1,M=!M}while(M);P=["brackets",D.slice(j,A+1),j,A],j=A}else A=D.indexOf(")",j+1),O=D.slice(j,A+1),-1===A||S.test(O)?P=["(","(",j]:(P=["brackets",O,j,A],j=A);break;case s:case i:T=r===s?"'":'"',A=j;do{if(M=!1,A=D.indexOf(T,A+1),-1===A){if(R||t){A=j+1;break}z("string")}I=A;while(D.charCodeAt(I-1)===n)I-=1,M=!M}while(M);P=["string",D.slice(j,A+1),j,A],j=A;break;case C:k.lastIndex=j+1,k.test(D),A=0===k.lastIndex?D.length-1:k.lastIndex-2,P=["at-word",D.slice(j,A+1),j,A],j=A;break;case n:A=j,E=!0;while(D.charCodeAt(A+1)===n)A+=1,E=!E;if(r=D.charCodeAt(A+1),E&&r!==o&&r!==l&&r!==a&&r!==u&&r!==h&&r!==c&&(A+=1,x.test(D.charAt(A)))){while(x.test(D.charAt(A+1)))A+=1;D.charCodeAt(A+1)===l&&(A+=1)}P=["word",D.slice(j,A+1),j,A],j=A;break;default:r===o&&D.charCodeAt(j+1)===w?(A=D.indexOf("*/",j+2)+1,0===A&&(R||t?A=D.length:z("comment")),P=["comment",D.slice(j,A+1),j,A],j=A):(_.lastIndex=j+1,_.test(D),A=0===_.lastIndex?D.length-1:_.lastIndex-2,P=["word",D.slice(j,A+1),j,A],U.push(P),j=A);break}return j++,P}function G(e){B.push(e)}return{back:G,endOfFile:V,nextToken:W,position:q}}},14273:function(e){"use strict";let t={};e.exports=function(e){t[e]||(t[e]=!0,"undefined"!==typeof console&&console.warn&&console.warn(e))}},31651:function(e){"use strict";class t{constructor(e,t={}){if(this.type="warning",this.text=e,t.node&&t.node.source){let e=t.node.rangeBy(t);this.line=e.start.line,this.column=e.start.column,this.endLine=e.end.line,this.endColumn=e.end.column}for(let r in t)this[r]=t[r]}toString(){return this.node?this.node.error(this.text,{index:this.index,plugin:this.plugin,word:this.word}).message:this.plugin?this.plugin+": "+this.text:this.text}}e.exports=t,t.default=t},72615:function(e){var t=String,r=function(){return{isColorSupported:!1,reset:t,bold:t,dim:t,italic:t,underline:t,inverse:t,hidden:t,strikethrough:t,black:t,red:t,green:t,yellow:t,blue:t,magenta:t,cyan:t,white:t,gray:t,bgBlack:t,bgRed:t,bgGreen:t,bgYellow:t,bgBlue:t,bgMagenta:t,bgCyan:t,bgWhite:t,blackBright:t,redBright:t,greenBright:t,yellowBright:t,blueBright:t,magentaBright:t,cyanBright:t,whiteBright:t,bgBlackBright:t,bgRedBright:t,bgGreenBright:t,bgYellowBright:t,bgBlueBright:t,bgMagentaBright:t,bgCyanBright:t,bgWhiteBright:t}};e.exports=r(),e.exports.createColors=r},23404:function(e,t,r){function s(e,t){if(i("noDeprecation"))return e;var r=!1;function s(){if(!r){if(i("throwDeprecation"))throw new Error(t);i("traceDeprecation")?console.trace(t):console.warn(t),r=!0}return e.apply(this,arguments)}return s}function i(e){try{if(!r.g.localStorage)return!1}catch(s){return!1}var t=r.g.localStorage[e];return null!=t&&"true"===String(t).toLowerCase()}e.exports=s},35006:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAgCAMAAACrZuH4AAAAXVBMVEUAAAD///////////////////////////////////////////////////////////////8UFBRsbGwxMTGJiYmnp6fi4uJPT09OTk7x8fF7e3vExMReXl5AQEDT09N0/agsAAAAEHRSTlMAYO+/r6CQQDAQ38+Ab1AgJFHwPgAAAKNJREFUOMvNlMsSwyAIRTV9pO8iojYm6f9/ZsFlp+Cuk7O4G4+jI4ITxsMAP9l517iDzv7MggeLIxsXMHHuBD3j0TX8342EiXNGmhQjhRAI3pxFMZDXItTAKAa13WgYE8aSTSOLZRqvhXC1jXZTLBWjaWQ5zzSAYrGNxLmaL0aBMV69wsy56JUjye/KbeSP9bvBXbvG2O1Kd7M7W3gq02GQ6fABdNo+ZZelA3oAAAAASUVORK5CYII="},1939:function(e){"use strict";e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKAAAACgCAMAAAC8EZcfAAAAe1BMVEUAAAAAVWMAVWIAUGAAVGMAVWIAVGMAVGAAVWMAVGIAVGIAU2IAVWMAVGQAVmIAVWMAVWMAVWIAVGMAVWP///8fane/1Njf6uxAgIrv9fWfv8SAqrFglZ0QYG2vys4QX20wdYBwn6eQtbvP4OKPtbvv9PVQi5RQipSPtLswYaK3AAAAE3RSTlMA798QQDC/IJ+AcJDPgH+Pr2DP1/kHhwAAA8lJREFUeNrtndly2kAQRUcLiwFvuVpGuwDHzv9/YQoTWYBaQ1SUPJdE59H9ctytGbW2RvXj+avl3HUwKo47f1n5nhqM/zTHN7J5nqkBeILd6Livi7/Wc2AFZ7lg1vtUfLp+7Lmwiuub0/cM6zwb/BYbEOD2HokzFxS4sx4/ByQ4M9EPRMyY8yfncEFy/PWuFDI/YOOpUwj2P+N+6IMQn7nAB9y2yE+g5OFrBVPtMC1Ok8IlSHngTuBXCl9BywPvEj4yp2sSpL1wDWIONf4BYuZKeaDG4zwNt/hqBWpW6gXULNUc1MyZt+kDrqI9ER9xFMiZBCfBgUyCdEyCk+BAJkE6bhQsaq1D9BJqrQvcwG2CRRYFB+JUQ0C/JZ/hfR7iKmMIbpPgi7TjEEZtNM5whREEiyg4Ja5xRp2chcsKJkYQLPbBOT9rwa9lbzIcQ/AtuCQOT+obd8K/0MsYgu9Bl7INp0JYo4dRBKPApBBK0Qg9jCEYBhK7NoESNWTGENyKBnET3ovhLWTGECwDkQqfFHI0hcgogpGsEMoHgPkg/D8F+0pMI7gTDX6CRlCLBiWPIBLJICcSzMRtkEiwiLsCmkkQYafIGagEOx1fBjLB854v2YJOEMjjRi+rwCgI1Ntdmm51BZAKNkyCA+EX3FbiX3narSgOcUnxRtQPRp2bHUWWMDWs0Wd39VHgDzpLuDrqpuXfl29ZlkYJXctPf00yCU6CR+IyTcso4RRMyvcKR+p8Tyd40SFCR1yCUSh0t0SCGQRCnrtbw5gE71kwzLNs+1HAiDXBIoubHVrDhCVBHQfCozwJO4KZ9BhKxopg63fd0IZgOKCXsSIYD3mOY0FQy7fYe7AgmIpRDRkLgrEY3UHm+wULOVqih+mahFAwlRUqmhJfeaAtL5IMIqMIavMT6531bQaR0UAb8iswgqCoUJr9c1zldkGTQhK20TCR6y8zgqD0NPEDJ+SW2y103n5KLiqYn+dw/50Nq7RWo9r4D+wqmBlFEGEam66K8uhPcncWXnBsqN/zvO5NT6XzPA9xA//+S7YdJkE6JsFJcCCTIB2T4L8vSP/5Lv0H0PSfkNPOcjnywj/GgH4QBP0oDe5VslFKPYCYNetQsIaZUtQ1dpXirvGrOuDRno6dheJO4VIp6hQeEkidwkfV4FH2XO49DUhknA62Vqd4FEN2T3Hvbcwp/aBYsil6s3scVsw/7pllpWwu1wfZfrj27nlo+4FHm2PvHz11ncXSjqKsJyu+Wij0XNAzMFv/wEjIdr4ajOevXr7j5z+Wxp//+A00HS74d4gdbgAAAABJRU5ErkJggg=="},66598:function(e,t,r){"use strict";e.exports=r.p+"img/zkme.7c821f25.png"},49746:function(){},19977:function(){},197:function(){},21866:function(){},52739:function(){},95042:function(e){let t="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",r=(e,t=21)=>(r=t)=>{let s="",i=r;while(i--)s+=e[Math.random()*e.length|0];return s},s=(e=21)=>{let r="",s=e;while(s--)r+=t[64*Math.random()|0];return r};e.exports={nanoid:s,customAlphabet:r}}}]);