<template>
  <div class="pg-account-detail" id="pg-account-detail">
    <div class="sty1-bread-crumb mui-fl-vert mui-shr-0">
      <i class="mcico-dashboard taplight2" @click="$router.back()"></i>
      <span>/</span>
      Applicant Data
      <span>/</span>
      <div style="font-weight: 700;">KYT</div>
      <!-- <div>
        <m-input type="text" v-model="findAddress" @blur="setSelect('ETH', 'findAddress')"></m-input>
      </div> -->
    </div>

    <!-- <ul class="account mui-fl-btw">
      <li class="mui-fl-vert">
        <p class="t1">Account</p>
        <p class="t2">{{ RP && RP.email }}</p>
      </li>
      <li class="mui-fl-vert">
        <p class="t3 mui-fl-vert">
          <i class="mico-AppID"></i>
          ZIS ID:
        </p>
        <p class="t4">{{ RP && RP.tokenId }}</p>
      </li>
      <li class="mui-fl-vert">
        <p class="t3 mui-fl-vert">
          <i class="mico-APIKey"></i>
          ZIS wallet:
        </p>
        <p class="t4">{{ RP && RP.walletAddress | formatPubKey }}</p>
      </li>
      <li class="download mui-fl-central taplight" @click="downloadPdf">
        <i class="mico-download"></i>
      </li>
    </ul> -->

    <div class="mui-fl-btw kyt-title-mg-top24">
      <div class="mui-fl-1">
        <span class="kyt-title-text">Wallet</span>
        <div class="kyt-bor">
          <m-skeleton :rows="7" :loading="skeleton.tokens" animated style="position: relative;">
            <div class="mui-fl-end kyt-wallet-token" style="height: 64px;margin-bottom: 16px;">
              <div class="tokenImgsty">
                <i :class="['chainImg', `icon-${requestParams.replace('.e', '')}`]"></i>
                <!-- <i class="chainImg icon-ETH"></i> -->
              </div>
              <m-select class="sty1-select kyt-select" v-model="chain" @change="setSelect($event, 'chain')">
                <template #prefix>
                  <div class="prefix">
                    <img style="width: 22px; height: 22px;" src="@/assets/img/chain/base.png" alt=""
                      v-if="tokenList[0]?.coin === 'ETH-Base'">
                    <i :class="[`icon-${tokenList[0]?.coin}`]" v-else></i>
                  </div>
                </template>
                <m-option v-for="item in selectList" :key="item.token" :label="item.chain" :value="item"></m-option>
              </m-select>
            </div>
            <div><span class="kyt-content-text">{{ tokenName }}</span><span class="kyt-segmentation">|</span><span
                class="kyt-chain-text">EOA</span></div>
            <div class="kyt-token-title-text kyt-wallectAddress">{{ userWalletAddress }}</div>
            <div class="kyt-token-title-text">Asset Held:</div>
            <div class="mui-fl-vert mui-fl-wrap">
              <div class="kyt-tokenList" v-for="(data, index) of tokenList" :key="index">
                <i :class="[`icon-${data?.coin.replace('.e', '')}`]" @click="setSelect(data, 'token')"></i>
              </div>
            </div>
          </m-skeleton>
        </div>
      </div>
      <div class="mui-fl-1">
        <span class="kyt-title-text">Overview</span>
        <div class="kyt-bor">
          <m-skeleton :rows="7" :loading="skeleton.addressOverview" animated style="height: 100%;">
            <div class="mui-fl-btw" v-if="addressOverview">
              <div class="kyt-content-analysis-text-mg4 kyt-content-analysis-text-mg32">
                <div>
                  <div class="kyt-content-analysis-text">Balance</div>
                  <div class="kyt-content-text">{{ addressOverview?.balance | thousandth }} {{ tokenName }}</div>
                </div>
                <div class="kyt-title-mg-top24">
                  <div class="kyt-content-analysis-text">Total received</div>
                  <div class="kyt-content-text">{{ addressOverview?.total_received | thousandth }} {{ tokenName }}</div>
                </div>
                <div class="kyt-title-mg-top24">
                  <div class="kyt-content-analysis-text">First seen (UTC)</div>
                  <div class="kyt-content-text">{{ addressOverview?.first_seen * 1000 | formatUTCDate }}</div>
                </div>
                <div class="kyt-title-mg-top24">
                  <div class="kyt-content-analysis-text">Incoming txn</div>
                  <div class="kyt-content-text">{{ addressOverview?.received_txs_count | thousandth }}</div>

                </div>
              </div>
              <div class="kyt-content-analysis-text-mg4">
                <div>
                  <div class="kyt-content-analysis-text">Txs count</div>
                  <div class="kyt-content-text">{{ addressOverview?.txs_count | thousandth }}</div>
                </div>
                <div class="kyt-title-mg-top24">
                  <div class="kyt-content-analysis-text">Total spent</div>
                  <div class="kyt-content-text">{{ addressOverview?.total_spent | thousandth }} {{ tokenName }}</div>
                </div>
                <div class="kyt-title-mg-top24">
                  <div class="kyt-content-analysis-text">Last seen (UTC)</div>
                  <div class="kyt-content-text">{{ addressOverview?.last_seen * 1000 | formatUTCDate }}</div>
                </div>
                <div class="kyt-title-mg-top24">
                  <div class="kyt-content-analysis-text">Outgoing txn</div>
                  <div class="kyt-content-text">{{ addressOverview?.spent_txs_count }}</div>
                </div>
              </div>
            </div>
            <div class="no-data mui-fl-col mui-fl-central" v-else>
              <img src="@/assets/img/no-table-data.png" alt="">
              <div>No Content</div>
            </div>
          </m-skeleton>
        </div>
      </div>
      <div class="mui-fl-1">
        <span class="kyt-title-text mui-fl-vert">
          AML Risk Score
          <m-popover popper-class="NotRecord_popover kyt_popover" placement="bottom-end" trigger="hover" :offset="230">
            <div slot="reference" class="mui-fl-vert">
              <i class="mico-help-linear"></i>
            </div>
            <slot>
              <div style="word-break: break-word;">
                The system evaluates the AML risk score based on three factors: 1) the entity of the address, 2) the
                transactions associated with the address, and 3) the malicious wallet address database. High-risk
                entities,
                such as mixers, or addresses with funds connected to known risky entities, are flagged as risky addresses.
                Additionally, verified addresses involved in ransomware, scams, and stolen crypto listed in the malicious
                wallet address database are considered high risk. The following risk levels and corresponding scores are
                assigned:
              </div>
            </slot>
            <m-table style="width: 300px" border :data="tableData">
              <m-table-column width="149" property="level" label="Risk Level"></m-table-column>
              <m-table-column width="149" property="score" label="Risk Score"></m-table-column>
            </m-table>
          </m-popover>
        </span>
        <div class="kyt-bor kyt-title-mg-right0">
          <m-skeleton :rows="7" :loading="skeleton.riskscore" animated style="height: 100%;">
            <div class="mui-fl-btw" v-if="riskscore">
              <div class="moderateb">
                <div
                  :class="['kyt-score-text', 'mui-fl-hori', 'moderate', 'kyt-content-title-text', `moderate-${riskscore?.risk_level || 'Low'}`]"
                  style="width: 62px;">{{
                    riskscore?.risk_level || 'Low' }}</div>
                <div class="kyt-score-price-text mui-fl-hori moderatenb">{{ riskscore?.score || 0 }}</div>
              </div>
              <div>
                <div id="AML" class="e-chart-radar"></div>
              </div>
            </div>
            <div class="kyt-warn-text mui-flex" v-if="riskscore && paginatedData.length">
              <div>
                <i class="mico-warning"></i>
              </div>
              <div class="mui-fl-btw mui-fl-vert" style="width: 100%;">
                <div v-if="riskscore?.detail_list">{{ riskscore?.detail_list.toString().replace(/,/g, ", ") }}</div>
                <div class="detail" @click="detail">Detail</div>
              </div>
            </div>
            <div class="no-data mui-fl-col mui-fl-central" v-if="!riskscore">
              <img src="@/assets/img/no-table-data.png" alt="">
              <div>No Content</div>
            </div>
          </m-skeleton>
        </div>
      </div>
    </div>

    <div class="mui-fl-btw kyt-title-mg-top40">
      <div class="mui-fl-1">
        <span class="kyt-title-text mui-fl-vert">
          Transaction Actions Analysis
          <m-popover popper-class="NotRecord_popover kyt_popover" placement="top-start" trigger="hover" :offset="230">
            <div slot="reference" class="mui-fl-vert">
              <i class="mico-help-linear"></i>
            </div>
            <slot>
              The transaction action analysis module will analyze all historical <br />transactions associated
              with the address and provide a comprehensive <br />summary in an easily understandable way.
              Through this analysis module, it is easy to create a behavioral profile of the target address.
            </slot>
          </m-popover>
        </span>
        <div class="kyt-bor kyt-title-mg-right0">
          <m-skeleton :class="[!addressAction ? 'mui-fl-hori' : 'mui-fl-btw']" :rows="6" :loading="skeleton.addressAction"
            animated>
            <div v-show="addressAction" style="min-width: 600px">
              <span class="kyt-analysis-text mui-fl-hori">Incoming Transaction Actions</span>
              <div v-show="transaction.Incoming" class="mui-fl-hori" style="max-width: 650px;">
                <div style="position: relative;" class="mui-fl-central">
                  <div class="kyt-transaction-actions">
                    <div class="mui-fl-hori"><img src="@/assets/img/Incoming.png" alt=""></div>
                    <div class="title mui-fl-hori">{{ incoming.name || 'Exchange' }}</div>
                    <div class="kyt-analysis-token-text mui-fl-hori">{{ incoming.rate || 100 }}%</div>
                  </div>
                  <div id="Incoming" class="eChart"></div>
                </div>
                <m-skeleton :rows="5" :loading="!addressAction" animated class="mui-fl-vert">
                  <div class="mui-fl-wrap" style="width: 100%;">
                    <div v-for="(data, index) of addressAction?.[0]" :key="index"
                      style="width: auto; padding: 24px 40px 0;">
                      <div class="kyt-content-analysis-text mui-fl-vert">
                        <span class="kyt-incoming-pilot" :style="{ background: data.styColor }"></span>{{ data.name }}
                      </div>
                      <div class="kyt-analysis-price-text">{{ data.rate }}%</div>
                    </div>
                  </div>
                </m-skeleton>
              </div>
              <div v-show="!transaction.Incoming" style="height: calc(100% - 50px);" class="mui-fl-central">
                <div class="no-data mui-fl-col mui-fl-central">
                  <img src="@/assets/img/no-table-data.png" alt="">
                  <div class="mui-fl-hori">No Content</div>
                </div>
              </div>
            </div>
            <div v-show="addressAction" style="min-width: 600px">
              <span class="kyt-analysis-text mui-fl-hori">Outgoing Transaction Actions</span>
              <div v-show="transaction.Outgoing" class="mui-flex" style="max-width: 650px;">
                <div style="position: relative;" class="mui-fl-central">
                  <div class="kyt-transaction-actions">
                    <div class="mui-fl-hori"><img src="@/assets/img/Outgoing.png" alt=""></div>
                    <div class="title mui-fl-hori">{{ outgoing.name || 'Exchange' }}</div>
                    <div class="kyt-analysis-token-text mui-fl-hori">{{ outgoing.rate || 100 }}%</div>
                  </div>
                  <div id="Outgoing" class="eChart"></div>
                </div>
                <m-skeleton :rows="5" :loading="!addressAction" animated class="mui-fl-vert">
                  <div class="mui-fl-wrap" style="width: 100%;">
                    <div v-for="(data, index) of addressAction?.[1]" :key="index"
                      style="width: auto;padding: 24px 40px 0;">
                      <div class="kyt-content-analysis-text mui-fl-vert">
                        <span class="kyt-incoming-pilot" :style="{ background: data.styColor }"></span>{{ data.name }}
                      </div>
                      <div class="kyt-analysis-price-text">{{ data.rate }}%</div>
                    </div>
                  </div>
                </m-skeleton>
              </div>
              <div v-show="!transaction.Outgoing" style="height: calc(100% - 50px);" class="mui-fl-central">
                <div class="no-data mui-fl-col mui-fl-central">
                  <img src="@/assets/img/no-table-data.png" alt="">
                  <div class="mui-fl-hori">No Content</div>
                </div>
              </div>
            </div>
            <div class="no-data" v-if="!addressAction">
              <img src="@/assets/img/no-table-data.png" alt="">
              <div class="mui-fl-hori">No Content</div>
            </div>
          </m-skeleton>
        </div>
      </div>
    </div>
    <div class="mui-fl-btw kyt-title-mg-top40">
      <div class="mui-fl-1">
        <span class="kyt-title-text mui-fl-btw">
          <div class="mui-fl-vert">
            Address Labels
            <m-popover popper-class="NotRecord_popover kyt_popover" placement="top-start" trigger="hover" :offset="230">
              <div slot="reference" class="mui-fl-vert">
                <i class="mico-help-linear"></i>
              </div>
              <slot>
                Address labels consist of three categories, which include the associated <br /> entity, on-chain behavior,
                and some off-chain data.
              </slot>
            </m-popover>
          </div>
          <div class="kyt-token-title-text mui-fl-vert" style="cursor: pointer; line-height: 25px;" @click="copy">
            Copy all
            <i class="mico-copy"></i>
          </div>
        </span>
        <div class="kyt-bor kyt-title-mg-right0">
          <m-skeleton :rows="6" :loading="skeleton.addressLabels" animated>
            <div v-if="addressLabels && addressLabels?.length" class="mui-fl-wrap">
              <div v-for="(data, index) of addressLabels" :key="index">
                <div class="kyt-analysis-text kyt-price-box kyt-labels-box mui-fl-vert">
                  <i class="mico-tag"></i>
                  {{ data }}
                </div>
              </div>
            </div>
            <div class="mui-fl-central" v-else>
              <div class="no-data">
                <img src="@/assets/img/no-table-data.png" alt="">
                <div class="mui-fl-hori">No Content</div>
              </div>
            </div>
          </m-skeleton>
        </div>
      </div>
    </div>

    <div class="kyt-title-mg-top40" style="min-width: 800px;">
      <span class="kyt-title-text mui-fl-vert">
        Address Profile Analysis
        <m-popover popper-class="NotRecord_popover kyt_popover" placement="top-start" trigger="hover" :offset="230">
          <div slot="reference" class="mui-fl-vert">
            <i class="mico-help-linear"></i>
          </div>
          <slot>
            The address profile analysis module can generate a comprehensive summary in
            an easy-to-understand format through analyzing all interactions associated with the address.
          </slot>
        </m-popover>
      </span>
      <div class="kyt-bor kyt-title-mg-right0">
        <m-skeleton :rows="6" :loading="skeleton.addressTrace" animated>
          <div class="mui-fl-btw" v-if="addressTrace">
            <div class="mui-fl-btw mui-fl-vert_end kyt-Profile-text-height" style="max-width: 576px;">
              <div>
                <div class="kyt-analysis-text kyt-analysis-text-mg kyt-price-text">Platform Interaction</div>
                <div class="kyt-price-box kyt-price-analysis-box">
                  <div class="kyt-analysis-text mui-fl-hori">{{ addressTrace?.exchange?.count }}</div>
                  <div class="kyt-address-analysis-text mui-fl-hori">Exchange</div>
                </div>
              </div>
              <div>
                <div class="kyt-price-box kyt-price-analysis-box">
                  <div class="kyt-analysis-text mui-fl-hori">{{ addressTrace?.dex?.count }}</div>
                  <div class="kyt-address-analysis-text mui-fl-hori">DeFi</div>
                </div>
              </div>
              <div>
                <div class="kyt-price-box kyt-price-analysis-box">
                  <div class="kyt-analysis-text mui-fl-hori">{{ addressTrace?.mixer?.count }}</div>
                  <div class="kyt-address-analysis-text mui-fl-hori">Coin Mixer</div>
                </div>
              </div>
              <div>
                <div class="kyt-price-box kyt-price-analysis-box">
                  <div class="kyt-analysis-text mui-fl-hori">{{ addressTrace?.nft?.count }}</div>
                  <div class="kyt-address-analysis-text mui-fl-hori">NFT</div>
                </div>
              </div>
            </div>
            <div class="mui-fl-btw mui-fl-vert_end kyt-Profile-text-height" style="max-width: 432px;;">
              <div>
                <div class="kyt-analysis-text kyt-analysis-text-mg kyt-price-text">Related Events</div>
                <div class="kyt-price-box kyt-price-analysis-box">
                  <div class="kyt-analysis-text mui-fl-hori">{{ addressTrace?.stealing?.count }}</div>
                  <div class="kyt-address-analysis-text mui-fl-hori">Theft</div>
                </div>
              </div>
              <div>
                <div class="kyt-price-box kyt-price-analysis-box">
                  <div class="kyt-analysis-text mui-fl-hori">{{ addressTrace?.phishing?.count }}</div>
                  <div class="kyt-address-analysis-text mui-fl-hori">Phishing</div>
                </div>
              </div>
              <div>
                <div class="kyt-price-box kyt-price-analysis-box">
                  <div class="kyt-analysis-text mui-fl-hori">{{ addressTrace?.phishing?.count }}</div>
                  <div class="kyt-address-analysis-text mui-fl-hori">Ransom</div>
                </div>
              </div>
            </div>
            <div class="mui-fl-btw mui-fl-vert_end kyt-Profile-text-height" style="max-width: 432px;">
              <div>
                <div class="kyt-analysis-text kyt-analysis-text-mg kyt-price-text">Related Information</div>
                <div class="kyt-price-box kyt-price-analysis-box">
                  <div class="kyt-analysis-text mui-fl-hori">{{ addressTrace?.wallet?.count }}</div>
                  <div class="kyt-address-analysis-text mui-fl-hori">Wallet</div>
                </div>
              </div>
              <div>
                <div class="kyt-price-box kyt-price-analysis-box">
                  <div class="kyt-analysis-text mui-fl-hori">{{ addressTrace?.ens?.count }}</div>
                  <div class="kyt-address-analysis-text mui-fl-hori">ENS</div>
                </div>
              </div>
              <div>
                <div class="kyt-price-box kyt-price-analysis-box">
                  <div class="kyt-analysis-text mui-fl-hori">{{ addressTrace?.twitter?.count }}</div>
                  <div class="kyt-address-analysis-text mui-fl-hori">Twitter</div>
                </div>
              </div>
            </div>
          </div>
          <div class="no-data" v-else>
            <div class="mui-fl-hori">
              <img src="@/assets/img/no-table-data.png" alt="">
            </div>
            <div class="mui-fl-hori">No Content</div>
          </div>
        </m-skeleton>
      </div>
    </div>

    <div class="mui-fl-col txn-graph">
      <span class="kyt-title-text mui-fl-vert">
        Transaction Graph
        <m-popover popper-class="NotRecord_popover kyt_popover" placement="top-start" trigger="hover" :offset="230">
          <div slot="reference" class="mui-fl-vert">
            <i class="mico-help-linear"></i>
          </div>
          <slot>
            The transaction graph displays the relationship of all the incoming and <br />outgoing
            transactions of the address. It can perform data filtering and <br />address search through the table on the
            right side.
          </slot>
        </m-popover>
      </span>
      <div v-if="transactionsInvestigationDataLoading" class="txn-graph-loading-or-empty mui-fl-central">
        <div class="mui-fl-col">
          <LoadingLottie :size="'40px'" />
          <p class="loading-txt">Loading...</p>
        </div>
      </div>
      <div
        v-else-if="!transactionsInvestigationDataLoading && !transactionsInvestigation?.in && !transactionsInvestigation?.out"
        class="no-data mui-fl-central mui-fl-col">
        <img src="@/assets/img/no-table-data.png" alt="">
        <div>No Content</div>
      </div>
      <KytTransactionGraph v-else :coin="tokenName" :decimals="decimals" :requestParams="selectValue"
        :transactionsInvestigationData="transactionsInvestigation" :walletAddress="this.userWalletAddress"
        :graphStartTime="addressOverview?.first_seen !== null ? addressOverview?.first_seen : Date.now()"
        :graphEndTime="addressOverview?.last_seen !== null ? addressOverview?.last_seen : Date.now()"
        :graphFilterStartTime="graphFilterStartTime" :graphFilterEndTime="graphFilterEndTime"
        @paginateTxnInvestigation="paginateTxnInvestigation">
      </KytTransactionGraph>
    </div>

    <kytDetails v-if="paginatedData.length" :kytDetailstip="kytDetailstip" :paginatedData="paginatedData"
      :labelList="labelList" @detail="detail">
    </kytDetails>
  </div>
</template>

<script>
import kytDetails from '@/components/kyt-details'
import KytTransactionGraph from '@/components/kyt_transaction_graph'
import LoadingLottie from '@/components/loading-lottie'
import * as echarts from 'echarts'
export default {
  components: { KytTransactionGraph, LoadingLottie, kytDetails },
  data () {
    return {
      RP: null,
      incoming: {},
      outgoing: {},
      addressLabels: null,
      addressOverview: null,
      addressAction: [],
      transactionsInvestigation: null,
      addressTrace: {},
      riskscore: null,
      tokenList: [],
      kycProgramId: '',
      transaction: {
        Incoming: false,
        Outgoing: false
      },
      tableData: [
        {
          level: 'Severe',
          score: '91-100'
        },
        {
          level: 'High',
          score: '71-90'
        },
        {
          level: 'Moderate',
          score: '31-70'
        },
        {
          level: 'Low',
          score: '0-30'
        }
      ],
      selectList: [
        {
          chain: 'Ethereum',
          token: 'ERC20'
        },
        // {
        //   chain: 'Bitcoin',
        //   token: 'BTC'
        // },
        // {
        //   chain: 'TRON',
        //   token: 'TRC20'
        // },
        {
          chain: 'BSC',
          token: 'BEP20'
        },
        // {
        //   chain: 'IoTeX',
        //   token: 'IoTeX'
        // },
        {
          chain: 'Polygon',
          token: 'Polygon'
        },
        {
          chain: 'Avalanche',
          token: 'Avalanche'
        },
        {
          chain: 'Arbitrum One',
          token: 'Arbitrum'
        },
        // {
        //   chain: 'OP Mainnet',
        //   token: 'Optimism'
        // },
        {
          chain: 'Base',
          token: 'Base'
        }
      ],
      chain: 'Ethereum',
      selectValue: 'ERC20',
      symbol: 'ETH',
      tokenName: '',
      skeleton: {
        addressLabels: false,
        addressOverview: false,
        riskscore: false,
        transactionsInvestigation: false,
        addressAction: false,
        addressTrace: false,
        tokens: false
      },
      findAddress: '',
      labelList: ['Risk Type', 'Address/Risk label', 'Volume(USD)%'],
      paginatedData: [],
      scoreText: '',
      requestParams: 'ETH',
      kytDetailstip: false,
      transactionsInvestigationDataLoading: true,
      transactionsInvestigationData: {},
      graphFilterStartTime: null,
      graphFilterEndTime: null,
      // tokenImgName: '',
      decimals: 18,
      controller: new AbortController()
    }
  },
  computed: {
  },
  watch: {
  },
  async created () {
    this.blockchainId = this.$route.query.blockchainId
    this.kycProgramId = this.$route.query.kycProgramId
    if (this.blockchainId === '7') {
      this.chain = 'Polygon'
      this.selectValue = 'Polygon'
      this.symbol = 'MATIC'
      this.tokenName = 'Polygon'
    } else if (this.blockchainId === '8') {
      this.chain = 'Base'
      this.selectValue = 'Base'
      this.symbol = 'ETH'
      this.tokenName = 'Base'
    }
    this.userWalletAddress = this.$route.query.userWalletAddress
    this.zkmeId = this.$route.query.zkmeId
    await this.getTokenList()
    this.getDetail()
  },
  methods: {
    async setSelect (val, flg) {
      this.skeleton = {
        addressLabels: false,
        addressOverview: false,
        riskscore: false,
        transactionsInvestigation: false,
        addressAction: false,
        addressTrace: false,
        tokens: false
      }
      this.paginatedData = []
      this.transactionsInvestigationDataLoading = true
      this.decimals = Number(val.decimals)
      this.controller.abort()
      this.controller = new AbortController()
      if (flg === 'chain') {
        this.chain = val.chain
        this.selectValue = val.token
        this.symbol = val.symbol
        // this.tokenName = val.chain
        await this.getTokenList()
        await this.getDetail()
      } else if (flg === 'token') {
        this.requestParams = val.coin
        this.symbol = val.symbol
        // this.tokenImgName = val.coin
        this.tokenName = val.symbol
        await this.getDetail()
      } else if (flg === 'findAddress' && this.findAddress) {
        this.userWalletAddress = this.findAddress
        // this.requestParams = val.coin
        await this.getTokenList()
        await this.getDetail()
      }
    },
    setAxisChart () {
      const myChart = echarts.init(document.getElementById('AML'))
      let value = []
      let graphicalColor = ''
      let graphicalBorderColor = ''
      if (this.riskscore?.risk_level === 'Low') {
        value = [33, 33, 33]
      } else {
        if (this.riskscore?.hacking_event || this.riskscore.risk_detail.find(x => (x.type === 'malicious' || x.type === 'suspected_malicious') && x.address === this.userWalletAddress)) {
          value = [33, this.riskscore.score, 33]
        } else if (this.riskscore?.risk_detail && this.riskscore.risk_detail.length === 1 && this.riskscore.risk_detail.find(x => (x.type === 'high_risk' || x.type === 'medium_risk') && x.address === this.userWalletAddress)) {
          value = [this.riskscore.score, 33, 33]
        } else if (this.riskscore?.risk_detail && this.riskscore.risk_detail.length) {
          value = [33, 33, this.riskscore.score]
        } else {
          value = [33, 33, 33]
        }
      }

      if (this.riskscore.score >= 91 && this.riskscore.score <= 100) {
        graphicalColor = 'rgba(254, 164, 145, 0.5)'
        graphicalBorderColor = '#FEA491'
      } else if (this.riskscore.score >= 71 && this.riskscore.score <= 90) {
        graphicalColor = 'rgba(255, 226, 142, 0.5)'
        graphicalBorderColor = '#FFE28E'
      } else if (this.riskscore.score >= 31 && this.riskscore.score <= 70) {
        graphicalColor = 'rgba(100, 171, 255, 0.5)'
        graphicalBorderColor = '#64ABFF'
      } else if (this.riskscore.score <= 30) {
        graphicalColor = 'rgba(169, 225, 211, 0.5)'
        graphicalBorderColor = '#005563'
      }
      myChart.setOption({
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          left: 'center'
        },
        radar: [
          {
            indicator: [
              { text: 'Risky entity', max: 100 },
              { text: 'Hacking event', max: 100 },
              { text: 'Suspicious txn', max: 100 }
            ],
            axisName: {
              padding: [-3, -50],
              color: '#738C8F',
              fontSize: 12,
              lineHeight: 16,
              fontWeight: 500,
              verticalAlign: 'top'
            },
            center: ['50%', '63%'],
            radius: 100,
            splitNumber: 3,
            splitArea: {
              areaStyle: {
                color: ['#F7F7F7', '#FFFFFF']
              }
            },
            axisLine: {
              lineStyle: {
                color: '#005563'
              }
            }
          }
        ],
        series: [
          {
            type: 'radar',
            itemStyle: {
              color: '#005563',
              borderColor: ['#005563', 'red', 'red']
            },
            lineStyle: {
              width: 1
            },
            data: [
              {
                value: value,
                symbolSize: 4,
                areaStyle: {
                  color: graphicalColor
                },
                lineStyle: {
                  color: graphicalBorderColor
                }
              },
              {
                value: [100, 100, 100],
                symbolSize: 0
              }
            ]
          }
        ]
      })
    },
    setPieChart (id, data) {
      const colorArr = [
        '#64ABFF', '#FEA491', '#88F0DA', '#FFE28E', '#C08EFF', '#FF8EB7', '#8EFF93', '#8EE4FF', '#8EA0FF',
        '#FF8E8E', '#EF8EFF', '#F6FF8E', '#FFC48E', '#8EA0FF', '#8EF8FF', '#8EFFD0', '#FFB26C', '#B56CFF', '#FF6C6C'
      ]
      var myChart = echarts.init(document.getElementById(id))
      data.forEach((v, i) => {
        // const color = this.generateRandomColor()
        v.name = v.action
        v.value = v.count
        v.rate = v.proportion
        // colorArr.push(color)
        v.styColor = colorArr[i]
      })
      this.addressAction.push(data)
      // 绘制图表
      myChart.setOption({
        tooltip: {
          show: false
        },
        color: colorArr,
        series: [
          {
            // name: 'Access From',
            type: 'pie',
            radius: ['65%', '95%'],
            top: 0,
            bottom: 0,
            left: 0,
            startAngle: 0,
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            labelLine: {
              show: false
            },
            data: data
          }
        ]
      })
      if (id === 'Incoming') {
        this.incoming = data[0]
      }
      if (id === 'Outgoing') {
        this.outgoing = data[0]
      }
      myChart.on('mousemove', (params) => {
        const { value, name, color, rate } = params.data
        if (id === 'Incoming') {
          this.incoming = { value, name, color, rate }
        }
        if (id === 'Outgoing') {
          this.outgoing = { value, name, color, rate }
        }
      })
      myChart.on('mouseout', () => {
        if (id === 'Incoming') {
          this.incoming = data[0]
        }
        if (id === 'Outgoing') {
          this.outgoing = data[0]
        }
      })
    },
    requestSet (data, i) {
      return new Promise((resolve, reject) => {
        (async () => {
          try {
            const rp = await this.$api.request(`kyt.${data}`, {
              coin: this.requestParams,
              walletAddress: this.userWalletAddress
            }, this.controller, true)
            resolve(Object.assign(rp, { flg: data }))
          } catch (error) {
            reject(data)
          }
        })()
      })
    },
    async getDetail () {
      const rp = await this.$api.request('dashboard.getDetail', {
        blockchainId: this.blockchainId,
        userWalletAddress: this.userWalletAddress,
        zkmeId: this.zkmeId,
        kycProgramId: this.kycProgramId
      })
      if (rp.code === 80000000) {
        this.RP = rp.data
      }

      const arr = ['addressLabels', 'addressOverview', 'riskscore', 'transactionsInvestigation', 'addressAction', 'addressTrace']
      arr.forEach((v, i) => {
        this.skeleton[v] = true
        this.requestSet(v, i)
          .then((values) => {
            this.skeleton[values.flg] = false
            if (values.code === 80000000 && values.flg === 'addressLabels') {
              this.addressLabels = values.data?.data.label_list.filter(x => x)
            } else if (values.code === 80000000 && values.flg === 'addressOverview') {
              this.addressOverview = values.data?.data
              this.graphFilterStartTime = null
              this.graphFilterEndTime = null
            } else if (values.code === 80000000 && values.flg === 'riskscore') {
              this.riskscore = values.data.data
              this.paginatedData = values.data.data.risk_detail
              setTimeout(() => {
                this.setAxisChart()
              })
            } else if (values.code === 80000000 && values.flg === 'transactionsInvestigation') {
              this.transactionsInvestigation = values.data?.data
              this.transactionsInvestigationDataLoading = false
            } else if (values.code === 80000000 && values.flg === 'addressAction') {
              if (values.data.msg === 'UnsupportedAddressType') {
                this.addressAction = null
              } else {
                setTimeout(() => {
                  this.addressAction = []
                  if (values.data?.action_dic?.received_txs.length) {
                    this.transaction.Incoming = true
                    this.setPieChart('Incoming', values.data?.action_dic?.received_txs)
                  } else {
                    this.transaction.Incoming = false
                  }
                  if (values.data?.action_dic?.spent_txs.length) {
                    this.transaction.Outgoing = true
                    this.setPieChart('Outgoing', values.data?.action_dic?.spent_txs)
                  } else {
                    this.transaction.Outgoing = false
                  }
                })
              }
            } else if (values.code === 80000000 && values.flg === 'addressTrace') {
              if (values.data.msg === 'UnsupportedAddressType') {
                this.addressTrace = null
              } else {
                const addressTrace = {}
                for (const x in values.data.data) {
                  for (const y in values.data.data[x]) {
                    Object.assign(addressTrace, { [y]: values.data.data[x][y] })
                  }
                }
                this.addressTrace = addressTrace
              }
            } else if (values.code !== 80000000 && values.flg === 'transactionsInvestigation') {
              this.transactionsInvestigationDataLoading = false
              this[v] = null
            } else {
              this[v] = null
            }
          })
          .catch((errFlg) => {
            this.skeleton[errFlg] = false
            this.transactionsInvestigation = null
            this.transactionsInvestigationDataLoading = false
          })
      })
    },
    generateRandomColor () {
      // 生成随机的RGB数值
      const r = Math.floor(Math.random() * 256)
      const g = Math.floor(Math.random() * 256)
      const b = Math.floor(Math.random() * 256)

      // 将RGB数值转换为16进制
      const hexValue = '#' + ((1 << 24) | (r << 16) | (g << 8) | b).toString(16).slice(1)

      return hexValue
    },
    async getTokenList () {
      this.skeleton.tokens = true
      const rp = await this.$api.request('kyt.tokens', {
        chain: this.selectValue,
        address: this.userWalletAddress
      }, {}, true)
      this.skeleton.tokens = false
      if (rp.code === 80000000) {
        this.tokenList = rp.data
        // this.tokenImgName = this.tokenList[0]?.coin
        this.requestParams = rp.data[0].coin
        this.tokenName = rp.data[0].symbol
      }
    },
    async paginateTxnInvestigation (page, startTimestamp, endTimestamp) {
      try {
        this.transactionsInvestigationDataLoading = true
        if (startTimestamp) {
          this.graphFilterStartTime = startTimestamp
        }
        if (endTimestamp) {
          this.graphFilterEndTime = endTimestamp
        }
        const rp = await this.$api.request('kyt.transactionsInvestigation', {
          coin: 'ETH',
          walletAddress: this.userWalletAddress,
          page,
          ...(startTimestamp ? { startTimestamp: startTimestamp / 1000 } : {}),
          ...(endTimestamp ? { endTimestamp: endTimestamp / 1000 } : {})
        }, {}, true)
        if (rp.code === 80000000) {
          this.transactionsInvestigation = rp.data.data
          this.transactionsInvestigationDataLoading = false
        } else {
          this.transactionsInvestigation = null
          this.transactionsInvestigationDataLoading = false
        }
      } catch (error) {
        console.log(error)
        this.transactionsInvestigationDataLoading = false
      }
    },
    downloadPdf () {
      this.$message({
        message: 'Coming soon',
        iconClass: 'mico-lightTip',
        customClass: 'sty1-message',
        duration: 3000,
        offset: 32,
        center: true
      })
    },
    detail () {
      this.kytDetailstip = !this.kytDetailstip
    },
    copy () {
      if (!this.addressLabels) return
      const elInput = document.createElement('input')
      elInput.value = this.addressLabels.toString()
      document.body.appendChild(elInput)
      elInput.select()
      document.execCommand('Copy')
      elInput.remove()
      this.$message({
        message: 'Copied',
        iconClass: 'mcico-success',
        customClass: 'sty4-message',
        duration: 3000,
        offset: 32,
        center: true
      })
    },
    updateWalletAddress (val) {
      this.userWalletAddress = val
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/dashboard/_account-kyt.scss" scoped></style>
<style lang="scss" src="@/assets/css/views/dashboard/_account-kyt-icon.scss" scoped></style>
