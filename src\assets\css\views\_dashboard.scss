.statistics {
  margin-top: 30px;
  li {
    border-radius: 16px;
    box-sizing: border-box;
    padding: 24px;
    img {
      width: 60px;
      height: 60px;
      margin-right: 20px;
    }
    .t1 {
      line-height: 18px;
      color: #33585C;
    }
    .t2 {
      font-weight: 700;
      font-size: 24px;
      line-height: 30px;
      color: #002E33;
      margin-top: 4px;
    }
    &:nth-child(1) {
      background: rgba(17, 121, 133, 0.08);
      i {
        background: #005563;
      }
    }
    &:nth-child(2) {
      background: rgba(31, 192, 170, 0.08);
      i {
        background: #88F0DA;
      }
    }
    &:nth-child(3) {
      background: rgba(100, 152, 253, 0.08);
      i {
        background: #64ABFF;
      }
    }
    &:nth-child(4) {
      background: rgba(255, 119, 119, 0.08);
      i {
        background: #FEA491;
      }
    }
  }
  li + li {
    margin-left: 32px;
  }
}

.sty1-title {
  font-weight: 700;
  font-size: 20px;
  line-height: 25px;
  color: #002E33;
  margin-right: 16px;
}

.table {
  margin-top: 40px;
  .icon-download {
    cursor: pointer;
    display: inline-block;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #F7F7F7;
    font-size: 20px;
    text-align: center;
    line-height: 36px;
    margin-left: 16px;
    color: #002E33;
    &.disabled {
      color: rgba(0, 46, 51, 0.4);
    }
  }
  .table-header-btn {
    font-size: 14px;
    line-height: 20px;
    color: #738C8F !important;
    padding: 0;
    i {
      margin-left: 4px;
      font-size: 12px;
      font-weight: 600;
    }
  }
  .table-wrap {
    margin-top: 24px;
    // height: 538px;
  }
  .ico-status {
    width: 18px;
    height: 18px;
    border-radius: 3.6px;
    font-size: 14.4px;
    text-align: center;
    line-height: 18px;
    margin-right: 8px;
  }
  .mico-verified {
    background: rgba(119, 217, 128, 0.15);
    color: #77D980;
  }
  .mico-processing {
    background: rgba(95, 146, 244, 0.15);
    color: #5F92F4;
  }
  .mico-rejected {
    background: rgba(238, 105, 105, 0.15);
    color: #EE6969;
  }
  .network img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
}
.network-list i {
  font-size: 18px;
  margin-right: 4px;
}
.firstPanel i {
  margin: 0;
}

.information_select div{
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #002E33;
  padding: 5px 8px;
  cursor: pointer;
  border-radius: 6px;
}
.information_select div + div {
  margin-top: 2px;
}

.information_select div:hover{
  background-color: #F7F7F7;
}

.frist-table-name {
  padding-left: 20px;
}

.endButton {
  margin-top: 36px;
  button {
    width: auto;
  }
}

.selectedText {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #738C8F;
  margin-left: 16px;
}
.kycStatus i {
  font-size: 18px;
  margin-right: 4px;
}

.marg-l12 {
  margin-left: 12px;
}
.marg-l16 {
  margin-left: 16px;
}

@media screen and (max-width: 1100px) {
  .statistics {
    li {
      padding: 30.5px 24px;
      i {
        width: 36px;
        height: 36px;
        font-size: 19px;
        line-height: 36px;
        border-radius: 8px;
        margin-right: 12px;
      }
      .t2 {
        font-size: 20px;
        line-height: 25px;
      }
    }
  }
}



.kycmessage {
  min-width: 140px;
  height: 36px;
  background-color: #F2F7F7;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  i {
    margin-right: 6px;
    font-size: 24px;
    color: #005563 !important;
  }
  p {
    font-weight: 500;
    line-height: 18px;
    color: #002E33;
  }
}
.authorization-info-view-btn {
  color: var(---, #64ABFF);
  font-size: 14px;
  line-height: 18px; 
  cursor: pointer;
}
.authorization-info-view-content ul {
  color: #738C8F;
  font-size: 12px;
  line-height: 20px; 
}
.authorization-info-view-content i {
  color: #002E33;
  font-size: 16px;
  margin-left: 10px;
}
.icon-copy-wallet {
  font-size: 18px;
  margin-left: 4px;
}