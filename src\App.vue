<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
export default {
  // 登录态
  computed: {
    user () {
      return this.$store.state.auth.user || {}
    }
  },
  async created () {
    this.$api.defaults.headers.iToken = this.user.iToken

    this.$api.defaults.errorResponseHandler = err => {
      // 声明消息变量
      let message

      // 401 登录态过期
      // 不显示登录态过期的提示，并重新登录
      try {
        if ([400, 401, 403].includes(err.request?.status)) {
          const rp = JSON.parse(Buffer.from(err.response.data).toString())
          this.$message({
            message: err.request?.status === 401 ? rp?.msg || rp.code : 'Current network is insecure. Please switch networks and try again.',
            type: 'error',
            duration: 3000
          })
          // nest code error
          if (rp.statusCode !== 401) {
            return
          }
        }
      } catch (error) {
        console.log(error)
      }
      if (err.request?.status === 401) {
        this.cleanUser()
        return
      }

      if (['/login', '/forgot-password/step1', '/forgot-password/step2', '/reset-password', '/set-password/step1'].includes(this.$route.path)) {
        return
      }

      // 设置提示信息，如果是接口返回的错误信息则弹出 msg 字段的内容
      // 否则弹出 axios 的 message 字段的内容
      if (typeof err.code !== 'undefined') {
        message = err.msg || (`${err.code}`)
      } else {
        message = err.message || err
      }

      // 弹出错误信
      if (err.code === 80000014) {
        this.$message({
          message,
          type: 'error'
        })
      }

      // 如果是 Error 类型的消息则输出到控制台
      if (/Error/.test(Object.prototype.toString.call(err))) {
        console.error(err)
      }
    }
  },
  methods: {
    cleanUser () {
      // 清除登录态
      this.$store.commit('CLEAN_USER')
      // 重新登录
      if (this.$route.name !== 'Login') {
        this.$router.push({ name: 'Login' })
      }
    }
  }
}
</script>
