/**
 * 获取 dashboard - ZIS 表格数据
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/binding-cooperator/getListUsingPOST_3
 */
export const getList = ({ kycStatus, blockchainId, account, endTime, mchNo, page, size, sorts, startTime, tokenId, userWalletAddress, blockchainName, userId, citizenship, programName }) => {
  return {
    method: 'post',
    url: '/binding-cooperator/getListV2',
    data: {
      kycStatus, blockchainId, account, endTime, mchNo, page, size, sorts, startTime, tokenId, userWalletAddress, blockchainName, userId, citizenship, programName
    }
  }
}

/**
 * 导出 表格 数据
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/binding-cooperator/exportListUsingPOST
 */
export const exportList = ({ blockchainId, blockchainName, account, endTime, mchNo, page, size, sorts, startTime, tokenId, userWalletAddress, citizenship, programName }) => {
  return {
    method: 'post',
    url: '/binding-cooperator/exportList',
    data: {
      blockchainId, blockchainName, account, endTime, mchNo, page, size, sorts, startTime, tokenId, userWalletAddress, citizenship, programName
    },
    responseType: 'blob'
  }
}

/**
 * 获取账号详情
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/binding-cooperator/queryUserKycInfoUsingPOST
 */
export const getDetail = ({ kycProgramId, zkmeId }) => {
  return {
    method: 'post',
    url: '/binding-cooperator/queryUserKycInfoV2',
    data: {
      kycProgramId,
      zkmeId
    }
  }
}

/**
 * 获取 dashboard - meID 表格数据
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/binding-cooperator/getListUsingPOST_3
 */
export const getMeIDList = ({ account, endTime, mchNo, page, size, sorts, startTime, userId, userWalletAddress }) => {
  return {
    method: 'post',
    url: '/meIdInfo/getList',
    data: {
      account, endTime, mchNo, page, size, sorts, startTime, userId, walletAddress: userWalletAddress
    }
  }
}

/**
 * 获取 dashboard - meID 表格数据
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/binding-cooperator/getListUsingPOST_3
 */
export const exportMeIDList = ({ account, endTime, mchNo, page, size, sorts, startTime, userId, userWalletAddress }) => {
  return {
    method: 'post',
    url: '/meIdInfo/exportMeIdList ',
    data: {
      account, endTime, mchNo, page, size, sorts, startTime, userId, walletAddress: userWalletAddress
    },
    responseType: 'blob'
  }
}
/**
 * 下载pdf表格
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/binding-cooperator/getListUsingPOST_3
 */
export const downloadKycReport = ({ blockchainId, userWalletAddress, zkmeId, kycProgramId, account, popBindingId }) => {
  return {
    method: 'post',
    url: '/binding-cooperator/downloadKycReport',
    data: {
      blockchainId,
      userWalletAddress,
      zkmeId,
      kycProgramId,
      account,
      popBindingId
    }
  }
}
