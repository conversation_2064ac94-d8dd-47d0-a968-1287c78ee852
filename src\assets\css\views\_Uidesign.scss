.ui_bg {
  background-color: #F0F0F0;
  border-radius: 13px;
  padding: 17px 20px;
  max-width: 1880px;
}
.mcico-dark {
  background: #FFFFFF;
}
.mcico-light {
  background: #005563;
}
.mcico-light, .mcico-dark {
  font-size: 30px;
  border-radius: 50%;
  padding: 2px;
  margin-top: 4px;
  margin-left: 8px;
  cursor: pointer;
}
.ui_selector {
  background-color: #FFFF;
  // max-width: 380px;
  border-radius: 12px;
  padding: 16px 20px 0 20px;
  min-width: 310px;
  .copy {
    font-size: 12px;
    line-height: 17px;
    background-color:#A9E1D3;
    padding: 3px 10px;
    border-radius: 26px;
    cursor: pointer;
  }
}
.ui_text {
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  color: #002E33;
}

.ui_color {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  margin: 0 16px 16px 0;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  // &:nth-child(6) {
  //   margin: 0 0 16px 0;
  // }
  // &:nth-child(12) {
  //   margin: 0 0 16px 0;
  // }
  // &:nth-child(18) {
  //   margin: 0 0 16px 0;
  // }
  .ui_color_hand {
    width: 50px;
    height: 25px;
  }
  .ui_color_Lfoot, .ui_color_Rfoot {
    width: 25px;
    height: 25px;
  }
  .mico-drawing {
    font-size: 24px;
    color:#002E33;
  }
}
.mico-light, .mico-dark, .mico-device, .mico-monitor-alt, .mico-mobile-alt {
  font-size: 16px;
  margin-right: 4px;
}
.ui_mgb_16 {
  margin-bottom: 16px;
}
.ui_mgt_14 {
  margin-top: 14px;
}
.ui_color_design {
  background-color: #F2F7F7;
  cursor: pointer;
  position: relative;
  .color_picker {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
  }
}
.mode_choose {
  background-color: #F3F3F3;
  border-radius: 58px;
  padding: 4px;
  margin: 16px 0;
  color:#002E33;
  div {
    font-weight: 500;
    font-size: 14px;
    line-height: 28px;
    text-align: center;
    cursor: pointer;
  }
  .select {
    color: #FFFF;
    border-radius: 100px;
    background-color: #005563;
  }
}
.ui_textarea {
  margin-top: 16px;
  width: calc(100% - 20px);
  background-color: #F7F7F7;
  border-radius: 8px;
  padding: 10px;
  resize: none;
  &:focus-visible {
    outline: none;
  }
}
.save {
  width: 80px;
  color: #FFFF;
  background-color: #005563;
  border-radius: 100px;
  margin-bottom: 16px;
  border: none;
}
.reset {
  color:#005563;
  background-color: #A9E1D3;
}
.save_configure {
  margin-top: 33px;
  width: auto;
}
.modeDark {
  .ui_bg {
    background-color: #282828;
  }
  .ui_selector {
    background-color: #141414;
  }
  .mode_choose {
    color: #FFFFFF;
    background-color: #272626;
  }
  .ui_text {
    color: #FFFFFF;
  }
  .mico-drawing {
    color: #FFFFFF;
  }
  .ui_textarea {
    background-color: #282828;
    color: #FFFFFF;
  }
  .copy {
    background-color: #A9E1D3;
    color: #002E33;
  }
  .ui_color_design {
    background-color: #282828;
  }
}
.ui_selector_collapse {
  padding: 16px 20px;
}
.ui_tip {
  font-weight: 450;
  font-size: 12px;
  line-height: 16px;
}
.mcico-ui-success {
  position: absolute;
  top: 5px;
  right: 5px;
}

.preview_con{
  .mode_choose {
    background-color: #FFFFFF;
    width: 185px;
    margin-bottom: 11px;
  }

  .component_preview_con {
    position: relative;
    .preview_content {
      // transform: scaleX(0.8);
      // transform: scaleY(0.8);
      margin: 0px 20px;
      border-radius: 17.6px;
      box-sizing: border-box;
      background: #FFFFFF;
    }
  
    .preview_btn {
      width: 24px;
      height: 24px;
      cursor: pointer;
      padding: 4px 4px;
      border-radius: 36px;
      background-color: #FFF;
    }

    .preview_btn.choose_color{
      cursor: no-drop;
    }
  
    .chevron-left {
      font-size: 16px;
      margin-left: -2px;
      transform: rotate(90deg);
    }
  
    .chevron-right {
      font-size: 16px;
      margin-left: 2px;
      transform: rotate(-90deg);
    }
  
  }
}

.preview_con.dark {
  .mode_choose {
    background-color: #141414;
  } 

  .preview_btn {
    background-color: #0D0D0D;
    color:#FFFFFF;
  }

  // .chevron-left, .chevron-right {

  // }
}
.choose_color {
  opacity: 0.5;
  cursor: no-drop;
}

.leavetip_text {
  width: 340px;
  line-height: 18px;
  color: #002E33;
}
.leavetip_title {
  font-weight: 500;
  line-height: 22px;
  font-size: 18px;
  margin-bottom: 24px;
  color: #002E33;
}
.mico-warning {
  font-size: 20px;
  color: #EE6969;
  margin-right: 8px;
}
.dialog_button {
  .kycSave, .kycCancel {
    width: 109px;
    height: 36px;
    background-color: #F7F7F7;
    color: #33585C;
    border-radius: 26px;
    border: none;
    margin-top: 24px;
  }
  .kycCancel {
    background: rgba(238, 105, 105, 0.06);
    color: #EE6969;
    margin-left: 12px;
    margin: 24px 0 24px 12px;
  }
}
.warin_button {
  .kycSave, .kycCancel {
    width: auto !important;
  }
  .kycSave {
    padding: 0 36px !important;
  }
  .kycCancel {
    margin: 24px 0 24px 12px !important;
  }
  .Delete {
    padding: 0 30px;
  }
  .Apply, .Stay {
    padding: 0 36px;
  }
}

.custom-tooltip {
  position: relative;
  .tooltip-content {
    position: absolute;
    bottom: 0;
    left: -20px;
    transform: translateX(-100%);
    background-color: white;
    border-radius: 8px;
    padding: 6px 10px;
    color: #33585C;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    z-index: 1000;
  }
  
  .tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 8px 0 8px 8px;
    border-color: transparent transparent transparent #FFFFFF;
    right: -7px;
    top: 50%;
    transform: translateY(-50%);
  }
}

.custom-tooltip.dark {
  .tooltip-content{
    background-color: #343434;
    color: #FFF;
  }

  .tooltip-arrow {
    border-color: transparent transparent transparent #343434;
  }
}

@media screen and (max-width: 1480px) {
  .preview_con {
    transform: scale(0.8);
    transform-origin: top;
  }
  .mode_choose div {
    font-size: 12px;
  }
  .mcico-light, .mcico-dark {
    font-size: 26px;
  }
  .custom-tooltip {
    transform: scale(1.2);
    .tooltip-content {
      left: 50%;
      transform: translate(-50%, 50%);
      bottom: auto;
    }
    .tooltip-arrow {
      top: -11px;
      left: 50%;
      transform: rotate(270deg);
    }
  } 
}
@media screen and (max-width: 1200px) {
  .mode_choose div {
    font-size: 10px;
  }
}