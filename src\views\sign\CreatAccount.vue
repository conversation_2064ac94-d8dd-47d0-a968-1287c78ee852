<template>
  <div class="pg-login mui-fl-central">
    <div class="toplogo mui-fl-vert">
      <i class="mcico-colorLogo"></i>
      <div class="logo" />
      <div class="top-logo-name mui-fl-vert">Business</div>
    </div>
    <div class="login-left mui-fl-central">
      <div class="c creat-account mui-fl-col mui-fl-btw">
        <div class="mui-shr-0">
          <p class="t3">Welcome to zkMe</p>
          <div class="t4 t6">Already have an account?
            <router-link :to="{ name: 'Login', params: { id: 'step1' } }" class="t6 t5">Log in.</router-link>
          </div>
        </div>
        <m-form :model="form" ref="form" :rules="rules" :disabled="60 > count ? true : false" class="sty1-form sty4-form cen-align-err mui-fl-1 mui-fl-col mui-fl-btw" @submit.native.prevent>
          <div>
            <m-form-item prop="name" label="Name" class="mgb-26" >
              <m-input class="pdl35 creatInput" v-model="form.name" clearable placeholder="Enter the name you wish for us to call you"></m-input>
            </m-form-item>
            <m-form-item prop="company" label="Company" class="mgb-26">
              <m-input class="pdl35 creatInput" v-model="form.company" clearable placeholder="Enter your company"></m-input>
            </m-form-item>
            <m-form-item prop="website" label="Website" class="mgb-26">
              <m-input class="pdl35 creatInput" v-model="form.website" clearable placeholder="Enter project website"></m-input>
            </m-form-item>

            <m-form-item prop="email" label="Email">
              <m-input class="pdl35" v-model="form.email" clearable placeholder="Enter your email" :disabled="60 > count ? true : false" @input="listPassWord('email')"  @keyup.enter.native="creatClick"></m-input>
            </m-form-item>
          </div>
          <div class="t4 protocol" v-if="count === 60">
            I confirm my authority to register on behalf of the specified
            legal entity ("Company"). By clicking "Sign up" below, I verify
            that the Company acknowledges, accepts, and consents to
            <a href="https://zk.me/user-license-agreement" target="_blank" class="t5">zkMe's End-User License Agreement</a> and
            <a href="https://zk.me/app-privacy-policies" target="_blank" class="t5">Privacy Notice</a> in full.
          </div>
          <m-form-item class="btn">
            <div class="mui-fl-central timeClear" v-if="60 > count">
              <i class="mico-clock2"></i> You can resend after {{ count }} seconds...
            </div>
            <m-button type="primary" :loading="btnLoading" @click="creatClick" :disabled="!form.name || !form.company || !form.website || !form.email || 60 > count">{{ btnTxt }}</m-button>
          </m-form-item>
        </m-form>
      </div>
      <SignRightComponent></SignRightComponent>
    </div>
  </div>
</template>

<script>
import SignRightComponent from '@/components/sign/SignRightComponent.vue'
export default {
  name: 'Login',
  components: {
    SignRightComponent
  },
  data () {
    return {
      btnLoading: false,
      emailflag: false,
      passwordflag: false,
      typepassword: true,
      placeholder: '',
      checkForm: true,
      formValidator32: /([\s\S]){33}/,
      formValidator128: /([\s\S]){129}/,
      emailValidator: /^[0-9a-zA-Z_.-]+[@][0-9a-zA-Z_.-]+([.][a-zA-Z]+){1,2}$/,
      form: {
        name: '',
        company: '',
        website: '',
        email: '',
        token: ''
      },
      chineseValidator: /[\u4e00-\u9fa5\u3000-\u303F-\\，。、；’【】、（）·￥“”：《》？——！……\\]/g,
      setInterval: null,
      count: 60,
      clockOver: false,
      clickMessage: true,
      btnTxt: 'Sign up',
      rules: {
        name: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (this.formValidator32.test(value)) {
                this.checkForm = false
                callback(new Error('The length of Name can not exceed 32 characters.'))
              } else if (value) {
                this.form.name = this.form.name.trim()
                callback()
              } else {
                this.checkForm = false
                callback(new Error('Please enter your name.'))
              }
            }
          },
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value) {
                if (Object.values(this.form).indexOf('') === -1) {
                  this.checkForm = true
                } else {
                  this.checkForm = false
                }
                callback()
              }
            }
          }
        ],
        company: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (this.formValidator32.test(value)) {
                this.checkForm = false
                callback(new Error('The length of Company can not exceed 32 characters.'))
              } else if (value) {
                this.form.company = this.form.company.trim()
                callback()
              } else {
                this.checkForm = false
                callback(new Error('Please enter your company.'))
              }
            }
          },
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value) {
                if (Object.values(this.form).indexOf('') === -1) {
                  this.checkForm = true
                } else {
                  this.checkForm = false
                }
                callback()
              }
            }
          }
        ],
        website: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (this.formValidator128.test(value)) {
                this.checkForm = false
                callback(new Error('The length of Website can not exceed 128 characters.'))
              } else if (value) {
                this.form.website = this.form.website.trim()
                callback()
              } else {
                this.checkForm = false
                callback(new Error('Please enter your website.'))
              }
            }
          },
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value) {
                if (Object.values(this.form).indexOf('') === -1) {
                  this.checkForm = true
                } else {
                  this.checkForm = false
                }
                callback()
              }
            }
          }
        ],
        email: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (!this.emailValidator.test(value)) {
                this.checkForm = false
                callback(new Error('Please enter the correct email.'))
              } else if (value) {
                this.form.website = this.form.website.trim()
                callback()
              } else {
                this.checkForm = false
                callback(new Error('Please enter your email.'))
              }
            }
          },
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value) {
                if (Object.values(this.form).indexOf('') === -1) {
                  this.checkForm = true
                } else {
                  this.checkForm = false
                }
                callback()
              }
            }
          }
        ]
      }
    }
  },
  watch: {
    count (val) {
      if (!val) {
        this.btnTxt = 'Resend'
        clearTimeout(this.setInterval)
        this.count = 60
        this.clickMessage = true
        this.clockOver = true
      }
    },
    'form.email' (newVal, oldVal) {
      this.$nextTick(() => {
        this.$refs.form.clearValidate('email')
        this.rules.email = this.rules.email.slice(0, 1)
      })
    }
  },
  async beforeRouteLeave (to, from, next) {
    if (to.name === 'Login') {
      this.$store.commit('SET_USER_IF', null)
    }
    next()
  },
  created () {
    this.form = this.$store.state.auth.setUserif || this.form
  },
  methods: {
    creatClick (e) {
      e.preventDefault()
      window.grecaptcha.ready(() => {
        window.grecaptcha.execute(process.env.VUE_APP_RECAPTCHA_TOKEN, { action: 'submit' }).then((token) => {
          this.form.token = token
          this.onSubmit()
        })
      })
    },
    listPassWord (target) {
      this.form[target] = this.form[target].replace(this.chineseValidator, '')
    },
    onSubmit () {
      for (const i in this.form) {
        this.form[i] = this.form[i].trim()
      }
      this.$nextTick(() => {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            // this.checkForm = true
            this.btnLoading = true
            // this.$store.commit('SET_USER_IF', this.form)
            // this.$router.push('/set-password/step1')
            const rp = await this.$api.request('auth.sendVerifyEmail', this.form, {}, {}, false)
            this.btnLoading = false

            if (rp?.code === 80000000) {
              this.btnTxt = 'Verify link sent'
              this.checkForm = false
              this.count = this.count - 1
              this.setInterval = setInterval(() => {
                this.count = this.count - 1
              }, 1000)
            } else if (rp?.code === 80000012) {
              this.emailFieldErrorHandler(rp?.code)
            } else {
              if (rp.code === 80001000 || rp.code === 80000014) {
                return
              }
              if (rp.code === 80000017) {
                this.messages('Invalid email address.')
                return
              }
              this.messages('An error occurred while sending the email. Please try again later.')
            }
          }
        })
      })
    },
    emailFieldErrorHandler (errorCode) {
      this.rules.email.push({
        validator: (rule, value, callback) => {
          if (errorCode === 80000012) {
            this.checkForm = false
            callback(new Error('The email address has already been registered.'))
          } else if (errorCode === 80000011) {
            callback()
          }
          // else if (errorCode === 80000014) {
          //   callback(new Error('Too frequent operations. Please try again after 60 seconds.'))
          // }
        }
      })

      this.$refs.form.validateField('email')
      if (errorCode === 80000014) {
        this.setAutoClearError('email', 60000)
      }
    },
    setAutoClearError (fieldName, timeout) {
      setTimeout(() => {
        // 清除特定字段的验证结果
        this.$refs.form.clearValidate(fieldName)
        this.rules.email = [
          {
            required: true,
            type: 'email',
            message: 'Please enter the correct email.',
            trigger: 'blur'
          }
        ]
      }, timeout)
    },
    messages (msg) {
      this.$message({
        duration: 3000,
        offset: 32,
        center: true,
        customClass: 'sty1-message sty3-message',
        iconClass: 'mico-lightTip',
        message: msg
      })
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/sign/_login.scss" scoped></style>
