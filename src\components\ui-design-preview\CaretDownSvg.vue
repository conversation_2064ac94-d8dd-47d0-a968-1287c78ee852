<template>
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g opacity="0.5">
            <path
                d="M10.7124 6H5.28758C4.8837 6 4.68176 6 4.58826 6.07986C4.50712 6.14916 4.46406 6.25311 4.47243 6.35949C4.48208 6.48208 4.62487 6.62487 4.91046 6.91046L7.62288 9.62288C7.75488 9.75488 7.82088 9.82088 7.89699 9.84561C7.96394 9.86737 8.03606 9.86737 8.10301 9.84561C8.17912 9.82088 8.24512 9.75488 8.37712 9.62288L11.0895 6.91046C11.3751 6.62487 11.5179 6.48208 11.5276 6.35949C11.5359 6.25311 11.4929 6.14916 11.4117 6.07986C11.3182 6 11.1163 6 10.7124 6Z"
                :fill="themeColor1" stroke="themeColor1" stroke-width="1.33333" stroke-linecap="round"
                stroke-linejoin="round" />
        </g>
    </svg>
</template>
<script>
export default {
  name: 'CaretDownSvg',
  props: {
    themeColor1: {
      type: String,
      required: true,
      default: () => {
        return '#005563'
      }
    }
  }
}
</script>
