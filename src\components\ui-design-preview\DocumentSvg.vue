<template>
   <svg
    width="293"
    height="208"
    viewBox="0 0 293 208"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="279"
      y="14"
      width="180"
      height="265"
      rx="30"
      transform="rotate(90 279 14)"
      :fill="themeColor3"
    />
    <path
      d="M279 153L279 164C279 180.569 265.569 194 249 194L44 194C27.4314 194 14 180.569 14 164L14 153L279 153Z"
      :fill="themeColor2"
      fill-opacity="0.6"
    />
    <path
      d="M36 6V6C19.4315 6 6 19.4315 6 36V36"
      :stroke="themeColor1"
      stroke-opacity="0.8"
      stroke-width="10"
      stroke-linecap="round"
    />
    <path
      d="M257 202V202C273.569 202 287 188.569 287 172V172"
      :stroke="themeColor1"
      stroke-opacity="0.8"
      stroke-width="10"
      stroke-linecap="round"
    />
    <path
      d="M257 6V6C273.569 6 287 19.4315 287 36V36"
      :stroke="themeColor1"
      stroke-opacity="0.8"
      stroke-width="10"
      stroke-linecap="round"
    />
    <path
      d="M36 202V202C19.4315 202 6 188.569 6 172V172"
      :stroke="themeColor1"
      stroke-opacity="0.8"
      stroke-width="10"
      stroke-linecap="round"
    />
    <rect
      opacity="0.5"
      x="34"
      y="32"
      width="35"
      height="8"
      rx="4"
      :fill="themeColor1"
    />
    <rect
      opacity="0.5"
      x="224"
      y="32"
      width="35"
      height="8"
      rx="4"
      :fill="themeColor1"
    />
    <rect
      opacity="0.5"
      x="34"
      y="167"
      width="64"
      height="8"
      rx="4"
      :fill="themeColor1"
    />
    <rect
      opacity="0.5"
      x="235"
      y="167"
      width="24"
      height="8"
      rx="4"
      :fill="themeColor1"
    />
    <rect
      opacity="0.2"
      x="118"
      y="58"
      width="35"
      height="8"
      rx="4"
      :fill="themeColor1"
    />
    <rect
      opacity="0.5"
      x="118"
      y="74"
      width="88"
      height="8"
      rx="4"
      :fill="themeColor1"
    />
    <rect
      opacity="0.2"
      x="118"
      y="96"
      width="35"
      height="8"
      rx="4"
      :fill="themeColor1"
    />
    <rect
      opacity="0.5"
      x="118"
      y="112"
      width="142"
      height="8"
      rx="4"
      :fill="themeColor1"
    />
    <rect
      opacity="0.5"
      x="118"
      y="128"
      width="76"
      height="8"
      rx="4"
      :fill="themeColor1"
    />
    <rect
      x="38"
      y="60"
      width="60"
      height="73"
      rx="8"
      :fill="themeColor3"
      :stroke="themeMode === 'dark'? themeColor1 : themeColor2"
      stroke-width="7"
    />
    <circle
      cx="57.1444"
      cy="91.1444"
      r="3.14442"
      :fill="themeColor1"
      fill-opacity="0.8"
    />
    <path
      d="M78.7063 98.7813C78.9544 98.7813 79.1569 98.9827 79.1423 99.2303C78.8615 103.983 74.148 107.765 68.3746 107.765C62.6012 107.765 57.8878 103.983 57.607 99.2303C57.5924 98.9827 57.7949 98.7813 58.0429 98.7813L68.3746 98.7812L78.7063 98.7813Z"
      :fill="themeColor1"
      fill-opacity="0.8"
    />
    <circle
      cx="79.6054"
      cy="91.1444"
      r="3.14442"
      :fill="themeColor1"
      fill-opacity="0.8"
    />
  </svg>
</template>
<script>
export default {
  name: 'DocumentSvg',
  props: {
    themeColor1: {
      type: String,
      required: true,
      default: () => {
        return '#005563'
      }
    },
    themeColor2: {
      type: String,
      required: true,
      default: () => {
        return '#CCDDE0'
      }
    },
    themeColor3: {
      type: String,
      required: true,
      default: () => {
        return '#E8F4F5'
      }
    },
    themeMode: {
      type: String,
      required: true,
      default: () => {
        return 'light'
      }
    }
  }
}
</script>
