<template>
    <div class="bg">
        <header class="mui-fl-central sty1-header">
            <i class="popico-back header-back" />
            <span>Proof-of-Citizenship (PoC)</span>
        </header>
        <div class="mui-fl-vert step-box">
            <i class="popico-credit-card-scan mui-fl-central icon" />
            <p class="c1">Government-issued ID (2/3)</p>
        </div>
        <div class="padd">
            <h2 class="t1">Scan your ID</h2>
            <p class="c2">Please have your document ready.</p>
            <DocumentSvg class="img" :themeMode="themeMode" :themeColor1="themeColor1" :themeColor2="themeColor2" :themeColor3="themeColor3" />
            <ul class="list">
                <li v-for="item of list" :key="item.id" class="mui-fl-vert list-item">
                    <i :class="[item.icon, 'icon1', 'mui-fl-central']" />
                    <p class="c3">{{ item.title }}</p>
                </li>
            </ul>
            <div class="mui-fl-central sty1-footer">
                <m-button class="width-3" type="primary" round size="large">Continue</m-button>
            </div>
        </div>
    </div>
</template>
<script>
import { DocumentSvg } from '@/components/ui-design-preview'
export default {
  name: 'DocumentScan',
  components: { DocumentSvg },
  props: {
    themeColor1: {
      type: String,
      required: true,
      default: () => {
        return '#005563'
      }
    },
    themeColor2: {
      type: String,
      required: true,
      default: () => {
        return '#CCDDE0'
      }
    },
    themeColor3: {
      type: String,
      required: true,
      default: () => {
        return '#E8F4F5'
      }
    },
    themeMode: {
      type: String,
      required: true,
      default: () => {
        return 'light'
      }
    }
  },
  data () {
    return {
      list: [
        {
          id: 1,
          icon: 'popico-image-polaroid-user',
          title: 'Use portrait orientation'
        },
        {
          id: 2,
          icon: 'popico-bolt',
          title: 'Turn off the flash on your camera'
        },
        {
          id: 3,
          icon: 'popico-dial-low',
          title: 'Use a dark background'
        },
        {
          id: 4,
          icon: 'popico-images-user',
          title: 'Take the photo on a flat surface'
        }
      ]
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/ui-design-preview/_document_scan.scss" scoped></style>
