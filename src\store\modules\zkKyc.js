import * as types from './../mutation-types'

export default {
  state: {
    selectCountries: 0,
    countriesLength: 0,
    geoCountriesLength: 0
  },
  mutations: {
    [types.SET_SELECTCOUNTRIES] (state, payload) {
      state.selectCountries = payload
    },
    [types.SET_GEOSELECTCOUNTRIES] (state, payload) {
      state.geoCountriesLength = payload
    },
    [types.SET_COUNTRIESLENGTH] (state, payload) {
      state.countriesLength = payload
    }
  }
}
