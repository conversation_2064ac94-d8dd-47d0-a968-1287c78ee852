<template>
  <div style="min-width: 880px;">
    <div class="pg-title1 mui-fl-vert">
      <i class="mcico-active-integration"></i>
      Integration
    </div>
    <div v-if="access" class="noAccess mui-fl-vert mui-fl-col">
      <div class="n">No access</div>
      <div class="contact mui-fl-vert e">
        <i class="mico-a-apiemail"></i>
        Contact us to get access to zkMe APIs:
        <a class="email" href="mailto: <EMAIL>"><EMAIL></a>
      </div>
      <div class="mui-fl-vert mui-fl-col b">
        <div class="apis">zkMe APIs include:</div>
        <div class="zkKyc p">zkKYC</div>
        <div class="zkKyc">MeID Verify</div>
      </div>
    </div>
    <div v-else>
      <div class="mui-fl-btw zkmeApi">
        <m-collapse class="igCollapse" @change="collapse = !collapse" v-model="collapseFlg">
          <m-collapse-item name="1">
            <template slot="title">
              <div class="mui-fl-btw">
                <div class="ct mui-fl-vert">
                  <div>zkMe API Access Permissions</div>
                  <i :class="['mico-fold', !collapse ? 'rt' : '']"></i>
                </div>
              </div>
            </template>
            <div class="authority">
              <div v-for="(index, key) of apiList.apiAccessPermissions" :key="key" :class="['mui-fl-vert', index.valid ? 'kmVerify' : '']">
                <!-- <div class="radio"></div> -->
                <span class="meName">{{ index.name }}</span>
                <span class="meState" v-if="!index.valid">No access yet</span>
              </div>
            </div>
          </m-collapse-item>
        </m-collapse>

        <div class="contact igcon">
          <i class="mico-a-apiemail"></i>
          Contact us to get access to zkMe APIs:
          <a class="email" href="mailto: <EMAIL>"><EMAIL></a>
        </div>
      </div>

      <IntegrationWhitelist v-if="whiteList" ref="IntegrationWhitelist" @leaveFlg="leaveFlg" @changeTabs="changeTabs" :whiteList="whiteList" @updataList="getIntegrationList"></IntegrationWhitelist>

      <div v-if="tabsFlg" class="apiKeycla">
        <div class="title">
          Your API key
        </div>
        <div class="tip mui-fl-vert">
          For all server-side integrations, please make use of the API Key provided. It is imperative that the API Key is never exposed or utilized in any client-side integration.
        </div>
        <div class="content mui-fl-vert">
          <span class="apd mui-fl-vert"><i class="mico-AppID"></i> AppID:</span>
          <span class="apn mui-fl-vert">
            {{ apiList.mchNo }}
            <m-popover
              placement="top"
              width="64"
              trigger="manual"
              popper-class="NotRecord_popover WhiteList"
              content="Copied!"
              v-model="popoverFlg"
            >
              <i slot="reference" class="mico-copy" @click="copy(apiList.mchNo, 'mchNo')"></i>
            </m-popover>
          </span>
          <span class="api mui-fl-vert"><i class="mico-APIKey"></i> API Key:</span>
          <m-button class="apb" v-if="!setGenerated" @click="generateApi">
            Generate API Key
          </m-button>
          <span class="mui-fl-vert" v-else>
            <span class="apn generated">Generated</span>
            <div class="reset mui-fl-central " @click="restTime">
              <i class="mico-a-apireset" />
              <span class="apt">Reset</span>
            </div>
          </span>
        </div>
      </div>
    </div>

    <m-dialog
      :visible.sync="dialogVisible"
      :show-close="apiRest"
      width="500px"
      class="integration-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="timeClear"
    >
      <div class="dbg mui-fl-central" slot="title">
        <img src="@/assets/img/api-logo.png" alt="">
      </div>
      <div class="dh mui-fl-hori" v-if="!apiRest">Your API Key</div>
      <div class="dh mui-fl-hori" v-else>Reset API Key</div>

      <div v-if="!apiRest">
        <div class="dk mui-fl-vert">{{ apiKey }}<i class="mico-copy" @click="copy(apiKey)"></i></div>
        <div class="dt">
          <div>
            Please keep your API Key safe,<br/>
            as it <span class="will">WILL NOT</span> be displayed on our website again.
          </div>
          <div>
            You can reset your API Key at any time,<br/>
            but this will cause the original API Key to become invalid.
          </div>
        </div>
        <div class="mui-fl-hori">
          <m-button class="db" @click="acknowledged('Acknowledged')">Acknowledged</m-button>
        </div>
      </div>

      <div v-else>
        <div class="tipWarning mui-flex">
          <div>
            <i class="mico-warning"></i>
          </div>
          <div class="tipText">
            <div class="tipHand">
              Please notice:<br/>
            </div>
            API Key reset will cause the original API Key to<br/>
            become invalid.
          </div>
        </div>
        <div class="mui-fl-hori">
          <m-button v-if="stime !== 10" class="db watitdb">Confirm ({{ stime }})</m-button>
          <m-button v-else class="db" @click="acknowledged('Confirm')">Confirm</m-button>
        </div>
      </div>
    </m-dialog>
  </div>
</template>
<script>
import IntegrationWhitelist from '@/components/Integration-whitelist/IntegrationWhitelist.vue'
export default {
  name: 'Login',
  components: { IntegrationWhitelist },
  data () {
    return {
      dialogVisible: false,
      apiRest: false,
      stime: 10,
      setI: '',
      handFlagf: 'Activation',
      inp: null,
      apiState: 3,
      apiKey: '',
      apiList: {
        mchNo: '',
        apiAccessPermissions: []
      },
      setGenerated: false,
      access: false,
      collapse: false,
      collapseFlg: ['1'],
      tabsFlg: false,
      whiteList: null,
      popoverFlg: false,
      leaveTip: false,
      subText: ''
    }
  },
  async created () {
    await this.getIntegrationList()
  },
  async beforeRouteLeave (to, from, next) {
    if (this.subText !== 'leave') {
      await this.$refs.IntegrationWhitelist?.changeTabs(to.name)
    }
    if (this.subText !== 'Save') {
      next()
    }
  },
  methods: {
    leaveFlg (val) {
      this.subText = val
    },
    changeTabs (val) {
      this.tabsFlg = val
    },
    async getIntegrationList () {
      const rp = await this.$api.request('apikey.mchInfo', {}, {}, true)
      if (rp.code === 80000000) {
        this.whiteList = rp.data
        this.apiList.mchNo = rp.data.mchNo
        this.apiRest = rp.data.apiKeyGenerated
        this.setGenerated = rp.data.apiKeyGenerated
        rp.data.apiAccessPermissions.sort((x, y) => {
          if (x.valid < y.valid) {
            return 1
          }
          if (x.valid > y.valid) {
            return -1
          }
          return 0
        })
        if (!rp.data.apiAccessPermissions.find(x => x.valid)) {
          this.access = true
        }
        this.apiList.apiAccessPermissions = rp.data.apiAccessPermissions
      }
    },
    timeClear () {
      if (this.stime === 10) {
        this.apiRest = true
      }
      this.stime = 10
      clearInterval(this.setI)
    },
    async acknowledged (val) {
      if (val === 'Acknowledged') {
        this.setGenerated = true
        this.dialogVisible = false
      } else if (val === 'Confirm') {
        await this.generateApi()
        this.apiRest = !this.apiRest
      }
    },
    restTime () {
      this.dialogVisible = true
      this.stime = 9
      this.setI = setInterval(() => {
        this.stime = this.stime - 1
        if (this.stime <= 0) {
          clearInterval(this.setI)
          this.stime = 10
        }
      }, 1000)
    },
    copy (data, index) {
      const inp = document.createElement('input')
      document.body.appendChild(inp)
      inp.value = data
      inp.select()
      document.execCommand('Copy')
      document.body.removeChild(inp)
      if (index) {
        this.popoverFlg = true
        let time = null
        time = setTimeout(() => {
          this.popoverFlg = false
          clearTimeout(time)
        }, 1000)
        return
      }
      this.$message({
        message: 'Copied',
        iconClass: 'mcico-success',
        customClass: 'sty4-message',
        duration: 3000,
        offset: 32,
        center: true
      })
    },
    async generateApi () {
      const rp = await this.$api.request('apikey.generate', {}, {}, true)
      if (rp.code === 80000000) {
        this.dialogVisible = true
        this.apiKey = rp.data.apiKey
      }
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/_integration.scss" scoped />
