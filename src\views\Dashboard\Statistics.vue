<template>
  <div>
    <div class='pg-title1 mui-fl-vert mui-shr-0'>
      <i class='mcico-active-dashboard'></i>
      Statistics (Beta)
    </div>
    <div :class="[(userDay === '7day' || !MoonData.data.length) && !colStyle ? 'mui-fl-btw' : 'mui-fl-col']">
      <div :class="['mui-fl-1', 'echarts-block', (userDay === '7day' || !MoonData.data.length) && !colStyle ? 'mgr40' : 'mgb45']" ref="leftBlock">
        <div class="name">User Number</div>
        <div class="title">The number of users who have passed KYC. </div>
        <div class="mui-fl-btw">
          <div :class="['block-day', 'mui-fl-1', 'mgr12', userDay !== '7day' && 'isSelect']" @click="changeDay('7day')">
            <div class="name">Users (7 days)</div>
            <div class="total">{{ (weekData.total || 0) | thousandth }}</div>
          </div>
          <div :class="['block-day', 'mui-fl-1', userDay !== '30day' && 'isSelect']" @click="changeDay('30day')">
            <div class="name">Users (30 days)</div>
            <div class="total">{{ (MoonData.total || 0) | thousandth }}</div>
          </div>
        </div>
        <div style="position: relative;">
          <div :style="{opacity: (userDay === '7day' ? weekData.data?.length : MoonData.data?.length) || 0}">
            <div class='e-chart-radar' id='polyline'></div>
          </div>
          <div class="noData mui-fl-central" v-if="userDay === '7day' ? !weekData.data?.length : !MoonData.data?.length">
            <img src="@/assets/img/norecord.png" />
          </div>
        </div>

        <div class="mui-fl-hori">
          <div class="mui-fl-vert">
            <img src="@/assets/img/year.png" style="margin-right: 4px;">
            {{ new Date().getFullYear() }}
          </div>
        </div>
      </div>
      <div style="flex: 1" class="echarts-block" ref="rightBlock">
        <div class="name">KYC Process Funnel</div>
        <div class="title">This funnel chart illustrates the total number of users who successfully passed each stage of
          the KYC process, along with the corresponding pass rate.</div>
        <div class="block-time">
          <div class="mui-fl-btw mui-fl-vert">
            <div class="name">Total conversion rate：{{totalConversionRate}}%</div>
            <m-date-picker class="sty1-date-editor sty3-date-editor mui-shr-0" popper-class="sty1-date-popper" prefix-icon="mico-date"
              v-model="picker" value-format="timestamp" format="yyyy.MM.dd" type="daterange" align="right"
              range-separator="-" start-placeholder="start" end-placeholder="end" unlink-panels :clearable="false"
              :editable="false" @change="userEventCount" :default-time="['00:00:00', '23:59:59']">
            </m-date-picker>
          </div>
          <div style="position: relative;">
            <ul class="nameList" :style="{left: colStyle ? '0%' : (userDay === '7day' || !MoonData.data.length) ? '7%' : '5%' }">
              <li v-for="(value, index) of titleName" :key="index">
                <i :class="value.icon"></i>
                {{ value.name }}
              </li>
            </ul>
            <div style="height: 600px;" class='e-chart-radar' id='funnel'></div>
          </div>
        </div>
      </div>
    </div>
    <m-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-class="sty1-dialog sty4-dialog sty5-dialog"
      title="Datails"
      :visible.sync="dialogVisible"
      width="760px">
      <div class="datailsTitle">
          This table shows the number of users who passed the previous step, the current step, and those who failed at the current step with detailed failure reasons.
        </div>
      <div class="mui-fl-btw">
        <div>
          <span class="title">Type</span>
          <div>
            <div class="kycDetailsList">{{ totalDetail.first }}</div>
            <div class="kycDetailsList">{{ totalDetail.last }}</div>
          </div>
        </div>
        <div class="mui-fl-vert">
          <div class="mgr12 sty1">
            <span class="title">Users</span>
            <div class="kycDetailsList">{{ totalDetail.firstRate | thousandth }}</div>
            <div class="kycDetailsList">{{ totalDetail.lastRate | thousandth }}</div>
          </div>
          <!-- <div class="sty2">
            <span class="title">Rate</span>
            <div class="kycDetailsList">{{ this.kycDetailsList.verification.rate }}%</div>
            <div class="kycDetailsList">{{ this.kycDetailsList.OCR.rate }}%</div>
          </div> -->
        </div>
      </div>
      <m-collapse class="igCollapse sty3-collapse sty4-collapse" @change="handleChange">
        <m-collapse-item name="1">
          <template slot="title">
            <div class="mui-fl-btw">
              <div class="mui-fl-vert error">
                <i :style="{transform: rotateError ? 'rotate(0deg)' : 'rotate(270deg)'}" v-if="totalDetail.error.errorTotal" class="mico-fold"></i> Failure Reasons
              </div>
              <div class="mui-fl-vert" style="margin-right: 12px;">
                <div style="min-width: 123px;">{{ totalDetail.error.errorTotal | thousandth }}</div>
                <!-- <div>{{ kycDetailsList.errorRate }}%</div> -->
              </div>
            </div>
          </template>
          <ul v-if="totalDetail.error.errorList" class="errorList">
            <li v-for="(data, index) in totalDetail.error.errorList" :key="index">
              <div class="mui-fl-btw">
                <div>{{ index }}</div>
                <div class="mui-fl-vert">
                  <div style="margin-right: -52px;min-width: 123px;">{{ totalDetail.error.errorList[index] | thousandth }}</div>
                  <!-- <div>{{ data.rate }}%</div> -->
                </div>
              </div>
            </li>
          </ul>
        </m-collapse-item>
      </m-collapse>
    </m-dialog>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { thousandth } from '@/utils/filters'
import { mapState } from 'vuex'

export default {
  data () {
    return {
      userDay: '7day',
      polylineChart: null,
      funnelChart: null,
      dialogVisible: false,
      rotateError: 0,
      kycDetailsList: {
        verification: {
          name: 'Verification Started',
          users: '',
          rate: ''
        },
        OCR: {
          name: 'OCR Passed',
          users: '',
          rate: ''
        },
        errorTotal: 0,
        errorRate: 0,
        error: {
          name: 'error',
          errorList: [
            {
              name: 'Unable to fill in required fields',
              users: 0,
              rate: 0
            },
            {
              name: 'Issuing institution format error',
              users: 0,
              rate: 0
            },
            {
              name: 'Document does not match',
              users: 0,
              rate: 0
            },
            {
              name: 'Nationality does not match',
              users: 0,
              rate: 0
            }
          ]
        }
      },
      query: {
        startTime: new Date() - 3600 * 1000 * 24 * 7,
        endTime: new Date().setHours(23, 59, 59, 0o0)
      },
      colStyle: false,
      weekData: {
        data: [],
        total: 0
      },
      MoonData: {
        data: [],
        total: 0
      },
      totalConversionRate: 0,
      totalDetail: {
        first: '',
        firstRate: 0,
        last: '',
        lastRate: 0,
        error: {
          errorTotal: 0,
          errorList: null
        }
      }
    }
  },
  computed: {
    ...mapState({
      level: ({ auth }) => auth.user && auth.user.level,
      isNoMint: ({ auth }) => auth.user && auth.user.isNoMint
    }),
    picker: {
      get () {
        return this.query.startTime
          ? [this.query.startTime, this.query.endTime]
          : ''
      },
      set (val) {
        this.query.startTime = val ? new Date(val[0]).getTime() : ''
        this.query.endTime = val ? new Date(val[1]).getTime() : ''
      }
    },
    titleName () {
      let text = [
        {
          name: 'Verification Started',
          icon: 'mcico-verification'
        },
        {
          name: 'OCR Passed',
          icon: 'mcico-ocr'
        },
        {
          name: 'Liveness Checked',
          icon: 'mcico-liveness'
        },
        {
          name: 'ZKP Generated',
          icon: 'mcico-zkp'
        },
        {
          name: 'SBT Minted',
          icon: 'mcico-sbtMinted'
        },
        {
          name: 'OnChain Minted',
          icon: 'mcico-Authorized'
        },
        {
          name: 'KYC Passed',
          icon: 'mcico-Passed'
        }
      ]
      if (this.isNoMint) {
        text = text.filter(x => x.name !== 'SBT Minted' && x.name !== 'OnChain Minted')
      }
      if (this.level !== 0) {
        text = text.filter(x => x.name !== 'OnChain Minted')
      }
      return text
    },
    funnelList () {
      let arr = [
        { value: 0, name: '', itemStyle: { color: '#A9E1D3' }, titileName: 'Verification Started', flag: 1 },
        { value: 0, label: { color: '#738C8F' }, itemStyle: { color: 'transparent', height: '30px' }, titileName: 'Verification Started' },
        { value: 0, name: '', itemStyle: { color: '#64ABFF' }, titileName: 'OCR Passed', flag: 1 },
        { value: 0, label: { color: '#738C8F' }, itemStyle: { color: 'transparent', height: '30px' }, titileName: 'OCR Passed' },
        { value: 0, name: '', itemStyle: { color: '#FFE28E' }, titileName: 'Liveness Checked', flag: 1 },
        { value: 0, label: { color: '#738C8F' }, itemStyle: { color: 'transparent', height: '30px' }, titileName: 'Liveness Checked' },
        { value: 0, name: '', itemStyle: { color: '#8EDEE3' }, titileName: 'ZKP Generated', flag: 1 },
        { value: 0, label: { color: '#738C8F' }, itemStyle: { color: 'transparent', height: '30px' }, titileName: 'ZKP Generated' },
        { value: 0, name: '', itemStyle: { color: '#88F0DA' }, titileName: 'SBT minted', flag: 1 },
        { value: 0, label: { color: '#738C8F' }, itemStyle: { color: 'transparent', height: '30px' }, titileName: 'SBT minted' },
        { value: 0, name: '', itemStyle: { color: '#B2D5FF' }, titileName: 'OnChain Minted', flag: 1 },
        { value: 0, label: { color: '#738C8F' }, itemStyle: { color: 'transparent', height: '30px' }, titileName: 'OnChain Minted' },
        { value: 0, name: '', itemStyle: { color: '#77D980' }, titileName: 'KYC Passed', flag: 1 }
      ]
      if (this.isNoMint) {
        arr = arr.filter(x => x.titileName !== 'SBT minted' && x.titileName !== 'OnChain Minted')
      }
      if (this.level !== 0) {
        arr = arr.filter(x => x.titileName !== 'OnChain Minted')
      }
      return arr
    }
  },
  async mounted () {
    this.colStyle = window.innerWidth <= 1550
    await this.handleChangeDate()
    this.userEventCount()
    this.$nextTick(() => {
      this.changeDay('7day', 'mounted')
      const polyline = document.getElementById('polyline')
      const funnel = document.getElementById('funnel')
      this.blockWidth = this.$refs.leftBlock.clientWidth
      // this.blockWidth = polyline.width
      this.polylineChart = echarts.init(polyline)
      this.funnelChart = echarts.init(funnel)
    })
  },
  methods: {
    handleChange (val) {
      this.rotateError = val.length
    },
    formatDate (date) {
      return new Date(date).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', second: '2-digit' }).replace(/\//g, '-')
    },
    async handleChangeDate () {
      const endTime = new Date().setHours(23, 59, 59, 0o0)
      const startTimeWeek = new Date() - 3600 * 1000 * 24 * 7
      const startTimeMoon = new Date() - 3600 * 1000 * 24 * 30

      const rpWeek = await this.$api.request('statistics.authorizedUserCount', {
        startTime: startTimeWeek,
        endTime
      })
      const rpMoon = await this.$api.request('statistics.authorizedUserCount', {
        startTime: startTimeMoon,
        endTime
      })
      if (rpWeek.code === 80000000) {
        this.weekData = {
          data: rpWeek.data.sort((a, b) => {
            if (new Date(a[0]).getTime > new Date(b[0]).getTime) {
              return 1
            } else {
              return -1
            }
          }),
          total: rpWeek.data.reduce((total, item) => total + item.count, 0)
        }
      }
      if (rpMoon.code === 80000000) {
        this.MoonData = {
          data: rpMoon.data.sort((a, b) => {
            if (new Date(a[0]).getTime > new Date(b[0]).getTime) {
              return 1
            } else {
              return -1
            }
          }),
          total: rpMoon.data.reduce((total, item) => total + item.count, 0)
        }
      }
      this.polylineChartFun(this.weekData.data)
    },
    calculateTotalCount (data) {
      if (this.isNoMint) {
        data['SBT minted'] = 0
        data['OnChain Minted'] = 0
      }
      if (this.level !== 0) {
        data['OnChain Minted'] = 0
      }
      return Object.values(data).reduce((total, count) => total + count, 0)
    },
    async userEventCount () {
      const rp = await this.$api.request('statistics.userEventCount', {
        startTime: this.query.startTime,
        endTime: this.query.endTime
      })
      const errRp = await this.$api.request('statistics.failReasonCount', {
        // eventCode: '',
        startTime: this.query.startTime,
        endTime: this.query.endTime
      })

      if (rp.code === 80000000 && errRp.code === 80000000) {
        const totalStartedVerification = rp.data['Verification Started']
        const totalAuthorized = rp.data['KYC Passed']
        let totalConversionRate = ((totalAuthorized / totalStartedVerification) * 100)
        if (totalConversionRate > 0) {
          totalConversionRate = totalConversionRate.toFixed(2)
        }
        this.totalConversionRate = totalConversionRate || 0
        this.funnelList.map((item, index) => {
          if (index % 2 === 0) {
            item.name = isNaN(thousandth(rp.data[item.titileName])) ? 0 : thousandth(rp.data[item.titileName])
          } else {
            const data = Number(((rp.data[this.funnelList[index + 1].titileName] / rp.data[item.titileName]) * 100).toFixed(2)) || 0
            item.name = data === Infinity ? 0 + '%' : data + '%'
            this.funnelList[index - 1].otherDatas = item.name
            let errorList = errRp.data[this.funnelList[index + 1].titileName]
            if (errorList) {
              const sortedEntries = Object.entries(errorList).sort((a, b) => {
                if (a[0] === 'other') return 1
                if (b[0] === 'other') return -1
                return b[1] - a[1]
              })
              errorList = Object.fromEntries(sortedEntries)
            }
            item.totalDetail = {
              first: item.titileName,
              firstRate: rp.data[item.titileName],
              last: this.funnelList[index + 1].titileName,
              lastRate: rp.data[this.funnelList[index + 1].titileName],
              error: {
                errorTotal: errRp.data[this.funnelList[index + 1].titileName] ? Object.values(errRp.data[this.funnelList[index + 1].titileName]).reduce((sum, value) => sum + value, 0) : 0,
                errorList: errorList
              }
            }
          }
          item.value = Number((rp.data[item.titileName] / this.calculateTotalCount(rp.data) * 100).toFixed(2)) || 0
        })
        // this.funnelList.sort((a, b) => {
        //   if (a.value > b.value) {
        //     return -1
        //   } else if (a.value < b.value) {
        //     return 1
        //   }
        // })
        this.kycDetailsList.verification.users = rp.data['Verification Started']
        this.kycDetailsList.verification.rate = Number(((rp.data['Verification Started'] / this.calculateTotalCount(rp.data) || 0) * 100).toFixed(2))
        this.kycDetailsList.OCR.users = rp.data['OCR Passed']
        this.kycDetailsList.OCR.rate = Number(((rp.data['OCR Passed'] / this.calculateTotalCount(rp.data) || 0) * 100).toFixed(2))
        // this.titleName = this.titleName.sort((a, b) => {
        //   if ((this.funnelList.find(x => x.titileName === (a.name === 'SBT Minted' ? 'SBT minted' : a.name)).value) >
        //     (this.funnelList.find(x => x.titileName === (b.name === 'SBT Minted' ? 'SBT minted' : b.name)).value)) {
        //     return -1
        //   } else {
        //     return 1
        //   }
        // })
      }
      this.funnelChartFun(rp)
      return rp
    },
    async changeDay (val, flag) {
      if (this.userDay === val) {
        return
      }
      this.userDay = val
      this.$nextTick(() => {
        if (!this.MoonData.data.length) {
          return
        }
        this.polylineChart.resize({
          width: val !== '7day' ? 'auto' : this.blockWidth
        })
        if (!this.colStyle) {
          this.funnelChart.resize({
            width: val !== '7day' ? 'auto' : this.blockWidth
          })
        }
        this.polylineChartFun(val !== '7day' ? this.MoonData.data : this.weekData.data)
      })
    },
    transformData (data) {
      const transformedData = data.map(item => { return [item.date, item.count] })

      const days = transformedData.map(item => item[1])

      const maxDay = Math.max(...days)
      const minDay = Math.min(...days)

      return {
        transformedData,
        maxDay,
        minDay
      }
    },
    async polylineChartFun (dataTime) {
      const chartDom = document.getElementById('polyline')
      const myChart = echarts.init(chartDom)

      const result = this.transformData(dataTime)
      console.log(result)
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            return params[0].data[0] + '<br />' +
            `<div style="background: #005563;width: 8px;height:8px;line-height: 8px;border-radius: 50%;"><div>
              <div style="margin: 10px 0px 10px 20px">${params[0].data[1]}</div>`
          },
          textStyle: {
            align: 'left'
          }
        },
        grid: {
          left: '10%',
          right: '10%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            formatter: 0,
            color: 'rgba(0, 0, 0, 0.7)'
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'background: rgba(0, 0, 26);'
            }
          },
          axisTick: {
            lineStyle: {
              color: 'none'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: result.maxDay <= 5 ? 5 : result.maxDay,
          axisLabel: {
            formatter: 0,
            color: 'rgba(0, 0, 0, 0.7)'
          },
          axisLine: {
            lineStyle: {
              type: 'dashed',
              color: 'none'
            }
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          },
          axisTick: {
            lineStyle: {
              color: 'none'
            }
          }
        },
        series: [
          {
            type: 'line',
            // clip: true,
            symbol: 'circle',
            symbolSize: 7.5,
            showSymbol: true,
            smooth: true,
            data: result.transformedData,
            width: '100%',
            lineStyle: {
              width: 2,
              color: '#005563',
              shadowColor: 'rgba(0, 85, 99, 0.40)',
              shadowBlur: 8,
              shadowOffsetY: 7
            },
            itemStyle: {
              color: '#005563',
              borderColor: '#FFF',
              borderWidth: 1
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(0, 85, 99, 0.6)'
                },
                {
                  offset: 1,
                  color: 'rgba(0, 85, 99, 0.05)'
                }
              ])
            }
          }
        ]
      }
      myChart.setOption(option)
    },
    funnelChartFun () {
      const chartDom = document.getElementById('funnel')
      const myChart = echarts.init(chartDom)
      const cascading = []
      // const max = Math.max(...this.funnelList.map(v => v.value))
      // const min = Math.min(...this.funnelList.map(v => v.value))
      for (const i in this.funnelList) {
        if (i & 1) {
          this.funnelList[i].emphasis = {
            label: {
              fontWeight: 600
            }
          }
          if (i === '1') {
            const data1 = JSON.parse(JSON.stringify(this.funnelList[i]))
            const data2 = JSON.parse(JSON.stringify(this.funnelList[i]))
            const data3 = JSON.parse(JSON.stringify(this.funnelList[Number(i) + 2]))
            data1.itemStyle.height = '48px'
            data2.itemStyle.color = 'rgba(0, 85, 99, 0.10)'
            data3.itemStyle.color = '#F2F7F7'
            data3.value = (data3.value * 0.76).toFixed(2) || 0
            cascading.push([{ ...data1 }, { ...data2 }, { ...data3 }])
          } else {
            const index = (i - 3) / 2
            if (cascading[index]) {
              const length = Number(i) === this.funnelList.length - 2
              const otherData = JSON.parse(JSON.stringify(cascading[index]))
              const otherData2 = JSON.parse(JSON.stringify(this.funnelList[i]))
              const otherData3 = JSON.parse(JSON.stringify(this.funnelList[i]))
              otherData3.itemStyle.color = '#F2F7F7'
              otherData[index].itemStyle.height = '78px'
              otherData[(index) + 1] = otherData2
              otherData[(index) + 1].itemStyle.height = '48px'
              otherData.slice(-1)[0].value = Number((length ? this.funnelList[this.funnelList.length - 1].value
                : JSON.parse(JSON.stringify(this.funnelList[Number(i) + 1])).value * 0.76).toFixed(2)) || 0
              otherData.push(otherData3)
              cascading.push(otherData)
            }
          }
        } else {
          this.funnelList[i].emphasis = {
            disabled: true
          }
        }
      }
      const series = [
        {
          name: 'Actual',
          type: 'funnel',
          left: this.colStyle ? '25%' : this.userDay === '7day' ? '35%' : '25%',
          width: this.colStyle ? '70%' : this.userDay === '7day' ? '65%' : '75%',
          minSize: '25%',
          label: {
            position: 'inside',
            formatter: '{b}',
            color: '#002E33',
            fontWeight: 500,
            fontSize: '16px'
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 0,
            height: '48px',
            borderJoin: 'miter'
          },
          emphasis: {
            // disabled: true,
          },
          data: this.funnelList,
          z: 100
        }
      ]
      cascading.forEach(v => {
        series.push({
          type: 'funnel',
          left: this.colStyle ? '25%' : this.userDay === '7day' ? '35%' : '25%',
          width: this.colStyle ? '70%' : this.userDay === '7day' ? '65%' : '75%',
          top: 60,
          minSize: '22%',
          emphasis: {
            disabled: true
          },
          label: {
            position: 'inside',
            formatter: '',
            color: '#738C8F',
            fontWeight: 400,
            fontSize: '14px'
          },
          itemStyle: {
            borderWidth: 0
          },
          labelLine: {
            show: false
          },
          data: v
        })
      })
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: (params) => {
            return params.data.otherDatas || params.data.name
          }
        },
        series: series
      }
      myChart.setOption(option)
      myChart.on('click', ({ data }) => {
        if (data.titileName && !data.flag) {
          this.totalDetail = data.totalDetail
          this.dialogVisible = true
        }
      })
    }
  }
}
</script>

<style lang='scss' src='@/assets/css/views/_statistics.scss' scoped></style>
