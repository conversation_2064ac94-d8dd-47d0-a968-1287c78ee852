<template>
    <svg
    width="266"
    height="266"
    viewBox="0 0 266 266"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect x="13" y="13" width="240" height="240" rx="36" :fill="themeColor3" />
    <rect
      x="94.4316"
      y="94.4326"
      width="77.1351"
      height="77.1351"
      rx="38.5676"
      :fill="themeColor3"
      :stroke="themeMode === 'dark' ? themeColor1 : themeColor2"
      stroke-width="7.58706"
    />
    <rect
      opacity="0.4"
      x="65"
      y="65"
      width="136"
      height="136"
      rx="68"
      :stroke="themeColor2"
      stroke-width="10"
      stroke-linejoin="round"
      stroke-dasharray="1 6"
    />
    <circle cx="118.361" cy="126.117" r="4.09896" :fill="themeColor1" />
    <path
      d="M146.467 136.071C146.79 136.071 147.054 136.334 147.035 136.657C146.669 142.853 140.525 147.783 132.999 147.783C125.473 147.783 119.329 142.853 118.963 136.657C118.944 136.334 119.208 136.071 119.531 136.071L132.999 136.071L146.467 136.071Z"
      :fill="themeColor1"
    />
    <circle cx="147.638" cy="126.117" r="4.09896" :fill="themeColor1" />
    <path
      d="M41 5V5C21.1177 5 5 21.1177 5 41V41"
      :stroke="themeColor1"
      stroke-width="10"
      stroke-linecap="round"
    />
    <path
      d="M225 261V261C244.882 261 261 244.882 261 225V225"
      :stroke="themeColor1"
      stroke-width="10"
      stroke-linecap="round"
    />
    <path
      d="M225 5V5C244.882 5 261 21.1177 261 41V41"
      :stroke="themeColor1"
      stroke-width="10"
      stroke-linecap="round"
    />
    <path
      d="M41 261V261C21.1177 261 5 244.882 5 225V225"
      :stroke="themeColor1"
      stroke-width="10"
      stroke-linecap="round"
    />
  </svg>
</template>
<script>
export default {
  name: 'FacialSvg',
  props: {
    themeColor1: {
      type: String,
      required: true,
      default: () => {
        return '#005563'
      }
    },
    themeColor2: {
      type: String,
      required: true,
      default: () => {
        return '#CCDDE0'
      }
    },
    themeColor3: {
      type: String,
      required: true,
      default: () => {
        return '#E8F4F5'
      }
    },
    themeMode: {
      type: String,
      required: true,
      default: () => {
        return 'light'
      }
    }
  }
}
</script>
