<template>
    <div ref="ndWrapper" class="bg mui-fl-col mui-fl-vert">
        <h1 class="t1">
            Proof-of-Citizenship (PoC)
        </h1>
        <div class="zkme" :class="themeMode" />
        <div>
            <p class="c1">
                Super easy with the following 3-step process:
            </p>
            <ul class="list">
                <li v-for="(item, index) of list" :key="item.id">
                    <ul class="mui-flex">
                        <li class="mui-fl-col mui-fl-vert list-item">
                            <i :class="[item.icon, 'icon', 'mui-fl-central']" />
                            <div v-show="index !== list.length - 1" class="spacer" />
                        </li>
                        <li class="mui-fl-col title-box">
                            <p :class="{ 'title': 1 }">{{ item.title }}</p>
                            <p v-show="item.cont" :class="{ 'cont': 1, 'cont-finished': !item.isFinished }">{{ item.cont }}
                            </p>
                        </li>
                    </ul>
                </li>
                <div class="user-tips mui-fl-vert">
                  <WarnTipSvg :themeMode="themeMode" :themeColor1="themeColor1" :themeColor2="themeColor2" />
                  Please note we will not save your facial information.
                </div>
            </ul>
        </div>
        <div class="mui-fl-central sty1-footer btn-box">
            <m-button class="width-3" type="primary" round size="large">Verify now</m-button>
        </div>
    </div>
</template>
<script>
import { WarnTipSvg } from '@/components/ui-design-preview'
export default {
  name: 'ProofOfIdentity',
  components: { WarnTipSvg },
  props: {
    themeColor1: {
      type: String,
      required: true,
      default: () => {
        return '#005563'
      }
    },
    themeColor2: {
      type: String,
      required: true,
      default: () => {
        return '#CCDDE0'
      }
    },
    themeMode: {
      type: String,
      required: true,
      default: () => {
        return 'light'
      }
    }
  },
  data () {
    return {
      list: [
        {
          id: '1',
          icon: 'popico-face-id',
          title: 'Scan your ID by mobile phone',
          isFinished: true,
          cont: ''
        },
        {
          id: '2',
          icon: 'popico-square-user',
          isFinished: false,
          title: 'Scan your face',
          cont: ''
        },
        {
          id: '3',
          icon: 'popico-wallet',
          isFinished: false,
          title: 'Get your PoC and SBT',
          cont: ''
        }
      ]
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/ui-design-preview/_proof_of_identity.scss" scoped></style>
