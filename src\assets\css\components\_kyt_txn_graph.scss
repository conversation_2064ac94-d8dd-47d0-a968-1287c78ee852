.main-container {
  width: 100%;
  height: 1020px;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  background: #f2f7f7;
  padding: 24px;
  margin-top: 16px;
  position: relative;
}

.graph-container {
  width: 100%;
  height: 1020px;
  overflow: hidden;
  user-select: none;
}

.download-img-renderer{
  pointer-events: none;
  position: absolute;
  top: 0;
  opacity: 0;
  .graph-container {
    width: 1000px;
  }
}

.wallet-icon {
  background-color: #fff;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-bottom: 3px;
}

.toolbar {
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  background: #fff;
  position: absolute;
  bottom: 50px;
  padding: 20px 40px;

  button:not(:first-child) {
    margin-left: 40px;
  }
  button i {
    cursor: pointer;
    font-size: 24px;
    color: #33585c;
  }
}

.wallet {
  position: relative;
  width: 160px;
  height: 84px;
  border-radius: 12px;
  background: #a9e1d3;
  padding: 12px;
  box-sizing: border-box;
}

.item-icon {
  background-color: #F7F7F7;
  width: 24px;
  height: 24px;
  border-radius: 50%;

  i {
    font-size: 13px;
    color:#33585C;
  }
}

.left-side .item-icon {
  margin-left: 5px;
}

.right-side .item-icon {
  margin-right: 5px;
}

.item-icon.warn{
  background-color: #EE6969;
  i {
    color: #FFFFFF;
  }
}

.left-side:not(.item-bot-left) {
  position: absolute;
  right: 164px;
  bottom: 35px;
}

.right-side:not(.item-bot-right) {
  position: absolute;
  left: 164px;
}

.right-side.item-top-right {
  bottom: 36px;
}

.right-side.item-cen-right {
  bottom: 35px;
}

.left-side.item-bot-left {
  position: absolute;
  right: 164px;
  top: 41px;
}

.right-side.item-bot-right {
  position: absolute;
  left: 164px;
  top: 44px;
}

.graph-item {
  position: absolute;
  cursor: pointer;
  height: 52px;
  margin-bottom: 24px;
  border-radius: 12px;
  background: #fff;
  padding: 8px 12px;
  box-sizing: border-box;
  display: flex;
}

.txns-count {
  text-decoration: underline;
  cursor: pointer;
}

.item-amount {
  display: flex;
  padding: 2.632px 5.264px;
  justify-content: center;
  white-space: pre;
  align-items: center;
  gap: 6.58px;
  border-radius: 38.823px;
  color: #33585C;
  font-size: 7.896px;
  font-style: normal;
  font-weight: 500;
  line-height: 10.528px;
  background: #f0f0f0;
}

.left-side .item-amount {
  margin-right: 12px;
}

.right-side .item-amount {
  margin-left: 12px;
}

.graph-item .mui-shr-0 p {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
}

.item-label {
  color: #002e33;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
}

.item-address {
  color: var(--t-3, #738c8f);
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}

.item-top-left .graph-item {
  right: 95px;
  top: -26px;
}

.item-cen-left .graph-item {
  right: 95px;
  bottom: -42px;
}

.item-bot-left .graph-item {
  right: 95px;
  bottom: -45px;
}

.item-top-right .graph-item {
  left: 94px;
  top: -26px;
}

.item-cen-right .graph-item {
  left: 95px;
  bottom: -42px;
}

.item-bot-right .graph-item {
  left: 95px;
  bottom: -45px;
}

.center-container {
  position: relative;
  width: 160px;
  height: 84px;
}

.filter-data-con {
  width: 610px;
  max-height: 1048px;
  flex-shrink: 0;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  background: #fff;
  padding: 27px 32px;
  position: relative;
  box-sizing: border-box;
}

.fold-button {
  position: absolute;
  left: -29px;
  top: 50%;
  cursor: pointer;
  transform: translateY(-50%);
  border-radius: 12px 0px 0px 12px;
  border: 1px solid #F0F0F0;
  background: #FFF;
  width: 29px;
  height: 62px;
  flex-shrink: 0;

  i {
    font-size: 20px;
    color: #738C8F;
  }
}

.expand-button {
  position: absolute;
  right: -1px;
  top: 50%;
  cursor: pointer;
  transform: translateY(-50%);
  border-radius: 12px 0px 0px 12px;
  border: 1px solid #F0F0F0;
  background: #FFF;
  width: 29px;
  height: 62px;
  flex-shrink: 0;

  i {
    font-size: 20px;
    color: #738C8F;
  }
}

.filter-data-con .title {
  color: #002e33;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  margin-bottom: 4px;
}

.data-filter {
  margin-top: 20px;
  margin-bottom: 36px;
}

.data-filter .el-select.sty1-select .el-input {
  width: 265px;
}

.analysis-con {
  margin-bottom: 16px;

  .analysis-title {
    color: #738c8f;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  .analysis-address {
    margin-left: 8px;
    color: #002e33;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
}

.in-txns-table{
  margin-top: 20px;
  margin-bottom: 20px;
}

.table-wrap {
  min-height: 300px;
}

.sender-add {
  color:#002E33;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
}

.sender-lab {
  color:#738C8F;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
}

.amount-range {
  margin-top: 20px;
}