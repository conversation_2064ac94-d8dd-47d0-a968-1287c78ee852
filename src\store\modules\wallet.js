import * as types from './../mutation-types'
import { createStore } from 'mipd'
import { <PERSON>rowser<PERSON>rovider, Contract, JsonRpcProvider, getAddress, ethers } from 'ethers'
import abi from '../../utils/contract'
import { MAINNET_NETWORK_LIST } from '../../utils/config'
const evmStore = createStore()
const evmState = {
  providers: evmStore.getProviders()
}
evmStore.subscribe((providers) => (evmState.providers = providers))

const findProvider = async (payload) => {
  if (!evmState.providers.length) return
  const walletName = evmState.providers.find(x => x.info.name === payload)?.info.rdns
  if (!walletName) return
  const { provider } = evmStore.findProvider({
    rdns: walletName || 'io.metamask'
  })
  const accounts = await provider.request({ method: 'eth_accounts', params: [] })
  return {
    provider: provider,
    accounts: accounts
  }
}

export default {
  state: {
    walletChainId: NaN,
    connectedAddress: '',
    provider: null,
    signer: null
  },
  mutations: {
    [types.SET_CONNECTED_ADDRESS] (state, payload) {
      state.connectedAddress = payload
    },
    [types.SET_WALLETCHAINID] (state, payload) {
      state.walletChainId = payload
    },
    [types.SET_PROVIDER] (state, payload) {
      state.provider = payload
    },
    [types.SET_SIGNER] (state, payload) {
      state.signer = payload
    }
  },
  actions: {
    async checkAddressAndConvert ({ state }, payload) {
      try {
        const checksumAddress = state.provider.getAddress(payload)
        console.log('Checksummed Address:', checksumAddress)
        return checksumAddress
      } catch (error) {
        console.error('Invalid address:', error)
        return null
      }
    },
    async getTransaction ({ state }, payload) {
      const provider = new ethers.JsonRpcProvider(MAINNET_NETWORK_LIST.find(x => x.chainId === payload.chainId).rpcUrls[0])
      const txReceipt = await provider.getTransactionReceipt(payload.txHash)
      const block = await provider.getBlock(txReceipt.blockNumber)
      return new Date(block.timestamp * 1000).getTime()
    },
    // 校验钱包地址下是否有代币
    async checkContract ({ state, commit, dispatch }, payload) {
      if (!await dispatch('checkCoin', payload.tokenAddress)) {
        return 'ErrorcontractAddress'
      }
      try {
        const contract = new Contract(
          payload.tokenAddress,
          abi,
          new JsonRpcProvider(MAINNET_NETWORK_LIST.find(x => x.chainId === payload.chainId).rpcUrls[0])
        )
        try {
          return {
            name: await contract.symbol(),
            tokenSymbol: await contract.decimals(),
            balanceOf: await contract.balanceOf(state.connectedAddress)
          }
        } catch (error) {
          return {
            name: await contract.name(),
            tokenSymbol: 6,
            balanceOf: 0
          }
        }
      } catch (error) {
        return 'ErrorcontractAddress'
      }
    },
    async checkCoin ({ state }, payload) {
      try {
        getAddress(payload)
        return true
      } catch (error) {
        return false
      }
    },
    // 连钱包
    async connectWallet ({ state, commit, dispatch }, payload) {
      if (!payload) return
      try {
        const provider = await findProvider(payload.chain)
        if (!provider || (!payload.type && !provider.accounts.length)) return
        const bp = new BrowserProvider(provider.provider)
        const signer = await bp.getSigner()
        // console.log(formatEther(await signer.getBalance()))
        // signer.balance = formatEther(await signer.getBalance())
        const { chainId } = await bp.getNetwork()
        commit(types.SET_CONNECTED_ADDRESS, signer.address)

        commit(types.SET_SIGNER, signer)

        commit(types.SET_WALLETCHAINID, Number(chainId))

        commit(types.SET_PROVIDER, provider.provider)

        dispatch('accountsandchainChangedChanged', payload.chain)

        dispatch('switchEthereumChain', '0x141f')
        // localStorage.setItem('walletAddress', signer.address)
      } catch (error) {
        commit(types.SET_CONNECTED_ADDRESS, '')
        return error
      }
    },
    // 断连钱包
    disconnect ({ commit }) {
      commit(types.SET_CONNECTED_ADDRESS, '')
    },
    // 切链
    async switchEthereumChain ({ state }, provider) {
      try {
        await state.provider.request({
          method: 'wallet_switchEthereumChain',
          params: [{ chainId: provider }]
        })
        // localStorage.setItem('chainId', provider)
      } catch (error) {
        // wallet_addEthereumChain
        if (error.code === 4902 || error.code === -32603) {
          const chainDetail = MAINNET_NETWORK_LIST.find(x => x.chainId === provider)
          await state.provider.request({
            method: 'wallet_addEthereumChain',
            params: [
              {
                chainId: chainDetail?.chainId,
                chainName: chainDetail?.chainName,
                nativeCurrency: chainDetail?.nativeCurrency,
                rpcUrls: chainDetail?.rpcUrls,
                blockExplorerUrls: chainDetail?.blockExplorerUrls
              }
            ]
          })
          // localStorage.setItem('chainId', provider)
        } else {
          throw new Error(`Switch chain failed: ${error.message}`)
        }
      }
    },
    // 监听钱包以及链发生变化
    async accountsandchainChangedChanged ({ state, commit, dispatch }) {
      state.provider.on('accountsChanged', (accounts) => {
        if (!accounts[0]) {
          dispatch('disconnect')
        } else {
          commit(types.SET_CONNECTED_ADDRESS, accounts[0])
        }
      })
      state.provider.on('chainChanged', (accounts) => {
        commit(types.SET_WALLETCHAINID, Number(accounts))
      })
    }

  }
}
