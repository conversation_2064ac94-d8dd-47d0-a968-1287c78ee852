<template>
  <div class="mui-fl-col mui-fl-vert">
    <m-form :model="form" ref="form" :rules="rules" class="sty5-form" @submit.native.prevent>
      <div class="t1">Create Token Distribution</div>
      <m-form-item prop="name">
        <div class="t2">Distribution Name</div>
        <m-input v-model="form.name" placeholder="Enter Distribution Name"></m-input>
      </m-form-item>

      <m-form-item prop="url">
        <div class="t2">Token Network</div>
        <m-dropdown trigger="click" class="sty1-dropdown">
          <span class="mui-fl-btw mui-fl-hori">
            Moca<i class="el-icon-arrow-down"></i>
          </span>
          <m-dropdown-menu slot="dropdown" class="sty1-dropdown-menu">
            <m-dropdown-item>Moca</m-dropdown-item>
          </m-dropdown-menu>
        </m-dropdown>
      </m-form-item>

      <m-form-item prop="address">
        <div class="t2">Token Contract Address</div>
        <m-input ref="contractAddress" v-model="form.address" placeholder="Enter the contract address of your token"></m-input>
      </m-form-item>

      <div v-if="contractFlag">
        <div v-if="contract.name" class="checkContract">
          <div class="mui-fl-btw t1">
            <div>{{ contract.name }}</div>
            <div>Amount: {{ contract.amount | toFormat }}</div>
          </div>
          <div class="t2">{{ form.address }}</div>
        </div>
        <div v-else-if="contractLoading" class="checkContract mui-fl-vert">
          <div class="loader loader3"></div>
          <div class="t3">Verifying</div>
        </div>
        <div v-else class="checkContractError mui-fl-vert">
          <i class="mico-kycTip"></i>
          <div>No token found with this token address. Please enter a valid token address.</div>
        </div>
      </div>

      <m-form-item prop="url">
        <div class="t2">Select Token DistributionType</div>
         <m-dropdown trigger="click" class="sty1-dropdown">
          <span class="mui-fl-btw mui-fl-hori">
            Airdrop<i class="el-icon-arrow-down"></i>
          </span>
          <m-dropdown-menu slot="dropdown" class="sty1-dropdown-menu">
            <m-dropdown-item>Airdrop</m-dropdown-item>
          </m-dropdown-menu>
        </m-dropdown>
      </m-form-item>

      <m-form-item prop="amount" :class="(contract.name && form.amount > Number(contract.amount)) && 'warnInput'">
        <div class="t2">Amount</div>
        <div class="mui-fl-vert">
          <m-input
            type="number"
            :disabled="!contract.name"
            @input="changeAmount('amount')"
            class="input1"
            v-model="form.amount"
            placeholder="Please Enter Token Distribution Amount">
          </m-input>
          <m-input
            type="text"
            :disabled="!contract.name || !contract.amount"
            @input="changeAmount('percentage')"
            class="input2"
            v-model="form.amountPercentage">
          </m-input>
        </div>
        <div v-if="contract.name && form.amount > Number(contract.amount)" class="insufficient">
          Insufficient funds. You may save the plan and deposit tokens before deploying.
        </div>
      </m-form-item>

      <m-form-item prop="url">
        <div class="t2">Display Timezone</div>
        <m-dropdown placement="bottom" trigger="click" class="sty1-dropdown" @command="handleCommand">
          <span class="mui-fl-btw mui-fl-hori">
            {{ selectTimezone }}<i class="el-icon-arrow-down"></i>
          </span>
          <m-dropdown-menu slot="dropdown" class="sty1-dropdown-menu sty1-dropdown-menu-step">
            <m-dropdown-item :command=data v-for="(data, index) of timeZone" :key="index">
              {{ data.name }}&nbsp;({{ data.country }})
            </m-dropdown-item>
          </m-dropdown-menu>
        </m-dropdown>
      </m-form-item>

      <m-form-item prop="twitterUrl">
        <div class="t2">X account(optional)</div>
        <m-input v-model="form.twitterUrl" placeholder="Enter your X (Twitter) profile URL"></m-input>
      </m-form-item>

      <m-form-item prop="websiteUrl">
        <div class="t2">Website (optional)</div>
        <m-input v-model="form.websiteUrl" placeholder="Enter your project's official Website URL"></m-input>
      </m-form-item>

      <m-form-item prop="telegramUrl">
        <div class="t2">Telegram (optional)</div>
        <m-input v-model="form.telegramUrl" placeholder="Enter your Telegram profile URL"></m-input>
      </m-form-item>

      <m-form-item prop="discordUrl">
        <div class="t2">Discord (optional)</div>
        <m-input v-model="form.discordUrl" placeholder="Enter your Discord profile URL"></m-input>
      </m-form-item>
    </m-form>
  </div>
</template>

<script>
import { TIME_ZONE } from '../../../utils/config'
import BigNumber from 'bignumber.js'
const BN = BigNumber.clone({ ROUNDING_MODE: 1, DECIMAL_PLACES: 0 })
export default {
  data () {
    return {
      timeZone: TIME_ZONE,
      selectTimezone: '(GMT + 0) UTC',
      contractLoading: false,
      contractFlag: false,
      checkAddressType: false,
      form: {
        name: '',
        address: '',
        amount: '',
        amountPercentage: '',
        twitterUrl: '',
        websiteUrl: '',
        telegramUrl: '',
        discordUrl: ''
      },
      rules: {
        name: [this.rulesInputChange('Distribution Name is required'), this.rulesInputBlur('')],
        address: [this.rulesInputChange('Token Contract Address is required'), this.rulesInputBlur('')],
        amount: [this.rulesInputChange('Amount is required'), this.rulesInputBlur('')],
        twitterUrl: [this.rulesInputChange('Please enter a valid URL.'), this.rulesInputBlur('')],
        websiteUrl: [this.rulesInputChange('Please enter a valid URL.'), this.rulesInputBlur('')],
        telegramUrl: [this.rulesInputChange('Please enter a valid URL.'), this.rulesInputBlur('')],
        discordUrl: [this.rulesInputChange('Please enter a valid URL.'), this.rulesInputBlur('')]
      },
      contract: {
        name: '',
        symbol: '',
        amount: ''
      }
    }
  },
  computed: {
    detail () {
      return this.$store.state.distribution.detail
    },
    step () {
      return this.$store.state.distribution.detail.step
    }
  },
  watch: {
  },
  created () {
  },
  mounted () {
    this.$nextTick(() => {
      const time = TIME_ZONE.find(x => x.offset === new Date().getTimezoneOffset())
      this.selectTimezone = time ? time.name + ' (' + time.country + ')' : '(GMT + 0) UTC'
      if (this.step === 1) return
      this.form = {
        name: this.detail.distributionName,
        address: this.detail.tokenContractAddress,
        amount: new BN(this.detail.amount).toString(),
        amountPercentage: 0,
        twitterUrl: this.detail.xAccount,
        websiteUrl: this.detail.website,
        telegramUrl: this.detail.telegram,
        discordUrl: this.detail.discord
      }
      this.selectTimezone = this.detail.displayTimezone
      this.$refs.contractAddress.focus()
      this.contractFlag = true
      setTimeout(() => {
        this.$refs.contractAddress.blur()
      })
    })
  },
  methods: {
    handleCommand (command) {
      this.selectTimezone = command.name + ' (' + command.country + ')'
    },
    pushAmount () {
      this.form.amountPercentage = Number(this.form.amountPercentage)
      this.form.amountPercentage = this.form.amountPercentage.toString().replace(/[^\d]/g, '')
      const bg1 = new BigNumber(this.form.amountPercentage)
      const bg2 = new BigNumber(this.contract.amount)
      const bg = new BN(bg1.div(100).times(bg2)).toString(10)
      this.form.amount = bg
    },
    changeAmount (name) {
      if (name === 'percentage') {
        this.form.amount = this.form.amount.replace(/\D/g, '')
        if (!this.form.amountPercentage) {
          this.form.amount = ''
          return
        }
        if (this.form.amountPercentage > 100) {
          this.form.amountPercentage = 100
        } else if (this.form.amountPercentage < 0) {
          this.form.amountPercentage = 0
        }
        this.pushAmount()
      } else {
        let value = this.form.amount
          .replace(/[^\d.]/g, '') // 只保留数字和小数点
          .replace(/^0+(\d)/, '$1') // 去除整数部分前导0
          .replace(/^\./, '') // 不允许以小数点开头
          .replace(/\.{2,}/g, '.') // 只保留一个小数点
          .replace('.', '#') // 临时替换第一个小数点
          .replace(/\./g, '') // 移除多余小数点
          .replace('#', '.') // 还原第一个小数点

        // 限制小数点后18位
        if (value.indexOf('.') !== -1) {
          const parts = value.split('.')
          value = parts[0] + '.' + parts[1].slice(0, 18)
        }

        this.form.amount = value
        this.form.amountPercentage = Number(this.contract.amount) ? Number(((this.form.amount / Number(this.contract.amount)) * 100).toFixed()) : '--'
      }
    },
    async checkAddress () {
      if (this.contract.name) return true
      const data = await this.$store.dispatch('checkContract', { tokenAddress: this.form.address, chainId: '0x141f' })
      if (data === 'ErrorcontractAddress') {
        this.contract = {
          name: '',
          symbol: '',
          amount: ''
        }
        return false
      }
      const amount = new BigNumber(data.balanceOf)
      this.contract = {
        name: data.name,
        symbol: data.tokenSymbol,
        amount: new BN(amount.div(1e18))
      }
      if (this.form.amount) {
        this.changeAmount()
      }
      return true
    },
    rulesInputChange (errorText) {
      return {
        required: true,
        trigger: 'change',
        validator: (rule, value, callbak) => {
          if (!value && (rule.field === 'twitterUrl' || rule.field === 'websiteUrl' || rule.field === 'telegramUrl' || rule.field === 'discordUrl')) {
            callbak()
            return
          }
          if (rule.field === 'address' && !value) {
            this.contractFlag = false
          }
          if (value) {
            callbak()
          } else {
            callbak(errorText)
          }
        }
      }
    },
    rulesInputBlur () {
      return {
        required: true,
        trigger: 'blur',
        validator: async (rule, value, callbak) => {
          if (rule.field === 'address') {
            if (value) {
              if (value.length !== 42 || !value.startsWith('0x') || !this.isAlphaNumericMix(value.slice(2))) {
                this.contractFlag = true
              } else {
                this.contractLoading = true
                this.contractFlag = true
                await this.checkAddress()
                this.contractLoading = false
              }
            } else {
              this.contractFlag = false
              this.contractLoading = false
            }
            callbak()
          } else if (rule.field === 'amount') {
            if (value.toString() && !Number(value)) {
              callbak('Amount is required')
            } else {
              callbak()
            }
          } else if (rule.field === 'twitterUrl' || rule.field === 'websiteUrl' || rule.field === 'telegramUrl' || rule.field === 'discordUrl') {
            if (value) {
              const urlPattern = /^(https?:\/\/)/
              if (value && !urlPattern.test(value)) {
                callbak('Please enter a valid URL.')
              } else if (value) {
                try {
                  const url = new URL(value)
                  if (rule.field === 'twitterUrl' && !this.isTwitterUrl(url.href)) {
                    throw new Error()
                  } else if (rule.field === 'telegramUrl' && !this.isTelegramUrl(url.href)) {
                    throw new Error()
                  } else if (rule.field === 'discordUrl' && !this.isDiscordUrl(url.href)) {
                    throw new Error()
                  }
                } catch (e) {
                  callbak('Please enter a valid URL.')
                  return
                }
                callbak()
              } else {
                callbak()
              }
            } else {
              callbak()
            }
          } else {
            if (value.length > 50) {
              callbak('More than 50 characters')
            } else {
              callbak()
            }
          }
        }
      }
    },
    isTwitterUrl (url) {
      return /^https?:\/\/(www\.)?x\.com\/[A-Za-z0-9_]+(\/.*)?$/i.test(url)
    },
    isTelegramUrl (url) {
      return /^https?:\/\/(www\.)?t\.me\/[A-Za-z0-9_]+(\/.*)?$/i.test(url)
    },
    isDiscordUrl (url) {
      return /^https?:\/\/(www\.)?discord\.com\/[A-Za-z0-9_]+(\/.*)?$/i.test(url)
    },
    isAlphaNumericMix (str) {
      // 只能是数字和字母，并且必须同时包含数字和字母
      return /^[A-Za-z0-9]+$/.test(str) && /[A-Za-z]/.test(str) && /\d/.test(str)
    },
    toCamelCase (str) {
      return str.replace(/_([a-z])/g, (match, p1) => p1.toUpperCase())
    },
    getChangedFields (obj1, obj2) {
      const changed = {}
      Object.keys(obj1).forEach(key => {
        if (key === 'amount') {
          const bg1 = new BigNumber(obj1[key])
          const bg2 = new BigNumber(obj2[key])
          if (!bg1.eq(bg2)) {
            changed[this.toCamelCase(key)] = obj2[key]
          }
        } else if (obj1[key] !== obj2[key] && obj2[key]) {
          changed[this.toCamelCase(key)] = obj2[key]
        }
      })
      return changed
    },
    async createDistribution () {
      const rp = await this.$api.request('disctrbution.createDistributionID', {}, {}, 'distribution')
      if (rp.code === 200) {
        this.$store.commit('SET_DISTRIBUTUION_ID', rp.planId)
        if (await this.$store.dispatch('postDistributionInfo')) {
          await this.$store.dispatch('createDistributionActivity', { totalAmount: this.form.amount })
          const getId = await this.$api.request('disctrbution.getDistributionList', { email: this.$store.state.auth.user.name, planId: rp.planId }, {}, 'distribution')
          this.$store.commit('SET_DETAIL', {
            onlyPlanId: getId['[]'][0].TdTokenDistributionPlan.id,
            onlyActivityId: getId['[]'][0]['TdDistributionActivity[]']?.[0]?.id,
            onlyConfigId: getId['[]'][0]['TdClaimPageUiConfig[]']?.[0]?.id
          })
          return true
        }
        return false
      }
    },
    async checkForm () {
      if (this.step >= 2) {
        return await new Promise((resolve, reject) => {
          this.$refs.form.validate(async (valid) => {
            if (valid) {
              const form = {
                distributionName: this.form.name,
                tokenContractAddress: this.form.address,
                amount: this.form.amount,
                website: this.form.websiteUrl,
                xAccount: this.form.twitterUrl,
                telegram: this.form.telegramUrl,
                discord: this.form.discordUrl,
                displayTimezone: this.selectTimezoned
              }
              const diff = this.getChangedFields(this.detail, form)
              if (!Object.keys(diff).length) {
                resolve(true)
                return
              }
              // if (Object.keys(diff).includes('amount')) {
              //   await this.$store.dispatch('putDistributionActivity', { totalAmount: this.form.amount })
              // }
              await this.$store.dispatch('putDistributionInfo', diff)
              resolve(true)
            } else {
              resolve(false)
            }
          })
        })
      }
      return await new Promise((resolve, reject) => {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            this.$store.commit('SET_DETAIL', {
              email: this.$store.state.auth.user.name,
              distributionName: this.form.name,
              tokenNetwork: 'Moca',
              tokenContractAddress: this.form.address,
              tokenSymbol: this.contract.name,
              distributionType: 'Airdrop',
              amount: this.form.amount,
              website: this.form.websiteUrl,
              xAccount: this.form.twitterUrl,
              telegram: this.form.telegramUrl,
              discord: this.form.discordUrl,
              kycProgramId: '',
              displayTimezone: this.selectTimezone,
              mchNo: this.$store.state.auth.user.mchNo,
              blockchainId: '0x141f',
              status: 'Draft',
              step: 2,
              mchAssetAddress: this.$store.state.wallet.connectedAddress
            })
            resolve(this.createDistribution())
          } else {
            resolve(false)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/tokenDistribution/_distributionDetailStep1.scss" scoped></style>
