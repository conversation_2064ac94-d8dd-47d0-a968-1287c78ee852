import BigNumber from 'bignumber.js'
import Crypto from 'crypto-js'
/**
 * @description Vue全局过滤器
 */

/**
 * 时间戳转为时间格式
 * @param {string} type 显示时间的格式 1.minute 显示到分
 */
export const timestampDate = (timestamp, type, symbol) => {
  const length = timestamp.toString().length
  if (length === 10 || length === 13) {
    const time = length === 10 ? new Date(Number(timestamp + '000')) : new Date(Number(timestamp))
    const Y = time.getFullYear()
    const M = (time.getMonth() + 1) < 10 ? '0' + (time.getMonth() + 1) : time.getMonth() + 1
    const d = time.getDate() < 10 ? '0' + time.getDate() : time.getDate()
    const h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours()
    const m = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()
    const s = time.getSeconds() < 10 ? '0' + time.getSeconds() : time.getSeconds()
    if (type === 'minute') {
      return Y + symbol || '-' + M + '-' + d + ' ' + h + ':' + m
    } else {
      return Y + '-' + M + '-' + d + ' ' + h + (symbol || ':') + m + (symbol || ':') + s
    }
  } else {
    return timestamp
  }
}

// function replaceGmtToUtc (str) {
//   return str.replace(/\(GMT \+ (\d+)\) UTC/g, 'UTC + $1')
// }
function formatUtcOffset (str) {
  // 匹配 UTC+0800 或 UTC-0530 等格式
  const match = str.match(/UTC([+-])(\d{1,2})(\d{2})?/i)
  if (!match) return str
  const sign = match[1]
  const hour = parseInt(match[2], 10)
  // 只保留小时部分
  return `UTC ${sign} ${hour}`
}

export const formatTimestampToUTC8 = (ts, utc = 0) => {
  if (!ts || ts === '--') return '--'
  // ts 可以是时间戳（毫秒）或字符串
  const date = new Date(ts)
  // 转为 UTC+8
  const utc8 = new Date(date.getTime() + 8 * 60 * 60 * 1000)

  const day = utc8.getUTCDate()
  const month = utc8.toLocaleString('en-US', { month: 'short', timeZone: 'UTC' })
  const year = utc8.getUTCFullYear()
  const hour = String(utc8.getUTCHours()).padStart(2, '0')
  const minute = String(utc8.getUTCMinutes()).padStart(2, '0')

  return `${day} ${month} ${year} ${hour}:${minute} ${formatUtcOffset(utc)}`
}

export const abbreviateUrl = (url, frontLen = 28, backLen = 6) => {
  if (url.length <= frontLen + backLen) return url
  return url.slice(0, frontLen) + '...' + url.slice(-backLen)
}

export const formatCustomDate = (value) => {
  if (!value) return ''
  const date = new Date(value)
  const options = { month: 'short', day: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit', hour12: true }
  return date.toLocaleString('en-US', options)
}

export const formatPubKey = (pubKey, length = 4, preLength) => {
  if (!pubKey) return
  if (!pubKey || typeof pubKey !== 'string' || pubKey.length < (length * 2 + 1)) {
    return pubKey
  }
  return pubKey.slice(0, preLength || length) + '...' + pubKey.slice(-4)
}

/**
  * 截断小数位而不是四舍五入
  * @param {boolean} needPos 是否需要补 “+” 号
  * @param {number} radio 保留的小数位数，默认 2
  */
export function subRadio (num, radio = 2) {
  const arr = num.toString().split('.')
  if (arr[1]) {
    num = `${arr[0]}.${arr[1].substring(0, radio)}`
    return arr[1].length === 1 ? num + '0' : num
  } else {
    return num + '.00'
  }
}

/**
 *  0-10万：全展示
 *  10万-99.99万：K
 *  100万以上：M（百万）
 */
export function formatStatistics (num) {
  const BN = BigNumber.clone({ ROUNDING_MODE: 1, DECIMAL_PLACES: 0 })
  const bgNum = new BigNumber(num)
  const thousand = new BigNumber(1000)
  const tenW = new BigNumber(100000)
  const hundredW = new BigNumber(1000000)
  if (bgNum.gte(hundredW)) {
    const step1 = new BN(bgNum.div(hundredW)).toString(10)
    return new BN(step1).toFormat() + 'M'
  } else if (bgNum.gte(tenW)) {
    const step1 = new BN(bgNum.div(thousand)).toString(10)
    return new BN(step1).toFormat() + 'K'
  } else {
    const step1 = new BN(bgNum).toString(10)
    return new BN(step1).toFormat()
  }
}

// AES加密
export function encrypt (data) {
  const key = Buffer.of(...[98, 97, 99, 107, 101, 110, 100, 90, 107, 109, 101, 64, 50, 48, 50, 51])
  return Crypto.AES.encrypt(JSON.stringify(data), key.toString()).toString()
}

export function formatGMTDate (date) {
  // const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
  const _date = new Date(date)
  // const month = months[_date.getMonth()]
  const day = _date.getDate()
  const year = _date.getFullYear()

  const _hours = _date.getHours()
  // let hours = _date.getHours()
  const minutes = _date.getMinutes()
  // const ampm = _hours >= 12 ? 'PM' : 'AM'

  // hours %= 12
  // hours = hours || 12 // 处理午夜时显示为 12 AM

  const time = `${_hours}:${minutes < 10 ? '0' + minutes : minutes}`

  return `${year}-${(_date.getMonth() + 1).toString().padStart(2, 0)}-${day.toString().padStart(2, 0)} ${time}`
}

// 时间格式 - Sep 20, 02:51 AM
export function formatUTCDate (input) {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
  const date = new Date(input)

  const month = months[date.getUTCMonth()]
  const day = date.getUTCDate()
  const year = date.getUTCFullYear()
  let hours = date.getUTCHours()
  const minutes = ('0' + date.getUTCMinutes()).slice(-2)
  const ampm = hours >= 12 ? 'PM' : 'AM'

  hours = hours % 12
  hours = hours || 12 // the hour '0' should be '12'

  return `${month} ${day}, ${year}, ${hours >= 10 ? hours : '0' + hours}:${minutes} ${ampm}`
}

export function simplifyAddress (address, head, end) {
  return address.substring(0, head) + '...' + address.substring(address.length - end)
}

export function thousandth (val) {
  return new Intl.NumberFormat('en-US').format(val)
}

export function fileCheck (file, fileSizes) {
  let fileType = false
  const BN = BigNumber.clone({ ROUNDING_MODE: 1, DECIMAL_PLACES: 0 })
  const fileSize = new BigNumber(file.size).div(1024).div(1024)
  const fs = new BN(fileSize.plus(fileSizes)).toString()
  const type = ['png', 'jpg', 'pdf', 'jpeg']
  for (const f of type) {
    if (file.name.toString().includes(f)) {
      fileType = true
      break
    }
  }
  return {
    fileSize: fs,
    fileCheck: fileType
  }
}

export function calculateSize (fileSizes, fileSize) {
  const BN = BigNumber.clone({ ROUNDING_MODE: 1, DECIMAL_PLACES: 0 })
  fileSizes = new BigNumber(fileSizes)
  fileSize = new BigNumber(fileSize).div(1024).div(1024)
  const fs = new BN(fileSizes.minus(fileSize)).toString()
  return fs
}

export function getExpireDateTime (expiredTime) {
  const BN = BigNumber.clone({ ROUNDING_MODE: 1, DECIMAL_PLACES: 0 })

  const newTime = new BigNumber(new Date().getTime())

  const overTime = new BN(newTime).gt(new BigNumber(new Date(expiredTime).getTime()))

  return overTime
}

export function fileSizeCalculate (size) {
  const BN = BigNumber.clone({ ROUNDING_MODE: 1, DECIMAL_PLACES: 2 })
  let fs

  const fileSizes = new BigNumber(size).div(1024).div(1024)

  if (fileSizes.lt(1)) {
    fs = new BN(fileSizes).multipliedBy(1024).toString(10) + 'KB'
  } else {
    fs = new BN(fileSizes).toString(10) + 'MB'
  }

  return fs
}

export function boundaryMount (content) {
  const type = 'application/json'
  const boundary = '--dio-boundary-' + Date.now().toString().substring(3)
  const blob = new Blob([
    '--' + boundary + '\r\n' + 'Content-Disposition: form-data; name="files"; filename="threshold-encrypted.json"' + '\r\n' + 'Content-Type: application/json' + '\r\n' + '\r\n',
    content,
    '\r\n' + '--' + boundary + '--' + '\r\n'
  ], { type })

  return {
    blob,
    boundary
  }
}

export function toFormat (num, mode = 0) {
  const BN = BigNumber.clone({ ROUNDING_MODE: mode, DECIMAL_PLACES: 0 })
  return new BN(num).toFormat(9).replace(/0+$/, '').replace(/\.$/, '')
}

export function saturation (val) {
  // 去除 '#' 字符（如果有）
  const hex = val.replace(/^#/, '')

  // 将十六进制颜色转换为 RGB
  const r = parseInt(hex.slice(0, 2), 16) / 255
  const g = parseInt(hex.slice(2, 4), 16) / 255
  const b = parseInt(hex.slice(4, 6), 16) / 255

  // 计算最大和最小的 RGB 分量
  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  const delta = max - min

  let s

  if (max === 0) {
    s = 0
  } else {
    s = delta / max
  }
  s = Math.round(s * 100)

  return s
}

export function hextoRgb (hexString, opacity = 1) {
  if (!hexString) {
    return ''
  }

  let hex = ''
  if (hexString.startsWith('#')) {
    hex = hexString.substring(1)
  }

  if (hex.length !== 6) {
    return
  }

  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)

  return `rgba(${r}, ${g}, ${b}, ${opacity})`
}

// 组装指令集
const filters = {
  thousandth,
  timestampDate,
  formatPubKey,
  subRadio,
  formatStatistics,
  formatGMTDate,
  simplifyAddress,
  formatUTCDate,
  fileCheck,
  getExpireDateTime,
  fileSizeCalculate,
  formatCustomDate,
  abbreviateUrl,
  formatTimestampToUTC8,
  boundaryMount,
  toFormat,
  hextoRgb
}

const install = function (Vue) {
  Object.keys(filters).forEach(key => {
    Vue.filter(key, filters[key])
  })
}

// auto install
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

export default Object.assign(filters, { install })
