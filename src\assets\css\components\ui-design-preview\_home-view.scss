.popico-lock {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--color-theme);
    border-radius: 12px;
    font-size: 24px;
    color: var(--color-text-8);
    text-align: center;
    line-height: 40px;
  }
  
  .t1 {
    font-size: 14px;
    line-height: 140%;
    color: var(--color-text-7);
    margin-top: 37px;
  }
  .t1.mt8 {
    margin-top: 8px;
  }
  .t1.mt12 {
    margin-top: 12px;
  }
  
  .proof {
    position: relative;
    margin-top: 24px;
    padding-bottom: 40px;
    width: 100%;
  }
  .proof::after {
    content: '';
    position: absolute;
    width: 400px;
    height: 1px;
    background-color: var(--color-background-8);
    bottom: 20px;
    left: 0;
    right: 0;
    margin: auto;
  }
  .proof li {
    font-weight: 700;
    font-size: 16px;
    line-height: 140%;
    color: var(--color-text-9);
  }
  .proof li + li {
    margin-top: 20px;
  }
  .proof li i {
    display: inline-block;
    width: 56px;
    height: 56px;
    background: var(--color-background-15);
    color: var(--color-theme);
    border-radius: 50%;
    margin-right: 12px;
    font-size: 24px;
    text-align: center;
    line-height: 56px;
  }
  
  .t2 {
    font-weight: 700;
    font-size: 14px;
    line-height: 140%;
    color: var(--color-text-9);
    margin-top: 24px;
  }
  
  .t3 {
    line-height: 140%;
    color: var(--color-text-9);
    margin-top: 8px;
    font-weight: 500;
    letter-spacing: -0.1px;
  }
  
  .t4 {
    color: var(--color-text-9);
    font-size: 16px;
    font-weight: 700;
    line-height: 140%; /* 22.4px */
    margin-top: 40px;
  }
  
  .bottom-act {
    margin-bottom: 20px;
  }
  
  .dot {
    /* margin-top: 18px; */
    /* margin-top: 24px; */
    margin-bottom: 20px;
  }
  .dot li {
    width: 8px;
    height: 8px;
    background: var(--vt-c-primary-3);
    border-radius: 50%;
  }
  .dot li + li {
    margin-left: 12px;
  }
  .dot .active {
    background-color: var(--color-theme) !important;
  }
  
  
  .step2-p1 {
    font-weight: 700;
    font-size: 16px;
    line-height: 140%;
    color: var(--color-text-9);
  }
  .step2-p2 {
    font-size: 12px;
    line-height: 140%;
    color: var(--color-text-10);
    margin-top: 9px;
  }
  .step2-ul {
    margin-top: 24px;
    width: 100%;
  }
  .step2-ul li + li {
    margin-top: 24px;
  }
  .step2-ul li i {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--color-background-15);
    color: var(--color-theme);
    margin-right: 12px;
    text-align: center;
    line-height: 40px;
    font-size: 18px;
  }
  .step2-ul li .t {
    font-weight: 700;
    font-size: 12px;
    line-height: 140%;
    color: var(--color-text-9);
    margin-bottom: 2px;
  }
  .step2-ul li .d {
    font-size: 12px;
    line-height: 140%;
    color: var(--color-text-7);
    /* min-height: 34px; */
  }
  
  .step2-protocol{
    background: var(--color-background-15);
    border-radius: 12px;
    padding: 12px;
    font-size: 10px;
    line-height: 140%;
    margin-top: 12px;
  }
  .step2-protocol.animation {
    animation: checkboxAnimation 0.9s cubic-bezier(.36, .07, .19, .97);
  }
  .step2-protocol .pro-text span {
    color:var(--color-text-17);
    // opacity: 0.8;
  }
  .step2-protocol .pro-text a{
    text-decoration: underline;
    font-weight: bold;
    color: var(--color-theme);
    cursor: pointer;
  }
  
  .step2-tip {
    text-align: center;
    margin-top: 12px;
    font-size: 12px;
    line-height: 140%;
    color: var(--color-text-7);
  }
  
  .step2-tip a {
    text-decoration: underline;
    color: var(--color-text-7);
  }
  
  .hero {
    width: 288px;
    margin: 72px 0 0;
  }
  .hero.kyc {
    margin: 30px 0 0;
  }
  
  .hero-flow {
    padding-bottom: 32px;
    width: 100%;
    text-align: center;
    border-bottom: 1px solid #E6E6E6;
    margin-bottom: 20px;
  }
  
  .zkme-web-iframe {
    display: none;
  }
  
  @media screen and (max-width: 509px) {
    .bottom-act {
      margin-bottom: 16px;
    }
  }