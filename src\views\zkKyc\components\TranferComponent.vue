<template>
  <ul class="expand-box" style="padding: 16px 0 0 0;">
    <li class="t1">
      <span class="expand-p-mar">
        Preset combinations:
      </span>
      <m-checkbox v-model="isBanCountries" class="sty1-checkbox" @change="sameCitizenship('countries')">Crypto ban
        countries</m-checkbox>
      <m-checkbox v-if="isGeolocation === 2" v-model="isBanCountries2" class="sty1-checkbox"
        @change="sameCitizenship('citizenship')">Same as citizenship</m-checkbox>
    </li>
    <li>
      <ul class="mui-fl-vert transfer-content" v-if="!isBanCountries2">
        <li v-for="(item, index) of transfers" :key="item.id"
          :class="[item.id === 2 ? 'mui-fl-col mui-fl-vert transfer-btns' : 'mui-shr-0 left-transfer']">
          <template v-if="item.id === 1">
            <p class="t1">
              <i :class="[item.icon, 'iconi']" />
              {{ item.title }}
            </p>
            <div class="transfer-box">
              <div class="mui-fl-vert top-select mui-fl-btw">
                <m-popover v-model="item.popVisible" placement="bottom-end" width="200" :offset="124" trigger="click"
                  popper-class="sty3-popper">
                  <ul>
                    <li v-for="item1 of selects" :key="item1.value" class="top-select-option"
                      :class="{ 'active': item.selectId === item1.value }" @click="popChange(item1, item, index)">
                      {{ item1.label }}
                    </li>
                  </ul>
                  <div slot="reference" class="mui-fl-vert">
                    <m-checkbox :indeterminate="item.isIndeterminate" v-model="item.checkAll" class="sty1-checkbox"
                      @change="checkAllChange($event, item, index)"></m-checkbox>
                    <p class="t2 select-p-color">Selected countries/regions {{ item.selectCountries.length }}/{{
                      getListLength(item.countries).length1 }}</p>
                    <i class="mico-fold"></i>
                  </div>
                </m-popover>
                <div class="mui-fl-vert" v-if="kycMode !== 3 && !isGeolocation">
                  <div class="mui-fl-central" style="margin-right: 16px;">
                    <m-checkbox :indeterminate="item.ispassport" v-model="item.passport" class="sty1-checkbox" style="padding-left: 0px;"
                      @change="otherCheckAllChange($event, item, 'passport')">
                      <p class="t2 select-p-color">Selected Passport</p>
                    </m-checkbox>
                  </div>
                  <div class="mui-fl-central">
                    <m-checkbox :indeterminate="item.isidCard" v-model="item.idCard" class="sty1-checkbox" style="padding-left: 0px;"
                      @change="otherCheckAllChange($event, item, 'idCard')">
                      <p class="t2 select-p-color">Selected ID Card</p>
                    </m-checkbox>
                  </div>
                </div>
              </div>

              <div class="search-box">
                <m-input v-model="item.keyword" placeholder="Search" prefix-icon="mico-search"
                  class="sty1-input-search width-1 hori-mar" clearable @input="handleInputSearch($event, index)">
                </m-input>

                <div class="nations" v-if="item.keyword">
                  <ul class="nation-item" v-if="item.searchCountries.length">
                    <li v-for="item2 of item.searchCountries" :key="item2.value" class="mui-fl-vert mui-fl-btw"
                      @click="checkboxChange(item2, index, item)">
                      <div class="mui-fl-vert">
                        <m-checkbox :value="item2.isSelect" class="sty1-checkbox"
                          @change="checkboxChange(item2, index, item)"></m-checkbox>
                        <div class="img nation-sprites mui-shr-0"
                          :style="{ backgroundPosition: `${(-5 - 34 * (item2.ids - 1))}px -5px` }" />
                        <p class="country-name" :title="item2.regularName">{{ item2.regularName }}</p>
                      </div>
                      <div class="mui-fl-vert" v-if="isGeolocation !== 3 && isGeolocation !== 2">
                        <m-button v-if="item2.supportDocuments.includes(4)" class="mui-fl-central network-tag"
                          :class="{ 'tag-checked': item2.selectDocuments.isSelect4, 'tag-checked-hover': item2.selectDocuments.isSelect4 }"
                          @click.stop="SelectCountry('isSelect4', item2)">
                          <p>Driver License</p>
                        </m-button>
                        <m-button v-if="item2.supportDocuments.includes(2)" class="mui-fl-central network-tag"
                          :class="{ 'tag-checked': item2.selectDocuments.isSelect2, 'tag-checked-hover': item2.selectDocuments.isSelect2 }"
                          @click.stop="SelectCountry('isSelect2', item2)">
                          <p>Passport</p>
                        </m-button>
                        <m-button v-if="item2.supportDocuments.includes(1)" class="mui-fl-central network-tag"
                          :class="{ 'tag-checked': item2.selectDocuments.isSelect1, 'tag-checked-hover': item2.selectDocuments.isSelect1 }"
                          @click.stop="SelectCountry('isSelect1', item2)">
                          <p>ID Card</p>
                        </m-button>
                      </div>
                    </li>
                  </ul>

                  <div v-else class="mui-fl-col mui-fl-central no-content">
                    <img src="@/assets/img/no-table-data.png" alt="">
                    <p>No Record</p>
                  </div>
                </div>

                <div class="nations" v-else-if="item.countries.length">
                  <ul :ref="`ndScrollRef${index + 1}`">
                    <li v-for="item1 of item.countries" :key="item1.letter" class="nation-item"
                      :ref="`ndLetterRef${index + 1}`">
                      <p class="t1">{{ item1.letter }}</p>
                      <ul>
                        <li v-for="item2 of item1.childrens" :key="item2.value" class="mui-fl-btw"
                          @click="checkboxChange(item2, index, item)">
                          <div class="mui-fl-vert">
                            <m-checkbox :value="item2.isSelect" class="sty1-checkbox"
                              @change="checkboxChange(item2, index, item)"></m-checkbox>
                            <div class="img nation-sprites mui-shr-0"
                              :style="{ backgroundPosition: `${(-5 - 34 * (item2.ids - 1))}px -5px` }" />
                            <p class="country-name" :title="item2.regularName">{{ item2.regularName }}</p>
                          </div>
                          <div class="mui-fl-vert" v-if="isGeolocation !== 3 && isGeolocation !== 2">
                            <m-button v-if="item2.supportDocuments.includes(4)" class="mui-fl-central network-tag"
                              :class="{ 'tag-checked': item2.selectDocuments.isSelect4, 'tag-checked-hover': item2.selectDocuments.isSelect4 }"
                              @click.stop="SelectCountry('isSelect4', item2)">
                              <p>Driver License</p>
                            </m-button>
                            <m-button v-if="item2.supportDocuments.includes(2)" class="mui-fl-central network-tag"
                              :class="{ 'tag-checked': item2.selectDocuments.isSelect2, 'tag-checked-hover': item2.selectDocuments.isSelect2 }"
                              @click.stop="SelectCountry('isSelect2', item2, item)">
                              <p>Passport</p>
                            </m-button>
                            <m-button v-if="item2.supportDocuments.includes(1)" class="mui-fl-central network-tag"
                              :class="{ 'tag-checked': item2.selectDocuments.isSelect1, 'tag-checked-hover': item2.selectDocuments.isSelect1 }"
                              @click.stop="SelectCountry('isSelect1', item2, item)">
                              <p>ID Card</p>
                            </m-button>
                          </div>
                        </li>
                      </ul>
                    </li>
                  </ul>

                  <!-- <ul class="letter-box">
                  <li v-for="item3 of item.letters" :key="item3" @click="handleLetterChange($event, index)">
                    {{ item3 }}
                  </li>
                </ul> -->
                </div>

                <div v-else class="mui-fl-col mui-fl-central no-content">
                  <img src="@/assets/img/no-table-data.png" alt="">
                  <p>No Record</p>
                </div>
              </div>
            </div>
          </template>

          <!-- <template v-else>
          <p class="to-right mui-fl-central" :class="{ 'to-disabled': toRightDisabled }" @click="toRight">
            <i class="mico-Fallback"></i>
          </p>
          <p class="to-left mui-fl-central" :class="{ 'to-disabled': toLeftDisabled }" @click="toLeft">
            <i class="mico-Fallback"></i>
          </p>
        </template> -->
        </li>
      </ul>
    </li>
  </ul>
</template>
<script>

export default {
  props: {
    countryList: {
      type: Array,
      required: true
    },
    id: {
      type: String,
      required: ''
    },
    pageType: {
      type: String,
      required: ''
    },
    isGeolocation: {
      type: Number,
      required: false
    },
    selectCountry: {
      type: Object,
      required: true,
      default: () => {
        return {
          avaliableList: [],
          unavailableList: []
        }
      }
    }
  },
  data () {
    return {
      isBanCountries: false,
      isBanCountries2: false,
      isSelect1: false,
      isSelect2: false,
      isSelect3: false,
      istransfers: [],
      leftKeyword: '',
      rightKeyword: '',
      radio: false,
      selects: [
        {
          value: 1,
          label: 'Select all'
        },
        {
          value: 2,
          label: 'Reverse select'
        }
      ],
      availableList: [],
      unavailableList: [],
      availableSelectedList: [],
      unavailableSelectedList: [],
      leftSearchList: [],
      rightSearchList: [],
      banCountries: [],
      isGeolocations: true,
      transfers: [
        {
          id: 1,
          keyword: '',
          title: 'Available countries/regions',
          icon: 'mcico-success-green',
          isIndeterminate: false,
          ispassport: false,
          isidCard: false,
          selectId: null,
          popVisible: false,
          checkAll: false,
          countries: [], // 格式：[{letter,childrens[{国家}]}]
          letters: [],
          selectCountries: [], // 格式：[{国家}]
          searchCountries: [] // 格式：[{国家}]
        },
        {
          id: 2
        },
        {
          id: 3,
          keyword: '',
          title: 'Unavailable countries/regions',
          icon: 'mcico-failed-red',
          isIndeterminate: false,
          selectId: null,
          popVisible: false,
          checkAll: false,
          countries: [],
          letters: [],
          selectCountries: [],
          searchCountries: []
        }
      ]
    }
  },
  mounted () {
    this.filterCountry()
    if (this.pageType === 'Modify' || this.pageType === 'Copy') {
      this.transfers[0].selectCountries = this.getAllSelects(this.transfers[0].selectCountries, this.transfers[0].countries, 'Modify')
      this.getIsSelectAll(this.transfers[0])
    } else {
      this.checkAllChange(true, this.transfers[0], 0)
    }
    // if (this.isGeolocation === 2) {
    //   this.isBanCountries2 = true
    //   this.$emit('geolocationSelect', this.isGeolocations)
    // }
  },
  watch: {
    leftKeyword (newValue) {
      if (newValue) {
        const list = []
        this.transfers[0].countries.forEach(i => {
          i.childrens.forEach(r => {
            if (r.name.toLocaleLowerCase().includes(newValue.toLocaleLowerCase())) {
              list.push(r)
            }
          })
        })
        this.transfers[0].searchCountries = list
      }
    },
    rightKeyword (newValue) {
      if (newValue) {
        const list = []
        this.transfers[2].countries.forEach(i => {
          i.childrens.forEach(r => {
            if (r.name.toLocaleLowerCase().includes(newValue.toLocaleLowerCase())) {
              list.push(r)
            }
          })
        })
        this.transfers[2].searchCountries = list
      }
    }
  },
  computed: {
    toLeftDisabled () {
      return !this.transfers[2].selectCountries.length
    },
    toRightDisabled () {
      return !this.transfers[0].selectCountries.length
    },
    kycMode () {
      return this.$route.query.mode ? Number(this.$route.query.mode) : this.$store.state.kyc.category
    }
  },
  methods: {
    SelectCountry (index, data, item) {
      if (Object.values(data.selectDocuments).filter(x => x).length === 1 && data.selectDocuments[index]) {
        this.$message({
          message: 'You need to select at least one document.',
          iconClass: 'mcico-warn2',
          customClass: 'sty4-message supportDocuments',
          duration: 3000,
          offset: 32,
          center: true
        })
        return
      }
      data.selectDocuments[index] = !data.selectDocuments[index]
      this.getIsSelectAll(item)
    },
    sameCitizenship (val) {
      if (val === 'countries') {
        if (this.isGeolocations) {
          this.filterCountry()
        }
        this.isGeolocations = false
        this.$emit('geolocationSelect', this.isGeolocations)
        this.isBanCountries2 = false
        this.bancountriesChange(this.isBanCountries, this.banCountries, val)
      } else {
        this.isGeolocations = false
        this.$emit('geolocationSelect', this.isGeolocations)
        this.filterCountry()
        this.isBanCountries = false
        const unavailableList = []
        this.selectCountry.unavailableList.forEach(v => {
          v.childrens.map(x => {
            x.isCrypto = 1
            unavailableList.push(x)
          })
        })
        this.bancountriesChange(this.isBanCountries2, unavailableList, val)
      }
    },
    // 将接口返回的国家列表重组成显示需要的样子：首字母后面接着对应数组
    filterCountry () {
      const avaliableList = []
      const unavailableList = []
      this.countryList.forEach(i => {
        const letter = i.regularName.substring(0, 1)
        // 如果是创建则没有isSelect属性，如果是更新或复制则有isSelect，isSelect表示是否为avaliable，重组之后isSelect表示是否被选中，用来转换avaliable或unavaliables
        // if (i.isSelect || !Object.keys(i).includes('isSelect')) {
        if (!avaliableList.find(r => r.letter === letter)) {
          avaliableList.push({ letter, childrens: [] })
        }
        const find = avaliableList.find(r => r.letter === letter)
        const childrens = find.childrens
        if (i.regularName.startsWith(letter) && (!childrens.find(r => r.regularName === i.regularName) || !childrens.length)) {
          const obj = i.supportDocuments.split(',').reduce((allNames, name) => {
            return {
              selectDocuments: {
                ...allNames?.selectDocuments,
                ['isSelect' + name]: i.supportDocumentList ? i.supportDocumentList.includes(Number(name)) : true
              }
            }
          }, {})
          childrens.push({ ...i, isSelect: !!i.isSelect, ...obj })
        }
        // } else {
        //   if (!unavailableList.find(r => r.letter === letter)) {
        //     unavailableList.push({ letter, childrens: [] })
        //   }
        //   const find = unavailableList.find(r => r.letter === letter)
        //   const childrens = find.childrens
        //   if (i.regularName.startsWith(letter) && (!childrens.find(r => r.regularName === i.regularName) || !childrens.length)) {
        //     childrens.push({ ...i, isSelect: false })
        //   }
        // }
      })
      const transfers = this.transfers
      transfers[0].countries = avaliableList
      transfers[0].letters = avaliableList.map(i => i.letter)
      transfers[2].countries = unavailableList
      transfers[2].letters = unavailableList.map(i => i.letter)
      this.$emit('select', { availables: transfers[0].countries, unavailables: transfers[2].countries, id: this.id })
      this.banCountries = this.countryList.filter(r => r.isCrypto)
      this.istransfers = transfers[0].countries
    },

    handleInputSearch (val, index) {
      if (val) {
        const list = []
        this.transfers[index].countries.forEach(i => {
          i.childrens.forEach(r => {
            if (r.regularName.toLocaleLowerCase().includes(val.toLocaleLowerCase())) {
              list.push(r)
            }
          })
        })
        this.transfers[index].searchCountries = list
      } else {
        this.transfers[index].searchCountries = []
      }
      this.$refs[`ndScrollRef${index + 1}`][0] && this.scrollTo(this.$refs[`ndScrollRef${index + 1}`][0], 0)
    },

    popChange (item1, item, index) {
      item.popVisible = !item.popVisible
      item.selectId = item1.value
      const obj = this.transfers[index]
      // Select all
      if (item1.value === 1) {
        obj.selectCountries = this.getAllSelects(obj.selectCountries, obj.countries)
        this.getIsSelectAll(item)
      } else {
        // Reverse select
        obj.selectCountries = this.getAllSelects(obj.selectCountries, obj.countries, 'Reverse')
        this.getIsSelectAll(item)
      }
      this.isBanCountries = this.getIsBancountries()
    },

    checkAllChange (val, item, index) {
      const obj = this.transfers[index]
      if (val) {
        obj.selectCountries = this.getAllSelects(obj.selectCountries, obj.countries)
        this.getIsSelectAll(item)
      } else {
        obj.selectCountries = this.getAllSelects(obj.selectCountries, obj.countries, 'None')
        this.getIsSelectAll(item)
      }
      this.isBanCountries = this.getIsBancountries()
    },
    otherCheckAllChange (flag, item, name) {
      if ((name === 'passport' && !flag && !item.idCard) || (name === 'idCard' && !flag && !item.passport)) {
        this.$message({
          message: 'You need to select at least one document.',
          iconClass: 'mcico-warn2',
          customClass: 'sty4-message supportDocuments',
          duration: 3000,
          offset: 32,
          center: true
        })
        return
      }
      item.countries.forEach(x => {
        x.childrens.map(y => {
          if (name === 'idCard') {
            y.selectDocuments.isSelect1 = flag
          } else {
            y.selectDocuments.isSelect2 = flag
          }
          return {
            ...y
          }
        })
      })
      this.getIsSelectAll(item)
    },

    // Select all和Reverse select
    getAllSelects (list, countries, type = 'All') {
      const data = [...list]
      const newData = []
      countries.forEach(i => {
        if (i.childrens.length) {
          i.childrens.forEach(r => {
            if (type === 'All') {
              r.isSelect = true
              !data.find(a => a.regularName === r.regularName) && data.push({ ...r, isSelect: false })
            } else if (type === 'None') {
              r.isSelect = false
            } else if (type === 'Reverse') {
              r.isSelect = !r.isSelect
              if (r.isSelect) {
                newData.push({ ...r, isSelect: false })
              }
            } else {
              if (r.isSelect) {
                newData.push({ ...r, isSelect: false })
              }
            }
          })
        }
      })
      return type === 'All' ? data : newData
    },

    bancountriesChange (val, list, name) {
      const obj = this.transfers[0]
      const banCountries = list
      if (!obj.countries.length) {
        return
      }

      const data = []
      obj.countries.forEach(r => {
        r.childrens.forEach(i => {
          if (val) {
            !banCountries.some(e => e.regularName === i.regularName) && data.push({ ...i })
          } else {
            data.push({ ...i })
          }
          if ((name === 'countries' && i.isCrypto) || (name === 'citizenship' && banCountries.find(x => x.id === i.id) && val)) {
            i.isSelect = !val
          } else {
            i.isSelect = true
          }
        })
      })
      obj.selectCountries = [...data]

      // 咱就是说把这个复选框的样式重新设置一下，毕竟列表改了
      this.getIsSelectAll(obj)
      this.$emit('change')
    },

    checkboxChange (item, index, pItem) {
      item.isSelect = !item.isSelect
      if (item.isSelect) {
        this.transfers[index].selectCountries.push({ ...item, isSelect: false })
      } else {
        this.transfers[index].selectCountries = this.transfers[index].selectCountries.filter(i => i.regularName !== item.regularName)
      }

      pItem.selectId = null
      this.$emit('select', { availables: this.transfers[0].countries, unavailables: this.transfers[2].countries, id: this.id })
      this.getIsSelectAll(pItem)
      this.isBanCountries = this.getIsBancountries()
      this.$emit('change')
    },

    // selected是否全选了的状态判断
    getIsSelectAll (pItem) {
      const listLength = this.getListLength(pItem.countries)
      const checkedCount = pItem.selectCountries.length
      this.$store.commit('SET_COUNTRIESLENGTH', listLength.length1)
      if (this.isGeolocation) {
        if (this.isBanCountries2) {
          this.$store.commit('SET_GEOSELECTCOUNTRIES', this.$store.state.zkKyc.selectCountries)
        } else {
          this.$store.commit('SET_GEOSELECTCOUNTRIES', checkedCount)
        }
      } else {
        this.$store.commit('SET_SELECTCOUNTRIES', checkedCount)
        if (this.isBanCountries2) {
          this.$store.commit('SET_GEOSELECTCOUNTRIES', checkedCount)
        }
      }
      pItem.checkAll = listLength.length1 ? (listLength.length1 === checkedCount) : false
      pItem.idCard = listLength.length2 ? (listLength.length2 === listLength.length1) : false
      pItem.passport = listLength.length3 ? (listLength.length3 === listLength.length1) : false
      pItem.isIndeterminate = checkedCount > 0 && checkedCount < listLength.length1
      pItem.isidCard = listLength.length2 > 0 && listLength.length2 < listLength.length1
      pItem.ispassport = listLength.length3 > 0 && listLength.length3 < listLength.length1
    },

    // 判断是否选了预设国家，是则按钮选中，否则不选
    getIsBancountries () {
      const obj = this.transfers[0]
      const selectCountries = this.sortList(obj.selectCountries, 'regularName')
      if (selectCountries.length !== this.banCountries.length) {
        return false
      }

      let result = true
      selectCountries.forEach((value, index) => {
        if (value.id !== this.banCountries[index].id) {
          result = false
        }
      })
      return result
    },

    toRight () {
      if (this.toRightDisabled) {
        return
      }
      // 把搜索去掉
      this.transfers[0].keyword = ''
      this.transfers[0].selectId = null
      this.transfers[2].selectId = null
      this.transfers[0].searchCountries = []
      this.transferList([...this.transfers[0].countries], [...this.transfers[0].selectCountries], 0)
      this.$emit('select', { availables: this.transfers[0].countries, unavailables: this.transfers[2].countries, id: this.id })
      this.isBanCountries = this.getIsBancountries()
    },

    toLeft () {
      if (this.toLeftDisabled) {
        return
      }
      // 把搜索去掉
      this.transfers[2].keyword = ''
      this.transfers[2].searchCountries = []
      this.transferList([...this.transfers[2].countries], [...this.transfers[2].selectCountries], 2)
      this.$emit('select', { availables: this.transfers[0].countries, unavailables: this.transfers[2].countries, id: this.id })
      this.isBanCountries = this.getIsBancountries()
    },

    // 数组转换
    transferList (list, selectList, index) {
      const result = [...this.transfers[index === 2 ? 0 : 2].countries]
      selectList.forEach(i => {
        const letter = i.regularName.substring(0, 1)
        if (!result.find(r => r.letter === letter)) {
          result.push({ letter, childrens: [] })
        }
        const find = result.find(r => r.letter === letter)
        const childrens = find.childrens
        if (i.regularName.startsWith(letter) && (!childrens.find(r => r.regularName === i.regularName) || !childrens.length)) {
          childrens.push({ ...i, isSelect: false })
        }
      })

      // 排序第二层
      result.forEach(i => {
        i.childrens = this.sortList(i.childrens, 'regularName')
      })

      // 将被移过去的数据添加到移过去的数组那里并排序第一层
      this.transfers[index === 2 ? 0 : 2].countries = this.sortList(result, 'letter')
      this.transfers[index === 2 ? 0 : 2].letters = this.sortList(result, 'letter').map(i => i.letter)

      // 将原数组中被移过去的数据删除
      const data = list.map((i) => {
        return {
          ...i,
          childrens: i.childrens
            .filter(r => !selectList.find(a => a.regularName === r.regularName))
            .map(a => { return { ...a, isSelect: false } })
        }
      })
      const filterData = data.filter(i => i.childrens.length)
      filterData.forEach(i => {
        i.childrens = this.sortList(i.childrens, 'regularName')
      })

      this.transfers[index].countries = this.sortList(filterData, 'letter')
      this.transfers[index].letters = this.sortList(filterData, 'letter').map(i => i.letter)
      this.transfers[index].selectCountries = []

      // 咱就是说把这个复选框的样式重新设置一下，毕竟列表改了
      this.getIsSelectAll(this.transfers[0])
      this.getIsSelectAll(this.transfers[2])
    },

    getListLength (data) {
      let length1 = 0
      let length2 = 0
      let length3 = 0
      data.forEach(r => {
        length1 += r.childrens.length
        length2 += r.childrens.filter(x => x.selectDocuments.isSelect1).length
        length3 += r.childrens.filter(x => x.selectDocuments.isSelect2).length
      })
      return { length1, length2, length3 }
    },

    // 列表排序
    sortList (list, txt) {
      return list.sort((a, b) => {
        if (a[txt] < b[txt]) {
          return -1
        } else {
          return 1
        }
      })
    },

    // 点击字母跳转指定字母位置
    handleLetterChange (e, index) {
      e.stopPropagation()
      const findIndex = this.$refs[`ndLetterRef${index + 1}`].findIndex((i) => i.textContent?.split('')[0] === e.target.innerText)
      if (findIndex !== -1) {
        this.scrollTo(this.$refs[`ndScrollRef${index + 1}`][0], findIndex ? this.$refs[`ndLetterRef${index + 1}`][findIndex].offsetTop : 0)
      }
    },

    /**
     * 缓动函数
     * @param {number} t current time（当前时间）
     * @param {number} b beginning value（初始值）
     * @param {number} c change in value（变化量）
     * @param {number} d duration（持续时间）
     */
    linear (t, b, c, d) {
      return c * t / d + b
    },

    /**
     * 滚动到指定位置
     * @param current 当前位置
     * @param target 目标位置
     * @param duration 持续时间
     */
    scrollTo (ndScrollRef, target, duration = 14) {
      const current = ndScrollRef.scrollTop
      let top = current
      let start = 0
      const change = target - current

      return new Promise(resolve => {
        const step = () => {
          start++
          top = this.linear(start, current, change, duration)
          ndScrollRef.scrollTop = top
          if (((current < target && top < target) || (current > target && top > target)) && start <= duration) {
            requestAnimationFrame(step)
          } else {
            resolve()
          }
        }
        step()
      })
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/_transfer_component.scss" scoped></style>
