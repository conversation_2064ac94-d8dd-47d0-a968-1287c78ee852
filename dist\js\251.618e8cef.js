"use strict";(self["webpackChunkzkme_web"]=self["webpackChunkzkme_web"]||[]).push([[251],{84082:function(e,t,s){s.d(t,{A:function(){return o}});var i=function(){var e=this,t=e._self._c;return t("m-dialog",{staticClass:"sty2-dialog dialog",attrs:{visible:e.warningtip,"show-close":!1,"close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(t){e.warningtip=t}}},[t("div",{staticClass:"leavetip_title"},[t("i",{staticClass:"mico-warning"}),"Delete"===e.model?t("span",[e._v("Confirm to delete the program?")]):"Cancel"===e.model?t("span",[e._v("Confirm to cancel the service?")]):"Apply"===e.model?t("span",[e._v("Confirm to apply the service?")]):t("span",[e._v("Warning")])]),"Delete"===e.model?t("div",{staticClass:"leavetip_text"},[e._v("It cannot be recovered once deleted.")]):"Cancel"===e.model?t("div",{staticClass:"leavetip_text"},[e._v("Your users will not be able to KYC until a new program is applied again.")]):"Apply"===e.model?t("div",{staticClass:"leavetip_text"},[e._v("The program cannot be modified after applying, please check it carefully.")]):t("div",{staticClass:"leavetip_text"},[e._v("There is unsaved solution. It will be lost if leaving the page.")]),"Delete"===e.model||"Cancel"===e.model?t("div",{staticClass:"dialog_button warin_button dialog_recorded_button mui-fl-end"},[t("m-button",{staticClass:"kycSave",on:{click:function(t){return e.$emit("Leave","Back")}}},[e._v("Back")]),t("m-button",{class:["kycCancel",e.model],on:{click:function(t){return e.$emit("Leave","Leave")}}},[e._v(e._s("Delete"===e.model?"Delete":"Cancel service"))])],1):t("div",{staticClass:"dialog_button warin_button mui-fl-end"},[t("m-button",{staticClass:"kycSave",on:{click:function(t){return e.$emit("Leave","Back")}}},[e._v(e._s("Stay"===e.model?"Leave":"Back"))]),t("m-button",{staticClass:"kycCancel",class:e.model,on:{click:function(t){return e.$emit("Leave","Leave")}}},[e._v(e._s("Apply"===e.model?"Confirm":e.model))])],1)])},l=[],a={props:{warningtip:{type:Boolean,default:!1},model:{type:String,default:""}}},r=a,n=s(81656),c=(0,n.A)(r,i,l,!1,null,"4ef6e4f1",null),o=c.exports},11417:function(e,t,s){s.d(t,{A:function(){return o}});var i=function(){var e=this,t=e._self._c;return t("div",[t("div",{class:{"mui-input-wrap-sty1":1,err:e.error,search:e.searchflg}},[t("div",{staticClass:"mui-fl-vert mui-fl-btw"},[t("input",{directives:[{name:"model",rawName:"v-model",value:e.value,expression:"value"}],ref:"ndInput",staticClass:"mui-input--input",attrs:{placeholder:e.placeholder},domProps:{value:e.value},on:{input:[function(t){t.target.composing||(e.value=t.target.value)},e.input],focus:e.focus,blur:e.blur,change:e.change,keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.enter.apply(null,arguments)}}}),t("p",{staticClass:"mui-fl-vert mui-input--icons"},[e.value&&e.closeflg?t("i",{staticClass:"mcico-celar",on:{mousedown:e.handleClear}}):e._e(),e.searchflg?t("span",{staticClass:"search",on:{mousedown:e.search}},[t("i",{staticClass:"mico-search"})]):e._e()])])]),e.error&&e.errorMsg?t("p",{staticClass:"mui-input-sty1--err"},[e._v(e._s(e.errorMsg))]):e._e()])},l=[],a={name:"MuiInput",model:{prop:"_value",event:"input"},props:{_value:{type:String,default:""},type:{type:String,default:"text"},placeholder:{type:String,default:"Please enter..."},error:{type:Boolean,default:!1},errorMsg:{type:String,default:""},searchflg:{type:Boolean,default:!1},closeflg:{type:Boolean,default:!1}},data(){return{realType:this.type}},computed:{value:{get(){return this._value},set(e){this.$emit("input",e)}}},methods:{input(){},focus(){this.$emit("focus")},blur(){this.$emit("blur")},change(){this.$emit("change")},enter(){this.$emit("enter")},handleClear(){this.value="",this.$emit("clear"),setTimeout((()=>{this.$refs.ndInput.focus()}),0)},search(){this.$emit("search")}}},r=a,n=s(81656),c=(0,n.A)(r,i,l,!1,null,"7f8c1ae0",null),o=c.exports},4746:function(e,t,s){s.d(t,{A:function(){return o}});s(98992),s(54520);var i=function(){var e=this,t=e._self._c;return e.list.length?t("ul",{staticClass:"progress"},e._l(e.list,(function(i,l){return t("div",{key:l},[i.exhibit?t("li",[i.id<=4&&e.kycMode<=2?t("p",{staticClass:"t1 title"},[e._v(" "+e._s(l+1)+". "+e._s(i.title)+" ")]):e._e(),5===i.id&&e.kycMode>=2?t("p",{staticClass:"t1 title"},[e._v(" "+e._s(3===e.kycMode?"1":e.list.filter((e=>e.exhibit)).length)+". "+e._s(i.title)+" ")]):e._e(),1===i.id&&e.kycMode<=2?t("div",{staticClass:"box"},e._l(i.adminKycPropertyBoList,(function(s,i){return t("ul",{key:i,staticClass:"mui-fl-vert box-tr network",class:{"border-top":0!==i&&s.value.length}},[s.value.length?[t("li",{staticClass:"t1 mui-shr-0"},[e._v(" "+e._s(s.kycVerifyProperty)+" ")]),t("li",{staticClass:"border-left"},[t("ul",{staticClass:"mui-fl-vert mui-fl-wrap"},e._l(s.value,(function(i,l){return t("li",{key:i.id,staticClass:"mui-fl-vert network-item",class:{"no-last-item":s.value.length!==l+1}},[t("img",{attrs:{src:i.symbol,alt:""}}),t("p",{staticClass:"t1"},[e._v(e._s(i.value))])])})),0)])]:e._e()],2)})),0):2===i.id&&e.kycMode<=2||5===i.id&&e.kycMode>=2?t("div",{staticClass:"box"},e._l(i.adminKycPropertyBoList,(function(l,a){return t("div",{key:a},["Age"===l.kycVerifyProperty?t("ul",{staticClass:"mui-fl-vert box-tr"},[t("li",{staticClass:"t1 mui-shr-0"},[e._v(" "+e._s(l.kycVerifyProperty)+" ")]),t("li",{staticClass:"t1 border-left"},[e._v(" Over "+e._s(e.getAge(i.adminKycPropertyBoList[0].value))+" ")])]):l.status?[t("ul",{staticClass:"mui-fl-vert mui-fl-btw box-tr citizen-search border-top"},[t("li",{staticClass:"t1 mui-shr-0"},[e._v(" "+e._s(l.kycVerifyProperty)+" ")]),t("li",{staticClass:"search-content"},[t("m-input",{staticClass:"sty1-input-search sty2-input-search",attrs:{placeholder:"Search","prefix-icon":"mico-search",clearable:""},on:{input:function(t){return e.handleClkSearch(e.keyword[i.id],i.id)},focus:function(t){return e.clearSearch(i.id)}},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleClkSearch(e.keyword[i.id],i.id)}},model:{value:e.keyword[i.id],callback:function(t){e.$set(e.keyword,i.id,t)},expression:"keyword[item.id]"}}),2===i.id&&e.keyword[2]||5===i.id&&e.kycMode>=2&&e.keyword[5]?[e.searchOptions.length?t("ul",{staticClass:"search-list"},e._l(e.searchOptions,(function(s){return t("li",{key:s.id,staticClass:"mui-fl-vert",on:{click:function(t){return e.selectOption(s,i.id)}}},[t("div",{staticClass:"img nation-sprites mui-shr-0",style:{backgroundPosition:-5-34*(s.ids-1)+"px -5px"}}),t("p",{attrs:{title:s.regularName}},[e._v(e._s(s.regularName))]),t("i",{class:[s.isSelect?"mcico-success2":"mcico-SelectNot"]})])})),0):t("div",{staticClass:"mui-fl-central mui-fl-col search-list no-list"},[t("img",{attrs:{src:s(26169),alt:""}}),t("div",{staticClass:"no-txt"},[e._v("No Results")])])]:e._e()],2)]),t("ul",{staticClass:"mui-flex citizen-box border-top"},e._l(5===i.id?e.GeolocationList:e.countryList,(function(s,l){return t("li",{key:s.id},[t("div",{staticClass:"available mui-fl-vert"},[t("i",{staticClass:"mcico-success-green"}),e._v(" Available countries/regions "+e._s(5===i.id?e.geoCountriesLength:e.selectCountries)+"/"+e._s(e.countriesLength)+" ")]),s.countries?.length?t("ul",{ref:`ndScrollRef${l+1}${i.id}`,refInFor:!0,staticClass:"nations"},e._l(s.countries,(function(s){return t("li",{key:s.letter,staticClass:"nation-item"},[t("p",{staticClass:"t1"},[e._v(e._s(s.letter))]),t("ul",e._l(s.childrens,(function(s){return t("li",{key:s.value,ref:`ndLetterRef${l+1}${i.id}`,refInFor:!0,staticClass:"mui-fl-btw mui-fl-central"},[t("div",[s.isSelect?t("i",{staticClass:"mcico-success2"}):t("i",{staticClass:"mcico-SelectNot"})]),t("div",{staticClass:"mui-fl-vert",staticStyle:{width:"100%"}},[t("div",{staticClass:"img nation-sprites mui-shr-0",style:{backgroundPosition:-5-34*(s.ids-1)+"px -5px"}}),t("p",{attrs:{title:s.regularName}},[e._v(e._s(s.regularName))])]),5!==i.id&&3!==e.kycMode?t("div",{staticClass:"mui-fl-central"},[s.selectDocuments?.isSelect4||!s.selectDocuments&&s.supportDocumentList?.includes(4)?t("m-button",{staticClass:"mui-fl-central network-tag tag-checked tag-checked-hover"},[t("span",[e._v("Driver License")])]):e._e(),s.selectDocuments?.isSelect2||!s.selectDocuments&&s.supportDocumentList?.includes(2)?t("m-button",{staticClass:"mui-fl-central network-tag tag-checked tag-checked-hover"},[t("span",[e._v("Passport")])]):e._e(),s.selectDocuments?.isSelect1||!s.selectDocuments&&s.supportDocumentList?.includes(1)?t("m-button",{staticClass:"mui-fl-central network-tag tag-checked tag-checked-hover"},[t("span",[e._v("ID Card")])]):e._e()],1):e._e()])})),0)])})),0):e._e()])})),0)]:e._e()],2)})),0):3===i.id&&e.kycMode<=2||4===i.id&&e.kycMode<=2?t("ul",{staticClass:"box aml-ul"},e._l(i.adminKycPropertyBoList,(function(s){return t("div",{key:s.id},[s.status?t("li",{staticClass:"t1"},[e._v(" "+e._s(s.kycVerifyProperty)+" ")]):e._e()])})),0):e._e()]):e._e()])})),0):e._e()},l=[],a=(s(72577),s(3949),{props:{kycInfo:{type:Number,required:1},list:{type:Array,required:!0},selectCountry:{type:Object,required:!0},selectGeolocation:{type:Object,required:!0}},data(){return{keyword:{2:"",5:""},searchOptions:[],loading:!1,isSelect1:!0,inputid:""}},computed:{kycLevel(){return localStorage.getItem("zkmeAdminUser")&&JSON.parse(localStorage.getItem("zkmeAdminUser")).level||this.$store.state.auth.user.level},kycMode(){return this.$route.query.mode?Number(this.$route.query.mode):this.kycInfo},countryList(){return[{id:1,title:"Available",countries:this.selectCountry.avaliableList,searchCountries:[]}]},GeolocationList(){return[{id:1,title:"Available",countries:this.selectGeolocation.avaliableList,searchCountries:[]}]},allCountries(){if(this.kycMode>=2&&!this.selectGeolocation.avaliableList.length&&!this.selectGeolocation.unavailableList.length)return[];if(this.kycMode<=2&&!this.selectCountry.avaliableList.length&&!this.selectCountry.unavailableList.length)return[];let e,t=[];return e=!this.kycMode||3===this.kycMode||2===this.kycMode&&5===this.inputid?this.selectGeolocation:this.selectCountry,e.avaliableList.length&&e.avaliableList.forEach((e=>{e.childrens.length&&(t=t.concat(e.childrens))})),e.unavailableList.length&&e.unavailableList.forEach((e=>{e.childrens.length&&(t=t.concat(e.childrens))})),t=this.sortList(t,"regularName"),t},selectCountries(){return this.$store.state.zkKyc.selectCountries||0},countriesLength(){return this.$store.state.zkKyc.countriesLength||248},geoCountriesLength(){return this.$store.state.zkKyc.geoCountriesLength||0}},methods:{handleClkSearch(e,t){this.inputid=t,this.searchOptions=this.allCountries.filter((t=>t.regularName.toLowerCase().indexOf(e.toLowerCase())>-1))},clearSearch(){this.keyword={2:"",5:""}},selectOption(e,t){this.clearSearch();const s=this.$refs[`ndLetterRef1${t}`],i=this.$refs[`ndScrollRef1${t}`][0],l=s.findIndex((t=>t.textContent.includes(e.regularName)));-1!==l&&this.scrollTo(i,l?s[208===e.ids?121:e.ids-1].offsetTop:0)},sortList(e,t){return e.sort(((e,s)=>e[t]<s[t]?-1:1))},getAge(e){return e.find((e=>e.isSelect))?.value||""},linear(e,t,s,i){return s*e/i+t},scrollTo(e,t,s=14){const i=e.scrollTop;let l=i,a=0;const r=t-i;return new Promise((n=>{const c=()=>{a++,l=this.linear(a,i,r,s),e.scrollTop=l,(i<t&&l<t||i>t&&l>t)&&a<=s?requestAnimationFrame(c):n()};c()}))}}}),r=a,n=s(81656),c=(0,n.A)(r,i,l,!1,null,"2b4fdaa1",null),o=c.exports}}]);