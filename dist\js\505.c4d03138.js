"use strict";(self["webpackChunkzkme_web"]=self["webpackChunkzkme_web"]||[]).push([[505],{74487:function(e,t,a){a.r(t),a.d(t,{default:function(){return p}});var s=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{padding:"0px 60px 0"}},[e.isSearch?t("div",{staticClass:"Search-title mui-fl-vert"},[t("i",{staticClass:"mico-Fallback icon",on:{click:function(t){return e.back()}}}),t("span",{staticClass:"fg"}),t("span",[e._v(e._s(e.titleTxt))])]):t("div",{staticClass:"pg-title1 mui-fl-vert"},[t("i",{staticClass:"mcico-active-activation"}),e._v(" Configuration ")]),t("div",{staticClass:"top_function"},[t("div",{staticClass:"top_title"},[e._v("Select verification configuration and create a program")]),t("div",{staticClass:"top_selected"},[e._v("Selected: "+e._s(e.isSelect1&&e.isSelect2?2:e.isSelect1||e.isSelect2?1:0))]),t("div",{staticClass:"mui-fl-vert"},[t("ul",{staticClass:"mui-fl-vert mui-fl-wrap"},[t("m-button",{staticClass:"mui-fl-central network-tag",class:{"tag-checked":e.isSelect1,"tag-checked-hover":e.isSelect1},on:{click:function(t){e.isSelect1=!e.isSelect1}}},[t("p",[e._v("Identity verification")])]),t("m-button",{staticClass:"mui-fl-central network-tag",class:{"tag-checked":e.isSelect2,"tag-checked-hover":e.isSelect2},on:{click:function(t){e.isSelect2=!e.isSelect2}}},[t("p",[e._v("Location")])])],1)]),t("m-button",{staticClass:"sty5-button",attrs:{disabled:!e.isSelect1&&!e.isSelect2},on:{click:e.createProgram}},[t("i",{staticClass:"mico-creat"}),e._v(" Create Program")])],1),t("div",{staticClass:"mui-fl-vert mui-fl-btw"},[t("m-checkbox",{staticClass:"sty2-checkbox",on:{change:function(t){return e.handleCurrentChange()}},model:{value:e.checked1,callback:function(t){e.checked1=t},expression:"checked1"}},[e._v("Pin current apply")]),t("MuiInput",{staticClass:"inp",attrs:{placeholder:"Search for program name",closeflg:!0,searchflg:!0},on:{enter:e.search,search:e.search,clear:e.clear},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}})],1),t("m-divider",{staticClass:"sty1-divider"}),t("div",{staticClass:"recorded_table-wrap mui-fl-col mui-fl-btw"},[e.dataflag?t("m-table",{ref:"table",class:["recorded_table",(e.checked1||e.checked2)&&"table_topping"],attrs:{data:e.tableData,"row-class-name":e.rowClassname,"cell-class-name":e.cellClassname,"default-sort":{prop:"lastUpdateTime",order:"descending"}},on:{"cell-click":e.expandDetails,"cell-mouse-enter":e.cellMouseEnter,"cell-mouse-leave":e.cellMouseLeave,"sort-change":e.sortchange}},[t("m-table-column",{attrs:{prop:"date",label:"Program Name","min-width":"110px"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("m-popover",{attrs:{offset:10,"popper-class":"NotRecord_popover Record_popover",disabled:a.offsetWidth+1<e.programNameWidth,placement:"top-start",trigger:"hover",content:a.programName}},[t("div",{ref:"programName",attrs:{slot:"reference"},slot:"reference"},[t("span",{ref:"programText",staticClass:"spanprogramName"},[e._v(" "+e._s(a.programName)+" ")])])])]}}],null,!1,2414614462)}),t("m-table-column",{attrs:{prop:"kycProgramId",label:"Program No","min-width":"130px"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("div",{staticClass:"mui-fl-vert"},[t("span",{staticClass:"kycProgramId"},[e._v(e._s(a.kycProgramId))]),t("i",{staticClass:"icon-copy-wallet mico-copy copyIcon",on:{click:function(t){return t.stopPropagation(),e.copy(a.kycProgramId)}}})])]}}],null,!1,3506027063)}),t("m-table-column",{attrs:{prop:"kycText",label:"Program Type","min-width":"130px"}},[e._v(" "+e._s(e.kycText)+" ")]),t("m-table-column",{attrs:{prop:"status",label:"Status","min-width":"100px"},scopedSlots:e._u([{key:"default",fn:function({row:a}){return[t("div",{class:["mui-fl-vert",a.statusdate.name.trim()+1]},[t("div",{staticClass:"tableballcolor ball"}),t("div",{staticClass:"row_status"},[e._v(e._s(a.statusdate.name))])])]}}],null,!1,2392589182)}),t("m-table-column",{attrs:{sortable:"custom",prop:"auditedUser",label:"Audited User","min-width":"135px"},scopedSlots:e._u([{key:"header",fn:function(){return[t("span",[e._v("Audited User")]),t("span",{staticClass:"caret-wrapper"},[t("i",{staticClass:"mico-sort-up"}),t("i",{staticClass:"mico-sort-dow"})])]},proxy:!0},{key:"default",fn:function({row:t}){return[e._v(" "+e._s(t.auditedUser?parseFloat(t.auditedUser).toLocaleString():"--")+" ")]}}],null,!1,2880292453)}),t("m-table-column",{attrs:{sortable:"custom",prop:"createTime",label:"Created Time","min-width":"165px"},scopedSlots:e._u([{key:"header",fn:function(){return[t("span",[e._v("Created Time")]),t("span",{staticClass:"caret-wrapper"},[t("i",{staticClass:"mico-sort-up"}),t("i",{staticClass:"mico-sort-dow"})])]},proxy:!0}],null,!1,3607198967)}),t("m-table-column",{attrs:{sortable:"custom",prop:"lastUpdateTime",label:"Last update time","min-width":"165px"},scopedSlots:e._u([{key:"header",fn:function(){return[t("span",[e._v("Last Update Time")]),t("span",{staticClass:"caret-wrapper"},[t("i",{staticClass:"mico-sort-up"}),t("i",{staticClass:"mico-sort-dow"})])]},proxy:!0}],null,!1,2586270828)}),t("m-table-column",{attrs:{label:"Operation","min-width":"80px"}},[[t("div",{staticClass:"view-edit"},[e._v(" View Edit ")])]],2)],1):e._e(),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.dataflag,expression:"!dataflag"}],staticClass:"no-table-data mui-fl-col mui-fl-vert"},[t("img",{attrs:{src:a(26169),alt:""}}),t("p",[e._v("Start creating your first KYC program!")])]),t("div",{staticClass:"mui-fl-end"},[t("m-pagination",{directives:[{name:"show",rawName:"v-show",value:e.tableData.length,expression:"tableData.length"}],staticClass:"sty1-pagination sty3-cell",attrs:{"hide-on-single-page":"","current-page":e.page.page,layout:"prev, pager, next","page-size":10,total:e.page.total},on:{"current-change":e.handleCurrentChange,"update:currentPage":function(t){return e.$set(e.page,"page",t)},"update:current-page":function(t){return e.$set(e.page,"page",t)}}})],1)],1),t("drawer-component",{ref:"drawerComponent",attrs:{drawerVisible:e.drawer,id:Number(e.update.id),createTime:e.update.createTime,"has-applied-program":e.hasAppliedProgram},on:{close:e.close,update:e.updateList}}),t("KycWarning",{attrs:{model:e.model,warningtip:e.warningtip},on:{Leave:e.Leave}})],1)},i=[],r=(a(44114),a(98992),a(72577),a(3949),a(37550),a(84082)),l=a(11417),o=a(66978),c=a(31214),n={components:{KycWarning:r.A,MuiInput:l.A,DrawerComponent:o.A},data(){return{titleTxt:"Search Results",isSearch:!1,update:{state:!1,createTime:null,id:""},parameter:{time:"",auditedUser:"",highestLevel:"",items:""},dataflag:!0,operationdata:{},model:"Recorded",drawerCopy:!1,isCollapse:!1,keyword:"",copystyle:!1,drawer:!1,checked1:!0,checked2:!1,warningtip:!1,dialogTableVisible:!1,lastUpdateTime:"descending",kycLevel:[],detailsList:{},tableDatas:[],tableData:[],tagindex:{val:"",id:""},page:{page:1,size:10},initialization:{},openModified:!1,programNameWidth:"",lv1offsetWidth:[],hasAppliedProgram:!1,isSelect1:!0,isSelect2:!1,time:null}},computed:{mode(){let e=0;return this.isSelect1?e=1:this.isSelect2&&(e=3),this.isSelect1&&this.isSelect2&&(e=2),e},kycText(){let e="";const t=localStorage.getItem("zkmeAdminUser")&&JSON.parse(localStorage.getItem("zkmeAdminUser")).level||this.$store.state.auth.user.level;switch(Number(t)){case 0:e="On-chain Mint";break;case 1:e="On-chain Transactional";break;case 2:e="Cross-chain";break}return e}},watch:{async"$route.query"(e){await this.queryUserKycList(),this.tableData.length&&await this.pushprogramName()}},async created(){localStorage.getItem("beforeunload")&&this.handleCurrentChange(),await this.queryUserKycList(),this.tableData.length&&this.pushprogramName(),this.update.state&&this.expandDetails(this.update)},async mounted(){this.onWinResize(),window.addEventListener("resize",this.onWinResize),window.addEventListener("beforeunload",this.beforeunload)},methods:{async updateList(){this.drawerVisible=!1,localStorage.getItem("beforeunload")&&this.handleCurrentChange(),await this.queryUserKycList(),this.tableData.length&&this.pushprogramName(),this.update.state&&this.expandDetails(this.update)},createProgram(){window.gtag("event","Create_program",{app_name:"zkMe Dashboard"}),this.keyword="",this.$store.commit(c.tq,null),this.$store.commit(c.oN,null),this.$store.commit(c.wh,"Create program"),this.$store.commit("SET_KYC_TITLE","Create program"),this.$router.push(`/zk-kyc/zkkyc-form?mode=${this.mode}`)},updateProgram(e){this.keyword=""},beforeunload(){},pushprogramName(){this.programNameWidth=this.programNameWidth||this.$refs.programName?.offsetWidth;const e=[...document.getElementsByClassName("spanprogramName")];this.tableData.length&&e.length&&this.tableData.forEach((t=>{e.forEach((e=>{t.programName===e.textContent.trim()&&this.$set(t,"offsetWidth",e.offsetWidth)}))}))},onWinResize(){this.isCollapse=document.body.clientWidth<=1e3,setTimeout((()=>{this.$refs.programName&&(this.programNameWidth=this.$refs.programName?.offsetWidth)}),200)},headercellclassname({column:e}){},sortchange({column:e,prop:t,order:a}){switch(this.parameter={time:"",auditedUser:"",highestLevel:"",items:""},a?this.lastUpdateTime=a:(this.lastUpdateTime="ascending",e.order=this.lastUpdateTime),t){case"lastUpdateTime":this.parameter.time="ascending"===this.lastUpdateTime?1:0;break;case"createTime":this.parameter.time="ascending"===this.lastUpdateTime?1:0;break;case"highestLevel":this.parameter.highestLevel="ascending"===this.lastUpdateTime?1:0;break;case"items":this.parameter.items="ascending"===this.lastUpdateTime?1:0;break;case"auditedUser":this.parameter.auditedUser="ascending"===this.lastUpdateTime?1:0;break}this.handleCurrentChange()},handleCurrentChange(e){"number"===typeof e&&(this.page.page=e,e=!1),this.initialization=e;const t={page:e?1:this.page.page,time:e?0:this.parameter.time,auditedUser:e?"":this.parameter.auditedUser,highestLevel:e?"":this.parameter.highestLevel,items:e?"":this.parameter.items,val:e?"":this.tagindex.val,id:e?"":this.tagindex.id,checked1:!!e||this.checked1,checked2:!e&&this.checked2,keyword:e?"":this.keyword};this.$router.push({query:t})},async queryUserKycList(e){this.page.page=Number(this.$route.query.page)||1,this.page.size=Number(this.$route.query.size)||10,this.checked1="true"===this.$route.query.checked1||this.checked1||!1,this.checked2="true"===this.$route.query.checked2||!1,e||(this.keyword=this.$route.query.keyword||""),this.parameter.time=this.$route.query.time||0,this.parameter.auditedUser=this.$route.query.auditedUser||"",this.parameter.highestLevel=this.$route.query.highestLevel||"",this.parameter.items=this.$route.query.items||"",this.tagindex.val=this.$route.query.val||"",this.tagindex.id=this.$route.query.id||"";const t={auditedUser:this.parameter.auditedUser,filterHighestLevel:this.tagindex.id,hideUnapplied:this.checked2?1:0,highestLevel:this.parameter.highestLevel,items:this.parameter.items,pageReq:this.page,pinCurrentApply:this.checked1?1:0,programName:e?this.$route.query.keyword:this.keyword,time:this.parameter.time},a=await this.$api.request("kyc.queryUserKycList",t);8e7===a.code&&(a.data.list&&a.data.list.length?this.dataflag=!0:this.dataflag=!1,this.hasAppliedProgram=!!a.data.list.find((e=>1===Number(e.status))),a.data.list.forEach((e=>{e.statusdate=this.statuscf(e.status)})),localStorage.setItem("beforeunload",""),this.tableData=a.data.list,this.page=a.data.page,clearTimeout(this.time),a.data.list.some((e=>"5"===e.status))&&(this.time=setTimeout((()=>{this.queryUserKycList("updateData"),this.drawer&&this.$refs.drawerComponent.queryKycInfo()}),5e3)))},statuscf(e){const t={name:"",timetip:"at "};switch(e){case"-1":t.name="Delete ";break;case"1":t.name="Apply ",t.timetip="from ";break;case"2":t.name="Created ";break;case"3":t.name="Created ";break;case"4":t.name="Expired ";break;case"5":t.name="Pending ";break;default:break}return t},clear(){this.keyword="",this.handleCurrentChange(),this.retrieval(!1)},close(){this.drawer=!1,this.update.id=""},search(){this.keyword&&(this.retrieval(!0,"Search Results"),this.handleCurrentChange())},retrieval(e,t){this.isSearch=e,this.titleTxt=t},tagclose(){this.tagindex.val="",this.tagindex.id="",this.keyword?this.search():(this.retrieval(!1),this.handleCurrentChange())},handleCommand(e){this.tagindex.val=e.levelName,this.tagindex.id=e.id,this.handleCurrentChange()},cellClassname(e){if("Program Name"===e.column.label&&"date"===e.column.property)return"cellClassname"},rowClassname(e){return this.statuscf(e.row.status).name},cellMouseEnter(e,t){"Program No."===t.label&&this.$set(e,"program_no",!0)},cellMouseLeave(e,t){"Program No."===t.label&&(e.program_no=!1)},copy(e){const t=document.createElement("input");t.value=e,document.body.appendChild(t),t.select(),document.execCommand("Copy"),t.remove(),this.$message({message:"Copied",iconClass:"mico-lightTip",customClass:"sty1-message",duration:3e3,offset:32,center:!0})},async Leave(e){if(this.warningtip=!1,"Leave"===e){const e=await this.$api.request("kyc.updateStatus",this.operationdata);8e7===e.code&&(this.drawer=!1,this.queryUserKycList())}},async expandDetails(e){this.update.id=e.id,this.update.createTime=new Date(e.createTime).getTime(),this.drawer=!0,this.$store.commit("SET_CATEGORY",e.category)},updateStatus(e,t){e&&(this.warningtip=!0),this.model=e,this.operationdata={id:this.detailsList.id,status:t}},back(){this.keyword="",this.handleCurrentChange(),this.retrieval(!1)}}},d=n,h=a(81656),m=(0,h.A)(d,s,i,!1,null,"981a60ea",null),p=m.exports}}]);