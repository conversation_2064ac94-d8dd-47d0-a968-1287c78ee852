# Default values for static.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1
image:
  repository: hub.bitkinetic.com/zkme/zkme-web
  tag: latest
  pullPolicy: IfNotPresent
imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""
aliyunLogsFlag: aliyun-logs-nginx
workDir: /www/html
service:
  type: ClusterIP
  port: 80
ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
resources: 
  limits:
    cpu: 500m
    memory: 800Mi
  requests:
    cpu: 10m
    memory: 30Mi
# nodeSelector:
#   svc: koolio
# tolerations:
#   - key: purpose
#     operator: Equal
#     value: koolio
#     effect: NoExecute
    
affinity: {}
