import Vue from 'vue'
import App from './App.vue'
import router from '@/router'
import store from '@/store'
import api from '@/api'
import filters from '@/utils/filters'
import MUI from '@/components/_mui'
import '@/assets/css/main.scss'
import lang from 'element-ui/lib/locale/lang/en'
import locale from 'element-ui/lib/locale'
import { Loading } from 'element-ui'

Vue.config.productionTip = false
Vue.use(MUI)
Vue.use(filters) // 全局注册过滤器
Vue.use(Loading.directive)
locale.use(lang)

// Vue的原型扩展
Object.defineProperty(Vue.prototype, '$api', { value: api }) // 服务端 api 请求

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
