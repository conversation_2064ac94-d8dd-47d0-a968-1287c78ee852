<template>
  <div class="wrap">
    <p class="t1">Compliance Meets <br>Decentralization with zkKYC</p>
    <p class="t2">Compliance without compromises on the decentralization & privacy ethos of web3.</p>

    <ul>
      <li v-for="(item, index) of list" :key="index">
        <p class="title mui-fl-vert">
          <i :class="[item.icon, 'mui-fl-central']" />
          {{ item.title }}
        </p>
        <p class="desc">{{ item.desc }}</p>
      </li>
    </ul>

    <p class="t3">Join 70+ web3 companies building on zkMe</p>
    <div class="img-list mui-fl-vert">
      <p class="mui-fl-vert">
        <img v-for="(item, index) of chainImgList" :key="index" :src="item.img" alt="">
      </p>
      <p class="num">70+</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SignRightComponent',
  data () {
    return {
      list: [
        {
          id: 0,
          icon: 'mico-citizenship-3',
          title: 'Streamline Compliance',
          desc: 'Simplify KYC/AML processes with privacy-preserving checks.'
        },
        {
          id: 1,
          icon: 'mico-badge-dollar',
          title: 'Reduce Costs',
          desc: 'Cut operational expenses associated with identity verification.'
        },
        {
          id: 2,
          icon: 'mico-house-shield',
          title: 'Enhance Security',
          desc: 'Cut operational expenses associated with identity verification.'
        },
        {
          id: 3,
          icon: 'mico-citizenship-8',
          title: 'Improve User Experience',
          desc: 'Offer seamless, privacy-focused onboarding for your clients.'
        }
      ],
      chainImgList: [
        {
          id: 0,
          img: require('@/assets/img/chain-01.png')
        },
        {
          id: 1,
          img: require('@/assets/img/chain-02.png')
        },
        {
          id: 2,
          img: require('@/assets/img/chain-03.png')
        },
        {
          id: 3,
          img: require('@/assets/img/chain-04.png')
        },
        {
          id: 4,
          img: require('@/assets/img/chain-05.png')
        },
        {
          id: 5,
          img: require('@/assets/img/chain-06.png')
        },
        {
          id: 6,
          img: require('@/assets/img/chain-07.png')
        },
        {
          id: 7,
          img: require('@/assets/img/chain-08.png')
        }
      ]
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/components/_sign-right-component.scss" scoped></style>
