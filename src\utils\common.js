export const loadJs = (url) => {
  return new Promise(resolve => {
    const ndScripts = document.querySelectorAll('head script')
    for (const x of ndScripts) {
      if (x.src === url || x.attributes.getNamedItem('src')?.value === url) {
        // 如果已存在相同的 js 则返回成功
        return resolve(true)
      }
    }

    const ndHead = document.querySelector('head')
    const ndScript = document.createElement('script')
    ndScript.onload = () => {
      resolve(true)
    }
    ndScript.onerror = () => {
      resolve(false)
      ndScript.remove()
    }
    ndScript.src = url
    ndHead && ndHead.appendChild(ndScript)
  })
}
