<template>
  <div class="login-bottom mui-fl-hori">
    <div class="bottomwd">
      <div class="mui-fl-btw mui-fl-vert">
        <p class="mui-fl-vert bottom-left" >
          <!-- <i class="taplight mico-twitter" />
          <i class="taplight mico-discord" />
          <i class="taplight mico-meidum" />
          <i class="taplight mico-telegram" />
          <i class="taplight mico-instagram" />
          <i class="taplight mico-ins" />
          <i class="taplight mico-email" /> -->
          <i v-for="(item, index) of mediaArr" :key="index" :class="[item.ico, 'taplight']" @click="handClkMedia(item)"/>
        </p>
        <p class="copyright bottom-right">2022 zkMe Technology Limited, All Rights Reserved. </p>
      </div>
    </div>
  </div>

</template>

<script>
export default {
  data () {
    return {
      mediaArr: [
        {
          ico: 'mico-twitter',
          link: 'https://twitter.com/zkme_'
        },
        {
          ico: 'mico-meidum',
          link: 'https://medium.com/@zkMe'
        },
        {
          ico: 'mico-ins',
          link: 'https://www.linkedin.com/company/zkme/'
        },
        {
          ico: 'mico-youtube',
          link: 'https://youtube.com/@zkMe_'
        },
        {
          ico: 'mico-discord',
          link: 'https://discord.gg/SJ2RDs9NGM'
        },
        {
          ico: 'mico-github',
          link: 'https://github.com/zkMeLabs'
        }
      ]
    }
  },
  methods: {
    handClkMedia (item) {
      if (!item.link) {
        this.coming()
      } else {
        window.open(item.link)
      }
    },
    coming () {
      this.$message({
        message: 'Coming soon',
        iconClass: 'mico-lightTip',
        customClass: 'sty1-message',
        duration: 3000,
        offset: 32,
        center: true
      })
    }
  }
}
</script>

<style lang="scss" Scoped>
.login-bottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  background: rgba(0, 46, 51, 0.4);
  backdrop-filter: blur(23px);
  .bottomwd {
    width: 100%;
    max-width: 1200px;
  }
  // .bottom-left {
  //   justify-content: center;
  //   flex: 1;
  //   // padding-right: 290px;
  // }
  // .bottom-right {
  //   text-align: center;
  //   flex: 1;
  // }
  .taplight + .taplight {
    margin-left: 8px;
  }
  > div {
    // max-width: 1200px;
    // margin: 0 auto;
    padding: 12px 0;
    i {
      font-size: 32px;
      color: rgba(255, 255, 255, 0.6);
    }
    .copyright {
      font-size: 12px;
      line-height: 14px;
      color: rgba(255, 255, 255, 0.8);
    }
  }
}
</style>
