"use strict";(self["webpackChunkzkme_web"]=self["webpackChunkzkme_web"]||[]).push([[981],{34981:function(t,e,s){s.d(e,{A:function(){return n}});var i=function(){var t=this,e=t._self._c;return e("div",[e("m-dialog",{attrs:{"close-on-click-modal":!1,"close-on-press-escape":!1,"custom-class":"sty1-dialog sty4-dialog",title:"Submission background",visible:t.reqStates.form,width:"610px"},on:{close:t.restState,"update:visible":function(e){return t.$set(t.reqStates,"form",e)}}},[e("div",{staticClass:"diaTitle"},[t._v("1. Purpose*")]),e("div",[e("m-select",{staticClass:"sty1-select sty4-select",attrs:{"popper-class":"sty6-popper",placeholder:"Please select a purpose"},on:{change:t.pushLabel},model:{value:t.selectValue,callback:function(e){t.selectValue=e},expression:"selectValue"}},t._l(t.options,(function(t){return e("m-option",{key:t.label,attrs:{label:t.value,value:t.label}})})),1)],1),e("div",{staticClass:"diaTitle"},[t._v("2. Detailed explanation*")]),e("div",[e("m-input",{staticClass:"sty2-input sty1-input-textarea",attrs:{resize:"none",type:"textarea",placeholder:"Please clearly give background information regarding the action submission.",maxlength:"300","show-word-limit":""},model:{value:t.fetchDate.detail,callback:function(e){t.$set(t.fetchDate,"detail",e)},expression:"fetchDate.detail"}})],1),e("div",{staticClass:"diaTitle"},[t._v("3. Supplementary documents")]),e("div",[e("m-upload",{class:["sty1-upload",t.fileList.length&&"sty2-upload"],attrs:{drag:"",accept:".jpg,.png,.pdf,.jpeg",action:"#","file-list":t.fileList,"show-file-list":!1,"auto-upload":!1,"on-change":t.fileCheck,multiple:""}},[e("div",[t.fileList.length?e("div",t._l(t.fileList,(function(s,i){return e("div",{key:i,staticClass:"mui-fl-btw uploadList mui-fl-vert"},[e("div",{staticClass:"mui-fl-vert"},[e("i",{staticClass:"mico-upload-file"}),e("span",{staticClass:"fileName"},[t._v(t._s(s.name))]),e("span",{staticStyle:{"margin-left":"12px"}},[t._v(t._s(t._f("fileSizeCalculate")(s.size)))])]),e("div",{staticClass:"mui-fl-vert"},["loading"===s.loading?e("div",{staticClass:"loading"}):"fail"===s.loading?e("i",{staticClass:"mcico-warn2"}):e("i",{staticClass:"mcico-upload-success"}),e("i",{staticClass:"mico-drawer-close",on:{click:function(e){return e.stopPropagation(),t.detelFileList(s,i)}}})])])})),0):e("div",{staticClass:"mui-fl-vert mui-fl-col"},[e("i",{staticClass:"mico-upload"}),e("div",{staticClass:"uploadText"},[t._v("Please upload relevant regulatory or audit documents as reference materials for this submission.")])])])]),e("div",{staticClass:"uploadTip",style:{color:t.wrongFlag?"red":"#B3C0C2"}},[t._v("PDF/JPG/PNG only, max. 10MB")])],1),e("div",{staticClass:"mui-fl-hori"},[e("m-button",{staticClass:"sty5-button mgt",attrs:{disabled:!t.fetchDate.purpose||!t.fetchDate.detail||t.wrongFlag},on:{click:function(e){return t.updateAction()}}},[t._v("Submit recovery")])],1)]),e("m-dialog",{attrs:{"close-on-click-modal":!1,"close-on-press-escape":!1,"custom-class":"sty1-dialog sty4-dialog",title:"Submission records",visible:t.reqStates.list,width:"764px"},on:{close:t.restState,"update:visible":function(e){return t.$set(t.reqStates,"list",e)}}},[e("m-table",{staticClass:"sty2-table sty3-table sty4-table",attrs:{data:t.tableList,"max-height":"466"}},[e("template",{slot:"empty"},[e("img",{attrs:{src:s(26169),alt:""}}),e("div",[t._v("No record")])]),e("m-table-column",{attrs:{property:"submissionTime",label:"Submission time",width:"170"}}),e("m-table-column",{attrs:{property:"userCount",label:"Number of records",width:"150"},scopedSlots:t._u([{key:"default",fn:function({row:e}){return[t._v(" "+t._s(new Intl.NumberFormat("en-US").format(e.userCount))+" ")]}}])}),e("m-table-column",{attrs:{property:"status",label:"Status",width:"120"},scopedSlots:t._u([{key:"default",fn:function({row:s}){return[e("div",{staticClass:"mui-fl-vert stext"},[t.getExpireDateTime(s.expireDateTime)&&2===s.status?e("i",{staticClass:"mcico-warn1"}):s.status<2?e("i",{staticClass:"mcico-Processing"}):e("i",{staticClass:"mcico-success-green"}),t.getExpireDateTime(s.expireDateTime)&&2===s.status?e("div",[t._v("Expired")]):s.status<2?e("div",[t._v("In approval")]):e("div",[t._v("Completed")])])]}}])}),e("m-table-column",{attrs:{property:"expireDateTime",label:"Expiry date",width:"170"},scopedSlots:t._u([{key:"default",fn:function({row:s}){return[e("div",{staticClass:"mui-fl-vert"},[t._v(" "+t._s(s.expireDateTime||"-")+" ")])]}}])}),e("m-table-column",{attrs:{label:"Operation",width:"100"},scopedSlots:t._u([{key:"default",fn:function({row:s}){return[e("m-button",{staticStyle:{color:"#64ABFF"},attrs:{type:"text"},on:{click:function(e){return t.tableView(s)}}},[t._v("View")])]}}])})],2),e("div",{staticClass:"mui-fl-hori"},[e("m-button",{staticClass:"sty5-button mgt",on:{click:function(e){return t.restState("open")}}},[t._v("Submit recovery")])],1)],1),e("m-dialog",{attrs:{"close-on-click-modal":!1,"close-on-press-escape":!1,"custom-class":"sty1-dialog sty4-dialog",title:"Expired"!==t.flag?"Submission process":"Recovery",visible:t.reqStates.step,width:"610px"},on:{close:function(e){return t.restState("time")},"update:visible":function(e){return t.$set(t.reqStates,"step",e)}}},["Expired"!==t.flag?e("div",[e("div",{staticClass:"mui-flex"},[e("div",{staticClass:"mui-fl-col mui-fl-vert"},[t.textStep?e("i",{staticClass:"mcico-success2"}):e("div",{staticClass:"loading"}),e("div",{staticClass:"sol"})]),e("div",[e("div",{staticClass:"t1"},[t._v("Retrieving encrypted data from decentralized storage.")])])]),e("div",{staticClass:"mui-flex"},[e("div",[t.textStep<2?e("i",{staticClass:"mico-step2"}):2===t.textStep?e("div",{staticClass:"loading"}):e("i",{staticClass:"mcico-success2"})]),e("div",[e("div",{staticClass:"t1"},[t._v("Waiting for Issuer and Regulator recovery approval.")])])]),e("div"),e("div",{staticClass:"mui-fl-hori"},[e("m-button",{staticClass:"sty5-button mgt",attrs:{disabled:t.textStep<=2}},[t._v("Submit recovery")])],1)]):e("div",{staticClass:"mui-fl-col mui-fl-vert expiredDate"},[e("img",{attrs:{src:s(26169),alt:""}}),e("div",[t._v("The decryption data you applied for has expired.")]),e("div",[t._v("Please submit your request again.")])])]),e("m-dialog",{attrs:{"close-on-click-modal":!1,"close-on-press-escape":!1,"custom-class":"sty1-dialog sty4-dialog",title:"Recovery",visible:t.reqStates.decryption,width:"610px"},on:{close:t.restState,"update:visible":function(e){return t.$set(t.reqStates,"decryption",e)}}},[e("div",{staticClass:"diaStepTitle1"},[t._v("Recovery submission approved!")]),e("div",{staticClass:"diaStepTitle"},[t._v("Follow the 2 steps to access decrypted data:")]),e("div",{staticClass:"st1"},[t._v("Step-1：Download partially decrypted data (1/2 threshold)")]),e("div",[e("a",{staticClass:"download",attrs:{href:t.downloadFile}},[t._v("Download data")])]),e("div",{staticClass:"st3"},[t._v("Step-2：Use the tool to recover data")]),e("div",[e("a",{staticClass:"download",attrs:{href:t.decryptLink,target:"_blank"}},[t._v("Open tool")])])])],1)},a=[],l=(s(44114),s(98992),s(72577),s(81454),s(37550),s(5626)),r={props:{type:{type:String,required:""},keyword:{type:String,required:""},state:{type:String,required:""},selectall:{type:Boolean,required:!1},selectAccount:{type:Object,default:()=>({})},startTime:{type:String,default:()=>""},endTime:{type:String,default:()=>""}},data(){return{reqStates:{form:!1,list:!1,step:!1,decryption:!1},getExpireDateTime:l.v3,alertFlag:!1,flag:"",fileList:[],tableList:[],description:"",selectValue:"",actionRequest:"",fileSize:0,wrongFlag:!1,fetchapplyDecrypt:!1,decryptLink:"https://test-dpa.zk.me",time:"",options:[{label:1,value:"Regulatory Data Release"},{label:2,value:"Suspicious Activity Report"},{label:3,value:"Audit"}],fetchDate:{detail:"",files:"",mchNo:"",purpose:"",typeId:"1",zkmeIds:""},userView:{},textStep:0,downloadFile:"",minWidthStyle:!1}},watch:{state(t){t&&(this.reqStates[t]=!0)},"reqStates.list"(t){t&&this.decryptList()}},created(){this.fetchDate.mchNo=this.$store.state.auth.user.mchNo},methods:{pushLabel(t){this.fetchDate.purpose=t.toString()},detelFileList(t,e){this.fileList.splice(e,1),this.fileSize=(0,l.GZ)(this.fileSize,t.size),this.fileList.length?this.fileList.some((t=>"fail"!==t.loading))&&this.fileSize<=10&&(this.wrongFlag=!1):this.wrongFlag=!1},async fileCheck(t){const e=(0,l.XD)(t,this.fileSize);if(this.fileSize=e.fileSize,t.name.length>30&&(t.name=t.name.slice(0,30)+"..."),!e.fileCheck||this.fileSize>10)return this.$set(t,"loading","fail"),this.fileList.push(t),void(this.wrongFlag=!0);const s=t.raw.name.substring(0,t.raw.name.indexOf("."));let i;i=s.length>20?s.replace(s,s.slice(0,10)+"..."+s.slice(-7))+t.raw.name.substring(t.raw.name.indexOf(".")):t.raw.name;const a=(new Date).getTime(),r=await this.$api.request("decrypt.generateUrl",{fileName:a+"-"+i});if(8e7===r.code){this.$set(t,"loading","loading"),this.$set(t,"fileOssUrl",r.data.url),this.fileList.push(t);const e=new FormData;e.append("OSSAccessKeyId",r.data.ossAccessKeyId),e.append("signature",r.data.postSignature),e.append("expire",r.data.expire),e.append("key",r.data.dir+a+"-"+i),e.append("policy",r.data.encodedPolicy),e.append("file",t.raw);const s={method:"POST",body:e};fetch(r.data.host,s).then((e=>{204===e.status?(this.$set(t,"loading",!1),this.fileList.reverse().reverse()):(this.$set(t,"loading","fail"),this.fileList.reverse().reverse())})).catch((()=>{this.$set(t,"loading","fail"),this.fileList.reverse().reverse()}))}else this.$message({message:"upload fail",type:"error"})},tableView(t){if((0,l.v3)(t.expireDateTime)&&2===t.status)this.flag="Expired";else if(this.flag="",t.status<1)this.textStep=t.status,this.time=setTimeout((()=>{this.decryptList(t.applicationId)}),2e3);else{if(2===t.status)return this.textStep=3,this.downloadFile=t.downloadLink,this.reqStates.list=!1,this.reqStates.step=!1,void setTimeout((()=>{this.reqStates.decryption=!0}),500);this.time=setTimeout((()=>{this.reqStates.step&&this.decryptList(t.applicationId)}),3e3),this.textStep=2}this.reqStates.list=!1,this.reqStates.step=!0},restState(t){this.fileList=[],this.selectValue="",this.fetchDate.detail="",this.fileSize=0,this.fetchDate.purpose="",this.$emit("restState"),"time"===t&&clearTimeout(this.time),"open"===t&&(this.reqStates.list=!1,this.$emit("selStateChange"))},async updateAction(){const t={allUser:this.selectall,detail:this.fetchDate.detail,files:this.fileList.map((t=>t.fileOssUrl)).toString(),mchNo:this.fetchDate.mchNo,purpose:Number(this.fetchDate.purpose),typeId:1,zkmeAccount:""===this.type||"0"===this.type?this.keyword:"",boundWallet:"1"===this.type?this.keyword:"",zisId:"2"===this.type?this.keyword:"",blockchain:"3"===this.type?this.keyword:"",userId:"4"===this.type?this.keyword:"",citizenship:"5"===this.type?this.keyword:"",programName:"6"===this.type?this.keyword:"",startTime:this.selectall?this.$options.filters.timestampDate(this.startTime):"",endTime:this.selectall?this.$options.filters.timestampDate(this.endTime):"",blockchainId:this.$route.query.blockchainId||"",SelectApplyDecryptBoList:Object.values(this.selectAccount).flat().map((t=>({programId:t.kycProgramId,zkmeId:t.zkmeId})))};if(this.fetchapplyDecrypt)return;this.fetchapplyDecrypt=!0;const e=await this.$api.request("decrypt.applyDecrypt",t);this.fetchapplyDecrypt=!1,8e7===e.code&&(this.reqStates.form=!1,this.reqStates.step=!0,this.$emit("closeSelect"),this.fileSize="",this.selectValue="",this.fetchDate.detail="",this.fileList=[],setTimeout((()=>{this.decryptList(e.data.applicationId)}),1e3))},async decryptList(t){const e=await this.$api.request("decrypt.queryDecrypt",this.fetchDate.mchNo);80001e3!==e.code?8e7===e.code&&(this.minWidthStyle=e.data.some((t=>!(0,l.v3)(t.expireDateTime)&&2===t.status)),this.tableList=e.data,t&&this.tableView(this.tableList.find((e=>e.applicationId===t)))):this.reqStates.step=!1}}},o=r,c=s(81656),d=(0,c.A)(o,i,a,!1,null,"4a228fae",null),n=d.exports},26169:function(t){t.exports="data:image/png;base64,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"}}]);