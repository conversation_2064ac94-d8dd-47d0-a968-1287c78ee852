/**
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/kyc/getKycPropertyListUsingPOST
 */
export const getKycPropertyList = ({ category }) => {
  return {
    method: 'post',
    url: '/kyc/getKycPropertyList',
    data: {
      category
    }
  }
}

/**
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/kyc/createKycProgramUsingPOST
 */
export const createKycProgram = ({ ids, programName, status, userId, countryBoList, category, locationIds, integer, walletAddressTon, walletAddressEvm, walletAddressSei, walletAddressNeutron, walletAddressAptos }) => {
  return {
    method: 'post',
    url: '/kyc/createKycProgram',
    data: {
      ids,
      countryBoList,
      programName,
      status,
      userId,
      category,
      locationIds,
      integer,
      walletAddressEvm,
      walletAddressSei,
      walletAddressNeutron,
      walletAddressAptos,
      walletAddressTon
    }
  }
}

/**
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/AdminKycLevel/getAllListUsingPOST
 */
export const getAllList = () => {
  return {
    method: 'post',
    url: '/kycLevel/getAllList'
  }
}

/**
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/kyc/queryUserKycListUsingPOST
 */
export const queryUserKycList = ({ chainId, auditedUser, filterHighestLevel, hideUnapplied, highestLevel, items, pageReq, pinCurrentApply, programName, time }) => {
  return {
    method: 'post',
    url: '/kyc/queryUserKycList',
    data: {
      chainId,
      auditedUser,
      filterHighestLevel,
      hideUnapplied,
      highestLevel,
      items,
      pageReq,
      pinCurrentApply,
      programName,
      time
    }
  }
}

/**
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/kyc/updateStatusUsingPOST
 */
export const updateStatus = ({ id, status }) => {
  return {
    method: 'post',
    url: '/kyc/updateStatus',
    data: {
      id,
      status
    }
  }
}

/**
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/kyc/updateStatusUsingPOST_1
 */
export const queryKycInfo = ({ id, category }) => {
  return {
    method: 'post',
    url: '/kyc/queryKycInfo',
    data: {
      id,
      category
    }
  }
}

/**
 * https://devzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/kyc/updateKycProgramUsingPOST
 */
export const updateKycProgram = ({ id, ids, countryBoList, programName, status, userId, category, locationIds }) => {
  return {
    method: 'post',
    url: '/kyc/updateKycProgram',
    data: {
      id,
      ids,
      countryBoList,
      programName,
      status,
      userId,
      category,
      locationIds
    }
  }
}

/**
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/kyc/getCountryListUsingPOST
 */
export const getCountryList = () => {
  return {
    method: 'post',
    url: '/kyc/getCountryList'
  }
}

export const querySupportChainByProgramId = ({ kycProgramIds }) => {
  return {
    method: 'post',
    url: '/kyc/querySupportChainByProgramId',
    data: {
      kycProgramIds
    }
  }
}
