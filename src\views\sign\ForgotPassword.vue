<template>
  <div class="pg-login mui-fl-central">
    <!-- <div class="toplogo mui-fl-vert">
      <div class="logo" />
      <div class="top-logo-name mui-fl-vert">Business</div>
    </div> -->
    <div class="toplogo mui-fl-vert">
      <i class="mcico-colorLogo"></i>
      <div class="logo" />
      <div class="top-logo-name mui-fl-vert">Business</div>
    </div>
    <!-- <div class="login-right"> -->
      <div class="login-left mui-fl-central">
        <!-- <div class="mui-flex"> -->
          <!-- <div class="login-left-bg">
            <div class="login-text">{{step === 'step1' || step === 'step5' ? 'Start building with zkMe' : "Let's become partners"}}</div>
            <img src="@/assets/img/login-bg4.png" alt="">
          </div> -->
          <div class="c mui-fl-col">
            <!-- <div class="logo" /> -->
            <p class="t1" v-if="Object.keys(this.$route.query).length && this.step === 'step1'">Reset your password</p>
            <p class="t1" v-else-if="step === 'step1'">Forgot password</p>
            <p class="t1" v-else-if="step === 'step2' || step === 'step5'">Reset your password</p>
            <p class="t1" v-else-if="step === 'step4'">Set your password</p>
            <p class="t1" v-else>Sign up with email</p>
            <m-form :model="form" ref="form" :rules="rules" class="sty1-form sty4-form cen-align-err mui-fl-1 mui-fl-col mui-fl-btw" :hide-required-asterisk="true"
              @submit.native.prevent>
              <div>
                <m-form-item prop="email" label="Email" v-if="!forgot && step === 'step1' || step === 'step3'">
                  <m-input class="pdl35" v-model="form.email" clearable placeholder="Enter your email" :disabled="60 > count ? true : false" @input="listPassWord('email')"
                    @keyup.enter.native="onSubmit"></m-input>
                </m-form-item>

                <m-form-item prop="currentassword" label="Current password" v-if="!forgot && (step === 'step2' || step === 'step5')">
                  <m-input :class="['passwords', eyesflag.currentassword ? 'closeeye' : 'openeye']" ref="currentassword"
                    @focus="fgpasswordasd('currentassword')" show-password @submit.native.prevent @keyup.enter.native="onSubmit" @input="listPassWord('currentassword')"
                    v-model="form.currentassword" clearable placeholder="Enter your password" type="password">
                  </m-input>
                </m-form-item>
                <m-form-item prop="password" label="Password" v-if="forgot || step === 'step4'" class="errorword mgb-24">
                  <m-input :class="['passwords', eyesflag.password ? 'closeeye' : 'openeye']" ref="password"
                    @focus="fgpasswordasd('password')" show-password @keyup.enter.native="onSubmit" v-model="form.password" clearable @input="listPassWord('password')"
                    placeholder="Enter your password" type="password">
                  </m-input>
                </m-form-item>
                <m-form-item prop="confirmPassword" label="Confirm Password" v-if="forgot || step === 'step4'">
                  <m-input :class="['passwords', eyesflag.confirmPassword ? 'closeeye' : 'openeye']" ref="confirmPassword"
                    @focus="fgpasswordasd('confirmPassword')" show-password @keyup.enter.native="onSubmit" v-model="form.confirmPassword" @input="listPassWord('confirmPassword')"
                    clearable placeholder="Enter your password" type="password">
                  </m-input>

                </m-form-item>
              </div>
              <m-form-item class="mgb-0">
                <div class="mui-fl-central timeClear" v-if="60 > count">
                  <i class="mico-clock2"></i> You can resend after {{ count }} seconds...
                </div>
                <m-button v-if="!forgot && step === 'step1' || step === 'step3'" type="primary" @click="onSubmit"
                  :disabled="60 > count || !form.email ? true : false">
                  <span v-if="step === 'step3'">
                    {{ 60 > count ? 'Verify link sent' : clockOver ? 'Resend' : 'Verify your email' }}
                  </span>
                  <span v-else>
                    {{ 60 > count ? 'Verify link sent' : clockOver ? 'Resend' : 'Send a verify link' }}
                  </span>
                </m-button>
                <m-button
                  v-if="forgot || step === 'step2' || step === 'step4' || step === 'step5'"
                  type="primary"
                  @click="onSubmit"
                  :disabled="
                    (!forgot && !form.currentassword) ||
                    (forgot && (!form.password || !form.confirmPassword))"
                >
                  Confirm
                </m-button>
              </m-form-item>
            </m-form>
          </div>
          <SignRightComponent></SignRightComponent>
        <!-- </div> -->
      </div>
    <!-- </div> -->
  </div>
</template>

<script>
import SignRightComponent from '@/components/sign/SignRightComponent.vue'
import { encrypt } from '@/utils/filters'
import vue from 'vue'

export default {
  name: 'ForgotPassword',
  components: {
    SignRightComponent
  },
  data () {
    return {
      revise: false,
      passwordCheck: false,
      count: 60,
      typepassword: true,
      forgot: false,
      eyesflag: {
        currentassword: false,
        password: false,
        confirmPassword: false
      },
      setInterval: '',
      password: '',
      qy: '',
      clickMessage: true,
      // verifyLink: false,
      requestCode: '',
      pwCheck: false,
      clockOver: false,
      form: {
        currentassword: '',
        email: '',
        password: '',
        confirmPassword: ''
      },
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*()_+={};':"|,.<>?])(?!.*[\u4E00-\u9FA5]).+$/,
      chineseValidator: /[\u4e00-\u9fa5\u3000-\u303F-\\，。、；’【】、（）·￥“”：《》？——！……\\]/g,
      formValidator32: /([\s\S]){33}/
    }
  },
  computed: {
    step () {
      let step = ''
      switch (this.$route.path) {
        case '/forgot-password/step1':
          step = 'step1'
          break
        case '/forgot-password/step2':
          step = 'step2'
          break
        case '/set-password/step1':
          step = 'step3'
          break
        case '/set-password/step2':
          step = 'step4'
          break
        case '/reset-password':
          step = 'step5'
          break
      }
      return step
    },
    rules () {
      return {
        email: [
          {
            required: true,
            type: 'email',
            message: 'Please enter the correct email.',
            trigger: 'blur'
          }
        ],
        currentassword: [
          // { type: 'string', pattern: /([\s\S]){8}/, message: 'Wrong password. Try again', trigger: 'blur' },
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value) {
                callback()
              }
            }
          },
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value) {
                if (value.length > 33) {
                  this.form.currentassword = value.slice(0, 33)
                }
                callback()
              } else {
                callback(new Error('Wrong password. Try again.'))
              }
            }
          }
        ],
        password: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callbak) => {
              if (!this.pattern.test(value) && value) {
                this.pwCheck = false
                callbak('The password should contain at least one uppercase letter, one lowercase letter, one number and special character.')
              } else if (value.length < 8 && value) {
                this.pwCheck = false
                callbak('The password must be longer than 8 characters.')
              } else {
                this.passwordCheck = false
                this.pwCheck = true
                callbak()
              }
            }
          },
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callbak) => {
              if (value) {
                this.passwordCheck = false
                callbak()
              }
            }
          }
        ],
        confirmPassword: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callbak) => {
              if (this.form.password !== value && value) {
                callbak('The passwords entered do not match.')
              } else if (this.formValidator32.test(value)) {
                callbak('The password can not be longer than 32 characters.')
              } else if (this.requestCode === 'error') {
                callbak(new Error('The new password cannot be the same as the original password.'))
              } else {
                callbak()
              }
            }
          },
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (value) {
                this.requestCode = ''
                this.passwordCheck = false
                callback()
              }
            }
          }
        ]
      }
    }
  },
  watch: {
    count (val) {
      if (!val) {
        clearTimeout(this.setInterval)
        this.count = 60
        this.clickMessage = true
        this.clockOver = true
      }
    },
    'form.email' (newVal, oldVal) {
      this.$nextTick(() => {
        this.$refs.form.clearValidate('email')
        this.rules.email = [{
          required: true,
          type: 'email',
          message: 'Please enter the correct email.',
          trigger: 'blur'
        }]
      })
    }
  },
  async beforeRouteEnter (to, from, next) {
    let step
    switch (to.path) {
      case '/forgot-password/step1':
        step = 'step1'
        break
      case '/set-password/step2':
        step = 'step2'
        break
    }
    if (step && to.query.validationCode) {
      const rp = await vue.prototype.$api.request(`auth.${step === 'step2' ? 'verifyLink' : 'pwdVerifyLink'}`, to.query.validationCode, {}, {}, false)

      if (rp.code !== 80000000) {
        next({
          name: 'ExpiredLink'
        })
        // this.$router.replace('/expired-link')
        // this.verifyLink = true
      } else {
        next()
      }
    } else {
      next()
    }
  },
  async created () {
    if (this.step === 'step3' && !this.$store.state.auth.setUserif) {
      this.$router.replace('/signup')
    }
    if (Object.keys(this.$route.query).length && (this.step === 'step1' || this.step === 'step4')) {
      this.forgot = true
      this.qy = this.$route.query.validationCode
    } else {
      this.qy = ''
    }
  },
  destroyed () {
    clearInterval(this.setInterval)
  },
  methods: {
    listPassWord (target) {
      this.form[target] = this.form[target].replace(this.chineseValidator, '')
    },
    fgpasswordasd (target) {
      this.eyesflag[target] = this.$refs[target].$el.children[0]?.type === 'text'
    },
    onSubmit () {
      if (this.count !== 60) {
        return
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.clickMessage) {
            this.clickMessage = false
            this.message()
          }
        }
      })
    },
    async message () {
      if (!this.forgot && this.step !== 'step4') {
        let rp = ''
        switch (this.step) {
          case 'step1':
            rp = await this.$api.request('auth.email', this.form.email, {}, {}, false)
            break
          case 'step5':
            rp = await this.$api.request('auth.verifyPwd', encrypt({ originalPwd: btoa(this.form.currentassword) }), {}, {}, false)
            break
          case 'step3':
            rp = await this.$api.request('auth.sendVerifyEmail', Object.assign({ email: this.form.email }, this.$store.state.auth.setUserif), {}, {}, false)
            break
        }
        this.clickMessage = true
        if (rp?.code === 80000000 || (this.step === 'step1' && rp.code === 80000004)) {
          if (this.step === 'step5') {
            this.forgot = true
            this.password = this.form.currentassword
          }
          if (this.step === 'step1' || this.step === 'step3') {
            this.count = this.count - 1
            this.setInterval = setInterval(() => {
              this.count = this.count - 1
            }, 1000)
          }
        } else {
          if (this.step === 'step1' && (rp.code === 80000014 || rp.code === 80000012)) {
            this.emailFieldErrorHandler(rp?.code)
          // } else if (this.step === 'step1' && rp.code === 80000004) {
          //   this.$message({
          //     duration: 3000,
          //     offset: 32,
          //     center: true,
          //     customClass: 'sty1-message sty3-message white',
          //     iconClass: 'mico-lightTip',
          //     message: 'Email not found. Please double-check your entry and try again.'
          //   })
          //   return
          } else if (this.step === 'step2' || this.step === 'step4') {
            this.emailFieldErrorHandler(rp?.code)
            this.messages('The new password cannot be the same as the original password.')
          } else if (this.step === 'step3' && rp.code === 80000011) {
            this.messages('An error occurred while sending the email. Please try again later.')
          }
          this.form.currentassword = ''
          this.emailFieldErrorHandler(rp?.code)
        }
      } else {
        if (this.form.password !== this.form.confirmPassword) {
          this.passwordCheck = true
          this.onSubmit()
          return
        }
        const form = {
          originalPwd: btoa(this.password),
          confirmPwd: btoa(this.form.confirmPassword),
          validateCode: this.qy
        }
        if (this.step === 'step4') {
          const form = {
            pwd: btoa(this.form.confirmPassword),
            validationCode: this.$route.query.validationCode
          }
          const rp = await this.$api.request('auth.register', encrypt(form), {}, {}, false)
          this.clickMessage = true
          if (rp.code === 80000000) {
            this.successMessages('Sign up success! Please login.')
            this.revise = true
            this.$router.replace('/login')
          } else if (rp.code === 80000013 || rp.code === 80000012) {
            this.messages('You have successfully set your password. Please do not perform frequent operations.')
            this.$router.replace('/login')
          }
          return
        }
        const rp = await this.$api.request(!this.qy ? 'auth.modifyPwd' : 'auth.reset', encrypt(form), {}, {}, false)
        this.clickMessage = true
        if (rp.code === 80000000) {
          this.successMessages('You have successfully reset your password!')
          this.revise = true
          this.$router.replace('/login')
        } else if (rp.code === 80000013 && this.qy) {
          this.messages('You have successfully reset your password. Please do not perform frequent operations.')
          this.$router.replace('/login')
        } else if (rp.code === 80102010) {
          this.requestCode = 'error'
          this.$refs.form.validateField('confirmPassword')
        }
      }
    },
    emailFieldErrorHandler (errorCode) {
      this.rules.email.push({
        validator: (rule, value, callback) => {
          if (errorCode === 80000012) {
            callback(new Error('The email address has already been registered.'))
          } else if (errorCode === 80000014) {
            callback(new Error('Too frequent operations. Please try again after 60 seconds.'))
          } else if (errorCode === 80000011) {
            callback()
          }
        }
      })

      this.$refs.form.validateField('email')
      if (errorCode === 80000014) {
        this.setAutoClearError('email', 60000)
      }
    },
    setAutoClearError (fieldName, timeout) {
      setTimeout(() => {
        // 清除特定字段的验证结果
        this.$refs.form.clearValidate(fieldName)
        this.rules.email = [
          {
            required: true,
            type: 'email',
            message: 'Please enter the correct email.',
            trigger: 'blur'
          }
        ]
      }, timeout)
    },
    messages (msg) {
      this.$message({
        duration: 3000,
        offset: 32,
        center: true,
        customClass: 'sty1-message sty3-message',
        iconClass: 'mico-lightTip',
        message: msg
      })
    },
    successMessages (msg) {
      this.$message({
        duration: 3000,
        offset: 32,
        center: true,
        customClass: 'sty1-message sty2-message',
        iconClass: 'mcico-success',
        message: msg
      })
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/sign/_login.scss" scoped></style>
