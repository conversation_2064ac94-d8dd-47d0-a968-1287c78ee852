
export const authorizedUserCount = ({ startTime, endTime }) => {
  return {
    method: 'post',
    url: '/zkadmin/statistics/authorizedUserCount',
    data: {
      startTime,
      endTime
    }
  }
}
export const userEventCount = ({ startTime, endTime }) => {
  return {
    method: 'post',
    url: '/zkadmin/statistics/userEventCount',
    data: {
      startTime,
      endTime
    }
  }
}
export const failReasonCount = ({ startTime, endTime }) => {
  return {
    method: 'post',
    url: '/zkadmin/statistics/failReasonCount',
    data: {
      startTime,
      endTime
    }
  }
}
