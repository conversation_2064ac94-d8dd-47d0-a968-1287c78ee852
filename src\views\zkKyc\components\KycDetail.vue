<template>
  <ul v-if="list.length" class="progress">
    <div v-for="(item, index) of list" :key="index">
      <li v-if="item.exhibit">
        <p class="t1 title" v-if="item.id <= 4 && (kycMode <= 2)">
          {{ index + 1 }}. {{ item.title }}
        </p>
        <p class="t1 title" v-if="item.id === 5 && (kycMode >= 2)">
          {{ kycMode === 3 ? '1' : list.filter(x => x.exhibit).length }}. {{ item.title }}
        </p>

        <div class="box" v-if="item.id === 1 && (kycMode <= 2)">
          <ul v-for="(network, nIndex) of item.adminKycPropertyBoList" :key="nIndex" class="mui-fl-vert box-tr network"
            :class="{ 'border-top': nIndex !== 0 && network.value.length }" >
            <template v-if="network.value.length">
              <li class="t1 mui-shr-0">
                {{ network.kycVerifyProperty }}
              </li>
              <li class="border-left">
                <ul class="mui-fl-vert mui-fl-wrap">
                  <li v-for="(chain, cIndex) of network.value" :key="chain.id" class="mui-fl-vert network-item"
                    :class="{ 'no-last-item': network.value.length !== (cIndex + 1) }">
                    <img :src="chain.symbol" alt="">
                    <p class="t1">{{ chain.value }}</p>
                  </li>
                </ul>
              </li>
            </template>
          </ul>
        </div>

        <div class="box" v-else-if="(item.id === 2 && kycMode <= 2) || (item.id === 5 && kycMode >= 2)">
          <div v-for="(requirement, rIndex) of item.adminKycPropertyBoList" :key="rIndex">
            <ul class="mui-fl-vert box-tr" v-if="requirement.kycVerifyProperty === 'Age'">
              <li class="t1 mui-shr-0">
                {{ requirement.kycVerifyProperty }}
              </li>
              <li class="t1 border-left">
                Over {{ getAge(item.adminKycPropertyBoList[0].value) }}
              </li>
            </ul>

            <template v-else-if="requirement.status">
              <ul class="mui-fl-vert mui-fl-btw box-tr citizen-search border-top">
                <li class="t1 mui-shr-0">
                  {{ requirement.kycVerifyProperty }}
                </li>

                <li class="search-content">
                  <m-input v-model="keyword[item.id]" placeholder="Search" prefix-icon="mico-search"
                    class="sty1-input-search sty2-input-search"
                    @keyup.native.enter="handleClkSearch(keyword[item.id], item.id)"
                    @input="handleClkSearch(keyword[item.id], item.id)"
                    @focus="clearSearch(item.id)"
                    clearable>
                  </m-input>

                  <template v-if="(item.id === 2 && keyword[2]) || ((item.id === 5 && kycMode >= 2) && keyword[5])">
                    <ul class="search-list" v-if="searchOptions.length">
                      <li v-for="option of searchOptions" :key="option.id" class="mui-fl-vert"
                        @click="selectOption(option, item.id)">
                        <div class="img nation-sprites mui-shr-0"
                          :style="{ backgroundPosition: `${(-5 - 34 * (option.ids - 1))}px -5px` }" />
                        <p :title="option.regularName">{{ option.regularName }}</p>
                        <i :class="[option.isSelect ? 'mcico-success2' : 'mcico-SelectNot']" />
                      </li>
                    </ul>

                    <div v-else class="mui-fl-central mui-fl-col search-list no-list">
                      <img src="@/assets/img/no-table-data.png" alt="">
                      <div class="no-txt">No Results</div>
                    </div>
                  </template>
                </li>
              </ul>
              <ul class="mui-flex citizen-box border-top">
                <li v-for="(coun, index) of item.id === 5 ? GeolocationList : countryList" :key="coun.id">
                  <div class="available mui-fl-vert">
                    <i class="mcico-success-green"></i>
                    Available countries/regions {{item.id === 5 ? geoCountriesLength : selectCountries}}/{{ countriesLength }}
                  </div>
                  <ul :ref="`ndScrollRef${index + 1}${item.id}`" class="nations" v-if="coun.countries?.length">
                    <li v-for="item1 of coun.countries" :key="item1.letter" class="nation-item">
                      <p class="t1">{{ item1.letter }}</p>
                      <ul>
                        <li v-for="item2 of item1.childrens" :key="item2.value" class="mui-fl-btw mui-fl-central"
                          :ref="`ndLetterRef${index + 1}${item.id}`">
                          <div>
                            <i v-if="item2.isSelect" class="mcico-success2"></i>
                            <i v-else class="mcico-SelectNot"></i>
                          </div>
                          <div class="mui-fl-vert" style="width: 100%;">
                            <div class="img nation-sprites mui-shr-0"
                            :style="{ backgroundPosition: `${(-5 - 34 * (item2.ids - 1))}px -5px` }" />
                            <p :title="item2.regularName">{{ item2.regularName }}</p>
                          </div>
                          <div class="mui-fl-central" v-if="item.id !== 5 && kycMode !== 3">
                            <m-button
                              v-if="item2.selectDocuments?.isSelect4 || (!item2.selectDocuments && item2.supportDocumentList?.includes(4))"
                              class="mui-fl-central network-tag tag-checked tag-checked-hover"
                            >
                              <span>Driver License</span>
                            </m-button>
                            <m-button
                              v-if="item2.selectDocuments?.isSelect2 || (!item2.selectDocuments && item2.supportDocumentList?.includes(2))"
                              class="mui-fl-central network-tag tag-checked tag-checked-hover"
                            >
                              <span>Passport</span>
                            </m-button>
                            <m-button
                              v-if="item2.selectDocuments?.isSelect1 || (!item2.selectDocuments && item2.supportDocumentList?.includes(1))"
                              class="mui-fl-central network-tag tag-checked tag-checked-hover"
                            >
                              <span>ID Card</span>
                            </m-button>
                          </div>
                        </li>
                      </ul>
                    </li>
                  </ul>
                </li>
              </ul>
            </template>
          </div>
        </div>

        <ul v-else-if="(item.id === 3 && kycMode <= 2)" class="box aml-ul">
          <div v-for="aml of item.adminKycPropertyBoList" :key="aml.id">
            <li v-if="aml.status" class="t1">
              {{ aml.kycVerifyProperty }}
            </li>
          </div>
        </ul>
        <ul v-else-if="(item.id === 4 && kycMode <= 2)" class="box aml-ul">
          <div v-for="aml of item.adminKycPropertyBoList" :key="aml.id">
            <li v-if="aml.status" class="t1">
              {{ aml.kycVerifyProperty }}
            </li>
          </div>
        </ul>
      </li>
    </div>
  </ul>
</template>
<script>
export default {
  props: {
    kycInfo: {
      type: Number,
      required: 1
    },
    list: {
      type: Array,
      required: true
    },
    selectCountry: {
      type: Object,
      required: true
    },
    selectGeolocation: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      keyword: {
        2: '',
        5: ''
      },
      searchOptions: [],
      loading: false,
      isSelect1: true,
      inputid: ''
    }
  },
  computed: {
    kycLevel () {
      return (localStorage.getItem('zkmeAdminUser') && JSON.parse(localStorage.getItem('zkmeAdminUser')).level) || this.$store.state.auth.user.level
    },
    kycMode () {
      return this.$route.query.mode ? Number(this.$route.query.mode) : this.kycInfo
    },
    countryList () {
      return [
        {
          id: 1,
          title: 'Available',
          // icon: 'mcico-success-green',
          countries: this.selectCountry.avaliableList,
          searchCountries: []
        }
      ]
    },
    GeolocationList () {
      return [
        {
          id: 1,
          title: 'Available',
          // icon: 'mcico-success-green',
          countries: this.selectGeolocation.avaliableList,
          searchCountries: []
        }
      ]
    },
    allCountries () {
      if (this.kycMode >= 2 && !this.selectGeolocation.avaliableList.length && !this.selectGeolocation.unavailableList.length) {
        return []
      }
      if (this.kycMode <= 2 && !this.selectCountry.avaliableList.length && !this.selectCountry.unavailableList.length) {
        return []
      }
      let result = []
      let country
      if (!this.kycMode || this.kycMode === 3 || (this.kycMode === 2 && this.inputid === 5)) {
        country = this.selectGeolocation
      } else {
        country = this.selectCountry
      }
      country.avaliableList.length && country.avaliableList.forEach(element => {
        if (element.childrens.length) {
          result = result.concat(element.childrens)
        }
      })
      country.unavailableList.length && country.unavailableList.forEach(element => {
        if (element.childrens.length) {
          result = result.concat(element.childrens)
        }
      })
      result = this.sortList(result, 'regularName')
      return result
    },
    selectCountries () {
      return this.$store.state.zkKyc.selectCountries || 0
    },
    countriesLength () {
      return this.$store.state.zkKyc.countriesLength || 248
    },
    geoCountriesLength () {
      return this.$store.state.zkKyc.geoCountriesLength || 0
    }
  },
  methods: {
    handleClkSearch (val, id) {
      this.inputid = id
      this.searchOptions = this.allCountries.filter(item => {
        return item.regularName.toLowerCase()
          .indexOf(val.toLowerCase()) > -1
      })
    },
    clearSearch () {
      this.keyword = {
        2: '',
        5: ''
      }
    },

    selectOption (item, id) {
      this.clearSearch()
      const ndLetterRef = this.$refs[`ndLetterRef1${id}`]
      const ndScrollRef = this.$refs[`ndScrollRef1${id}`][0]
      const findIndex = ndLetterRef.findIndex((i) => i.textContent.includes(item.regularName))
      if (findIndex !== -1) {
        this.scrollTo(ndScrollRef, findIndex ? ndLetterRef[item.ids === 208 ? 121 : item.ids - 1].offsetTop : 0)
      }
    },

    // 列表排序
    sortList (list, txt) {
      return list.sort((a, b) => {
        if (a[txt] < b[txt]) {
          return -1
        } else {
          return 1
        }
      })
    },

    getAge (list) {
      return list.find(i => i.isSelect)?.value || ''
    },

    /**
     * 缓动函数
     * @param {number} t current time（当前时间）
     * @param {number} b beginning value（初始值）
     * @param {number} c change in value（变化量）
     * @param {number} d duration（持续时间）
     */
    linear (t, b, c, d) {
      return c * t / d + b
    },

    /**
     * 滚动到指定位置
     * @param current 当前位置
     * @param target 目标位置
     * @param duration 持续时间
     */
    scrollTo (ndScrollRef, target, duration = 14) {
      const current = ndScrollRef.scrollTop
      let top = current
      let start = 0
      const change = target - current

      return new Promise(resolve => {
        const step = () => {
          start++
          top = this.linear(start, current, change, duration)
          ndScrollRef.scrollTop = top
          if (((current < target && top < target) || (current > target && top > target)) && start <= duration) {
            requestAnimationFrame(step)
          } else {
            resolve()
          }
        }
        step()
      })
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/_kyc_detail.scss" scoped></style>
