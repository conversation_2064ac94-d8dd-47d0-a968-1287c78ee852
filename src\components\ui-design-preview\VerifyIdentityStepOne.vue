<template>
    <div class="mui-fl-col mui-fl-vert mui-fl-btw mui-fl-1">
        <div class="mui-fl-col mui-fl-vert">
            <p class="sty1-title">Verify your Identity</p>
            <img class="hero kyc" src="~@/assets/img/popup/zkme.png" alt="KYC Image">
            <p class="t2">Proof-of-Citizenship</p>
            <p class="t1 mt8">
                To establish your identity on web3 ecosystems and fulfill compliance requirements to use dApps
                services, your eligibility needs to be verified and preserved based on proof of citizenship. An
                Identity Soulbound Token will be generated and associated with your asset wallet.
            </p>
            <p class="t3">The verification process is designed to ensure your anonymity, no private data is
                shared with anyone.</p>
        </div>

        <div class="bottom-act">
            <ul class="dot mui-fl-central">
                <li v-for="(i, index) of 2" :key="index" :class="{ 'active': 1 === i }"></li>
            </ul>
            <m-button type="primary" size="large" class="width-2 taplight" round>Continue</m-button>
        </div>
    </div>
</template>
<script>
export default {
  name: 'VerifyIdentityStepOne'
}
</script>
<style lang="scss" src="@/assets/css/components/ui-design-preview/_home-view.scss" scoped></style>
