// select弹出选择项的时候，防止页面出现抖动（出现抖动哦~）
.operate-bar {
  overflow: hidden;
}

// 溢出显示省略号
.txt-ovfl {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding-right: 10px;
  &.nopadd {
    padding-right: 0;
  }
}
.txt-ovfl-row {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  padding-right: 10px;
  &.nopadd {
    padding-right: 0;
  }
  &.row2 {
    -webkit-line-clamp: 2;
  }
  &.row6 {
    -webkit-line-clamp: 6;
  }
}

// 自定义滚动条
::-webkit-scrollbar {
  // display: none;
  width: 8px;
  height: 8px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  background-color: #B3C0C2;
  background-clip: content-box;
  border: 2px solid transparent;
  border-radius: 4px;
}
::-webkit-scrollbar-corner {
  display: none;
}
// 自定义滚动条
textarea {
  &::-webkit-scrollbar {
    display: none;
  }
}

/* 按钮点击效果
-------------------------- */
.taplight, .taplight-bf {
  position:relative;
  cursor: pointer;
  user-select: none;
}
.taplight:not(.invalid)::after,
.taplight-bf:not(.invalid)::before {
  content:"";
  position:absolute;
  top:50%;
  left:50%;
  width:100%;
  height:100%;
  border: inherit;
  border-radius: inherit;
  border-color: #000;
  transform: translate(-50%,-50%);
  pointer-events:none;
  transition:opacity .4s ease;
  background: #000;
  opacity: 0;
}

.taplight.invalid,
.taplight-bf.invalid {
  cursor: default;
}
.taplight:active::after,
.taplight-bf:active::before {
  opacity: 0.13;
  transition-duration:0s;
}
.taplight2 {
  transition:opacity .4s ease .05s;
  user-select: none;
  cursor: pointer;
}
.taplight3 {
  user-select: none;
  cursor: pointer;
  .tap-chd {
    transition:opacity .4s ease .05s;
  }
}
.taplight2:active:not(.is-disabled),
.taplight3:active .tap-chd {
  opacity:.4;
  transition-duration:0s;
  transition-delay: 0s;
}

.Simplelist {
  .personal {
    // min-width: 160px;
    white-space:nowrap;
    margin-right: 20px;
    .l {
      width: auto;
      padding: 6px 0;
      line-height: 18px;
      font-weight: 500;
      // margin-bottom: 6px;
    }
  }
  .recorded_personal {
    // min-width: 115px;
  }
  .information>.r {
    padding: 6px 0;
    line-height: 18px;
  }
  .recorded_information {
    padding: 6px 0;
    .r {
      color: #002e33;
      line-height: 18px;
    }
  }
  .information .division {
    border-left: 1px solid #F0F0F0;
    margin: 0 8px;
  }
  .ce {
    margin: 6px 0;
  }
}
.dialog {
  .el-dialog {
    top:50%;
    left: 50%;
    margin: 0;
    margin-top: 0 !important;
    transform: translate(-50%,-50%);
    width: 610px;
    border-radius: 16px;
  }
  .el-dialog__title {
    font-size: 18px;
    line-height: 22px;
  }
  .el-dialog__header {
    padding: 24px;
  }
  .el-dialog__headerbtn {
    top: 24px;
    right: 24px;
    color: #738C8F;
    i {
      font-size: 20px;
    }
  }
  .el-dialog__body {
    padding: 0;
  }
}

.integration-dialog {
  .el-dialog {
    border-radius: 32px;
  }
  .el-dialog__header {
    padding: 0;
  }
  .el-dialog__body {
    padding: 0px 34px 40px;
  }
  .dbg {
    border-radius: 32px 32px 0 0;
    width: 100%;
    height: 108px;
    background: url('~@/assets/img/api-bg1.png');
    background-size: cover;
    padding-top: 32px;
    img {
      width: 72px;
      height: 72px;
    }
  }
  .dh {
    font-weight: 700;
    font-size: 22px;
    line-height: 32px;
    color: #000000;
  }
  .dk {
    padding: 16px;
    margin-right: 10px;
    background-color: rgba(169, 225, 211, 0.40);
    border: 1px solid #A9E1D3;
    border-radius: 12px;
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    color: #005563;
    margin: 24px 0 16px;
    .mico-copy {
      cursor: pointer;
      font-size: 16px;
      color: #292C33;
      padding-left: 10px;
    }
  }
  .dt {
    font-weight: 400;
    font-size: 16px;
    line-height: 22px;
    color: #738C8F;
    :first-child {
      margin-bottom: 16px;
    }
    .will {
      font-weight: 700;
      color: #EE6969;
    }
  }
  .db {
    margin-top: 40px;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    background-color: #005563;
    color: #FFFFFF;
    border-radius: 23PX;
    width: 280px;
  }
}
.loader {
  width: 16px;
  --b: 4px; 
  aspect-ratio: 1;
  border-radius: 50%;
  background: conic-gradient(#0000 10%,#005563) content-box;
  -webkit-mask:
    repeating-conic-gradient(#0000 0deg,#000 1deg 20deg,#0000 21deg 36deg),
    radial-gradient(farthest-side,#0000 calc(100% - var(--b) - 4px),#000 calc(100% - var(--b)));
  -webkit-mask-composite: destination-in;
          mask-composite: intersect;
  animation:l4 1s infinite steps(10);
}
.loader2 {
  background: conic-gradient(#0000 10%,#FFFFFF) content-box;
}
.loader3 {
  background: conic-gradient(#0000 10%,#88F0DA) content-box;
}
.loader4 {
  --b: 8px; 
  width: 36px;
}
@keyframes l4 {to{transform: rotate(1turn)}}