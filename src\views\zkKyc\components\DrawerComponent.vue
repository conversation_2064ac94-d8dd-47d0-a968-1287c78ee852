<template>
  <div>
    <m-drawer :modal="false" @close="close" :visible="drawerVisible" class="recorded_drawer">
      <div v-if="!detail.kycProgramId" class="txn-graph-loading-or-empty mui-fl-central" style="width: 630px; height: 100%;">
        <div class="mui-fl-col">
          <LoadingLottie :size="'40px'" />
          <p class="loading-txt">Loading...</p>
        </div>
      </div>
      <template #title>
        <div v-if="detail.kycProgramId" class="drawer-title">
          {{ detail.programName }}
          <i v-if="!isOldKycProgram && !type" class="mico-edit icon-edit" @click="toEdit" />
        </div>
      </template>

      <div v-if="detail.kycProgramId" class="mui-flex program drawer_margin">
        <span>Program No.&nbsp;</span>
        <span>{{ detail.kycProgramId }}</span>
        <div class="m" style="height: 14px"></div>
        <span v-if="!drawerCopy" class="l" @click="copy(detail.kycProgramId)">Copy</span>
        <span v-else class="l cop">Copied</span>
      </div>

      <kyc-detail v-if="detail.kycList" :list="detail.kycList" :select-country="selectCountry" ref="detail"
        :select-geolocation="selectGeolocation" :kycInfo="category" />

      <div v-if="Object.keys(wallet).length && detail.kycProgramId">
        <div class="con-text">Account address</div>
        <div class="igTexts mbs">This account is utilized for invoking the zkMe smart contract and storing authorization
        information from your users.</div>
        <div class="mb mui-flex edAddress">
          <div class="addressWl">
            <div class="addresscor mui-flex" v-for="(data, key) in wallet" :key="key">
              <div class="igTexts addressHand" v-if="key === 'evm'">EVM address</div>
              <div class="igTexts addressHand" v-if="key === 'sei'">Cosmos address (sei)</div>
              <div class="igTexts addressHand" v-if="key === 'neutron'">Cosmos address (neutron)</div>
              <div class="igTexts addressHand" v-if="key === 'aptos'">Aptos address</div>
              <div class="igTexts addressHand" v-if="key === 'ton'">Ton address</div>
              <div class="igTexts addresscor">
                {{ data.address }}
                <m-popover placement="top" width="64" trigger="manual" :close-delay="1000" :value="data.flag"
                  popper-class="NotRecord_popover WhiteList" content="Copied!">
                  <i slot="reference" class="mico-copy igcur" @click="copyAddress(data)"></i>
                </m-popover>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="detail.recordsList" class="steps-box">
        <div class="drawer_records drawer_margin" v-if="detail.recordsList?.length">Records</div>

        <div class="drawer_margin drawer_step">
          <!-- status: -1 delete, 1 apply, 2 create, 3 update, 4 expired -->
          <div v-for="(i, d) of detail.recordsList" :key="d">
            <div v-if="(filterRecordsList(1).length && i.status !== 3) ||
              i.status === -1 ||
              i.status === 2 ||
              i.status === 4 ||
              i.status === 5 ||
              (!filterRecordsList(1).length && i.status === 3)
              " :class="['mui-flex', 'mui-fl-vert', status[i.status === 3 ? 2 : i.status]?.toLowerCase()]">
              <div class="boolball" v-if="i.status !== 3 || (i.status === 3 && getModifyStatus(i))" />
              <span class="drawer_text_hand" v-if="(i.status === 3) && getModifyStatus(i)">
                {{ status[i.status] }}
              </span>
              <span class="drawer_text_hand" v-else-if="i.status !== 3">{{ status[i.status] }}</span>
              <span class="drawer_text" v-if="i.status !== 3 || getModifyStatus(i)">
                {{ (i.status === 1 ? 'from ' : 'at ') }}
                {{ i.status !== 3 ? i.createTime : modifyCreateTime }}
              </span>
            </div>

            <!-- textma // 样式 -->
            <div :class="['drawer_division', 'mui-fl-vert',
              i.createTime === modifyCreateTime && 'textma']" v-if="(
                ((detail.recordsList.length - 1) - d)
                && i.status !== 3
                && (i.status === 1 || i.status === 5 || i.status === 4)
                && !filterRecordsList(3).length)
                || i.createTime === modifyCreateTime">
              <span class="text" v-if="i.createTime === modifyCreateTime">
                <span @click="openModified = !openModified">
                  Modified {{ filterRecordsList(3).length }} times <i
                    :class="['mico-fold', openModified && 'mico-fold-rotate']" />
                </span>
                <div v-if="openModified" style="cursor: default;">
                  <div class="foldtext" v-for="(i, d) of filterRecordsList(3)" :key="d">
                    Modified at {{ i.createTime }}
                  </div>
                </div>
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="mui-fl-end footer-box" v-if="!type">
        <!-- status: -1 delete, 1 apply, 2 create, 3 update, 4 expired -->
        <template v-if="detail.status === 2 || detail.status === 3">
          <m-button class="sty3-button fs-14" @click="toDelete">Delete</m-button>
          <m-button class="sty4-button fs-14" v-if="!isOldKycProgram" @click="modifyProgram">Modify program</m-button>
          <m-button class="sty2-button fs-14" v-if="!isOldKycProgram" :loading="applyLoading" @click="applyProgram">Apply
            program</m-button>
        </template>

        <template v-if="detail.status === 1 || detail.status === 4 || detail.status === 5">
          <m-button class="sty4-button fs-14 width-2" @click="duplicate">Duplicate</m-button>
          <m-button class="sty3-button fs-14 width-1" v-if="detail.status === 1 || detail.status === 5" @click="cancelService">Cancel
            program</m-button>
        </template>
      </div>
    </m-drawer>

    <KycWarning :model="model" :warningtip="warningtip" @Leave="Leave" />

    <m-dialog :visible.sync="editNameVisible" class="sty2-dialog dialog" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <template>
        <div class="leavetip_title">
          Modify program name
        </div>
        <div class="program-name">Program Name</div>
        <div>
          <MuiInput :closeflg="true" @input="input" :error="dialogwarning" v-model="dialoginp" class="inp" />
        </div>
        <div class="dialog_warning" v-if="dialogwarning && dialoginp">Numbers or letters only, no more than 50 characters.
        </div>
        <div class="dialog_warning" v-else-if="dialogwarning && !dialoginp">Please enter your program name.</div>
      </template>

      <!-- <template v-else>
        <div class="leavetip_title">
          Apply a new program
        </div>
        <div class="dialog_title">Already have a program in application, confirm to cancel the service and apply the new
          program?</div>
      </template> -->

      <div class="dialog_button warin_button mui-fl-end">
        <m-button class="kycSave" @click="cancel">Cancel</m-button>
        <m-button class="kycCancel Apply" :loading="applyLoading" @click="confirm">Confirm</m-button>
      </div>
    </m-dialog>
  </div>
</template>
<script>
import KycWarning from '@/components/kyc_warning'
import KycDetail from './KycDetail.vue'
import * as types from '@/store/mutation-types'
import MuiInput from '@/components/mui-input/MuiInput.vue'
import { netWorkType } from '@/utils/config'
import LoadingLottie from '@/components/loading-lottie'
export default {
  components: { KycDetail, KycWarning, MuiInput, LoadingLottie },
  props: {
    drawerVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: Number,
      required: true
    },
    // category: {
    //   type: Number,
    //   required: true
    // },
    createTime: {
      type: Number,
      required: false
    },
    hasAppliedProgram: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      netWorkType: netWorkType,
      netWork: [],
      detail: {},
      initDetail: {},
      drawerCopy: false,
      openModified: false,
      warningtip: false,
      model: 'Recorded' || 'program',
      msgTxt: '',
      operationData: {},
      editNameVisible: false,
      dialogwarning: false,
      Flagaddress: false,
      FlagseiAddress: false,
      FlagneutronAddress: false,
      FlagaptosAddress: false,
      wallet: {},
      dialogType: '',
      dialoginp: '',
      category: 1,
      form: {
        countryIds: [],
        id: '',
        ids: '',
        programName: '',
        status: 2,
        userId: '',
        category: ''
      },
      applyLoading: false,
      status: {
        '-1': 'Expired',
        1: 'Applied',
        2: 'Created',
        3: 'Last modified',
        4: 'Expired',
        5: 'Pending'
      }
    }
  },
  computed: {
    // 是否为旧版本的kycProgram
    isOldKycProgram () {
      return this.createTime ? (this.createTime < new Date('2023-09-28 18:00:00').getTime()) : false
    },
    modifyCreateTime () {
      return this.detail?.recordsList.filter(x => x.status === 3)[0]?.createTime || ''
    },
    kycMode () {
      return this.$store.state.kyc.category
    },
    kycLevel () {
      return (localStorage.getItem('zkmeAdminUser') && JSON.parse(localStorage.getItem('zkmeAdminUser')).level) || this.$store.state.auth.user.level
    }
  },
  watch: {
    id () {
      if (this.id) {
        this.queryKycInfo()
      }
    }
  },
  mounted () {
    this.queryKycInfo()
  },
  methods: {
    moveAndModify (arr, targetIndex) {
      // 复制数组以避免修改原始数组
      arr = arr.map(x => {
        return {
          ...x,
          ids: x.id
        }
      })
      const newArr = [...arr]

      // 获取最后一个对象
      const lastObject = newArr.pop()

      // 将最后一个对象插入到指定位置
      newArr.splice(targetIndex, 0, lastObject)

      newArr[targetIndex].ids = targetIndex

      // 修改 ID 值
      for (let i = targetIndex; i < newArr.length; i++) {
        newArr[i].ids = i + 1
      }

      return newArr
    },
    async queryKycInfo () {
      if (!this.id) {
        return
      }

      try {
        const rp = await this.$api.request('kyc.queryKycInfo', { id: this.id, category: this.kycMode })
        if (rp.code === 80000000) {
          this.category = this.kycMode
          this.netWork = []
          const list = []
          let ids = []
          let exhibit = true
          if (rp.data.kycList) {
            rp.data.kycList.forEach(i => {
              let title = ''
              let adminKycPropertyBoList = i.adminKycPropertyBoList
              if (i.id === 1) {
                title = 'Network'
                adminKycPropertyBoList = i.adminKycPropertyBoList.map(r => {
                  const filter = r.value.filter(n => n.isSelect && n.value !== 'Ethereum Goerli Testnet')
                  ids = ids.concat(filter.map(a => a.id))
                  const ntw = r.value.filter(x => x.isSelect).map(m => netWorkType[m.value])
                  ntw.length && this.netWork.push(...ntw)
                  return { ...r, value: filter }
                })
                if (this.kycLevel === 2) {
                  exhibit = false
                }
              } else if (i.id === 2) {
                title = 'Passable requirements'
                const filter1 = adminKycPropertyBoList[0].value.filter(n => n.isSelect)
                const filter2 = adminKycPropertyBoList[1].value.filter(n => n.isSelect)
                ids = ids.concat(filter1.map(a => a.id))
                ids = ids.concat(filter2.map(a => a.id))
                adminKycPropertyBoList = adminKycPropertyBoList.map(r => {
                  return {
                    ...r,
                    disabled: r.status === 2 || r.status === 1,
                    status: true,
                    value: r.kycVerifyProperty === 'Age' ? r.value.map(a => { return { ...a, isSelect: a.isSelect ?? a.value === '18' } }) : r.value
                  }
                })
              } else if (i.id === 3) {
                title = 'AML Screening'
                adminKycPropertyBoList = adminKycPropertyBoList.map(r => {
                  r.value[0].isSelect && ids.push(r.value[0].id)
                  if (this.kycMode === 3) {
                    ids = []
                  }
                  return { ...r, disabled: r.status === 2 || r.status === 1, status: true, id: r.value[0].id }
                })
                exhibit = i.adminKycPropertyBoList.filter(x => x.value[0].isSelect).length
              } else if (i.id === 4) {
                title = 'Uniqueness check'
                adminKycPropertyBoList = adminKycPropertyBoList.map(r => {
                  r.value[0].isSelect && ids.push(r.value[0].id)
                  if (this.kycMode === 3) {
                    ids = []
                  }
                  return { ...r, disabled: r.status === 2 || r.status === 1, status: true, id: r.value[0].id }
                })
                exhibit = i.adminKycPropertyBoList.filter(x => x.value[0].isSelect).length
              } else if (i.id === 5) {
                const filter = i.adminKycPropertyBoList[0].value.filter(n => n.isSelect)
                ids = ids.concat(filter.map(a => a.id))
                title = 'Location Check'
                adminKycPropertyBoList = adminKycPropertyBoList.map(r => {
                  return {
                    ...r,
                    disabled: r.status === 2 || r.status === 1,
                    status: true,
                    value: r.value
                  }
                })
              }
              list.push({ title, adminKycPropertyBoList, status: i.status, id: i.id, exhibit: exhibit })
              exhibit = true
            })
          }
          this.getIntegrationList(rp.data)

          const avaliableList = []
          const unavailableList = []
          const goavaliableList = []
          const gounavailableList = []
          if (rp.data.countryInfoBoList) {
            rp.data.countryInfoBoList = this.moveAndModify(rp.data.countryInfoBoList, 118)
            rp.data.countryInfoBoList.forEach(i => {
              const letter = i.regularName.substring(0, 1)
              if (i.isSelect && !this.kycMode) {
                if (!avaliableList.find(r => r.letter === letter)) {
                  avaliableList.push({ letter, childrens: [] })
                }
                const find = avaliableList.find(r => r.letter === letter)
                const childrens = find.childrens
                if (i.regularName.startsWith(letter) && (!childrens.find(r => r.regularName === i.regularName) || !childrens.length)) {
                  childrens.push({ ...i })
                }
              } else {
                if (!unavailableList.find(r => r.letter === letter)) {
                  unavailableList.push({ letter, childrens: [] })
                }
                const find = unavailableList.find(r => r.letter === letter)
                const childrens = find.childrens
                if (i.regularName.startsWith(letter) && (!childrens.find(r => r.regularName === i.regularName) || !childrens.length)) {
                  childrens.push({ ...i })
                }
              }
            })
            this.$store.commit('SET_SELECTCOUNTRIES', rp.data.countryInfoBoList.filter(x => x.isSelect).length)
            this.$store.commit('SET_COUNTRIESLENGTH', rp.data.countryInfoBoList.length)
          }

          if (rp.data.locationInfoBoList) {
            rp.data.locationInfoBoList = this.moveAndModify(rp.data.locationInfoBoList, 118)
            rp.data.locationInfoBoList.forEach(i => {
              const letter = i.regularName.substring(0, 1)
              if (!goavaliableList.find(r => r.letter === letter)) {
                goavaliableList.push({ letter, childrens: [] })
              }
              const find = goavaliableList.find(r => r.letter === letter)
              const childrens = find.childrens
              if (i.regularName.startsWith(letter) && (!childrens.find(r => r.regularName === i.regularName) || !childrens.length)) {
                childrens.push({ ...i })
              }
            })
            this.$store.commit('SET_GEOSELECTCOUNTRIES', rp.data.locationInfoBoList.filter(x => x.isSelect).length)
          }

          const countryBoList = rp.data.countryInfoBoList?.map(r => {
            return {
              ...r,
              id: r.id,
              isSelect: r.isSelect,
              supportDocuments: r.supportDocumentList
            }
          })
          const locationIds = rp.data.locationInfoBoList?.filter(r => r.isSelect).map(r => r.id)
          this.form = {
            countryBoList,
            ids,
            status: rp.data.status,
            id: rp.data.id,
            userId: new Date().getFullYear(),
            category: this.$store.state.kyc.category,
            locationIds
          }

          // 兼容旧版没有配置国家时默认为unavailable的情况：要求如果avaliable列表为空则把unavailable的数据挪到avaliable列表
          const availableResult = avaliableList.length ? avaliableList : unavailableList
          const unavailableResult = avaliableList.length ? unavailableList : []
          const goavailableResult = goavaliableList.length ? goavaliableList : gounavailableList
          const gounavailableResult = goavaliableList.length ? gounavailableList : []
          this.selectCountry = { avaliableList: availableResult, unavailableList: unavailableResult }
          this.selectGeolocation = { avaliableList: goavailableResult, unavailableList: gounavailableResult }
          this.detail = { ...rp.data, kycList: list }
          this.initDetail = {
            ...rp.data,
            countryInfoBoList: this.isOldKycProgram && !rp.data.countryInfoBoList.find(r => r.isSelect)
              ? rp.data.countryInfoBoList.map(r => {
                return { ...r, isSelect: true }
              })
              : rp.data.countryInfoBoList
          }
        }
      } catch (error) {
        console.log('queryKycInfo error: ', error)
      }
    },

    async getIntegrationList (value) {
      this.wallet = {}
      const netWork = [...new Set(this.netWork)]
      const setNetWork = netWork.length ? [...new Set(this.netWork)] : ['EVM', 'Aptos', 'Neutron', 'SEI', 'TON']
      for (const f of setNetWork) {
        switch (f) {
          case 'EVM':
            if (!value.walletAddressEvm) continue
            this.wallet.evm = {
              address: value.walletAddressEvm,
              flag: false
            }
            continue
          case 'Aptos':
            if (!value.walletAddressAptos) continue
            this.wallet.aptos = {
              address: value.walletAddressAptos,
              flag: false
            }
            continue
          case 'Neutron':
            if (!value.walletAddressNeutron) continue
            this.wallet.neutron = {
              address: value.walletAddressNeutron,
              flag: false
            }
            continue
          case 'SEI':
            if (!value.walletAddressSei) continue
            this.wallet.sei = {
              address: value.walletAddressSei,
              flag: false
            }
            continue
          case 'TON':
            if (!value.walletAddressTon) continue
            this.wallet.ton = {
              address: value.walletAddressTon,
              flag: false
            }
            continue
        }
      }
    },
    copyAddress (data, index) {
      const inp = document.createElement('input')
      document.body.appendChild(inp)
      inp.value = data.address
      inp.select()
      document.execCommand('Copy')
      document.body.removeChild(inp)
      this.wallet = Object.assign({}, this.wallet)
      data.flag = true
      let time = null
      time = setTimeout(() => {
        data.flag = false
        clearTimeout(time)
      }, 1000)
    },
    getModifyStatus (i) {
      if (!this.detail.recordsList) {
        return false
      }
      return i.createTime === this.detail.recordsList.filter(x => x.status === 3)[0].createTime
    },

    filterRecordsList (index) {
      return this.detail?.recordsList.filter(x => x.status === index) || []
    },

    close () {
      if (this.kycMode === 3 && this.selectGeolocation?.avaliableList.length) {
        this.selectGeolocation.avaliableList.length && this.$refs.detail.selectOption(this.selectGeolocation.avaliableList[0].childrens[0], 5)
      } else if (this.selectGeolocation?.avaliableList.length) {
        this.selectCountry.avaliableList.length && this.$refs.detail.selectOption(this.selectCountry.avaliableList[0].childrens[0], 2)
        this.selectGeolocation.avaliableList.length && this.$refs.detail.selectOption(this.selectGeolocation.avaliableList[0].childrens[0], 5)
      }
      this.$emit('close')
      this.detail = {}
    },

    async Leave (val) {
      this.warningtip = false
      if (val === 'Leave') {
        const rp = await this.$api.request('kyc.updateStatus', this.operationdata)
        if (rp.code === 80000000) {
          this.$message({
            customClass: 'kycmessage',
            type: 'success',
            iconClass: 'mcico-success',
            message: this.msgTxt
          })
          this.$emit('close')
          this.$emit('update')
        }
      }
    },

    toDelete () {
      this.warningtip = true
      this.model = 'Delete'
      this.msgTxt = 'Delete success!'
      this.operationdata = {
        id: this.detail.id,
        status: -1
      }
    },

    cancelService () {
      this.warningtip = true
      this.model = 'Cancel'
      this.msgTxt = 'Service canceled'
      this.operationdata = {
        id: this.detail.id,
        status: 4
      }
    },

    modifyProgram () {
      this.$store.commit(types.SET_KYCFORM_DETAIL, this.initDetail)
      this.$store.commit(types.SET_KYC_TITLE, 'Modify program')
      this.$router.push({ path: `/zk-kyc/zkkyc-form?mode=${this.$store.state.kyc.category}` })
    },

    duplicate () {
      this.$store.commit(types.SET_DUPLICATE_FORM, this.initDetail)
      this.$store.commit(types.SET_KYC_TITLE, 'Create program')
      this.$router.push({ path: `/zk-kyc/zkkyc-form?mode=${this.$store.state.kyc.category}` })
    },

    applyProgram () {
      this.operationdata = {
        id: this.detail.id,
        status: 1
      }
      // if (this.hasAppliedProgram) {
      //   this.editNameVisible = true
      //   this.dialogType = 'apply'
      //   return
      // }
      this.applyLoading = true
      this.applyConfirm()
    },

    async applyConfirm () {
      const rp = await this.$api.request('kyc.updateStatus', this.operationdata)
      if (rp.code === 80000000) {
        this.editNameVisible = false
        this.applyLoading = false
        this.$emit('close')
        this.$emit('update')
      }
    },

    input () {
      const zg = /^[0-9a-zA-Z ]*$/
      if (this.dialoginp.length > 50 || !this.dialoginp.length || !zg.test(this.dialoginp)) {
        this.dialogwarning = true
      } else {
        this.dialogwarning = false
      }
    },

    toEdit () {
      this.dialoginp = this.detail.programName
      this.dialogType = 'edit'
      this.editNameVisible = true
    },

    cancel () {
      this.editNameVisible = false
      setTimeout(() => {
        this.dialoginp = ''
        this.dialogwarning = false
      }, 100)
    },

    async confirm () {
      if (this.dialogType === 'apply') {
        this.applyLoading = true
        await this.applyConfirm()
        return
      }

      this.input()
      if (this.dialogwarning) {
        return
      }

      // status: -1 delete, 1 apply, 2 create, 3 update, 4 expired
      const rp = await this.$api.request('kyc.updateKycProgram', { ...this.form, programName: this.dialoginp })
      if (rp.code === 80000000) {
        this.editNameVisible = false
        this.$message({
          customClass: 'kycmessage',
          type: 'success',
          iconClass: 'mcico-success',
          message: 'Save Success!'
        })
        this.$emit('close')
        this.$emit('update')
      }
    },

    copy (row) {
      const elInput = document.createElement('input')
      elInput.value = typeof row === 'object' ? row.kycProgramId : row
      document.body.appendChild(elInput)
      elInput.select()
      document.execCommand('Copy')
      elInput.remove()

      if (typeof row === 'object') {
        this.$set(row, 'copystyle', true)
        setTimeout(() => {
          row.copystyle = false
        }, 500)
      } else {
        this.drawerCopy = true
        setTimeout(() => {
          this.drawerCopy = false
        }, 500)
      }
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/components/_drawer_component.scss" scoped></style>
