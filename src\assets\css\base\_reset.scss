html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video,
textarea, button {
  margin: 0;
  padding: 0;
  border: 0;
  font-family: "HarmonyOS_Sans", "PingFang SC", "Source Han Sans CN", BlinkMacSystemFont, "Microsoft YaHei UI", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
}

html, body {
  height: 100%;
}

body {
  font-size: 14px;
  line-height: 1;
  -webkit-text-size-adjust: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  // color: $mainTextColor;
  background-color: #fff;
}
.grecaptcha-badge {
  visibility: hidden;
}

button, input, textarea {
  font-family: "HarmonyOS_Sans", "PingFang SC", "Source Han Sans CN", BlinkMacSystemFont, "Microsoft YaHei UI", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
}

button {
  background: transparent;
}

ol, ul {
  list-style: none;
}

a {
  text-decoration: none;
  // color: $primaryColor;
}

input::-webkit-inner-spin-button {
  display: none !important;
}

input::-ms-reveal,
input::-ms-clear {
  display: none;
}

input[type=password]::-webkit-credentials-auto-fill-button {
  display: none !important;
  visibility: hidden;
  pointer-events: none;
  position: absolute;
  right: 0;
}

input:-internal-autofill-previewed,
input:-internal-autofill-selected {
  -webkit-text-fill-color: #002E33 !important;
  transition: background-color 5000s ease-in-out 0s !important;
}