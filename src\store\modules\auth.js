import Vue from 'vue'
import { encrypt } from '../../utils/filters'
import * as types from './../mutation-types'

export default {
  state: {
    user: JSON.parse(localStorage.getItem('zkmeAdminUser')), // 登录态
    setUserif: null
    // powers: null // 用户权限表
  },
  mutations: {
    [types.SET_USER] (state, payload) {
      // 如果后端返回的用户数据不是一个对象，这里重设为一个空对象
      Object.prototype.toString.call(payload) !== '[object Object]' && (payload = {})
      if (payload.iToken) {
        Vue.prototype.$api.defaults.headers.iToken = payload.iToken
        payload.timestamp = Date.now()
      }
      // state.user = payload
      state.user = Object.assign({}, state.user || {}, payload)
    },
    [types.CLEAN_USER] (state) {
      Vue.prototype.$api.defaults.headers.iToken = ''
      state.user = null
      localStorage.removeItem('zkmeAdminUser')
      localStorage.removeItem('kycLevel')
    },
    [types.SET_LEVEL] (state, payload) {
      state.user = Object.assign({}, state.user, { level: payload.data.level, isNoMint: payload.data.isNoMint })
      localStorage.setItem('zkmeAdminUser', JSON.stringify(state.user))
    },
    [types.SET_USER_IF] (state, payload) {
      state.setUserif = payload
    }

    // [types.UPDATE_POWERS] (state, payload) {
    //   const powers = {}
    //   payload.forEach(k => {
    //     powers[k] = 1
    //   })
    //   powers.dashboard_read = 1
    //   state.powers = powers
    // }
  },
  actions: {
    async logout ({ commit }) {
      const rp = await Vue.prototype.$api.request('auth.logout', {}, {}, false)

      if (rp.code === 80000000) {
        commit(types.CLEAN_USER)
        commit(types.SET_BLOCK_CHAIN_LIST, null)
      }

      return rp
    },

    async login ({ commit }, payload) {
      const rp = await Vue.prototype.$api.request('auth.login', encrypt(payload), {}, {}, false)

      if (rp.code === 80000000) {
        commit(types.SET_USER, Object.assign({ name: atob(payload.ia) }, rp.data))
      }

      return rp
    },

    async detail ({ state, commit }) {
      Vue.prototype.$api.defaults.headers.mchNo = state.user.mchNo
      const rp = await Vue.prototype.$api.request('auth.detail')

      if (rp.code === 80000000) {
        localStorage.setItem('kycLevel', rp.data.level)
        localStorage.setItem('centralizationUser', rp.data.isNoMint)
        console.log('rp.data', rp.data)
        commit(types.SET_LEVEL, rp)
      }
    }

    // async getPowers ({ commit, state }) {
    //   const u = state.user || {}
    //   Vue.prototype.$api.defaults.headers.iToken = u.token
    //   const rp = await Vue.prototype.$api.request('auth.getPowers')

    //   if (rp.code === 200) {
    //     commit(types.UPDATE_POWERS, rp.data)
    //   }

    //   return rp
    // }
  }
}
