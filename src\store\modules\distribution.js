import Vue from 'vue'
import wallet from './wallet'
import * as types from './../mutation-types'
import { Contract } from 'ethers'
import BigNumber from 'bignumber.js'
import abi from '../../utils/contract'

import evmContract from '../../utils/ZKMEMultiProjectDistributor'
function fillZero (target, length = 64) {
  target = target.replace('0x', '')
  const deta = length - target.length
  return '0x' + Array(deta).fill('0').join('') + target
}

export default {
  state: {
    detail: {
      onlyPlanId: '',
      onlyActivityId: '',
      onlyConfigId: '',
      planId: '',
      recipientId: '',
      activityId: '',
      mchAssetAddress: '',
      step: 1,
      email: '',
      description: '',
      symbolUrl: '',
      claimPageUrl: '',
      distributionName: '',
      tokenNetwork: '',
      tokenContractAddress: '',
      tokenSymbol: '',
      distributionType: '',
      amount: '',
      website: '',
      xAccount: '',
      telegram: '',
      discord: '',
      kycProgramId: '',
      displayTimezone: '',
      mchNo: '',
      blockchainId: '',
      startDate: '',
      endDate: '',
      whitelist: [],
      tableList: []
    },
    status: '',
    distribution_id: '',
    distributionStatus: false
  },
  mutations: {
    [types.SET_DETAIL] (state, payload) {
      state.detail = {
        ...state.detail,
        ...payload
      }
    },
    [types.SET_CHANGE_STATUS] (state, payload) {
      state.status = payload
    },
    [types.SET_STEP] (state, payload) {
      state.detail.step = payload
    },
    [types.SET_DISTRIBUTUION_ID] (state, payload) {
      state.detail.planId = payload
    },
    [types.SET_DISTRIBUTUION_STATUS] (state, payload) {
      state.distributionStatus = payload
    }
  },
  actions: {
    clearDetail ({ state }) {
      state.detail = {
        planId: '',
        step: 1,
        email: '',
        distributionName: '',
        tokenNetwork: '',
        tokenContractAddress: '',
        tokenSymbol: '',
        distributionType: '',
        amount: '',
        website: '',
        xAccount: '',
        telegram: '',
        discord: '',
        kycProgramId: '',
        displayTimezone: '',
        mchNo: '',
        blockchainId: ''
      }
      state.status = ''
    },
    async postDistributionInfo ({ state }, payload) {
      const rp = await Vue.prototype.$api.request('disctrbution.postDistributionInfo', state.detail, {}, 'distribution')
      if (rp.code === 200) return true
      return false
    },
    async putDistributionInfo ({ state, commit }, payload) {
      const data = Object.assign({}, payload, { id: state.detail.onlyPlanId, planId: state.detail.planId })
      const rp = await Vue.prototype.$api.request('disctrbution.putDistributionInfo', data, {}, 'distribution')
      if (rp.code === 200) {
        commit(types.SET_DETAIL, data)
        return true
      }
      return false
    },
    async createDistributionActivity ({ state, commit }, payload) {
      const getRp = await Vue.prototype.$api.request('disctrbution.createDistributionActivity', {}, {}, 'distribution')
      const data1 = Object.assign({}, {
        activityId: '',
        planId: state.detail.planId,
        claimedAmount: 0,
        totalAmount: payload.totalAmount,
        claimedAddresses: 0,
        totalRecipients: 0
      }, { activityId: getRp.activityId })
      const data2 = Object.assign({}, {
        planId: state.detail.planId,
        themeColor: '',
        secondaryColor: '',
        fontColor: '',
        buttonColor: ''
      }, { configId: getRp.configId })
      const rp1 = await Vue.prototype.$api.request('disctrbution.postDistributionActivity', data1, {}, 'distribution')
      const rp2 = await Vue.prototype.$api.request('disctrbution.postTdClaimPageUiConfig', data2, {}, 'distribution')
      if (rp1.code === 200 && rp2.code === 200) {
        commit(types.SET_DETAIL, data1)
        commit(types.SET_DETAIL, data2)
        return true
      }
      return false
    },
    async putDistributionActivity ({ state, commit }, payload) {
      const data = Object.assign({}, payload, { id: state.detail.onlyActivityId, activityId: state.detail.activityId })
      const rp = await Vue.prototype.$api.request('disctrbution.putDistributionActivity', data, {}, 'distribution')
      if (rp.code === 200) {
        commit(types.SET_DETAIL, data)
        return true
      }
      return false
    },
    async createTdRecipient ({ state, commit }, payload) {
      const rp = await Vue.prototype.$api.request('disctrbution.getTdRecipient', { planId: state.detail.planId }, {}, 'distribution')
      if (!rp.deta) {
        const recipientRp = await Vue.prototype.$api.request('disctrbution.createTdRecipient', {}, {}, 'distribution')
        if (recipientRp.code === 200) {
          commit(types.SET_DETAIL, { recipientId: recipientRp.recipientId })
        }
      }
    },

    async postTdRecipient ({ state, commit }, payload) {
      const rp = await Vue.prototype.$api.request('disctrbution.postTdRecipient', { postTdRecipient: payload.data }, {}, 'distribution')
      if (rp.code === 200) {
        commit(types.SET_DETAIL, { whitelist: payload.list })
        return true
      }
      return false
    },

    async callContract ({ state }, payload) {
      const rp = await Vue.prototype.$api.request('disctrbution.createProjectId', { planId: state.detail.planId }, {}, 'distribution')

      if (rp.code === 200) {
        try {
          const BN = BigNumber.clone({ ROUNDING_MODE: 1, DECIMAL_PLACES: 0 })
          const bgNum = new BigNumber(state.detail.amount)
          const bg = new BN(bgNum.times(1e18)).toString(10)
          const contract = new Contract(
            '******************************************',
            evmContract.abi,
            wallet.state.signer
          )
          const contracts = new Contract(
            state.detail.tokenContractAddress,
            abi,
            wallet.state.signer
          )
          const txRp = await contracts.approve('******************************************', bg)
          const txReceipt = await txRp.wait()
          if (txReceipt.status === 1) {
            const projectId = Buffer.from(payload.planId).toString('hex')
            console.log(fillZero(projectId))

            const tx = await contract.deposit(fillZero(projectId), state.detail.tokenContractAddress) // 普通代币

            await Vue.prototype.$api.request('disctrbution.putDistributionInfo', {
              status: 'Upcoming',
              planId: state.detail.planId,
              id: state.detail.onlyPlanId,
              contractStatus: 'Deployed'
            }, {}, 'distribution')

            return await tx.wait()
            // console.log(fillZero(projectId))
            // const tx = await contract.depositNative(fillZero(projectId), { value: bg }) // 原生代币
          } else {
            throw new Error()
          }
        } catch (error) {
          console.log(error)
          return false
        }
      } else {
        return false
      }
    }
  }
}
