.t1 {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

.title {
  height: 26px;
  line-height: 26px;
  background: #84B6B81F;
  padding-left: 12px;
  margin-bottom: 8px;
  color: #002E33;
  font-weight: 500;
}

.progress>div {
  margin-top: 16px;
}

.iconn {
  font-size: 20px;
  vertical-align: middle;
  margin-right: 4px;
}

.box {
  border: 1px solid #84B6B81F;
  border-radius: 8px;
}

.box-tr {
  // border-bottom: 1px solid #84B6B81F;
  box-sizing: border-box;
  color: #002E33;
  &>li {
    padding: 8px 12px;
  }
  &>li:nth-child(1) {
    width: 76px;
    // font-weight: 500;
  }
}

.network {
  &>li:not(:first-child) {
    padding: 8px 12px 2px 12px;
    width: 478px;
  }
}

.citizen-search {
  &>li:not(:first-child) {
    padding: 4px;
    box-sizing: border-box;
  }
}

.border-left {
  border-left: 1px solid #84B6B81F;
}

.network-item {
  padding-right: 8px;
  position: relative;
  margin-right: 8px;
  padding-bottom: 6px;
  img {
    width: 20px;
    margin-right: 6px;
  }
}

.no-last-item {
  &::after {
    content: '';
    position: absolute;
    top: 3px;
    right: 0;
    width: 1px;
    height: 14px;
    background: #84B6B81F;
  }
}

.border-top {
  // border-bottom: none;
  border-top: 1px solid #84B6B81F;
}

.citizen-box {
  color: #002E33;
  &>li {
    box-sizing: border-box;
    padding-left: 12px;
    width: 100%;
  }
  position: relative;
}
.network-tag {
  background: #F7F7F7;
  border-radius: 6px;
  padding: 4px 16px 4px 8px;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  position: relative;
  color: #002E33;
  margin-bottom: 32px;
  border: none;
  cursor: revert;
  top: 16px;
  img {
    width: 22px;
  }
}
.tag-checked {
  background: #84B6B81F;
  overflow: hidden;
  &::after {
    content: '';
    width: 12px;
    height: 12px;
    background: url('~@/assets/img/tag-checked.png') 0 0 no-repeat;
    background-size: 100%;
    position: absolute;
    top: 0;
    right: 0;
  }
}

.citizen-title {
  padding: 12px 12px 12px 0;
  height: 44px;
  box-sizing: border-box;
}

.nations {
  height: 264px;
  overflow-y: scroll;
  overflow-x: hidden;
  position: relative;
}

.nation-item {
  color: #002E33;
  .t1 {
    height: 24px;
    line-height: 24px;
    color: #738C8F;
    font-size: 12px;
  }
  p {
    height: 24px;
    line-height: 24px;
    color: #002E33;
    max-width: calc(100% - 36px);
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  li {
    height: 40px;
    // padding: 0 12px;
    // cursor: pointer;
    &:hover {
      background: #F7F7F7;
      .network-tag {
        background-color: #FFFFFF;
        color: #002E33;
      }
    }
  }
  .img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
  .item-checked {
    background: #F2F7F7;
    &:hover {
      background: #E6EDED;
    }
  }
}

.aml-ul {
  li {
    padding: 8px 12px;
    font-weight: 500;
    box-sizing: border-box;
    color: #002E33;
    &:not(:last-child) {
      border-bottom: 1px solid #84B6B81F;
    }
  }
}

.no-content {
  width: 100%;
  height: 264px;
  img {
    width: 128px;
  }
  p {
    font-size: 14px;
    font-weight: 450;
    line-height: 18px;
    color: #B3C0C2;
  }
}

.nation-sprites {
  width: 24px;
  height: 24px;
  background: url('~@/assets/img/nation_sprites2.png') no-repeat;
  background-size: 8466px;
  transform-origin: 0 50%;
  margin-right: 12px;
}

.search-content {
  position: relative;
}
.search-list {
  width: 188px;
  padding: 6px 0;
  box-sizing: border-box;
  position: absolute;
  left: 4px;
  top: 36px;
  border: 0.5px solid #CEDBDB;
  box-shadow: 0px 2px 4px -1px #0000001F;
  border-radius: 6px;
  background-color: #fff;
  height: 184px;
  overflow-y: scroll;
  z-index: 1;
  p {
    width: 98px;
    line-height: 24px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
  li {
    padding: 4px 8px 4px 14px;
    height: 32px;
    box-sizing: border-box;
    cursor: pointer;
    &:hover {
      background-color: #F7F7F7;
    }
  }
  .mcico-success2, .mcico-SelectNot {
    font-size: 20px;
    margin-right: 0px;
  }
}
.available {
  padding: 12px 0;
  i {
    font-size: 20px;
    margin-right: 4px;
  }
}
.mcico-success2, .mcico-SelectNot {
  font-size: 20px;
  margin-right: 12px;
}

.no-list {
  img {
    width: 88px;
  }
  .no-txt {
    color: #B3C0C2;
    font-size: 12px;
    line-height: 16px;
  }
}