<template>
  <div class="mui-fl-col mui-fl-btw">
    <div v-if="!pageSwitching">
      <div class="title1">Token Distribution Overview</div>
      <div class="title2">All your token distribution plans in one place—from draft to completion.</div>
      <div class="title" v-if="distributionList.length">
        Airdrop <span class="t1">{{ distributionList.length }} Distribution plans</span>
      </div>
      <ul class="listBox" v-if="distributionList.length">
        <li v-for="(data, index) of distributionList" :key="index" class="mui-fl-btw list" @click="jumpAnalyze(data)">
          <div class="block mui-fl-btw mui-fl-col mui-fl-1">
            <div class="mui-fl-vert name">
              <span>{{ data.distribution_name }}</span>
              <!-- <div class="percentage">25</div> -->
              <m-progress
                type="circle"
                :strokeWidth="3"
                defineBackColor="rgba(142, 222, 227, 0.30)"
                color="#005563"
                :width="32"
                class="sty1-progress"
                :percentage="((data.sRecipients / data.Recipients) * 100) || 0">
              </m-progress>
            </div>
            <div class="mui-fl-vert">
              <div class="startTime">
                <div class="t2"><i class="mico-clock2"></i> Start date: </div>
                <div class="t3">{{ data.start_date | formatTimestampToUTC8(data.display_timezone) }}</div>
              </div>
              <div class="">
                <div class="t2"><i class="mico-clock2"></i> End date: </div>
                <div class="t3">{{ data.end_date | formatTimestampToUTC8(data.display_timezone) }}</div>
              </div>
            </div>
          </div>
          <div class="block mui-fl-btw mui-fl-col mui-fl-1">
            <div>
              <div class="t2">Claimed / Allocated Amount</div>
              <div class="t4">
                {{ new Intl.NumberFormat('en-US').format(data.claimed_amount) || 0 }} /
                {{ new Intl.NumberFormat('en-US').format(data.amount) }} {{ data.token_network.toLocaleUpperCase() }}
              </div>
            </div>
            <div class="deployedText">
              <div class="t2">Deployed</div>
              <div class="t4">{{ data.contract_status | formatTimestampToUTC8(data.display_timezone) }}</div>
            </div>
          </div>
          <div class="block mui-fl-btw mui-fl-col mui-fl-1">
            <div>
              <div class="t2">Claimed / Total Recipients</div>
              <div class="t4">
                {{ new Intl.NumberFormat('en-US').format(data.claimed_addresses) || '--' }} /
                {{ new Intl.NumberFormat('en-US').format(data.total_recipients) || '--' }}
              </div>
            </div>
            <div>
              <div class="t2">Airdrop URL</div>
              <div :class="['t4', data.claim_page_url && 'link']" @click.stop="copy(data.claim_page_url)">
                {{ data.claim_page_url ? abbreviateUrl(data.claim_page_url, 12, 16) : '--' }}
              </div>
            </div>
          </div>
          <div :class="[data.status, 'mui-fl-vert']">
            <svg v-if="data.status === 'Draft'" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path d="M4.78401 1.5H2.625C2.00368 1.5 1.5 2.00368 1.5 2.625V4.78401C1.5 5.08238 1.61853 5.36853 1.8295 5.5795L6.62006 10.3701C6.96939 10.7194 7.50994 10.8059 7.92327 10.5352C8.96353 9.85419 9.85419 8.96353 10.5352 7.92327C10.8059 7.50994 10.7194 6.96939 10.3701 6.62006L5.5795 1.8295C5.36853 1.61853 5.08238 1.5 4.78401 1.5Z" stroke="#33585C" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M3 3H3.00375V3.00375H3V3Z" stroke="#33585C" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg v-if="data.status === 'Upcoming'" xmlns="http://www.w3.org/2000/svg" width="10" height="12" viewBox="0 0 10 12" fill="none">
              <path d="M3.375 6H5.25M3.375 7.5H5.25M3.375 9H5.25M6.75 9.375H7.875C8.49632 9.375 9 8.87132 9 8.25V3.05411C9 2.48662 8.5775 2.00508 8.01196 1.95814C7.82532 1.94265 7.63807 1.92929 7.45024 1.9181M4.54976 1.9181C4.51741 2.02301 4.5 2.13447 4.5 2.25C4.5 2.45711 4.66789 2.625 4.875 2.625H7.125C7.33211 2.625 7.5 2.45711 7.5 2.25C7.5 2.13447 7.48259 2.02301 7.45024 1.9181M4.54976 1.9181C4.69138 1.45878 5.11921 1.125 5.625 1.125H6.375C6.88079 1.125 7.30862 1.45878 7.45024 1.9181M4.54976 1.9181C4.36193 1.92929 4.17468 1.94265 3.98804 1.95814C3.4225 2.00508 3 2.48662 3 3.05411V4.125M3 4.125H1.3125C1.00184 4.125 0.75 4.37684 0.75 4.6875V10.3125C0.75 10.6232 1.00184 10.875 1.3125 10.875H6.1875C6.49816 10.875 6.75 10.6232 6.75 10.3125V4.6875C6.75 4.37684 6.49816 4.125 6.1875 4.125H3ZM2.25 6H2.25375V6.00375H2.25V6ZM2.25 7.5H2.25375V7.50375H2.25V7.5ZM2.25 9H2.25375V9.00375H2.25V9Z" stroke="#002E33" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <svg v-if="data.status === 'Active'" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path d="M7.6811 2.60693C9.12134 3.25047 10.125 4.6955 10.125 6.37495C10.125 8.65312 8.27817 10.4999 6 10.4999C3.72183 10.4999 1.875 8.65312 1.875 6.37495C1.875 5.26892 2.31029 4.26457 3.01891 3.52385C3.40215 4.05906 3.91024 4.49878 4.50061 4.80044C4.52316 3.41261 5.17399 2.17751 6.18106 1.36719C6.56277 1.87906 7.06894 2.30923 7.6811 2.60693Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M6 9.00014C7.03553 9.00014 7.875 8.16068 7.875 7.12514C7.875 6.17349 7.16603 5.38743 6.24745 5.26633C5.74328 5.7186 5.3931 6.33906 5.28515 7.03945C4.89385 6.9438 4.53265 6.77139 4.21841 6.53908C4.15779 6.72347 4.125 6.92048 4.125 7.12514C4.125 8.16068 4.96447 9.00014 6 9.00014Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            {{ data.status }}
          </div>
        </li>
        <div class="mui-fl-central" ref="loading" :style="{margin: !loading || !nextPage ? '0' : '24px 0'}">
          <div class="loader" v-if="loading && nextPage"></div>
        </div>
      </ul>
      <div v-else class="mui-fl-central noToken mui-fl-col">
        <div v-if="tableLoading" class="loader loader2 loader4"></div>
        <div v-else class="mui-fl-central noToken mui-fl-col">
          <img src="../../../assets/img/no-table-data.png" alt="">
          <div class="title3">No Token Distribution Plan Available</div>
          <div class="title4">Create a new token distribution with the button below</div>
          <m-button @click="$emit('onjumpStep', 'create') " class="sty2-button sty7-button sty13-button">Create Token Distribution Plan</m-button>
        </div>
      </div>
    </div>
    <DistributionAnalyze v-else></DistributionAnalyze>
  </div>
</template>

<script>
import DistributionAnalyze from './distributionAnalyze.vue'
import { abbreviateUrl } from '../../../utils/filters'

export default {
  components: { DistributionAnalyze },
  data () {
    return {
      loading: true,
      tableLoading: false,
      pageSwitching: false,
      distributionList: [],
      ob: null,
      page: 0,
      abbreviateUrl: abbreviateUrl,
      nextPage: false
    }
  },
  computed: {
    address () {
      return this.$store.state.wallet.connectedAddress || ''
    },
    provider () {
      return this.$store.state.wallet.provider
    }
  },
  watch: {
    nextPage (val) {
      if (val) {
        setTimeout(() => {
          const ob = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting && this.loading) {
                this.getDistributionList()
              }
            })
          }, {
            root: null, // null = 视口为监听对象
            threshold: 0.1 // 元素在视口中占据的比例 0-1之间
          })
          ob.observe(this.$refs.loading)
        })
      }
    },
    'distributionList.length' (val) {
      this.$emit('getlength', !!val)
    }
  },
  created () {
    this.getDistributionList()
  },
  methods: {
    toCamelCase (str) {
      return str.replace(/_([a-z])/g, (match, p1) => p1.toUpperCase())
    },
    getChangedFields (obj1) {
      const changed = {}
      Object.keys(obj1).forEach(key => {
        changed[this.toCamelCase(key)] = obj1[key]
      })
      return changed
    },
    closePageSwitching () {
      this.pageSwitching = false
    },
    jumpAnalyze (data) {
      this.$store.commit('SET_DETAIL', this.getChangedFields(data))
      if (data.status !== 'Draft') {
        // this.pageSwitching = true
      } else {
        this.$emit('onjumpStep')
      }
    },
    getWhitelistList (list) {
      list.forEach(async (v, i) => {
        v.contract_status = '--'
        if (v.create_project_tx_id) {
          // 获取区块链时间
          v.contract_status = await this.$store.dispatch('getTransaction', { chainId: '0x141f', txHash: v.create_project_tx_id })
        }
      })
      // Promise.all(list.map(x => {
      //   if (x.start_date) {
      //     return this.$api.request('disctrbution.getWhitelistList', { planId: x.plan_id }, {}, 'cleanupWhitelist')
      //   }
      // })).then(rp => {
      //   console.log(rp)
      //   rp.forEach((item, index) => {
      //     const data = this.distributionList.findIndex(x => x.plan_id === item?.data?.planId)
      //     if (data !== -1) {
      //       this.$set(this.distributionList[data], 'Recipients', item.data.count)
      //     }
      //   })
      // })
    },
    copy (val) {
      const elInput = document.createElement('input')
      elInput.value = val
      document.body.appendChild(elInput)
      elInput.select()
      document.execCommand('Copy')
      elInput.remove()
      this.$message({
        message: 'Copied',
        iconClass: 'mico-lightTip',
        customClass: 'sty1-message',
        duration: 3000,
        offset: 32,
        center: true
      })
    },
    async getDistributionList () {
      this.tableLoading = true
      const rp = await this.$api.request('disctrbution.getDistributionList', { email: this.$store.state.auth.user.name, mchNo: this.$store.state.auth.user.mchNo, page: this.page }, {}, 'distribution')
      if (!rp.TdMchWhiteList?.id) {
        await this.$api.request('disctrbution.TdMchWhiteList', { mchNo: this.$store.state.auth.user.mchNo }, {}, 'distribution')
      }
      this.tableLoading = false
      if (!rp['[]']) {
        this.loading = false
        // this.$emit('getlength', false)
        return
      }
      this.nextPage = rp.listTotal / 10 > (this.page + 1)
      const list = rp['[]'].map(x => {
        return Object.assign(
          {},
          x.TdTokenDistributionPlan,
          x['TdDistributionActivity[]']?.[0] || {},
          x['TdClaimPageUiConfig[]']?.[0] || {},
          {
            onlyPlanId: x.TdTokenDistributionPlan.id,
            onlyActivityId: x['TdDistributionActivity[]']?.[0]?.id
          },
          {
            onlyPlanId: x.TdTokenDistributionPlan.id,
            onlyConfigId: x['TdClaimPageUiConfig[]']?.[0]?.id
          }
        )
      })
      if (this.page) {
        this.distributionList = this.distributionList.concat(list)
      } else {
        this.distributionList = list || []
      }
      this.$store.commit('SET_DETAIL', { tableList: list })
      // this.$emit('getlength', true)
      this.getWhitelistList(list)
      if (list.length === 10) {
        this.page++
      } else {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/tokenDistribution/_distributionList.scss" scoped></style>
