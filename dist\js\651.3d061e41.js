"use strict";(self["webpackChunkzkme_web"]=self["webpackChunkzkme_web"]||[]).push([[651],{49651:function(t,e,i){i.r(e),i.d(e,{default:function(){return f}});var s=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{"min-width":"880px"}},[t._m(0),t.access?e("div",{staticClass:"noAccess mui-fl-vert mui-fl-col"},[e("div",{staticClass:"n"},[t._v("No access")]),t._m(1),t._m(2)]):e("div",[e("div",{staticClass:"mui-fl-btw zkmeApi"},[e("m-collapse",{staticClass:"igCollapse",on:{change:function(e){t.collapse=!t.collapse}},model:{value:t.collapseFlg,callback:function(e){t.collapseFlg=e},expression:"collapseFlg"}},[e("m-collapse-item",{attrs:{name:"1"}},[e("template",{slot:"title"},[e("div",{staticClass:"mui-fl-btw"},[e("div",{staticClass:"ct mui-fl-vert"},[e("div",[t._v("zkMe API Access Permissions")]),e("i",{class:["mico-fold",t.collapse?"":"rt"]})])])]),e("div",{staticClass:"authority"},t._l(t.apiList.apiAccessPermissions,(function(i,s){return e("div",{key:s,class:["mui-fl-vert",i.valid?"kmVerify":""]},[e("span",{staticClass:"meName"},[t._v(t._s(i.name))]),i.valid?t._e():e("span",{staticClass:"meState"},[t._v("No access yet")])])})),0)],2)],1),t._m(3)],1),t.whiteList?e("IntegrationWhitelist",{ref:"IntegrationWhitelist",attrs:{whiteList:t.whiteList},on:{leaveFlg:t.leaveFlg,changeTabs:t.changeTabs,updataList:t.getIntegrationList}}):t._e(),t.tabsFlg?e("div",{staticClass:"apiKeycla"},[e("div",{staticClass:"title"},[t._v(" Your API key ")]),e("div",{staticClass:"tip mui-fl-vert"},[t._v(" For all server-side integrations, please make use of the API Key provided. It is imperative that the API Key is never exposed or utilized in any client-side integration. ")]),e("div",{staticClass:"content mui-fl-vert"},[t._m(4),e("span",{staticClass:"apn mui-fl-vert"},[t._v(" "+t._s(t.apiList.mchNo)+" "),e("m-popover",{attrs:{placement:"top",width:"64",trigger:"manual","popper-class":"NotRecord_popover WhiteList",content:"Copied!"},model:{value:t.popoverFlg,callback:function(e){t.popoverFlg=e},expression:"popoverFlg"}},[e("i",{staticClass:"mico-copy",attrs:{slot:"reference"},on:{click:function(e){return t.copy(t.apiList.mchNo,"mchNo")}},slot:"reference"})])],1),t._m(5),t.setGenerated?e("span",{staticClass:"mui-fl-vert"},[e("span",{staticClass:"apn generated"},[t._v("Generated")]),e("div",{staticClass:"reset mui-fl-central",on:{click:t.restTime}},[e("i",{staticClass:"mico-a-apireset"}),e("span",{staticClass:"apt"},[t._v("Reset")])])]):e("m-button",{staticClass:"apb",on:{click:t.generateApi}},[t._v(" Generate API Key ")])],1)]):t._e()],1),e("m-dialog",{staticClass:"integration-dialog",attrs:{visible:t.dialogVisible,"show-close":t.apiRest,width:"500px","close-on-click-modal":!1,"close-on-press-escape":!1},on:{"update:visible":function(e){t.dialogVisible=e},closed:t.timeClear}},[e("div",{staticClass:"dbg mui-fl-central",attrs:{slot:"title"},slot:"title"},[e("img",{attrs:{src:i(56656),alt:""}})]),t.apiRest?e("div",{staticClass:"dh mui-fl-hori"},[t._v("Reset API Key")]):e("div",{staticClass:"dh mui-fl-hori"},[t._v("Your API Key")]),t.apiRest?e("div",[e("div",{staticClass:"tipWarning mui-flex"},[e("div",[e("i",{staticClass:"mico-warning"})]),e("div",{staticClass:"tipText"},[e("div",{staticClass:"tipHand"},[t._v(" Please notice:"),e("br")]),t._v(" API Key reset will cause the original API Key to"),e("br"),t._v(" become invalid. ")])]),e("div",{staticClass:"mui-fl-hori"},[10!==t.stime?e("m-button",{staticClass:"db watitdb"},[t._v("Confirm ("+t._s(t.stime)+")")]):e("m-button",{staticClass:"db",on:{click:function(e){return t.acknowledged("Confirm")}}},[t._v("Confirm")])],1)]):e("div",[e("div",{staticClass:"dk mui-fl-vert"},[t._v(t._s(t.apiKey)),e("i",{staticClass:"mico-copy",on:{click:function(e){return t.copy(t.apiKey)}}})]),e("div",{staticClass:"dt"},[e("div",[t._v(" Please keep your API Key safe,"),e("br"),t._v(" as it "),e("span",{staticClass:"will"},[t._v("WILL NOT")]),t._v(" be displayed on our website again. ")]),e("div",[t._v(" You can reset your API Key at any time,"),e("br"),t._v(" but this will cause the original API Key to become invalid. ")])]),e("div",{staticClass:"mui-fl-hori"},[e("m-button",{staticClass:"db",on:{click:function(e){return t.acknowledged("Acknowledged")}}},[t._v("Acknowledged")])],1)])])],1)},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"pg-title1 mui-fl-vert"},[e("i",{staticClass:"mcico-active-integration"}),t._v(" Integration ")])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"contact mui-fl-vert e"},[e("i",{staticClass:"mico-a-apiemail"}),t._v(" Contact us to get access to zkMe APIs: "),e("a",{staticClass:"email",attrs:{href:"mailto: <EMAIL>"}},[t._v("<EMAIL>")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"mui-fl-vert mui-fl-col b"},[e("div",{staticClass:"apis"},[t._v("zkMe APIs include:")]),e("div",{staticClass:"zkKyc p"},[t._v("zkKYC")]),e("div",{staticClass:"zkKyc"},[t._v("MeID Verify")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"contact igcon"},[e("i",{staticClass:"mico-a-apiemail"}),t._v(" Contact us to get access to zkMe APIs: "),e("a",{staticClass:"email",attrs:{href:"mailto: <EMAIL>"}},[t._v("<EMAIL>")])])},function(){var t=this,e=t._self._c;return e("span",{staticClass:"apd mui-fl-vert"},[e("i",{staticClass:"mico-AppID"}),t._v(" AppID:")])},function(){var t=this,e=t._self._c;return e("span",{staticClass:"api mui-fl-vert"},[e("i",{staticClass:"mico-APIKey"}),t._v(" API Key:")])}],l=(i(98992),i(72577),function(){var t=this,e=t._self._c;return e("div",{staticClass:"mui-fl-vert mui-fl-col asd"},[e("div",{staticClass:"mui-fl-vert igTabs"},[e("div",{class:["igTexT","igcur",!t.tabsFlg&&"borb"],staticStyle:{"margin-right":"40px"},on:{click:function(e){return t.changeTabs(!1,"Whitelist")}}},[t._v(" Whitelist")]),e("div",{class:["igTexT","igcur","mui-fl-vert",t.tabsFlg&&"borb"],style:{opacity:t.whiteList.officialUrls.length?"1":"0.5"},on:{click:function(e){return t.changeTabs(!0,"Configuration")}}},[t.whiteList.officialUrls.length?t._e():e("m-popover",{attrs:{width:"154px",placement:"top",trigger:"hover","popper-class":"NotRecord_popover WhiteList"}},[e("div",{staticClass:"mui-fl-central",attrs:{slot:"reference"},slot:"reference"},[e("i",{staticClass:"mico-lock cfLock"})]),e("div",{staticStyle:{"text-align":"left"}},[t._v(" To unlock this part, please save your information in the "),e("span",{staticStyle:{"font-weight":"700"}},[t._v("Whitelist")]),t._v(" first. ")])]),t._v(" Configuration ")],1)]),t.tabsFlg?e("div",{staticClass:"mui-fl-vert mui-fl-col"},[t._m(0),e("div",{staticClass:"configuration"},[e("div",{staticClass:"mui-fl-vert mbs"},[e("div",{staticClass:"con-text",staticStyle:{"font-size":"24px"}},[t._v("Your Private key")]),t.whiteList.pubKeyGenerated?e("div",{staticClass:"igTexts lock mui-fl-vert"},[e("i",{staticClass:"mico-lock"}),t._v(" "+t._s(t.whiteList.pubKeyGenerated?"Locked":"Unlock")+" ")]):t._e()]),e("div",{staticClass:"dwJson"},[t._m(1),e("div",{staticClass:"igTexts addresscor dwrad"},[e("div",{staticClass:"mui-fl-vert",staticStyle:{cursor:"pointer"},on:{click:function(e){!t.whiteList.pubKeyGenerated&&(t.radio=!t.radio)}}},[t.radio||t.whiteList.pubKeyGenerated?e("i",{staticClass:"mcico-success2 diaIcon"}):e("div",{staticClass:"radio"}),e("div",[t._v("I am aware of this and will ensure its proper safekeeping.")])])]),t.whiteList.pubKeyGenerated?e("div",{staticClass:"jsonSs mui-fl-vert"},[e("i",{staticClass:"mcico-success2"}),t._v(" JSON file downloaded ")]):e("m-button",{staticClass:"save dwbut",style:{opacity:t.radio?"1":"0.5"},on:{click:t.dwJson}},[t._v(" Download JSON file ")])],1)])]):e("div",{staticStyle:{"max-width":"820px"}},[e("div",{staticClass:"igTexT"},[t._v("Whitelist information")]),e("div",{staticClass:"igTexts igtip"},[t._v("To enhance your security and optimize your experience, we kindly request your permission to add your domain to our whitelist.")]),e("div",{staticClass:"igTexT",staticStyle:{"font-size":"18px"}},[t._v("Your official URL?")]),t._l(t.whiteUrlList,(function(i,s){return e("m-form",{key:s,staticClass:"url sty2-form",attrs:{model:i,rules:t.rules,"hide-required-asterisk":!0},nativeOn:{submit:function(t){t.preventDefault()}}},[e("m-form-item",{attrs:{prop:"url"}},[e("div",{staticClass:"mui-fl-vert creatURL",on:{mouseenter:function(e){t.deleteFlg=s},mouseleave:function(e){t.deleteFlg=""}}},[e("div",{staticClass:"igTexts urlText"},[t._v("URL"+t._s(s+1))]),"Save"===t.subText?e("m-input",{ref:"inp",refInFor:!0,staticClass:"sty1-input sty3-input creatinp",attrs:{"data-input":"",type:"text",placeholder:"Please enter..."},on:{clear:function(e){return t.dc(s)},input:function(e){return t.changeURL(s)}},model:{value:i.url,callback:function(e){t.$set(i,"url",e)},expression:"data.url"}},[i.url?e("i",{staticClass:"mcico-celar",attrs:{slot:"suffix"},on:{click:function(e){return t.dc(s,i,"celarIput")}},slot:"suffix"}):t._e()]):e("a",{staticClass:"openUrl",attrs:{"data-a":"",href:i.url,target:"_blank"}},[t._v(t._s(i.url))]),"Save"===t.subText&&1===t.whiteUrlList.length?e("m-popover",{attrs:{disabled:1!==t.whiteUrlList.length,width:"154px",placement:"top",trigger:"hover","popper-class":"NotRecord_popover WhiteList",content:"At least 1 URL is required"}},[e("div",{directives:[{name:"show",rawName:"v-show",value:t.deleteFlg===s,expression:"deleteFlg === index"}],class:["delete","igcur",1===t.whiteUrlList.length&&"unDelete"],attrs:{slot:"reference"},on:{click:function(e){return t.whiteUrlListaDel(s,i)}},slot:"reference"},[t._v(" Delete")])]):"Save"===t.subText&&t.whiteUrlList.length>1&&t.deleteFlg===s?e("div",{staticClass:"delete igcur",staticStyle:{"margin-top":"20px"},on:{click:function(e){return t.whiteUrlListaDel(s,i)}}},[t._v(" Delete ")]):t._e()],1)])],1)})),"Save"===t.subText&&t.whiteUrlList.length<=9?e("div",{staticClass:"addurl igcur mui-fl-vert",on:{click:t.whiteUrlListaAdd}},[e("i",{staticClass:"mico-creat"}),t._v("Add new URL ")]):t._e(),e("div",[e("m-button",{class:["save",t.saveFlg],on:{click:t.save}},[t._v(t._s(t.subText))])],1)],2),e("m-dialog",{attrs:{width:"400px","close-on-click-modal":!1,"close-on-press-escape":!1,visible:t.dialogTableVisible,"custom-class":"sty1-dialog sty4-dialog"},on:{"update:visible":function(e){t.dialogTableVisible=e},close:t.closeCode}},[e("div",{staticClass:"mui-fl-vert",attrs:{slot:"title"},slot:"title"},[e("i",{staticClass:"mcico-success2 diaIcon"}),e("div",{staticClass:"con-text"},[t._v("JSON file downloaded")])]),e("div",[e("div",{staticClass:"igTexts diaPls"},[t._v("Please keep your documents safe, as they can not be recovered once lost. ")]),e("div",{staticClass:"igTexts diaTip"},[t._v("Now enter your verification code on your file to lock "),e("br"),t._v(" your account information.")]),e("div",{staticClass:"container"},[e("div",{staticClass:"inputBox"},[e("div",t._l(t.captchas,(function(i,s){return e("input",{directives:[{name:"model",rawName:"v-model",value:i.num,expression:"item.num"}],key:s,staticClass:"auth-b",style:{borderColor:t.checkFlg?"#EE6969":"#f0f0f0"},attrs:{id:"captcha"+s,type:"tel",maxlength:"1"},domProps:{value:i.num},on:{input:[function(e){e.target.composing||t.$set(i,"num",e.target.value)},function(e){return t.inputFinish(s)}],focus:function(e){return t.adjust(s)},keydown:function(e){return t.inputDirection(s,e)}}})})),0)])]),e("div",{staticClass:"mui-fl-central"},[e("m-popover",{attrs:{width:"308px",placement:"top",trigger:"hover","popper-class":"NotRecord_popover WhiteList jsonTip"}},[e("div",{staticClass:"jsonTipText",attrs:{slot:"reference"},slot:"reference"},[e("div",{staticClass:"igTexts lockDialog"},[t._v("Why is it necessary to lock? "),e("i",{staticClass:"mico-question"})])]),e("div",{staticStyle:{"text-align":"left"}},[t._v(" zkMe won't save your JSON file. If your account"),e("br"),t._v(" information is not locked, re-entering the page will"),e("br"),t._v(" randomly generate a new address and the"),e("br"),t._v(" corresponding JSON file. ")])])],1),e("div",{staticClass:"mui-fl-central gTexts diaLockBut",on:{click:t.checkCode}},[e("span",[t._v("Lock account information")])]),e("div",{staticClass:"mui-fl-central igTexts diaFoot igcur lockDialog",staticStyle:{color:"#33585C"},on:{click:t.dwJson}},[t._v("Download failed? Re-download")])])]),e("m-dialog",{attrs:{"show-close":!1,width:"400px","close-on-click-modal":!1,"close-on-press-escape":!1,visible:t.leaveTip,"custom-class":"sty1-dialog sty4-dialog"},on:{"update:visible":function(e){t.leaveTip=e}}},[e("div",{staticClass:"mui-fl-vert",attrs:{slot:"title"},slot:"title"},[e("i",{staticClass:"mico-warning diaIcon"}),e("div",{staticClass:"leaveTipTitle"},[t._v("Confirm to leave the page?")])]),t.tabsFlg?e("div",{staticClass:"igTexts leaveTipText"},[t._v("Account information is not locked. Refreshing the page will generate a new account address and private key."),e("br"),t._v(" The current information will become invalid.")]):e("div",{staticClass:"igTexts leaveTipText"},[t._v("Still have unsaved information.")]),e("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e("m-button",{staticClass:"c igTexts",on:{click:function(e){t.leaveTip=!1}}},[e("span",[t._v("Cancel")])]),e("m-button",{staticClass:"l igTexts",attrs:{type:"primary"},on:{click:t.leave}},[e("span",[t._v("Leave")])])],1)])],1)}),c=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"con-title mui-fl-central"},[e("i",{staticClass:"mico-file"}),t._v(" "),e("a",{staticClass:"documentations",attrs:{href:"https://docs.zk.me/zkme-dochub/",target:"_blank"}},[t._v("View support documentations")])])},function(){var t=this,e=t._self._c;return e("div",{staticClass:"igTexts dwTip mui-fl-vert"},[e("img",{staticClass:"private",attrs:{src:i(10954),alt:""}}),e("div",{staticClass:"diaLockText"},[t._v(" This private key is used for decrypting users' original KYC files. We will not store this private key, so please ensure its proper safekeeping. ")])])}],o=(i(44114),i(54520),i(81454),i(64979),i(14603),i(47566),i(98721),i(60343));const n=window.sjcl;var r={props:{whiteList:{type:Object,required:!0,default:()=>({})}},data(){return{pk:"",url:"",inpIndex:0,leaveTip:!1,checkFlg:!1,captchaCode:"",activeInput:0,popoverFlg1:!1,popoverFlg2:!1,popoverFlg3:!1,popoverFlg4:!1,walletFlg:!1,captchas:[{num:""},{num:""},{num:""},{num:""}],dialogTableVisible:!1,dialogFlg:"",pubKeyGenerated:!0,radio:!1,tabsFlg:!1,deleteFlg:1,saveFlg:"",subText:"Save",callbakTip:!1,whiteUrlList:[],routeLeave:"/",wallet:{address:"",seiAddress:"",neutronAddress:"",aptosAddress:"",privateKey:""}}},mounted(){this.whiteList.officialUrls.length?(this.whiteList.officialUrls.map((t=>this.whiteUrlList.push({url:t,flg:!1}))),this.subText="Modify",this.$emit("leaveFlg","Modify"),this.saveFlg=""):this.whiteUrlList.push({url:"",flg:!1}),this.walletFlg=this.whiteList.pubKeyGenerated,this.findUrl()},computed:{rules(){return{url:[{required:!0,trigger:"change",validator:(t,e,i,s)=>{const a=/^(https?:\/\/)/;if(e)try{new URL(e)}catch(l){return i("Please enter the correct URL.")}if(!e.replace(a).includes("undefined")&&e)i("Please enter the correct URL."),this.saveFlg="saveDe";else{if(!e)return void(this.saveFlg="saveDe");if("error"===this.inpIndex)i("Please enter the correct URL."),this.saveFlg="saveDe";else{if(("delete"===this.inpIndex||"pass"===this.inpIndex)&&this.whiteUrlList.length>1)return;for(const t in this.whiteUrlList){if(this.whiteUrlList[t].url.length>=100)return i("Please enter the correct URL."),void(this.saveFlg="saveDe");if(this.whiteUrlList[t].url===e&&this.whiteUrlList.length>1&&t!==this.inpIndex.toString())return i("Please enter a different URL."),this.callbakTip=!0,void(this.saveFlg="saveDe");!this.whiteUrlList[t].url&&this.whiteUrlList.length>1?this.saveFlg="saveDe":this.saveFlg=""}}}}}]}}},watch:{"whiteList.officialUrls"(t,e){!e.length&&t.length&&this.changeTabs(!0)},tabsFlg(t){t&&(this.whiteList.pubKeyGenerated||(this.radio=!1),this.closeCode())}},methods:{closeCode(){setTimeout((()=>{this.generateMchKeys()}),0)},copy(t,e){const i=document.createElement("input");document.body.appendChild(i),i.value=t,i.select(),document.execCommand("Copy"),document.body.removeChild(i),"evm"===e?this.popoverFlg1=!0:"com"===e?this.popoverFlg2=!0:"neutron"===e?this.popoverFlg3=!0:"aptos"===e&&(this.popoverFlg4=!0);let s=null;s=setTimeout((()=>{this.popoverFlg1=!1,this.popoverFlg2=!1,this.popoverFlg3=!1,this.popoverFlg4=!1,clearTimeout(s)}),1e3)},changeURL(t){try{this.inpIndex="delete",setTimeout((()=>{this.dc(t,"","changeURL")}))}catch(e){this.inpIndex="error"}},async checkCode(){let t="";if(this.captchas.map((e=>{t+=e.num})),t===btoa(this.whiteList.randDigits)){const e={publicKey:this.pk,randDigits:t},i=await this.$api.request("apikey.updateMchConfig",e,{},!0);8e7===i.code&&(this.walletFlg=!0,this.$emit("updataList"),this.dialogTableVisible=!1)}else this.checkFlg=!0},async generateMchKeys(){if(this.walletFlg)return;const t=o.u.createRandom();this.wallet.privateKey=t.privateKey;const e=n.ecc.curves.c256.G.mult(new n.bn(this.wallet.privateKey));this.pk=n.codec.hex.fromBits(e.toBits())},adjust(t){this.checkFlg&&(this.checkFlg=!1,this.captchas=[{num:""},{num:""},{num:""},{num:""}]);const e=document.getElementById("captcha"+this.activeInput);if(t!==this.activeInput&&e&&e.focus(),!this.captchas.filter((t=>""!==t.num)).length){const t=document.getElementById("captcha0");t&&t.focus()}},inputDirection(t,e){const i=this.captchas[t].num;if(8===e.keyCode&&""===i){const e=document.getElementById("captcha"+(t-1));this.activeInput=t-1,e&&e.focus()}if(8!==e.keyCode&&""!==i){const e=document.getElementById("captcha"+(t+1));this.activeInput=t+1,e&&e.focus()}},inputFinish(t){if(isNaN(Number(this.captchas[t].num)))return void(this.captchas[t].num="");this.checkFlg=!1;const e=this.captchas[t].num;this.activeInput=e?t+1:t-1;const i=document.getElementById("captcha"+this.activeInput);if(i&&i.focus(),t===this.captchas.length-1&&(this.captchaCode=this.captchas.map((t=>t.num)).join("")),!this.captchas.find((t=>!t.num))){let t="";this.captchas.map((e=>{t+=e.num})),t===btoa(this.whiteList.randDigits)?this.checkFlg=!1:this.checkFlg=!0}},dwJson(){if(!this.radio)return;this.dialogTableVisible=!0;const t="application/json",e=new TextEncoder,i=new Blob([e.encode(JSON.stringify({privateKey:this.wallet.privateKey,code:btoa(this.whiteList.randDigits)}))],{type:t}),s=URL.createObjectURL(i),a=document.createElement("a");a.href=s,a.download="privateKey.json",a.click(),a.remove()},changeTabs(t,e){this.routeLeave="string"===typeof t?t:"",("Save"===this.subText&&this.whiteList.officialUrls.length||this.whiteUrlList.find((t=>t.url))&&!this.whiteList.officialUrls.length&&this.routeLeave)&&(this.leaveTip=!0,this.$emit("leaveFlg","Save")),this.whiteList.officialUrls.length&&!this.leaveTip&&"boolean"===typeof t&&(this.tabsFlg=t,this.$emit("changeTabs",t))},async save(){if(""===this.saveFlg&&"Modify"!==this.subText){const t=[];this.whiteUrlList.map((e=>t.push(e.url)));const e=await this.$api.request("apikey.updateUrls",t.join(","),{},!0);if(8e7!==e.code)return this.inpIndex="error",void e.msg.split(",").map((t=>{this.$refs.inp[t].elForm.validate((t=>{}))}));this.$emit("updataList")}"saveDe"!==this.saveFlg&&"Modify"!==this.subText?(this.subText="Modify",this.$emit("leaveFlg","Modify"),this.$message({message:"Save success",iconClass:"mcico-success",customClass:"sty4-message",duration:3e3,offset:32,center:!0})):"saveDe"!==this.saveFlg&&"Modify"===this.subText&&(this.subText="Save",this.$emit("leaveFlg","Save"))},whiteUrlListaAdd(){this.whiteUrlList.push({url:"",flg:!1}),this.saveFlg="saveDe"},whiteUrlListaDel(t,e){this.whiteUrlList.length>1&&(this.whiteUrlList.splice(t,1),this.inpIndex="delete",this.findUrl(),setTimeout((()=>{this.dc(t,"","delete")})))},findUrl(){for(const t in this.whiteUrlList){if(!this.whiteUrlList[t].url)return void(this.saveFlg="saveDe");this.saveFlg=""}},dc(t,e,i){"celarIput"===i&&(e.url="");for(const s in this.whiteUrlList)this.whiteUrlList[s].flg=!1,this.inpIndex=s,this.whiteUrlList.filter((t=>t.url===this.whiteUrlList[s].url)).length>1&&!this.whiteUrlList.filter((t=>t.flg)).length?this.whiteUrlList.filter((t=>t.flg)).length||(this.whiteUrlList[s].flg=!0,this.inpIndex="pass",this.$refs.inp[s].elForm.validate((t=>{}))):(this.whiteUrlList[s].flg=!1,this.$refs.inp[s].elForm.validate((t=>{})));"delete"!==i&&"changeURL"!==i&&(this.$refs.inp[t].focus(),this.saveFlg="saveDe")},leave(){if(this.routeLeave)return this.$emit("leaveFlg","leave"),void this.$router.push({name:this.routeLeave});if(this.tabsFlg)this.leaveTip=!1,this.tabsFlg=!1,this.$emit("changeTabs",!1);else{this.subText="Modify",this.$emit("leaveFlg","Modify"),this.saveFlg="",this.tabsFlg=!0,this.leaveTip=!1,this.$emit("changeTabs",!0),this.whiteUrlList=[];for(const t in this.whiteList.officialUrls)this.whiteUrlList.push({url:this.whiteList.officialUrls[t]})}}}},h=r,d=i(81656),u=(0,d.A)(h,l,c,!1,null,"e3fb3178",null),p=u.exports,v={name:"Login",components:{IntegrationWhitelist:p},data(){return{dialogVisible:!1,apiRest:!1,stime:10,setI:"",handFlagf:"Activation",inp:null,apiState:3,apiKey:"",apiList:{mchNo:"",apiAccessPermissions:[]},setGenerated:!1,access:!1,collapse:!1,collapseFlg:["1"],tabsFlg:!1,whiteList:null,popoverFlg:!1,leaveTip:!1,subText:""}},async created(){await this.getIntegrationList()},async beforeRouteLeave(t,e,i){"leave"!==this.subText&&await(this.$refs.IntegrationWhitelist?.changeTabs(t.name)),"Save"!==this.subText&&i()},methods:{leaveFlg(t){this.subText=t},changeTabs(t){this.tabsFlg=t},async getIntegrationList(){const t=await this.$api.request("apikey.mchInfo",{},{},!0);8e7===t.code&&(this.whiteList=t.data,this.apiList.mchNo=t.data.mchNo,this.apiRest=t.data.apiKeyGenerated,this.setGenerated=t.data.apiKeyGenerated,t.data.apiAccessPermissions.sort(((t,e)=>t.valid<e.valid?1:t.valid>e.valid?-1:0)),t.data.apiAccessPermissions.find((t=>t.valid))||(this.access=!0),this.apiList.apiAccessPermissions=t.data.apiAccessPermissions)},timeClear(){10===this.stime&&(this.apiRest=!0),this.stime=10,clearInterval(this.setI)},async acknowledged(t){"Acknowledged"===t?(this.setGenerated=!0,this.dialogVisible=!1):"Confirm"===t&&(await this.generateApi(),this.apiRest=!this.apiRest)},restTime(){this.dialogVisible=!0,this.stime=9,this.setI=setInterval((()=>{this.stime=this.stime-1,this.stime<=0&&(clearInterval(this.setI),this.stime=10)}),1e3)},copy(t,e){const i=document.createElement("input");if(document.body.appendChild(i),i.value=t,i.select(),document.execCommand("Copy"),document.body.removeChild(i),e){this.popoverFlg=!0;let t=null;t=setTimeout((()=>{this.popoverFlg=!1,clearTimeout(t)}),1e3)}else this.$message({message:"Copied",iconClass:"mcico-success",customClass:"sty4-message",duration:3e3,offset:32,center:!0})},async generateApi(){const t=await this.$api.request("apikey.generate",{},{},!0);8e7===t.code&&(this.dialogVisible=!0,this.apiKey=t.data.apiKey)}}},m=v,g=(0,d.A)(m,s,a,!1,null,"6242093e",null),f=g.exports},56656:function(t,e,i){t.exports=i.p+"img/api-logo.94a7ca41.png"},10954:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADgAAAA4CAMAAACfWMssAAABTVBMVEXp7+////8YbHkne4YhdYEecn4bb3ths7oUaXYRZnNtvsYqfolwwclIm6Vqu8NnuMBSpK1PoapMnqdClZ8whI8tgYwLYG5Akpxktr1YqrI8j5o5jJc2iZResLh2x85brbV5ytFGmKIIXWtzxMskeIN9zdM0h5EkeIQOY3APZHGA0NZVp7AFWmiC09kzhpFer7hbrLVVp6+G1ttltr10xcuEwcdUpq+K2d/b6utnucFFmKJFmKExeIPi7e7b5udbk5y45Oeq4OOy3eCFz9Wvyc1qvMOFrrMCWGXj7u7d7O3R5ujI4uW04OO72dy+09adyM6iyM2Isbdrpa5BhI3U6eq83uCv29611NiWy9CCys+Lx8yTxMp5vcSWur9YoKlhmKA+ipWi4OTA3uCK0day0dWozNGny9F+ytCxy850xcx6wshyusKZvMBzrLRLh5F2LT96AAAB/ElEQVRIx+2RaVMaQRCGd8193zGXSTasCpkEWMHV4MYVQZKAhss7Xrkv9f9/tOeq0ncWHf2qz8x0N/3WUxSFc7qZ/FydPLZTrcV9H4hot1a1/p5aHL05QBSDbTrtOHrVgyhu/0nW1rsPj6S7bnqbmTSdTCadlkOanlqonWATve0rlrRB7F62JAKxzxoQL1oD4gVrQDxvDYi+P+wP0/UJ6jTwQvj8iaIiEM9ZA+I1k6FOpz9hDeIDZKvlEq0tIwDxPvLDFcwZAYhDWTqcLF2ad1xFR6xlyfIExGfALy3+xATEp8CSFpcwAfEJ0NRiExMQ+4ENLW5gAuI95Iv0WkYAoud5EyUq1DzeSxNz4t+QK3E8CkolD8QbBnUu1s09iHeRvCsIjQDEO4j6jU0jAPE2Mi/FeSMAMWSMBWEQhNQCFrCCq8iHLOAJY5RSYSA+Bka1uIoJiC8EeVnz1EclazqgpcxBHMjlcgMKmnVTgyjqgfj/kiVrIC4/4hQKotKRXY9UNMsgNm5p3tHhqKZmuvLzNwdYvcmZERWZoauSfw5Sv2rFuGOwcN2CBSeBxkrxbypVTClo3P+KVFcaTiLji5Xnh/B78b3paHW68rIHg9Ooofp9MIEKaInq7GukMgtaL7X8dj8jpNnyqTyiGZv6aClpdYwDmp069bVM2hknYA/WZahUAafxbwAAAABJRU5ErkJggg=="}}]);