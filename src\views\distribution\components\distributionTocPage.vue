<template>
  <div class="mui-fl-col web" >
    <div v-if="previewMode === 'web'" style="padding: 12px 34px; border-radius: 16px;" :style="{ background: !changemode ? '#FFFFFF' : '#141414' }">
      <div class="mui-fl-btw">
        <div class="t1 mui-fl-vert" :style="{ color: changemode ? '#FFFFFF' : '#000000' }">
          Token Distribution
        </div>
        <div class="mui-fl-vert" style="gap: 12px;">
          <div class="block1 t2" :style="{ background: hextoRgb(modeColor[2], 0.5), color: changemode ? '#FFFFFF' : '#000000' } ">
            {{ wallet.connectedAddress | formatPubKey(4, 6) }}
          </div>
          <div class="block1 t2 mui-fl-vert" :style="{ background: hextoRgb(modeColor[2], 0.5), color: changemode ? '#FFFFFF' : '#000000' }">
            <img style="width: 14px;height: 14px; margin-right: 6px;" src="@/assets/img/networkicon/moca.svg">
            {{ detail.tokenNetwork }}
            <i class="mico-arrow-bottom" style="color: #ABB8BA; font-size: 12px; margin: 2px 0 0 12px;"></i>
          </div>
          <div>
            <img class="default" src="@/assets/img/default.png">
          </div>
        </div>
      </div>
      <div class="mui-fl-hori" style="gap: 14px; margin-top: 14px;">
        <div class="mui-fl-col mui-fl-1">
          <div class="mui-fl-btw block2" style="height: 100%;" :style="{ background: hextoRgb(modeColor[1], 0.12) }">
            <div>
              <div class="mui-fl-vert">
                <div class="t1" :style="{ color: changemode ? '#FFFFFF' : '#000000' }">{{ detail.distributionName }}</div>
              </div>
              <div class="mui-fl-vert logo mgt-6">
                <i class="mico-web logo1" :style="{ color: hextoRgb(modeColor[0], 0.4) }"></i>
                <i class="mico-twitter" :style="{ color: hextoRgb(modeColor[0], 0.4) }"></i>
                <i class="mico-youtube" :style="{ color: hextoRgb(modeColor[0], 0.4) }"></i>
                <i class="mico-tg logo1" :style="{ color: hextoRgb(modeColor[0], 0.4) }"></i>
              </div>
            </div>
            <!-- <div>
              <div class="mui-fl-vert t3">
                <div style="font-weight: 400;">Claimable:</div>
                {{ detail.amount | toFormat }} {{ detail.tokenSymbol }}
              </div>
              <div class="mui-fl-end" style="margin-top: 6px;">
                <m-button class="sty2-button sty7-button" style="height: 34px; border: none;">Claim</m-button>
              </div>
            </div> -->
          </div>
          <div class="block2 mui-fl-btw mgt-20 mui-fl-vert" :style="{ background: `linear-gradient(270deg , ${hextoRgb(modeColor[2])}, ${hextoRgb(modeColor[1])})` }">
            <div class="t13" :style="{ color: changemode ? '#FFFFFF' : '#000000' }">
              {{ detail.amount | toFormat }}
              <span class="t12" style="margin-right: 4px;" :style="{ color: changemode ? '#FFFFFF' : '#000000' }">
                ${{ detail.tokenSymbol }}
              </span>
            </div>
            <div class="mgt-6">
              <m-button class="sty2-button sty7-button"
              style="height: 34px; border: none;"
              :style="{ background: hextoRgb(modeColor[0]), color: changemode ? '#FFFFFF' : '#002E33' }"
            >Claim</m-button>
            </div>
          </div>
          <div class="block2 mgt-20" style="height: 100%;" :style="{ background: hextoRgb(modeColor[1], 0.12) }">
            <div class="mui-fl-hori" style="position: relative;">
              <div class="countdownBox">
                <div class="countdown" :style="{
                  background: `linear-gradient(0deg , ${hextoRgb(modeColor[1])}, ${hextoRgb(modeColor[2])})`,
                }">
                  <div class="countdownAfter" :style="{
                  background: `linear-gradient(0deg , ${hextoRgb(modeColor[1])}, ${hextoRgb(modeColor[2])})`,
                }"></div>
                  <div class="active mui-fl-vert" :style="{ background: hextoRgb(modeColor[2]), color: changemode ? '#FFFFFF' : hextoRgb(modeColor[0]) }">
                    <div class="point"
                      :style="{ background: changemode ? '#FFFFFF' : hextoRgb(modeColor[1]),
                      }"
                    >

                    </div>
                    ACTIVE
                  </div>
                  <div class="time" :style="{ color: changemode ? '#FFFFFF' : '#000000' }">24:24:24:24</div>
                </div>
              </div>
              <div class="countdownBox1"></div>
              <div class="countdownBox2"></div>
            </div>
            <div class="t2 mui-fl-vert mgt-28" style="font-weight: 400;" :style="{ color: changemode ? 'rgba(255, 255, 255, 0.6)' : '#000000' }">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                <g clip-path="url(#clip0_11816_9731)">
                  <path d="M9.7616 3.4697C9.7616 4.54903 8.01164 5.42401 5.85297 5.42401C3.69429 5.42401 1.94434 4.54903 1.94434 3.4697M9.7616 3.4697C9.7616 2.39036 8.01164 1.51538 5.85297 1.51538C3.69429 1.51538 1.94434 2.39036 1.94434 3.4697M9.7616 3.4697V8.79965C9.7616 9.87898 8.01164 10.754 5.85297 10.754C3.69429 10.754 1.94434 9.87898 1.94434 8.79965V3.4697M9.7616 3.4697V5.24635M1.94434 3.4697V5.24635M9.7616 5.24635V7.023C9.7616 8.10233 8.01164 8.97731 5.85297 8.97731C3.69429 8.97731 1.94434 8.10233 1.94434 7.023V5.24635M9.7616 5.24635C9.7616 6.32568 8.01164 7.20066 5.85297 7.20066C3.69429 7.20066 1.94434 6.32568 1.94434 5.24635" stroke="#002E33" stroke-width="0.909645" stroke-linecap="round" stroke-linejoin="round"/>
                </g>
                <defs>
                  <clipPath id="clip0_11816_9731">
                    <rect width="11.3706" height="11.3706" fill="white" transform="translate(0.16748 0.449463)"/>
                  </clipPath>
                </defs>
              </svg>
              Amount Claimed/Total
            </div>
            <div class="mgt-6 t7" :style="{ color: changemode ? '#FFFFFF' : 'rgba(0, 0, 0, 1)' }">{{ Number(detail.amount * 0.7) | toFormat }}
              <span class="t8" :style="{ color: changemode ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)' }">/{{ detail.amount | toFormat }}</span>
            </div>
            <div class="progressBar mgt-12">
              <div class="progress" :style="{
                background: `linear-gradient(90deg, ${hextoRgb(modeColor[1])}, ${hextoRgb(modeColor[0])})`,
                // width: (Number(detail.claimedAmount) / Number(detail.amount) * 100) + '100px'
              }"></div>
              <img src="@/assets/img/ProgressBar.png" alt="">
              <!-- <img :style="{left: (Number(detail.claimedAmount) / Number(detail.amount) * 100) + 'px'}" src="@/assets/img/ProgressBar.png" alt=""> -->
            </div>
            <div class="mui-fl-btw mgt-28">
              <div>
                <div class="t9" :style="{ color: changemode ? '#FFFFFF' : 'rgba(0, 0, 0, 0.6)' }">
                  <i class="mico-clock2" style="margin-right: 2px;font-size: 12px;"></i>
                  Start Date:
                </div>
                <div class="t10 mgt-6" :style="{ color: changemode ? '#FFFFFF' : '#000000' }">
                  {{ detail.startDate | formatTimestampToUTC8(detail.displayTimezone) }}
                </div>
              </div>
              <div>
                <div class="t9" :style="{ color: changemode ? '#FFFFFF' : 'rgba(0, 0, 0, 0.6)' }">
                  <i class="mico-clock2" style="margin-right: 2px;font-size: 12px;"></i>
                  End Date:
                </div>
                <div class="t10 mgt-6" :style="{ color: changemode ? '#FFFFFF' : '#000000' }">
                  {{ detail.endDate | formatTimestampToUTC8(detail.displayTimezone) }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="mui-fl-col block2" :style="{ background: hextoRgb(modeColor[2], changemode ? 0.12 : 0.16) }">
          <div class="mui-fl-col">
            <div class="mui-fl-btw">
              <div class="mui-fl-vert t8" :style="{ color: changemode ? '#FFFFFF' : '#000000' }">
                <img class="kaiaImg" src="@/assets/img/networkicon/moca.svg" style="">
                {{ detail.tokenSymbol }}
              </div>
              <div class="mui-fl-vert" :style="{ color: changemode ? '#FFFFFF' : 'rgba(0, 0, 0, 0.6)' }">
                <img class="kaiaImg1 t3" src="@/assets/img/networkicon/moca.svg">
                {{ detail.tokenContractAddress | formatPubKey(4, 6) }}
                <i class="mico-copy" style="color: #33585C; font-size: 14px;margin-left: 4px;"></i>
              </div>
            </div>
            <div class="mgt-24">
              <img class="symbolUrl" :src="detail.symbolUrl" alt="">
            </div>
            <div class="t3 mgt-24" :style="{ color: changemode ? '#FFFFFF' : '#000000' }">Project Description</div>
            <div class="mgt-6 t11" :style="{ color: changemode ? '#FFFFFF' : 'rgba(0, 0, 0, 0.6)' }">
              {{ detail.description }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="mui-fl-btw">
        <div>9:14</div>
        <div>
          <img src="" alt="">
          <img src="" alt="">
          <img src="" alt="">
        </div>
      </div>
      <div class="mui-fl-btw">
        <div>
          <img src="" alt="">
          {{ detail.connectedAddress }}
        </div>
        <div>
          <img src="" alt="">
          Log out
        </div>
      </div>
      <div class="mui-fl-col">
        <div class="mui-fl-btw">
          <div>
            <img src="" alt="">
            $zkMe
          </div>
          <div><img src="@/assets/img/networkicon/moca.svg"></div>
        </div>
        <div>
          <img src="" alt="">
        </div>
        <div>
          <img src="" alt="">
          <span>100,000,000</span>
          <span>$zkMe</span>
        </div>
        <div>24:24:24:24</div>
        <div>
          <img src="" alt="">
          Amount Claimed/Total
        </div>
        <div>
          80,000,000 /100,000,000
        </div>
        <div>
          <img src="" alt="">
        </div>
        <div class="mui-fl-btw">
          <div>
            <img src="" alt="">
            End Date:
          </div>
          <div>2025-05-25, 12:00 UTC</div>
        </div>
        <div>
          <div>Description</div>
          <div>{{ detail.description }}</div>
        </div>
        <m-button>Claim</m-button>
      </div>
    </div>
  </div>
</template>

<script>
import { hextoRgb } from '@/utils/filters'
export default {
  name: 'DistributionTocPage',
  props: {
    previewMode: {
      type: String,
      required: ''
    },
    selectColorData: {
      type: String,
      required: ''
    },
    changemode: {
      type: Number,
      required: 0
    }
  },
  data () {
    return {
      block1: null,
      block2: null,
      hextoRgb: hextoRgb
    }
  },
  computed: {
    detail () {
      return this.$store.state.distribution.detail
    },
    wallet () {
      return this.$store.state.wallet
    },
    modeColor () {
      const color = this.selectColorData.split(',')
      if (!this.changemode) {
        return [color[0], color[1], color[2]]
      }
      return [color[3], color[4], color[5]]
    }
  },
  watch: {
    previewMode () {
    }
  },
  created () {
  },
  mounted () {
  },
  methods: {
  }
}
</script>

<style lang="scss" src="@/assets/css/views/tokenDistribution/_distributionTocPage.scss" scoped></style>
