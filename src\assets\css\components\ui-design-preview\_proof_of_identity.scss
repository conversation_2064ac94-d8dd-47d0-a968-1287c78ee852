.zkme {
  width: 288px;
  height: 180px;
  background: url("~@/assets/img/popup/poc-hero.png") no-repeat;
  background-size: 288px;
  margin-top: 20px;
}

.zkme.dark {
  background: url("~@/assets/img/popup/poc-hero-dark.png") no-repeat;
  background-size: 288px;
}

.t1 {
  height: 60px;
  font-weight: 700;
  font-size: 18px;
  line-height: 60px;
  color: rgb(var(--color-text-6));
}

.c1 {
  font-size: 14px;
  line-height: 20px;
  color: rgba(var(--vt-c-white-other-3-rgb), 0.6);
  margin-top: 28px;
}

.list {
  width: 100%;
  margin: 32px auto auto;
  padding-bottom: 100px;
}

.list-item:nth-child(1) {
  margin-right: 10px;
  margin-bottom: 6px;
}

.icon {
  width: 20px;
  height: 20px;
  background: var(--vt-c-primary-1);
  font-size: 12px;
  border-radius: 50%;
  color: var(--vt-c-primary-3);
}

.icon-finished {
  opacity: 0.2;
}

.spacer {
  height: 20px;
  width: 1px;
  background: var(--vt-c-primary-3);
  margin-top: 6px;
}

.title-box {
  width: 100%;
}

.title {
  width: calc((100% - 30px));
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  /* color: var(--color-text-4); */
  color: rgb(var(--color-text-6));
}

.title-finished {
  opacity: 0.6;
}

.cont {
  font-size: 12px;
  line-height: 16px;
  color: rgba(var(--vt-c-primary-1-rgb), 0.8);
  margin-top: 4px;
}

.cont-finished {
  opacity: 0.4;
}

.btn-box {
  background: transparent;
}

/* .btn {
    width: 255px;
    height: 48px;
    border: none;
    background: var(--color-text-4);
    color: var(--vt-c-white-other);
    font-weight: 500;
    font-size: 16px;
    line-height: 48px;
    text-align: center;
    border-radius: 48px;
  } */
.user-tips {
  margin-top: 20px;
  padding: 10px;
  border-radius: 12px;
  background: var(--vt-c-secondary-5);
  color: rgba(var(--vt-c-white-other-3-rgb), 0.6);
  font-size: 12px;
  line-height: 16px;
}

.user-tips img {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}