<template>
  <m-dialog :class="['sty3-dialog', !connection && 'sty4-dialog', errorConnectWallet && 'sty5-dialog']" :close-on-click-modal="false" :close-on-press-escape="false"
    :visible.sync="connectDialogOpened" width="510px" height="480px" :show-close="false">
    <template slot="title">
      <i v-if="connection" @click="$emit('close') " class="mico-drawer-close"></i>
      <i v-else @click="back" class="mico-Fallback"></i>
      <div class="conn-header">
        <h4 v-if="connection">Select a wallet to connect</h4>
        <h4 v-else>MetaMask</h4>
      </div>
    </template>
    <template>
      <ul v-if="connection" class="options">
        <li class="mui-fl-col mui-fl-central taplight" v-for="item of walletList" :key="item.id"
          :style="{ background: item.bgColor }" @click="connectWallet(item)">
          <img class="option-icon" :src="item.logo" />
          <p>{{ item.name }}</p>
        </li>
      </ul>
      <div v-else class="metamaskLogo mui-fl-central">
        <div class="metamaskLogoBox">
          <img class="img1" src="../../assets/img/wallets/evm_metamask_logo.png" alt="">
          <img v-if="errorConnectWallet" class="img2" src="../../assets/img/wallets/errorConnect.png" alt="">
        </div>
      </div>
    </template>
    <div v-if="connection" class="footText1">
      By connecting you agree to our
      <a href="https://www.zk.me/terms" target="_blank" class="t1">User Agreement</a> and
      <a href="https://www.zk.me/app-privacy-policies" target="_blank" class="t1">Privacy Policy.</a>
    </div>
    <div v-else class="footText">
      <div class="t1">
        {{ errorConnectWallet ? 'Request Cancelled' : 'Requesting Connection...' }}
      </div>
      <div>
        {{ errorConnectWallet ? 'You cancelled the request. Please try again.' :
        'Open the MetaMask browser extension to connect your wallet.' }}
      </div>
      <m-button v-if="errorConnectWallet" class="sty2-button sty6-button" @click="connectWallet('')">Try again</m-button>
    </div>
  </m-dialog>
</template>

<script>
import { EVM_WALLET_LIST } from '@/utils/config.js'
export default {
  name: 'ConnectWalletDialog',
  props: {
    connectDialogOpened: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      walletList: EVM_WALLET_LIST,
      walletConnecting: true,
      connection: true,
      errorConnectWallet: false,
      walletName: ''
    }
  },
  watch: {
    '$store.state.wallet.connectedAddress' (val) {
      if (!val) {
        this.connection = true
      }
    }
  },
  async mounted () {
    this.$store.dispatch('connectWallet', {
      chain: 'MetaMask',
      type: ''
    })
  },
  methods: {
    back () {
      this.connection = true
      this.errorConnectWallet = false
    },
    async connectWallet (item) {
      if (!item) item = this.walletName
      this.walletName = item
      this.connection = false
      const errorConnectWallet = await this.$store.dispatch('connectWallet', {
        chain: 'MetaMask',
        type: 'click'
      })
      if (errorConnectWallet) {
        this.errorConnectWallet = true
      } else {
        this.errorConnectWallet = false
      }
    }
  }
}
</script>

<style lang="scss" scoped src="@/assets/css/components/_connectWalletDialog.scss"></style>
