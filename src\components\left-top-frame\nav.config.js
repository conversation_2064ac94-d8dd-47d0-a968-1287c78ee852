// 用于与服务端返回的权限数据相匹配动态生成导航

export default () => [
  {
    level: 1,
    any: 1, // 表示无需权限随意访问
    // power: 'dashboard_read',
    path: '/dashboard',
    name: 'Dashboard',
    icon: 'mcico-dashboard',
    activeIcon: 'mcico-active-dashboard',
    child: [
      {
        level: 2,
        any: 1, // 表示无需权限随意访问
        // power: 'account/user/list',
        name: 'User List',
        path: '/dashboard'
      },
      {
        level: 2,
        any: 1, // 表示无需权限随意访问
        // power: 'account/user/list',
        name: 'Statistics (Beta)',
        path: '/statistics'
      }
    ]
  },
  {
    level: 1,
    any: 1, // 表示无需权限随意访问
    // power: 'dashboard_read',
    name: 'Configuration',
    path: '/zk-kyc',
    icon: 'mcico-activation',
    activeIcon: 'mcico-active-activation'
    // child: [
    //   {
    //     level: 2,
    //     any: 1, // 表示无需权限随意访问
    //     // power: 'account/user/list',
    //     name: 'Anti-bot/sybil',
    //     path: '/anti-bot'
    //   },
    //   {
    //     level: 2,
    //     any: 1, // 表示无需权限随意访问
    //     // power: 'account/user/list',
    //     name: 'zkKYC',
    //     path: '/zk-kyc'
    //   }
    //   // {
    //   //   level: 2,
    //   //   any: 1, // 表示无需权限随意访问
    //   //   // power: 'account/user/list',
    //   //   name: 'newZkKYC',
    //   //   path: '/new-zkkyc'
    //   // },
    //   // {
    //   //   level: 2,
    //   //   any: 1, // 表示无需权限随意访问
    //   //   // power: 'account/user/list',
    //   //   name: 'zkGeolocation',
    //   //   path: '/zk-geo'
    //   // }
    // ]
  },
  {
    level: 1,
    any: 1, // 表示无需权限随意访问
    // power: 'dashboard_read',
    // path: '/integration',
    name: 'Token Distribution',
    icon: 'mcico-zkKYC',
    activeIcon: 'mcico-active-activation',
    child: [
      {
        level: 2,
        any: 1, // 表示无需权限随意访问
        // power: 'account/user/list',
        name: 'Plans',
        path: '/distributionList'
      },
      {
        level: 2,
        any: 1, // 表示无需权限随意访问
        // power: 'account/user/list',
        name: 'Credential Base',
        path: '/credentialBase'
      }
    ]
  },
  {
    level: 1,
    any: 1, // 表示无需权限随意访问
    // power: 'dashboard_read',
    // path: '/integration',
    name: 'Integration',
    icon: 'mcico-zkKYC',
    activeIcon: 'mcico-active-zkKYC',
    child: [
      {
        level: 2,
        any: 1, // 表示无需权限随意访问
        // power: 'account/user/list',
        name: 'Setting',
        path: '/integration'
      },
      {
        level: 2,
        any: 1, // 表示无需权限随意访问
        // power: 'account/user/list',
        name: 'UI Design',
        path: '/uiDesign'
      }
    ]
  },
  // {
  //   level: 1,
  //   any: 1, // 表示无需权限随意访问
  //   // power: 'dashboard_read',
  //   path: '/zk-kyc',
  //   name: 'zkKYC',
  //   icon: 'mcico-zkKYC',
  //   activeIcon: 'mcico-active-zkKYC'
  // },
  // {
  //   level: 1,
  //   any: 1, // 表示无需权限随意访问
  //   // power: 'dashboard_read',
  //   path: '/activation',
  //   name: 'Activation',
  //   icon: 'mcico-activation',
  //   activeIcon: 'mcico-active-activation'
  // },
  {
    level: 1,
    any: 1, // 表示无需权限随意访问
    // power: 'account',
    name: 'Resources',
    icon: 'mcico-developers-center',
    activeIcon: 'mcico-active-developers-center',
    child: [
      {
        level: 2,
        any: 1, // 表示无需权限随意访问
        // power: 'account/user/list',
        name: 'API',
        path: '/api'
      },
      {
        level: 2,
        any: 1, // 表示无需权限随意访问
        // power: 'account/user/list',
        name: 'Support',
        path: '/support'
      }
    ]
  }
]
