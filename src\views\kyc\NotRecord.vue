<template>
  <div>
    <div class="kycmain mui-fl-hori">
      <div class="whole">
        <div class="kyctitle" v-if="entry.Exhibition" style="margin-bottom: 32px">
          😊 Start creating your first KYC program!
        </div>
        <div v-for="(i, d) of stepslist" :key="d" :class="[i.status === 1 ? 'wholecolor' : '']">
          <div class="mui-fl-vert">
            <div class="punctuation"></div>
            <div class="level">{{i.levelName}} - {{i.levelDescription}}</div>
            <div v-if="i.status === 1" class="cs">Coming soon</div>
          </div>
          <div class="contentboxbored">
            <div class="contentbox">
              <div v-for="(n, d) of i.adminKycPropertyBoList" :key="d" :class="['content', !i.status && !n.status && n.disabled ? 'switch_disabled' : '']">
                <div class="mui-fl-vert">
                  <m-popover
                    :open-delay="1000"
                    popper-class="NotRecord_popover"
                    :disabled="(!n.disabled || !n.status)"
                    placement="top"
                    trigger="manual"
                    v-model="n.visible"
                    content="Cannot be closed">
                    <template #reference>
                      <span @mouseenter="trigger(n, 'mouseenter')" @click="trigger(n, 'click')" @mouseleave="trigger(n, 'mouseleave')">
                        <m-switch
                          slot="reference"
                          class="switch paddings"
                          :width="37"
                          active-color="#005563"
                          inactive-color="#E6EDED"
                          v-model="n.status"
                          @change="switchbut(n, i)"
                          :disabled="n.disabled"
                        />
                      </span>
                    </template>
                  </m-popover>
                  <span class="title">{{n.kycVerifyProperty}}<span class="additionalTitle">{{n.additionalTitle}}</span></span>
                </div>

                <div class="extend">
                  <div v-if="n.kycVerifyProperty === 'Age' && n.status" class="extendtitle paddings">Passable age:</div>
                  <div v-if="n.kycVerifyProperty === 'ID document' && n.status" class="extendtitle paddings mui-fl-vert">
                    <m-popover
                      width="180"
                      placement="top"
                      trigger="hover"
                      popper-class="NotRecord_popover Types_popover"
                      content="The types of documents that can be verified are automatically matched by country.">
                      <i class="mcico-doubt" slot="reference"></i>
                    </m-popover>
                    Types Include:
                  </div>
                  <div v-if="n.kycVerifyProperty === 'Citizenship' && n.status" class="extendtitle paddings">Non-passable citizenship:</div>

                  <div v-if="n.kycVerifyProperty === 'Age' && n.status">
                    <span class="extendcenter paddings">Over</span>
                    <m-dropdown trigger="click" @command="handleCommand(n, $event)">
                      <m-button class="dropdownbut">
                        <span>{{n.age || 18}}</span> <span><i class="mico-fold"></i></span>
                      </m-button>
                      <m-dropdown-menu slot="dropdown" class="extendlist">
                        <m-dropdown-item v-for="(f, d) of n.value" :key="d" :command="f" :class="[(n.age || '18') === f.value ? 'dropdownindex' : '']">{{f.value}}</m-dropdown-item>
                      </m-dropdown-menu>
                    </m-dropdown>
                  </div>

                  <div v-if="n.kycVerifyProperty === 'ID document' && n.status" class="mui-flex idtext paddings">
                    <span v-for="(f, d) of n.value" :key="d" class="mui-fl-wrap mui-fl-vert">
                      <div>{{f.value}}</div>
                      <div class="division" style="margin: 0 16px;" v-if="(n.value.length -1) - d"></div>
                      <!-- <m-button :class="['Documentbut', f.idchek === f.id ? 'Documentbutborder' : '']" @click="document(f, )">{{f.value}}</m-button>
                      <div class="icon" v-if="f.idchek === f.id">
                        <i class="mcico-a-Cornermarker"></i>
                      </div> -->
                    </span>
                  </div>

                  <div id="warningtip" class="warning" v-if="n.kycVerifyProperty === 'Citizenship' && select && n.status"><i class="mico-warning"></i>Select at least one.</div>

                  <div v-if="n.kycVerifyProperty === 'Citizenship' && n.status" class="paddings">
                    <m-checkbox v-for="(f, d) of n.value" :key="d" class="extendcheckbox" :label="0" @change="checkbox(f, n)" :value="f.citchek">{{f.value}}</m-checkbox>
                  </div>
                  <m-divider v-if="i.levelName === 'Lvl.1' && (i.adminKycPropertyBoList.length - 1) - d && n.status"></m-divider>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="kycbut" v-if="stepslist.length">
          <m-button class="kycSave" @click="save">Save</m-button>
          <m-button class="kycCancel" @click="routerleave()" v-if="!entry.Exhibition">Cancel</m-button>
        </div>
      </div>
    </div>

    <!-- 二次确认弹窗 -->
    <m-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="Confirm your KYC configuration"
      :visible.sync="dialogTableVisible"
      class="dialog"
      @close="dialoginp = '', dialogwarning = false">
      <div class="mui-flex dialog_padding">
        <div>
          <div class="dialog_Name_nb">Program Name</div>
          <div>
            <MuiInput :closeflg="true" @input="input" :error="dialogwarning" v-model="dialoginp" class="inp" />
          </div>
          <div class="dialog_warning" v-if="dialogwarning && dialoginp">Numbers or letters only, no more than 50 characters.</div>
          <div class="dialog_warning" v-else-if="dialogwarning && !dialoginp">Please enter your program name.</div>
        </div>
        <!-- <div>
          <div class="dialog_Name_nb no">Program No. </div>
          <div class="nb"><span class="nodivider">-</span>001</div>
        </div> -->
      </div>
      <div class="Sybil" v-if="confirmKyc.switchbut.length"><m-divider class="Sybil" content-position="left">Lvl.0 - Sybil Protection</m-divider></div>
      <div class="Facial dialog_padding"><div v-for="d of confirmKyc.switchbut" :key="d">{{d}}</div></div>
      <div class="Sybil" v-if="confirmKyc.age || confirmKyc.checkbox.length || confirmKyc.document.length"><m-divider class="Sybil" content-position="left">Lvl.1 - Simple Due Diligence</m-divider></div>

      <div class="Simplelist mui-flex dialog_padding"  v-if="confirmKyc.age || confirmKyc.checkbox.length || confirmKyc.document.length">

        <div class="personal">
          <div class="l cet" v-if="confirmKyc.age">Age</div>
          <div class="l cet" v-if="confirmKyc.document.length">ID document</div>
          <div class="l cet" v-if=" confirmKyc.checkbox.length">Non-passable citizenship</div>
        </div>

        <div class="information">
          <div class="r cet" v-if="confirmKyc.age">Over {{confirmKyc.age}}</div>

          <div class="r mui-fl-wrap" v-if="confirmKyc.document.length">
            <div class="mui-flex cet" v-for="(f, d) of confirmKyc.document" :key="d">
              {{f}}
              <div class="division" v-if="((confirmKyc.document.length -1) - d)"></div>
            </div>
          </div>

          <div class="r rlist mui-fl-wrap" v-if=" confirmKyc.checkbox.length">
            <div :style="{marginBottom: confirmKyc.checkbox.length === 3 && d !== 2 ? '4px' : ''}" class="mui-flex cet" div v-for="(f, d) of confirmKyc.checkbox" :key="d">
              <div>{{f}}</div>
              <div class="division" v-if="(confirmKyc.checkbox.length -1) - d"></div>
            </div>
          </div>

        </div>
      </div>

      <div class="dialog_button mui-fl-end">
        <m-button class="kycSave" @click="dialogTableVisible = false">Cancel</m-button>
        <m-button style="margin-left: 12px" class="kycCancel" @click="confirm">Confirm</m-button>
      </div>
    </m-dialog>

    <!-- 离开页面 -->
    <KycWarning model="Stay" :warningtip="warningtip" @Leave="Leave"></KycWarning>

  </div>
</template>
<script>
import { nextTick } from 'vue'
import KycWarning from '@/components/kyc_warning/KycWarning.vue'
import MuiInput from '@/components/mui-input/MuiInput.vue'
export default {
  components: { KycWarning, MuiInput },
  props: {
    entry: {
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    },
    changedate: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      time: '',
      select: false,
      updatelist: 0,
      warningtip: false,
      routerleavestatue: false,
      dialogTableVisible: false,
      dialogwarning: false,
      checked1: '',
      checked2: '',
      dialoginp: '',
      stepslist: [],
      confirmKyc: {
        switchbut: [],
        age: '',
        document: [],
        checkbox: []
      },
      pushdate: [],
      cachepushdate: []
    }
  },
  async mounted () {
    this.updatelist = Object.keys(this.entry.data).length
    if (!this.updatelist) {
      await this.getkycprlist()
    } else {
      await this.inheritlist()
    }
    this.cachepushdate = this.pushdate
    this.$emit('cachedate', this.cachepushdate, this.pushdate)
  },
  methods: {
    inheritlist () {
      this.entry.data.kycList.forEach(v => {
        v.adminKycPropertyBoList.forEach(f => {
          f.visible = false
          f.disabled = f.status === 2 || f.status === 1
          f.status = !!f.value.filter(x => x.isSelect).length
          if (v.levelDescription === 'Liveness check' && f.value[0].isSelect) {
            this.confirmKyc.switchbut.push(f.kycVerifyProperty)
            this.pushdate.push(f.value[0].id)
          }
          if (f.kycVerifyProperty === 'Age') {
            if (f.status) {
              const isSelect = f.value.find(x => x.isSelect)
              if (isSelect) {
                this.pushdate.push(isSelect.id)
                this.confirmKyc.age = isSelect.value
                f.age = isSelect.value
              }
            } else {
              this.confirmKyc.age = ''
            }
          }
          if (f.kycVerifyProperty === 'ID document' && f.status) {
            f.value.map(x => this.confirmKyc.document.push(x.value))
            f.value.map(x => this.pushdate.push(x.id))
          }
          if (f.kycVerifyProperty === 'Citizenship') {
            if (f.status) {
              const isSelect = f.value.filter(x => x.isSelect)
              if (isSelect.length) {
                this.confirmKyc.checkbox = []
                isSelect.forEach(t => {
                  t.citchek = t.isSelect
                  this.pushdate.push(t.id)
                  this.confirmKyc.checkbox.push(t.value)
                })
              }
            }
          }
        })
      })
      this.stepslist = this.entry.data.kycList
    },
    trigger (n, ev) {
      if (n.disabled && n.status) {
        if (ev === 'mouseenter') {
          this.time = setTimeout(() => {
            n.visible = true
          }, 1000)
        } else if (ev === 'click') {
          n.visible = true
        }
        if (ev === 'mouseleave') {
          n.visible = false
          window.clearInterval(this.time)
        }
      }
    },
    async getkycprlist () {
      const rp = await this.$api.request('kyc.getKycPropertyList')
      if (rp.code === 80000000) {
        rp.data.forEach(v => {
          v.adminKycPropertyBoList.forEach(f => {
            f.visible = false
            f.disabled = f.status === 2 || f.status === 1
            f.status = (f.status === 0 || f.status === 2) && f.status !== 3 && f.status !== 1
            if (v.levelDescription === 'Liveness check' && f.status) {
              this.pushdate.push(f.value[0].id)
              this.confirmKyc.switchbut.push(v.levelDescription)
            }
            if (f.kycVerifyProperty === 'Age') {
              if (f.status) {
                this.pushdate.push(f.value[0].id)
              }
              f.age = ''
            }
            if (f.kycVerifyProperty === 'ID document' && f.status) {
              f.value.map(x => this.confirmKyc.document.push(x.value))
              f.value.map(x => this.pushdate.push(x.id))
            }
            // if (f.kycVerifyProperty === 'Citizenship') {
            //   f.value.map(x => (x.citchek = x.value === 'crypto restriction legislation'))
            //   if (f.status) {
            //     this.pushdate.push(f.value.filter(x => x.value === 'crypto restriction legislation')[0]?.id)
            //     this.confirmKyc.checkbox.push('crypto restriction legislation')
            //   }
            // }
          })
        })
        this.stepslist = rp.data
      }
    },
    Leave (val) {
      if (val === 'Back') {
        this.cachepushdate = this.pushdate
        this.$emit('cachedate', this.cachepushdate, this.pushdate)
        this.$emit('Results', this.routerleavestatue ? 'gobacksearch' : '')
        return true
      } else {
        this.routerleavestatue = false
      }
      this.warningtip = false
    },
    handleCommand (n, val) {
      n.age = val.value
      this.confirmKyc.age = val.value
    },
    checkbox (data, index) {
      if (data.citchek) {
        const indexOfname = this.confirmKyc.checkbox.indexOf(data.value)
        if (indexOfname !== -1) {
          this.confirmKyc.checkbox.splice(indexOfname, 1)
        }
        data.citchek = false
      } else {
        this.select = false
        data.citchek = true
        this.confirmKyc.checkbox = index.value.filter(x => x.citchek).map(x => x.value)
      }
    },
    // document (data) {
    //   if (data.idchek === data.id) {
    //     data.idchek = ''
    //     const indexOfname = this.confirmKyc.document.indexOf(data.value)
    //     const indexOfid = this.pushdate.indexOf(data.id)
    //     if (indexOfname !== -1) {
    //       this.confirmKyc.document.splice(indexOfname, 1)
    //     }
    //     if (indexOfid !== -1) {
    //       this.pushdate.splice(indexOfid, 1)
    //     }
    //   } else {
    //     data.idchek = data.id
    //     this.pushdate.push(data.id)
    //     this.confirmKyc.document.push(data.value)
    //   }
    // },
    switchbut (d, i) {
      if (!d.status && d.kycVerifyProperty === 'Age') {
        this.confirmKyc.age = ''
      } else if (d.status && d.kycVerifyProperty === 'Age') {
        d.age = '18'
        d.value.forEach(v => {
          v.isSelect = false
          if (v.value === '18') {
            v.isSelect = true
          }
        })
        this.confirmKyc.age = '18'
      }

      if (d.status && i.levelName === 'Lvl.0') {
        this.confirmKyc.switchbut.push(d.kycVerifyProperty)
      } else {
        const indexOfname = this.confirmKyc.switchbut.indexOf(d.kycVerifyProperty)
        if (indexOfname !== -1) {
          this.confirmKyc.switchbut.splice(indexOfname, 1)
        }
      }

      if (d.status && d.kycVerifyProperty === 'ID document') {
        d.value.map(x => this.confirmKyc.document.push(x.value))
      } else if (!d.status && d.kycVerifyProperty === 'ID document') {
        this.confirmKyc.document = []
      }

      if (d.status && d.kycVerifyProperty === 'Citizenship') {
        this.select = false
        d.value.forEach(v => {
          v.citchek = false
        })
      } else if (!d.status && d.kycVerifyProperty === 'Citizenship') {
        this.confirmKyc.checkbox = []
      }
    },

    filterdate () {
      const age = this.stepslist[1].adminKycPropertyBoList[0].value.filter(x => this.pushdate.includes(x.id))
      const aget = this.stepslist[1].adminKycPropertyBoList[0].value.filter(x => x.value === this.confirmKyc.age)
      if (this.confirmKyc.age) {
        if (aget.length) {
          this.pushdate = this.pushdate.filter(x => age[0]?.id !== x)
          this.pushdate.push(aget[0].id)
        } else if (age.length) {
          this.pushdate = this.pushdate.filter(x => age[0]?.id !== x)
        }
      } else if (!this.confirmKyc.age && age.length) {
        this.pushdate = this.pushdate.filter(x => age[0]?.id !== x)
      }
      this.stepslist.forEach(v => {
        if (v.levelName === 'Lvl.0') {
          v.adminKycPropertyBoList.forEach(y => {
            const id = y.value.map(a => a.id)
            if (y.status) {
              this.pushdate = [...new Set(this.pushdate.concat(id))]
            } else {
              const did = new Set(this.pushdate)
              id.map(x => did.delete(x))
              this.pushdate = [...did]
            }
          })
        }
        if (v.levelName === 'Lvl.1') {
          v.adminKycPropertyBoList.forEach(y => {
            if (y.kycVerifyProperty === 'ID document') {
              const id = y.value.map(a => a.id)
              if (y.status) {
                this.pushdate = [...new Set(this.pushdate.concat(id))]
              } else {
                const did = new Set(this.pushdate)
                id.map(x => did.delete(x))
                this.pushdate = [...did]
              }
            }
            if (y.kycVerifyProperty === 'Citizenship') {
              const id = y.value.filter(a => a.citchek).map(x => x.id)
              const allid = y.value.map(a => a.id)
              const did = new Set(this.pushdate)
              allid.map(x => did.delete(x))
              this.pushdate = [...did]
              if (y.status) {
                this.pushdate = [...new Set(this.pushdate.concat(id))]
              }
            }
          })
        }
      })
      this.$emit('cachedate', this.cachepushdate, this.pushdate)
    },
    input () {
      const zg = /^[0-9a-zA-Z]*$/

      if (this.dialoginp.length > 50 || !this.dialoginp.length || !zg.test(this.dialoginp)) {
        this.dialogwarning = true
      } else {
        this.dialogwarning = false
      }
    },
    async save () {
      this.filterdate()
      if (!this.confirmKyc.checkbox.length) {
        this.select = true
      }
      if (this.updatelist && !this.entry.copy) {
        this.dialoginp = this.entry.data.programName
      }
      await nextTick()
      const warning = document.getElementsByClassName('warning')
      if (!warning.length) {
        this.dialogTableVisible = true
      } else {
        this.dialogTableVisible = false
      }
      if (warning.length) {
        const nd = document.getElementById('warningtip')
        if (nd) {
          nd.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' })
        }
      }
    },
    async routerleave (val) {
      await this.filterdate()
      this.routerleavestatue = true
      if (!this.changedate) {
        if (val === 'path') {
          this.routerleavestatue = false
        }
        this.warningtip = true
      } else {
        this.$emit('Results', 'gobacksearch')
        // this.$emit('gobacksearch', false)
      }
    },
    async confirm () {
      this.input()
      if (!this.dialogwarning) {
        let data = {}
        if (this.updatelist && !this.entry.copy) {
          data = {
            id: this.entry.data.id,
            ids: this.pushdate,
            programName: this.dialoginp,
            status: 2,
            userId: new Date().getFullYear()
          }
        } else {
          data = {
            ids: this.pushdate,
            programName: this.dialoginp,
            status: 2,
            userId: new Date().getFullYear()
          }
        }
        const rp = await this.$api.request(this.updatelist && !this.entry.copy ? 'kyc.updateKycProgram' : 'kyc.createKycProgram', data)
        if (rp.code === 80000000) {
          this.dialogTableVisible = false
          this.$message({
            customClass: 'kycmessage',
            type: 'success',
            iconClass: 'mcico-success',
            message: 'Save Success'
          })
          if (this.updatelist && !this.entry.copy) {
            this.$emit('Results', 'update', this.entry.data.id)
          } else {
            this.$emit('Results', 'create')
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" src="@/assets/css/views/kycpage/_not_recorded.scss" Scoped></style>
