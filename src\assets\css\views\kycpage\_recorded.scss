.top_function button > span, .recorded_table .has-gutter, .personal>.l, .information>.r, .drawer_records, .drawer_step>.drawer_text, .dialog>.dialog_title, .recdialog .dialog_title{
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #002E33;
}
.program, .<PERSON>ybil>.el-divider__text, .program_copy{
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #738C8F;
}
.top_function {
  margin: 32px 0 16px;
  .el-button {
    color: #002E33;
    border: none;
    background-color: #A9E1D3;
    border-radius: 26px;
    padding: 8px 20px;
    height: 36px;
    span {
      font-weight: 500;
    }
  }
  .inp {
    width: 280px;
  }
}
.mico-creat {
  font-size: 16px;
}
.extendcheckbox {
  margin-right: 20px;
  .el-checkbox__inner {
    border-color: #B3C0C2;
  }
  .is-checked > .el-checkbox__inner::after{
    display: none;
  }
  .is-checked > .el-checkbox__inner::before{
    color: #FFFFFF;
    font-family: "iconfont";
    content: "\e60d";
    transform: scale(0.4);
    display: block;
    height: 11px;
    margin-right: 8px;
  }
  .is-focus > .el-checkbox__inner{
    border-color: #B3C0C2;
  }
  .is-checked > .el-checkbox__inner{
    background-color: #005563;
    border-color: #005563;
  }
  .el-checkbox__label {
    padding-left: 8px;
  }
}
.redivision {
  height: 20px;
  border-left: 1px solid #F0F0F0;
  margin: 0 32px;
}
.divider {
  margin: 16px 0 32px;
  background-color: #F0F0F0;
}
// .recorded_pagination {
//   margin-top: 24px;
// }
.FN {
  margin-left: 0px;
}
.recorded_drawer {
  .drawer_end {
    position: absolute;
    right: 0;
    bottom: 0;
  }
  .drawer_margin {
    margin: 0 20px 0 24px;
  }
  .lv0 {
    color: #002E33;
    font-weight: 500;
    padding: 8px 0;
  }
  .program {
    .m {
      border-left: 1px solid #CEDBDB;
      margin: 0 8px;
    }
    .l {
      font-weight: 450;
      line-height: 18px;
      color: #B3C0C2;
      cursor: pointer;
    }
    .cop {
      opacity: 0.5;
    }
  }
  .Sybil>.el-divider__text {
    font-size: 12px;
    line-height: 16px;
  }
  .recorded_button {
    font-weight: 700;
    padding: 9px 16px;
    width: auto;
    height: 36px;
    color: #005563;
    background-color: #E6EDED;
    border-radius: 26px;
    margin: 20px 0 36px 0;
    border: none;
  }
  .drawer_records {
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    margin-bottom: 20px;
  }
  .dialog_button_cancel {
    background: #F7F7F7;
    color: #33585C;
  }
  .dialog_button_confirm {
    background-color: #A9E1D3;
    color: #002E33;
    margin-left: 12px;
  }
  .dialog_button_cancel, .dialog_button_confirm {
    padding: 8px 32px;
  }
  .cancel_button {
    padding: 14px 40px;
    width: auto;
    height: 48px;
    color: #33585C;
    background-color: #F7F7F7;
  }
  .freebut {
    color: #A9E1D3;
    background-color: #002E33;
    margin-left: 12px;
  }
  .drawer_step {
    margin-bottom: 36px;
    .boolball {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #6CCC81;
      margin-right: 8px;
    }
    .drawer_division {
      padding: 10px 0;
      min-width: 1px;
      margin-left: 4px;
      border-left: 2px solid #F2F7F7;
      &.textma{
        padding: 20px 0;
      }
      .text {
        cursor: pointer;
        margin-left: 12px;
        color: #6684BF;
        .mico-fold-rotate {
          transform: rotate(-180deg);
        }
      }
      .foldtext {
        line-height: 18px;
        margin-top: 10px;
        color: #B3C0C2;
      }
    }
    .drawer_text_hand {
      font-weight: 500;
      font-size: 16px;
      line-height: 20px;
      margin-right: 4px;
    }
    .drawer_text {
      line-height: 20px;
    }
    .Apply {
      .boolball {
        background-color: #6CCC81;
      }
      color: #6CCC81;
    }
    .Created {
      .boolball {
        background-color: #6684BF;
      }
      color: #6684BF;
    }
    .Expired {
      .boolball {
        background-color: #B3C0C2;
      }
      color: #B3C0C2;
    }
  }
}
.Apply1 {
  .tableballcolor {
    background-color: #6CCC81;
  }
  color: #6CCC81;
}
.Created1 {
  .tableballcolor {
    background-color: #6684BF;
  }
  color: #6684BF;
}
.Expired1 {
  .tableballcolor {
    background-color: #B3C0C2;
  }
  color: #B3C0C2;
}
.Apply1, .Created1, .Expired1 {
  .ball {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
  }
}
.recdialog {
  .el-dialog__header {
    padding: 24px;
    button {
      top: 24px;
      right: 24px;
      i {
        color: #738C8F;
        font-size: 20px;
      }
    }
    .el-dialog__title {
      font-weight: 400;
      font-size: 18px;
      line-height: 24px;
      color: #002E33;
    }
  }
  .el-dialog__body {
    padding: 0 24px;
  }
  .dialog_title {
    width: 550px;
    line-height: 18px;
    margin-bottom: 24px;
  }
}
.kycProgramId {
  position: relative;
}
.program_copy {
  padding: 0 4px;
  position: absolute;
  z-index: 10;
  color: #005563;
  top: -1px;
  right: 0;
  line-height: 20px;
  background: #f7f7f7;
  box-shadow: -10px 0px 10px #F7F7F7;
  cursor: pointer;
}
.Copied {
  color: rgba($color: #005563, $alpha: 0.5);
}
.recorded_but {
  width: 166px;
  height: 32px !important;
  font-weight: 400;
  background-color: #FFFFFF !important;
  color: #B3C0C2 !important;
  margin: 0 !important;
  .mico-fold {
    line-height: 15px;
    margin-left: 12px;
    font-size: 12px;
  }
}
.recorded_list {
  margin-top: 4px !important;
  width: 166px;
}
// .applyball, .expiredball, .createdball {
//   width: 8px;
//   height: 8px;
//   border-radius: 50%;
//   background-color: #6CCC81;
//   margin-right: 6px;
// }
// .expiredball {
//   background-color: #B3C0C2;
// }
// .createdball {
//   background-color: #6684BF;
// }
.row_status {
  line-height: 18px;
}


.recorded_table-wrap {
  height: 522px;
}