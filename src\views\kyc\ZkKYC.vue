<template>
  <div style="padding: 0px 60px 0;">
    <div class="pg-title1 mui-fl-vert" v-if="!search">
      <i class="mcico-active-activation"></i>
      zkKYC
    </div>
    <div class="Search-title mui-fl-vert" v-else>
      <i class="mico-Fallback" @click="gobacksearch(false)"></i>
      <span class="fg"></span>
      <span>{{classification}}</span>
    </div>
    <NotRecord v-if="programme" :entry="entry" @Results="Results" @cachedate="cachedate" :changedate="changedate" @gobacksearch="gobacksearch" :ref="'NotRecord'"/>
    <Recorded :ref="'recorded'" @Results="Results" :searchtitle="search" :update="update" @modify="modify" @retrieval="retrieval" @fentry="fentry" v-else/>
  </div>
</template>
<script>
import { nextTick } from 'vue'
import NotRecord from './NotRecord.vue'
import Recorded from './Recorded.vue'
export default {
  components: { NotRecord, Recorded },
  data () {
    return {
      update: {
        state: false,
        id: ''
      },
      fentrydata: {},
      search: false,
      programme: false,
      changedate: true,
      entry: {
        Exhibition: false,
        data: {}
      },
      path: '',
      classification: !this.programme ? 'Search Results' : 'Create Program'
    }
  },
  // computed: {
  //   classification () {
  //     return !this.programme ? 'Search Results' : 'Create Program'
  //   }
  // },
  watch: {
    classification (val) {
      if (val === 'Create Program') {
        localStorage.setItem('kycstate', 'Create')
      } else if (val === 'Get a copy') {
        localStorage.setItem('kycstate', 'copy')
        localStorage.setItem('kycdata', JSON.stringify(this.fentrydata))
      } else if (val === 'Modify program') {
        localStorage.setItem('kycstate', 'Modify')
        localStorage.setItem('kycdata', JSON.stringify(this.fentrydata))
      }
    },
    programme (val) {
      if (!val) {
        localStorage.setItem('kycstate', '')
        localStorage.setItem('kycdata', '')
      }
    }
  },
  async beforeRouteLeave (to, from, next) {
    if (!this.$refs.NotRecord) {
      next()
    } else if (this.$refs.NotRecord) {
      await this.$refs.NotRecord.filterdate()
      if (this.changedate) {
        localStorage.setItem('kycstate', '')
        localStorage.setItem('kycdata', '')
        next()
      } else {
        await this.$refs.NotRecord.routerleave('path')
        this.path = to.path
      }
    }
  },
  created () {
    const state = localStorage.getItem('kycstate')
    if (state === 'Create') {
      this.Results('Create Program')
    } else if (state === 'copy') {
      this.fentry(JSON.parse(localStorage.getItem('kycdata')), true)
    } else if (state === 'Modify') {
      this.fentry(JSON.parse(localStorage.getItem('kycdata')), false)
    }
  },
  methods: {
    cachedate (cacheval, val) {
      cacheval = !cacheval.length ? 0 : cacheval.reduce((a, c) => a + c)
      val = !val.length ? 0 : val.reduce((a, c) => a + c)
      this.changedate = cacheval === val
    },
    async gobacksearch (val) {
      if (this.programme) {
        await this.$refs.NotRecord.routerleave()
        return
      }
      if (typeof val === 'boolean') {
        this.search = val
        this.path = ''
      }
      this.$refs.recorded.handleCurrentChange({
        page: 1,
        time: 0,
        auditedUser: '',
        highestLevel: '',
        items: '',
        val: '',
        id: '',
        checked1: true,
        checked2: false,
        recordedinp: ''
      })
    },
    // 跳转创建页面清除数据
    async Results (val, id, flg) {
      if (flg) {
        this.fentrydata = {}
      }
      this.entry.Exhibition = false
      this.programme = !this.programme
      this.update.state = false
      this.entry.data = {}
      await nextTick()
      if (!this.search) {
        this.search = !this.search
      }
      if (val === 'update') {
        this.search = false
        this.update = {
          state: true,
          id: id
        }
      }
      if (val === 'gobacksearch') {
        this.gobacksearch()
        this.search = false
        this.path = ''
      } else if (val) {
        this.classification = val
      }
      if (val === 'create') {
        this.search = false
      }
      if (this.path) {
        this.$router.push(this.path)
      }
    },

    // 弹出返回功能
    retrieval (val, title) {
      this.search = val
      if (title) {
        this.classification = title
      }
    },
    // 首次跳转创建页面
    modify (val) {
      this.entry.Exhibition = val
      this.programme = true
    },
    fentry (val, copy) {
      this.fentrydata = val
      if (copy) {
        this.classification = 'Get a copy'
      } else {
        this.classification = 'Modify program'
      }
      this.search = true
      this.programme = true
      this.entry = {
        copy: copy,
        Exhibition: false,
        data: val
      }
    }
  }
}
</script>
<style lang="scss" Scoped>
  .mcico-modular-zkKYC {
    font-size: 30px;
  }
  .Search-title {
    // padding: 13px 0;
    font-weight: 700;
    font-size: 24px;
    color: #002E33;
  }
  .fg {
    margin-left: 8px;
  }
  .mico-Fallback {
    padding: 16px 0;
    color: #002E33;
    line-height: 24px;
    cursor: pointer;
    font-size: 30px;
  }
</style>
