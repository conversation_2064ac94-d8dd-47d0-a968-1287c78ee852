<template>
  <div :class="[(mode === 1 || (mode !== 0 && themeMode === 'dark')) && 'modeDark', 'previewMode']">
    <div class="pg-title1 mui-fl-vert">
      <i class="mcico-active-integration"></i>
      🎨 UI Design
    </div>
    <div class="sty2-cell ui_bg mui-fl-btw">
      <div class="mui-fl-col">
        <!-- <div class="ui_selector">
          div class="ui_text">
            Set your color mode
          </div>
          <div class="mode_choose mui-fl-btw mui-fl-vert">
            <div :class="['mui-fl-1', 'mui-fl-central', mode === 0 && 'select']" @click="selectMode(0)">
              <i class="mico-light"></i>
              Light mode
            </div>
            <div :class="['mui-fl-1', 'mui-fl-central', mode === 1 && 'select']" @click="selectMode(1)">
              <i class="mico-dark"></i>
              Dark mode
            </div>
            <div :class="['mui-fl-1', 'mui-fl-central', mode === 2 && 'select']" @click="selectMode(2)">
              <i class="mico-device"></i>
              Device system
            </div>
          </div>
        </div> -->
        <div class="ui_selector ui_mgt_14">
          <div class="ui_text ui_mgb_16">
            Set your theme color
          </div>
          <div class="mui-fl-wrap mui-fl-start" style="max-width: 396px;">
            <div v-for="(color, index) of mode === 0 || themeMode !== 'dark' ? colorsOp : darkColorsOp" :key="index"
              class="ui_color" @click="selectColor(color, index)">
              <div class="ui_color_hand" :style="{ background: color[0] }"></div>
              <div class="mui-flex">
                <div class="ui_color_Lfoot" :style="{ background: color[1] }"></div>
                <div class="ui_color_Rfoot" :style="{ background: color[2] }"></div>
              </div>
              <i v-show="indexColor === index" class="mcico-ui-success"></i>
            </div>
            <div class="ui_color ui_color_design mui-fl-central">
              <i v-show="indexColor === 'custom'" class="mcico-ui-success"></i>
              <i class="mico-drawing"></i>
              <div class="color_picker">
                <m-color-picker ref="colorPicker" v-model="colorPicker" @active-change="activeChange"
                  :popper-class="(mode === 1 || (mode !== 0 && themeMode === 'dark')) ? 'sty2-ColorPicker' : 'sty1-ColorPicker'"
                  :show-alpha="false" :predefine="predefineColors">
                </m-color-picker>
              </div>
            </div>
          </div>
        </div>
        <div class="ui_selector ui_mgt_14 mui-fl-col ui_selector_collapse">
          <m-collapse ref="collapse"
            :class="['sty1-collapse', (mode === 1 || (mode !== 0 && themeMode === 'dark')) && 'sty2-collapse']">
            <m-collapse-item title="Advance setting">
              <template slot="title">
                <div class="ui_text">
                  Advance setting
                  <m-tooltip
                    :popper-class="(mode === 1 || (mode !== 0 && themeMode === 'dark')) ? 'sty2-tooltip' : 'sty1-tooltip'"
                    effect="light" placement="top">
                    <template slot="content">
                      If you need more advanced customization, you can <br /> edit CSS code to achieve more advanced
                      UI<br /> configurations.
                    </template>
                    <i class="mico-help-linear"></i>
                  </m-tooltip>
                </div>
              </template>
              <textarea :placeholder="textareaPlaceholder" v-model="cssTextarea" class="ui_textarea" cols="30"
                rows="10"></textarea>
            </m-collapse-item>
          </m-collapse>
        </div>
      </div>
      <div
        :class="[(mode === 1 || (mode !== 0 && themeMode === 'dark')) && 'dark', 'preview_con', 'mui-fl-col', 'mui-fl-central', 'mui-fl-1']">
        <div class="mui-fl-vert ui_mode">
          <div class="mode_choose mui-fl-btw mui-fl-vert">
            <div :class="['mui-fl-1', 'mui-fl-central', previewMode === 'web' && 'select']"
              @click="selectPreviewMode('web')"><i class="mico-monitor-alt"></i>PC</div>
            <div :class="['mui-fl-1', 'mui-fl-central', previewMode === 'mobile' && 'select']"
              @click="selectPreviewMode('mobile')"><i class="mico-mobile-alt"></i>Mobile</div>
          </div>
          <i v-if="mode === 1 || (mode !== 0 && themeMode === 'dark')" @click="selectMode(0)" class="mcico-dark"></i>
          <i v-else @click="selectMode(1)" class="mcico-light"></i>
        </div>
        <div class="component_preview_con mui-fl-vert">
          <button class="preview_btn mui-shr-0" :class="(preivewStep <= 1) && 'choose_color'" @click="previousStep"><i
              class="mico-fold chevron-left"></i></button>
          <div class="preview_content">
            <div class="cent-wrap mui-fl-central">
              <div class="mui-fl-col mui-fl-btw">
                <PreviewContent :cssTextarea="cssTextarea"
                  :themeMode="(mode === 1 || (mode !== 0 && themeMode === 'dark')) ? 'dark' : 'light'" :step="preivewStep"
                  :previewMode="previewMode" :propsColorsOp="selectColorData" />
                <div v-if="preivewStep >= 3 && previewMode === 'web'" class="custom-tooltip" :class="(mode === 1 || (mode !== 0 && themeMode === 'dark')) ? 'dark' : 'light'" style="position: relative;">
                  <div class="tooltip-content" aria-hidden="false">
                    This step requires users to <br>perform on their mobile<br> device.
                    <div class="tooltip-arrow"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <button class="preview_btn mui-shr-0" :class="(preivewStep >= 6) && 'choose_color'" @click="nextStep"><i
              class="mico-fold chevron-right"></i></button>
        </div>
      </div>
    </div>
    <m-button
      :class="['save', 'save_configure', (!indexColor.toString() && !cssTextarea) && 'choose_color']"
      @click="configure">Save and configure</m-button>
    <m-button v-if="keepColor" class="save save_configure reset" @click="reset">Reset</m-button>

    <m-dialog :visible.sync="warningtip" class="sty2-dialog dialog" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <div class="leavetip_title"><i class="mico-warning"></i>
        <span>Confirm to reset the UI Design？</span>
      </div>
      <div class="leavetip_text">Resetting will restore default colors, and the current configuration will not be saved.
      </div>
      <div class="dialog_button warin_button mui-fl-end">
        <m-button class="kycSave" @click="warningtip = false">Cancel</m-button>
        <m-button class="kycCancel" @click="confirmReset = true, reset()">Reset</m-button>
      </div>
    </m-dialog>
  </div>
</template>
<script>
import { generate } from '@ant-design/colors'
// import { saturation } from '../../utils/filters'
import { PreviewContent } from '@/components/ui-design-preview'
export default {
  components: { PreviewContent },
  data () {
    return {
      colors: [
        '#0057FF', '#0082FF', '#00A8EF', '#14BCD4', '#00D29F', '#00CB49', '#C0E725',
        '#E6C832', '#FFB800', '#df9143', '#FF6800', '#A65529', '#FF339B', '#EB35F7', '#A14FFF', '#5A46F9'
      ],
      colorsOp: [],
      darkColorsOp: [],
      themeMode: 'light',
      mode: 2,
      changemode: '',
      preivewStep: 1,
      previewMode: 'web',
      colorPicker: null,
      predefineColors: [
        '#FF6B00',
        '#FFE500',
        '#05FF00',
        '#00F0FF',
        '#00ced1',
        '#EBEBE5',
        '#EBEBEA',
        '#EBEBEB',
        '#EBEBEC'
      ],

      hexColor: '',
      indexColor: '',
      selectColorData: [[], []],
      tempColor: '',
      keepColor: false,
      warningtip: false,
      confirmReset: false,
      localStorageColor: JSON.parse(localStorage.getItem('localStorageColor')),
      cssTextarea: '',
      textareaPlaceholder:
        ` // Change background color 
:root {
    --color-background: #fff;
    --vt-c-white-bg: #fff;
}`
    }
  },
  computed: {
    userName () {
      return this.$store.state.auth.user.name
    }
  },
  async created () {
    if (this.localStorageColor) {
      for (const i in this.localStorageColor) {
        if (this.userName === this.localStorageColor[i].userName) {
          this.predefineColors = this.localStorageColor[i].colorList
        }
      }
    }
    this.classification()
    this.widgetDesignConfig()
  },
  mounted () {
    this.$watch(() => this.$refs.colorPicker?.$data.showPicker, (newValue) => {
      if (!newValue) {
        this.colorChange(this.tempColor, 'push')
      }
    })
    this.getSystemMode(true)
  },
  methods: {
    classification () {
      this.colors.forEach(v => {
        // const saturationColor = saturation(v)
        const color = this.colorSelect(v)
        // if (saturationColor < 70) {
        //   this.colorsOp.push([color[5], color[2], color[0]])
        // } else {
        //   this.colorsOp.push([color[5], color[4], color[1]])
        // }
        this.colorsOp.push([color[5], color[2], color[0]])
        const darkcolor = this.colorSelect(v, 'dark')
        this.darkColorsOp.push([darkcolor[5], darkcolor[3], darkcolor[1]])
      })
    },
    async widgetDesignConfig () {
      const rp = await this.$api.request('Uidesign.widgetDesignConfig', {}, {}, true)
      if (rp.code === 80000000) {
        if (rp.data?.themeColor) {
          let index = ''
          this.changemode = 1
          this.colorsOp.forEach((v, i) => {
            if (v.includes(rp.data?.themeColor)) {
              index = i
            }
          })
          this.selectColor(rp.data?.themeColor, index)
          if (!index.toString()) {
            this.colorPicker = rp.data?.themeColor
            this.indexColor = 'custom'
          } else {
            this.indexColor = index
          }
          this.keepColor = true
        } else {
          this.keepColor = false
        }
        if (rp.data?.customCss) {
          this.cssTextarea = rp.data?.customCss
        }
        this.mode = typeof rp.data?.mode === 'undefined' ? 2 : rp.data?.mode
      }
    },
    colorSelect (color, mode = 'default') {
      return generate(color, {
        theme: mode
      })
    },
    selectMode (mode) {
      this.changemode = mode
      this.mode = mode
    },
    selectPreviewMode (mode) {
      this.previewMode = mode
    },
    previousStep () {
      if (this.preivewStep <= 1) return
      this.preivewStep = this.preivewStep - 1
    },
    nextStep () {
      if (this.preivewStep >= 6) return
      this.preivewStep = this.preivewStep + 1
    },
    componentToHex (val) {
      var hex = val.toString(16)
      return hex.length === 1 ? '0' + hex : hex
    },
    activeChange (val) {
      this.tempColor = val
      this.colorChange(val, 'watch')
    },
    colorChange (val, index) {
      var matchColors = /rgb\((\d{1,3}), (\d{1,3}), (\d{1,3})\)/
      var match = matchColors.exec(val)
      if (match !== null) {
        const hex = '#' + this.componentToHex(Number(match[1])) + this.componentToHex(Number(match[2])) + this.componentToHex(Number(match[3]))
        this.hexColor = hex
        this.colorPicker = hex
        this.indexColor = 'custom'
        this.selectColor(this.colorPicker, 'custom')
        if (index === 'watch' || this.predefineColors.includes(hex.toUpperCase())) {
          return
        }
        this.predefineColors.pop()
        this.predefineColors.unshift(hex.toUpperCase())
        const local = []
        let concatColor
        local.push({
          userName: this.userName,
          colorList: this.predefineColors
        })
        if (this.localStorageColor) {
          concatColor = local.concat(this.localStorageColor)
        } else {
          concatColor = local
        }
        const uniqueArr = [...new Map(concatColor.map(item => [item.userName, item])).values()]
        localStorage.setItem('localStorageColor', JSON.stringify(uniqueArr))
      }
    },
    selectColor (val, index) {
      this.indexColor = index
      if (val === '#005563') {
        this.selectColorData = [[], []]
        return
      }
      if (typeof index === 'number') {
        this.selectColorData = [
          [
            this.colorsOp[index][0],
            this.colorsOp[index][1],
            this.colorsOp[index][2]
          ],
          [
            this.darkColorsOp[index][0],
            this.darkColorsOp[index][1],
            this.darkColorsOp[index][2]
          ]
        ]
      } else {
        const color = this.colorSelect(val)
        const darkcolor = this.colorSelect(val, 'dark')
        this.selectColorData = [
          [
            color[5],
            color[3],
            color[0]
          ],
          [
            darkcolor[5],
            darkcolor[2],
            darkcolor[0]
          ]
        ]
      }
    },
    async reset () {
      if (!this.confirmReset) {
        this.warningtip = true
        return
      }
      const rp = await this.$api.request('Uidesign.resetWidgetDesign', {}, {}, true)
      this.warningtip = false
      if (rp.code === 80000000) {
        this.indexColor = ''
        this.selectColorData = [[], []]
        this.cssTextarea = ''
        this.keepColor = false
        this.confirmReset = false
        this.mode = 2
        this.changemode = ''
        this.$message({
          message: 'Reset success!',
          iconClass: 'mcico-success',
          customClass: 'sty4-message',
          duration: 3000,
          offset: 32,
          center: true
        })
      }
    },
    async configure () {
      if (!this.indexColor.toString() && !this.changemode.toString() && !this.cssTextarea) {
        return
      }
      let request
      if (!this.selectColorData[0].length) {
        request = {
          lightColor1: '',
          lightColor2: '',
          lightColor3: '',
          darkColor1: '',
          darkColor2: '',
          darkColor3: '',
          mode: 0,
          customCss: this.cssTextarea
        }
      } else {
        request = {
          lightColor1: this.selectColorData[0][0] || '',
          lightColor2: this.selectColorData[0][1] || '',
          lightColor3: this.selectColorData[0][2] || '',
          darkColor1: this.selectColorData[1][0] || '',
          darkColor2: this.selectColorData[1][1] || '',
          darkColor3: this.selectColorData[1][2] || '',
          mode: 0,
          customCss: this.cssTextarea
        }
      }
      const rp = await this.$api.request('Uidesign.updateWidgetDesign', request, {}, true)
      if (rp.code === 80000000) {
        this.keepColor = true
        this.widgetDesignConfig()
        this.$message({
          message: 'Save and configure success!',
          iconClass: 'mcico-success',
          customClass: 'sty4-message',
          duration: 3000,
          offset: 32,
          center: true
        })
      }
    },
    setThemeMode (val) {
      this.themeMode = val
    },
    addThemeEventListener () {
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const lightModeQuery = window.matchMedia('(prefers-color-scheme: light)')
      // 添加事件监听器以侦听深浅模式更改
      const darkModeListener = (e) => {
        if (e.matches) {
          this.setThemeMode('dark')
        }
      }
      const lightModeListener = (e) => {
        if (e.matches) {
          this.setThemeMode('light')
        }
      }
      darkModeQuery.addEventListener('change', darkModeListener)
      lightModeQuery.addEventListener('change', lightModeListener)
    },
    getSystemMode (mounted) {
      // 获取系统深浅模式
      // （接口返回跟随系统时有效）
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const lightModeQuery = window.matchMedia('(prefers-color-scheme: light)')
      this.setThemeMode((lightModeQuery.matches && 'light') || (darkModeQuery.matches && 'dark'))
      if (mounted) {
        this.addThemeEventListener()
      }
    }
  },
  watch: {
    mode () {
      if (this.mode === 0) {
        this.setThemeMode('light')
      } else if (this.mode === 1) {
        this.setThemeMode('dark')
      } else {
        this.getSystemMode(false)
      }
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/_Uidesign.scss" scoped />
