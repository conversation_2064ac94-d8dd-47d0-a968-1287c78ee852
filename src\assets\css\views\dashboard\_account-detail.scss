.pg-account-detail {
  max-width: 1200px;
  min-width: 1050px;
}

.account {
  margin-top: 24px;
  border-radius: 12px;
  border: 1px solid rgba(169, 225, 211, 0.60);
  background: rgba(132, 182, 184, 0.12);
  padding: 26px 32px;
}
.account li .t1 {
  color: #33585C;
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
}
.account li .t2 {
  margin-left: 12px;
  padding: 8px 20px;
  border-radius: 38px;
  color: #002E33;
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
  background-color: #fff;
  max-width: 210px;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space:nowrap;
}

.account li p i {
  font-size: 24px;
  margin-right: 6px;
  color: #738C8F;
  margin-right: 6px;
}
.account li .t3 {
  color: #33585C;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}
.account li .t4 {
  margin-left: 4px;
  color: #33585C;
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
}
.account .download {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #fff;
  font-size: 20px;
}
.more {
  font-size: 12px;
  font-weight: 500;
  line-height: 22px;
  cursor: pointer;
  span {
    text-decoration: underline;
  }
}

.setting-items {
  margin-top: 48px;
}
.setting-items .title {
  color: #002E33;
  font-size: 18px;
  font-weight: 700;
  line-height: 25px;
}
.setting-items ul.sty1{
  margin-top: 16px;
  padding: 16px 20px;
  border-radius: 12px;
  border: 1px solid #F0F0F0;
  background-color: #fff;
}
.setting-items ul.sty2 {
  margin-top: 16px;
  padding: 16px 20px;
  border-radius: 12px;
  border: 1px solid #F0F0F0;
  background: #F7F7F7;
}

.client-settings  li {
  color: var(--t-1, #002E33);
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  text-wrap: nowrap;
  margin-right: 10vw;
}
.client-settings  li + li {
  // margin-left: 100px;
}

.regulations {
  color: #002E33;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
}

.key-findings {
  width: 100%;
  box-sizing: border-box;
}
.key-findings li {
  width: 50%;
}
.key-findings li:nth-child(3), .key-findings li:nth-child(4) {
  margin-top: 36px;
}
.key-findings li .t1 {
  color: #002E33;
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
  margin-bottom: 12px;
}
.key-findings-items > div {
  min-width: 50%;
  color: #33585C;
  font-size: 14px;
  font-weight: 500;
  line-height: 48px;
}
.key-findings-items-error > div {
  line-height: 20px;
  margin-top: 16px;
}
.exceptionData {
  .detail {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: #B3C0C2;
  }
  li {
    width: 100%;
    margin-top: 6px !important;
    padding-left: 24px;
    .title {
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      color: #738C8F;
    }
    .detail {
      margin-bottom: 16px;
    }
  }
}

.events-items {
  padding-left: 46px !important;
}
.events-items li {
  position: relative;
}
.events-items li::before {
  content: '';
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #6CCC81;
  position: absolute;
  left: -21px;
  top: 6px;
}
.events-items li::after {
  content: '';
  display: inline-block;
  width: 2px;
  height: 28px;
  background: #F2F7F7;
  position: absolute;
  left: -17px;
  top: 21px;
}
.events-items .event-name {
  color: #6CCC81;
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
}
.events-items .eventTime-bg .event-name {
  color: #B3C0C2;
}
.events-items .eventTime-bg::before {
  background: #B3C0C2;
}
.events-items li.purple-bg:after {
  background: none;
}

.events-items li:last-child::after {
  display: none;
}

.events-items li + li {
  margin-top: 28px;
}


.events-items .event-name.purple {
  color: #6684BF;
}
.events-items .event-applicant {
  color: #B3C0C2;
  font-size: 12px;
  line-height: 16px;
}
.events-items .event-applicant .value {
  color: #738C8F;
  font-size: 500;
}
.a-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 4px;
}
.marg-l10 {
  margin-left: 10px;
}
.information_select div{
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #002E33;
  padding: 5px 8px;
  cursor: pointer;
  border-radius: 6px;
}
.boundCopy, .moreCopy {
  font-size: 18px !important;
  color: #738C8F;
  cursor: pointer;
}
.moreCopy {
  font-size: 12px !important;
  color: #FFFF;
}
.mcico-wait {
  font-size: 20px;
}
.information_select div + div {
  margin-top: 2px;
}

.information_select div:hover{
  background-color: #F7F7F7;
}
.a-icon-checked {
  background: url('~@/assets/img/css_sprites.png') -10px -50px;
  background-size: 80px 80px;
}
.a-icon-error {
  background: url('~@/assets/img/css_sprites.png') -10px -10px;
  background-size: 80px 80px;
}
.a-icon-success {
  background: url('~@/assets/img/css_sprites.png') -50px -10px;
  background-size: 80px 80px;
}

.regulationsIcon i {
  font-size: 20px;
}