// 有色图标，tabs
@font-face {
  font-family: "color-iconfont"; 
  src: 
       url('~@/assets/css/fonts/color-iconfont/iconfont.woff2') format('woff2'),
       url('~@/assets/css/fonts/color-iconfont/iconfont.woff') format('woff'),
       url('~@/assets/css/fonts/color-iconfont/iconfont.ttf') format('truetype');
}

// 单色图标
@font-face {
  font-family: "iconfont"; /* Project id 3769785 */
  src: url('~@/assets/css/fonts/iconfont/iconfont.woff2') format('woff2'),
       url('~@/assets/css/fonts/iconfont/iconfont.woff') format('woff'),
       url('~@/assets/css/fonts/iconfont/iconfont.ttf') format('truetype');
}

[class*=" mico-"], [class^=mico-] {
  font-family: "iconfont" !important;
}
[class*=" mcico-"], [class^=mcico-] {
  font-family: "color-iconfont" !important;
}
[class*=" mico-"], [class^=mico-], [class*=" mcico-"], [class^=mcico-] {
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: baseline;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 有色图标

.mcico-verification:before {
  content: "\e619";
}

.mcico-liveness:before {
  content: "\e61a";
}

.mcico-ocr:before {
  content: "\e61b";
}

.mcico-zkp:before {
  content: "\e61c";
}

.mcico-light:before {
  content: "\e617";
}

.mcico-dark:before {
  content: "\e618";
}

.mcico-supportedTip:before {
  content: "\e616";
}

.mcico-sbtMinted:before {
  content: "\e615";
}

.mcico-network:before {
  content: "\e613";
}

.mcico-wait:before {
  content: "\e614";
}

.mcico-Authorized:before {
  content: "\e60f";
}

.mcico-Passed:before {
  content: "\e610";
}

.mcico-Processing:before {
  content: "\e611";
}

.mcico-Failed:before {
  content: "\e612";
}

.mcico-SelectNot:before {
  content: "\e60e";
}

.mcico-warn1:before {
  content: "\e60c";
}

.mcico-warn2:before {
  content: "\e60d";
}

.mcico-upload-success:before {
  content: "\e60b";
}

.mcico-colorLogo:before {
  content: "\e60a";
}

.mcico-ui-success:before {
  content: "\e609";
}

.mcico-success2:before {
  content: "\e608";
}

.mcico-success-green:before {
  content: "\e607";
}

.mcico-failed-red:before {
  content: "\e606";
}

.mcico-close2:before {
  content: "\e605";
}

.mcico-eye:before {
  content: "\e604";
}

.mcico-eye-close:before {
  content: "\e603";
}

.mcico-modular-zkKYC:before {
  content: "\e602";
}

.mcico-celar:before {
  content: "\e731";
}

.mcico-doubt:before {
  content: "\e601";
}

.mcico-success:before {
  content: "\e600";
}

.mcico-activation:before {
  content: "\e721";
}

.mcico-active-zkKYC:before {
  content: "\e722";
}

.mcico-active-dashboard:before {
  content: "\e723";
}

.mcico-active-activation:before {
  content: "\e724";
}

.mcico-zkKYC:before {
  content: "\e725";
}

.mcico-developers-center:before {
  content: "\e726";
}

.mcico-integration:before {
  content: "\e727";
}

.mcico-active-integration:before {
  content: "\e728";
}

.mcico-active-developers-center:before {
  content: "\e729";
}

.mcico-dashboard:before {
  content: "\e72a";
}


// 单色图标
.mico-web:before {
  content: "\e644";
}

.mico-tg:before {
  content: "\e645";
}

.mico-kycTip:before {
  content: "\e646";
}

.mico-citizenship-8:before {
  content: "\e790";
}

.mico-citizenship-3:before {
  content: "\e78f";
}

.mico-house-shield:before {
  content: "\e7b5";
}

.mico-badge-dollar:before {
  content: "\e7c9";
}

.mico-upload:before {
  content: "\e641";
}

.mico-upload-file:before {
  content: "\e643";
}

.mico-step2:before {
  content: "\e63f";
}

.mico-information:before {
  content: "\e640";
}

.mico-step1:before {
  content: "\e642";
}

.mico-clock2:before {
  content: "\e63e";
}

.mico-monitor-alt:before {
  content: "\e63c";
}

.mico-mobile-alt:before {
  content: "\e63d";
}

.mico-drawing:before {
  content: "\e63a";
}

.mico-dark:before {
  content: "\e63b";
}

.mico-device:before {
  content: "\e638";
}

.mico-light:before {
  content: "\e639";
}

.mico-arrow-up-from-arc:before {
  content: "\e637";
}

.mico-warning1:before {
  content: "\e636";
}

.mico-chevron-right-double:before {
  content: "\e634";
}

.mico-chevron-left-double:before {
  content: "\e635";
}

.mico-wallet:before {
  content: "\e630";
}

.mico-tag:before {
  content: "\e631";
}

.mico-on-chain:before {
  content: "\e632";
}

.mico-that-person:before {
  content: "\e633";
}

.mico-reduction:before {
  content: "\e625";
}

.mico-clean:before {
  content: "\e62a";
}

.mico-full-screen:before {
  content: "\e62b";
}

.mico-download1:before {
  content: "\e62c";
}

.mico-center:before {
  content: "\e62d";
}

.mico-reduce:before {
  content: "\e62e";
}

.mico-amplify:before {
  content: "\e62f";
}

.mico-APIKey:before {
  content: "\e622";
}

.mico-AppID:before {
  content: "\e623";
}

.mico-edit:before {
  content: "\e624";
}

.mico-question:before {
  content: "\e626";
}

.mico-file:before {
  content: "\e627";
}

.mico-lock:before {
  content: "\e628";
}

.mico-unLock:before {
  content: "\e629";
}

.mico-help-linear:before {
  content: "\e607";
}

.mico-twitter:before {
  content: "\e745";
}

.mico-copy:before {
  content: "\e621";
}

.mico-a-apireset:before {
  content: "\e620";
}

.mico-a-apiemail:before {
  content: "\e61e";
}

.mico-a-apikey:before {
  content: "\e61c";
}

.mico-a-apiaccess:before {
  content: "\e61d";
}

.mico-a-apitip:before {
  content: "\e61f";
}

.mico-github:before {
  content: "\e61b";
}

.mico-youtube:before {
  content: "\e61a";
}

.mico-email:before {
  content: "\e619";
}

.mico-telegram:before {
  content: "\e618";
}

.mico-discord:before {
  content: "\e617";
}

.mico-fold:before {
  content: "\e601";
}

.mico-Fallback:before {
  content: "\e616";
}

.mico-Select:before {
  content: "\e60d";
}

.mico-sort-dow:before {
  content: "\e613";
}

.mico-sort-up:before {
  content: "\e615";
}

.mico-drawer-close:before {
  content: "\e614";
}

.mico-close-input:before {
  content: "\e602";
}

.mico-creat:before {
  content: "\e604";
}

.mico-lightTip:before {
  content: "\e734";
}

.mico-countries:before {
  content: "\e72f";
}

.mico-age:before {
  content: "\e730";
}

.mico-credentials:before {
  content: "\e731";
}

.mico-liveness:before {
  content: "\e732";
}

.mico-aml:before {
  content: "\e733";
}

.mico-close1:before {
  content: "\e727";
}

.mico-a-Frame48097706:before {
  content: "\e728";
}

.mico-download:before {
  content: "\e612";
}

.mico-close:before {
  content: "\e60a";
}

.mico-search:before {
  content: "\e60b";
}

.mico-date:before {
  content: "\e60f";
}

.mico-rejected:before {
  content: "\e60c";
}

.mico-total:before {
  content: "\e60e";
}

.mico-processing:before {
  content: "\e610";
}

.mico-verified:before {
  content: "\e611";
}

.mico-warning:before {
  content: "\e600";
}

.mico-help:before {
  content: "\e608";
}

.mico-settings:before {
  content: "\e609";
}

.mico-instagram:before {
  content: "\e603";
}

.mico-ins:before {
  content: "\e605";
}

.mico-meidum:before {
  content: "\e606";
}

.mico-arrow-bottom:before {
  content: "\e724";
}

.mico-arrow-top:before {
  content: "\e725";
}
