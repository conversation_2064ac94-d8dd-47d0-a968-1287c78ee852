.sty2-cell {
  box-sizing: border-box;
  // overflow-x: scroll;
}

.mcico-modular-zkKYC {
  font-size: 30px;
}
.Search-title {
  // padding: 13px 0;
  font-weight: 700;
  font-size: 24px;
  color: #002E33;
}
.fg {
  margin-left: 8px;
}
.kycLevel {
  padding: 4px 6px;
  background-color: #002E33;
  border-radius: 6px;
  margin-left: 8px;
  color: #FFFFFF;
  font-weight: 700;
  font-size: 16px;
  line-height: 18px;
}


.kycmain {
  min-width: 810px;
  margin: 32px 0px 104px;

  .whole {
    width: 810px;
  }
  .kyctitle {
    font-size: 28px;
    line-height: 36px;
    color: #002E33;
    font-weight: 700;
  }
}

.kycbut {
  margin: 0;
  padding: 16px 0 36px;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  z-index: 1;
  width: 810px;
  background-color: #ffffff;
}

h3 {
  font-size: 18px;
  font-weight: 700;
  line-height: 25px;
  color: #002E33;
}
.t1 {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

.t2 {
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
}

.h3-mar {
  margin-bottom: 20px;
}

.li-mar {
  margin-top: 32px;
}

.mico-help-linear {
  font-size: 20px;
  color: #002E33;
  vertical-align: middle;
  cursor: pointer;
}

.network-box {
  border: 1px solid #F0F0F0;
  border-radius: 12px;
  padding: 16px 20px;
  &:nth-child(2) {
    margin-bottom: 16px;
  }
}

.network-title {
  // margin-bottom: 16px;
  p:first-child {
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    margin-right: 12px;
    color: #002E33;
  }
  p:nth-child(2) {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #738C8F;
  }
}

.network-tag {
  background: #F7F7F7;
  border-radius: 6px;
  padding: 8px 24px 8px 20px;
  font-size: 14px;
  line-height: 20px;
  position: relative;
  margin-right: 20px;
  margin-top: 16px;
  color: #002E33;
  cursor: pointer;
  img {
    width: 22px;
  }
  p {
    margin-left: 6px;
  }
}

.tag-checked {
  background: #84B6B81F;
  overflow: hidden;
  &::after {
    content: '';
    width: 12px;
    height: 12px;
    background: url('~@/assets/img/tag-checked.png') 0 0 no-repeat;
    background-size: 100%;
    position: absolute;
    top: 0;
    right: 0;
  }
}

.tag-checked-hover {
  &:hover {
    position: relative;
    overflow: visible;
    &::after {
      content: '';
      width: 20px;
      height: 20px;
      background: url('~@/assets/img/close.png') 0 0 no-repeat;
      background-size: 100%;
      position: absolute;
      top: -6px;
      right: -6px;
    }
  }
}

.requirement-box {
  border: 1px solid #F0F0F0;
  border-radius: 12px;
  padding: 16px 0;
}

.age-box {
  padding: 0 20px;
}

.active-border {
  border-bottom: 1px solid #F0F0F0;
  margin-bottom: 16px;
}

.switch-box {
  padding: 12px 0;
  p {
    font-size: 16px;
    font-weight: 400;
    line-height: 18px;
    color: #002E33;
    margin-left: 12px;
  }
}

.expand-box {
  padding: 16px 0 24px 0;
  color: #002E33;
  &>li:first-child {
    margin-bottom: 16px;
  }
}

.expand-p-mar {
  margin-right: 12px;
}
.condition-text {
  font-weight: 500;
  font-size: 16px;
  line-height: 18px;
}


@media screen and (max-width: 1200px) {
  .kycbut {
    padding: 16px 60px 36px;
    width: calc(100% - 150px);
    right: 0;
    // bottom: 6px;
  }
}