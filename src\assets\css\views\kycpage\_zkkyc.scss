.mcico-modular-zkKYC {
  font-size: 30px;
}
.Search-title {
  // padding: 13px 0;
  font-weight: 700;
  font-size: 24px;
  color: #002E33;
}
.fg {
  margin-left: 8px;
}
.icon {
  padding: 16px 0;
  color: #002E33;
  line-height: 24px;
  cursor: pointer;
  font-size: 30px;
}

.recorded_table .has-gutter, .personal>.l, .information>.r, .drawer_records, .drawer_step>.drawer_text, .dialog>.dialog_title, .recdialog .dialog_title{
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #002E33;
}
.program, .program_copy{
  font-weight: 400;
  font-size: 14px;
  line-height: 18px;
  color: #738C8F;
}
.top_function {
  border-radius: 12px;
  border: 1px solid#F0F0F0;
  padding: 16px 20px;
  margin: 32px 0 24px;
  .top_title {
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    color: #002E33;
  }
  .top_selected {
    margin: 12px 0 16px;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #738C8F;
  }
}
.inp {
  width: 280px;
}
.mico-creat {
  font-size: 16px;
}

.redivision {
  height: 20px;
  border-left: 1px solid #F0F0F0;
  margin: 0 32px;
}
// .divider {
//   margin: 16px 0 32px;
//   background-color: #F0F0F0;
// }
// .recorded_pagination {
//   margin-top: 24px;
// }
.FN {
  margin-left: 0px;
}

.Apply1 {
  .tableballcolor {
    background-color: #6CCC81;
  }
  color: #6CCC81;
}
.Created1 {
  .tableballcolor {
    background-color: #6684BF;
  }
  color: #6684BF;
}
.Expired1 {
  .tableballcolor {
    background-color: #B3C0C2;
  }
  color: #B3C0C2;
}
.Pending1 {
  .tableballcolor {
    background-color: #FFD14B;
  }
  color: #FFD14B;
}
.Apply1, .Created1, .Expired1, .Pending1 {
  .ball {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
  }
}

.kycProgramId {
  position: relative;
}
.program_copy {
  padding: 0 4px;
  position: absolute;
  z-index: 10;
  color: #005563;
  top: -1px;
  right: 0;
  line-height: 20px;
  background: #f7f7f7;
  box-shadow: -10px 0px 10px #F7F7F7;
  cursor: pointer;
}
.Copied {
  color: rgba($color: #005563, $alpha: 0.5);
}
.recorded_but {
  width: 166px;
  height: 32px !important;
  font-weight: 400;
  background-color: #FFFFFF !important;
  color: #B3C0C2 !important;
  margin: 0 !important;
  .mico-fold {
    line-height: 15px;
    margin-left: 12px;
    font-size: 12px;
  }
}
.recorded_list {
  margin-top: 4px !important;
  width: 166px;
}
// .applyball, .expiredball, .createdball {
//   width: 8px;
//   height: 8px;
//   border-radius: 50%;
//   background-color: #6CCC81;
//   margin-right: 6px;
// }
// .expiredball {
//   background-color: #B3C0C2;
// }
// .createdball {
//   background-color: #6684BF;
// }
.row_status {
  line-height: 18px;
}

// .recorded_table-wrap {
//   height: 522px;
// }

.view-edit {
  font-size: 14px;
  line-height: 18px;
  color: #005563;
}
.kycProgramId {
  margin-right: 6px;
}
.copyIcon {
  font-size: 16px;
}

.kycmessage {
  min-width: 140px;
  height: 36px;
  background-color: #F2F7F7;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  i {
    margin-right: 6px;
    font-size: 24px;
    color: #005563 !important;
  }
  p {
    font-weight: 500;
    line-height: 18px;
    color: #002E33;
  }
}
.network-tag {
  background: #F7F7F7;
  border-radius: 6px;
  padding: 8px 24px 8px 20px;
  font-size: 14px;
  line-height: 20px;
  position: relative;
  color: #002E33;
  margin-bottom: 32px;
  border: none;
  cursor: pointer;
  img {
    width: 22px;
  }
  p {
    margin-left: 6px;
  }
}
.tag-checked {
  background: #84B6B81F;
  overflow: hidden;
  &::after {
    content: '';
    width: 12px;
    height: 12px;
    background: url('~@/assets/img/tag-checked.png') 0 0 no-repeat;
    background-size: 100%;
    position: absolute;
    top: 0;
    right: 0;
  }
}

.tag-checked-hover {
  &:hover {
    position: relative;
    overflow: visible;
    &::after {
      content: '';
      width: 20px;
      height: 20px;
      background: url('~@/assets/img/close.png') 0 0 no-repeat;
      background-size: 100%;
      position: absolute;
      top: -6px;
      right: -6px;
    }
  }
}