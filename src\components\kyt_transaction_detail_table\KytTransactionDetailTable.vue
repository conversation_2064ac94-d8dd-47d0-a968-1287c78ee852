<template>
  <m-dialog
  class="txn-detail-dialog"
  title="Transaction Details"
  :visible="visible"
  :close-on-click-modal="false"
  :close-on-press-escape="false"
  @close="close"
  >
    <m-table v-loading="loading" :data="tableData" class="sty2-table">
        <m-table-column label="Time(UTC)" min-width="164">
          <template #default="{ row }">
            <p>{{ row.time | formatUTCDate }}</p>
          </template>
        </m-table-column>
        <m-table-column label="Sender" min-width="220">
          <template #default>
            <p>{{ fromAddress | simplifyAddress(8, 7) }}
              <i class="mico-copy" @click="copy(fromAddress)"></i>
              <!-- <i class="mico-arrow-up-from-arc" @click="updateWalletAddress(fromAddress)"></i> -->
            </p>
          </template>
        </m-table-column>
        <m-table-column label="Recipient" min-width="220">
          <template #default>
            <p>{{ toAddress | simplifyAddress(8, 7) }}
              <i class="mico-copy" @click="copy(toAddress)"></i>
              <!-- <i class="mico-arrow-up-from-arc" @click="updateWalletAddress(toAddress)"></i> -->
            </p>
          </template>
        </m-table-column>
        <m-table-column label="Amount" min-width="160">
          <template #default="{ row }">
            <p>{{ formatNumber(row.amount) | thousandth }} {{ coin }}</p>
          </template>
        </m-table-column>
        <m-table-column min-width="220" label="TXID">
          <template #default="{ row }">
            <p>{{ row.txId | simplifyAddress(8, 7) }}
              <i class="mico-copy" @click="copy(row.txId)"></i>
              <i class="mico-arrow-up-from-arc" @click="blockchainExplore(row.txId)"></i>
            </p>
          </template>
        </m-table-column>
    </m-table>
    <div class="table-pagination mui-fl-end">
          <m-pagination
            v-show="hashList.length > 0"
            class="sty1-pagination sty3-cell"
            background
            :current-page.sync="currPage"
            :page-size="itemsPerPage"
            layout="prev, pager, next"
            :total="hashList.length"
          >
          </m-pagination>
        </div>
  </m-dialog>
</template>
<script>
export default {
  name: 'KytTransactionDetailTable',
  props: {
    hashList: {
      type: Array,
      required: true,
      default: () => {
        return []
      }
    },
    visible: {
      type: Boolean,
      required: true,
      default: false
    },
    walletAddress: {
      type: String,
      required: true
    },
    fromAddress: {
      type: String,
      required: true
    },
    toAddress: {
      type: String,
      required: true
    },
    decimals: {
      type: Number,
      required: true
    },
    coin: {
      type: String,
      required: true
    },
    requestParams: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      currPage: 1,
      itemsPerPage: 10,
      tableData: [],
      firstLoaded: false,
      loading: false,
      controller: new AbortController()
    }
  },
  watch: {
    async hashList (newVal, oldVal) {
      if (newVal !== oldVal) {
        await this.loadTxnsDetail()
        if (oldVal.length === 0) {
          this.firstLoaded = true
        }
      }
    },
    async currPage (newVal, oldVal) {
      if (newVal !== oldVal) {
        await this.loadTxnsDetail()
      }
    }
  },
  methods: {
    paginatedData (data, currentPage) {
      const start = (currentPage - 1) * this.itemsPerPage
      const end = currentPage * this.itemsPerPage
      return data.slice(start, end)
    },
    close () {
      this.firstLoaded = false
      this.$emit('close')
    },
    formatNumber (num) {
      num = Number(num.toFixed(4))
      const rounded = Math.round(num * 1e6) / 1e6
      const parts = rounded.toString().split('.')
      if (parts[1]) {
        return `${parts[0]}.${parts[1].padEnd(6, '0').replace(/0+$/, '')}`
      }
      return parts[0]
    },
    async loadTxnsDetail (attempts = 3) {
      this.loading = true
      try {
        const reqobj = {
          hashList: this.paginatedData(this.hashList, this.currPage),
          from: this.fromAddress,
          to: this.toAddress,
          address: this.walletAddress,
          decimals: this.decimals || 18,
          chain: this.requestParams,
          symbol: this.coin
        }
        this.controller.abort()
        this.controller = new AbortController()
        const rp = await this.$api.request('kyt.txnsDetail', reqobj, this.controller, true)
        if (rp.code === 80000000) {
          this.tableData = rp.data
          this.loading = false
        } else {
          if (attempts <= 1) {
            console.log(`Failed to fetch data. Retrying... (${attempts - 1} attempts left)`)
            return this.loadTxnsDetail(attempts - 1)
          }
        }
      } catch (err) {
        if (attempts <= 1) throw err
        console.log(`Failed to fetch data. Retrying... (${attempts - 1} attempts left)`)
        return this.loadTxnsDetail(attempts - 1)
      }
    },
    copy (data) {
      const inp = document.createElement('input')
      document.body.appendChild(inp)
      inp.value = data
      inp.select()
      document.execCommand('Copy')
      document.body.removeChild(inp)
      this.$message({
        message: 'Copied',
        iconClass: 'mcico-success',
        customClass: 'sty4-message',
        duration: 3000,
        offset: 32,
        center: true
      })
    },
    blockchainExplore (hash) {
      let explorerLink = ''
      switch (this.requestParams) {
        case 'ERC20':
          explorerLink = 'https://etherscan.io/tx/'
          break
        case 'BEP20':
          explorerLink = 'https://bscscan.com/tx/'
          break
        case 'Polygon':
          explorerLink = 'https://polygonscan.com/tx/'
          break
        case 'Avalanche':
          explorerLink = 'https://avascan.info/blockchain/c/tx/'
          break
        case 'Arbitrum':
          explorerLink = 'https://arbiscan.io/tx/'
          break
        case 'Optimism':
          explorerLink = 'https://optimistic.etherscan.io/tx/'
          break
        case 'Base':
          explorerLink = 'https://basescan.org/tx/'
          break
      }
      window.open(`${explorerLink}${hash}`, '_blank')
    }
  }
}
</script>
<style>
.el-dialog {
  width: 55%;
}

.txn-detail-dialog .el-dialog{
  border-radius: 16px;
}

.txn-detail-dialog .el-dialog .el-dialog__header{
  padding: 24px;
}

.txn-detail-dialog .el-dialog .el-dialog__body {
  padding: 0px 24px 24px 24px;
}

.txn-detail-dialog .el-dialog .el-dialog__body .el-table__cell{
  padding: 0px;
  height: 42px;
}

.cell .mico-arrow-up-from-arc {
  margin-left: 6px;
}
</style>
