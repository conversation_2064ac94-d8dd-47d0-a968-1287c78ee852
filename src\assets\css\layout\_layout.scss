.mui-flex {
  display: flex;
}

// 自动换行的 flex
.mui-fl-wrap {
  display: flex;
  flex-wrap: wrap;
}

// 竖形排列的 flex
.mui-fl-col{
  display: flex;
  flex-direction: column;
}

// 上下居中的 flex
.mui-fl-vert {
  display: flex;
  align-items: center;
}

// 左右居中的 flex
.mui-fl-hori {
  display: flex;
  justify-content: center;
}

// 上下左右居中的 flex
.mui-fl-central {
  display: flex;
  align-items: center;
  justify-content: center;
}

.mui-fl-vert_start {
  display: flex;
  align-items: flex-start;
}

// 置于底部的 flex
.mui-fl-vert_end {
  display: flex;
  align-items: flex-end;
}

// 置于最右的 flex
.mui-fl-end {
  display: flex;
  justify-content: flex-end;
}

// 置于最左的 flex
.mui-fl-start {
  display: flex;
  justify-content: flex-start;
}

.mui-fl-btw {
  display: flex;
  justify-content: space-between;
}

.mui-fl-str {
  display: flex;
  align-items: stretch;
}

.mui-shr-0 {
  flex-shrink: 0;
}

.mui-fl-1 {
  flex: 1;
}

.mui-fl-1-ovhide {
  flex: 1;
  overflow-y: hidden;
}

.mui-ma-vert {
  margin: 0 auto;
}

.mui-fl-start {
  display: flex;
  align-items: flex-start;
}
