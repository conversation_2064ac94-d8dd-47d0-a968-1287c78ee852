// 创建项目ID号
import { boundaryMount } from '@/utils/filters'
export const createDistributionID = () => {
  return {
    method: 'get',
    url: '',
    data: {
      value: 10,
      'planId()': 'generateSeq(value)'
    }
  }
}
// 创建分发活动统计表
export const createDistributionActivity = () => {
  return {
    method: 'get',
    url: '',
    data: {
      activity_id: '12',
      'activityId()': 'generateSeq(activity_id)',
      config_id: '13',
      'configId()': 'generateSeq(config_id)'
    }
  }
}

// 获取项目列表
export const getDistributionList = ({ email, planId, mchNo, page }) => {
  return {
    method: 'get',
    url: '',
    data: {
      '[]': {
        page,
        count: 10,
        query: 2,
        TdTokenDistributionPlan: {
          email,
          plan_id: planId
        },
        'TdDistributionActivity[]': {
          TdDistributionActivity: {
            'plan_id@': '[]/TdTokenDistributionPlan/plan_id'
          }
        },
        'TdClaimPageUiConfig[]': {
          TdClaimPageUiConfig: {
            'plan_id@': '[]/TdTokenDistributionPlan/plan_id'
          }
        }
      },
      TdMchWhiteList: {
        mch_no: mchNo
      }
    }
  }
}

// 获取分发活动统计表
export const getDistributionActivity = ({ planId }) => {
  return {
    method: 'get',
    url: '',
    data: {
      'TdDistributionActivity[]': {
        page: 0,
        count: 10,
        TdDistributionActivity: {
          plan_id: planId
        }
      }
    }
  }
}

// 新增活动统计
export const postDistributionActivity = ({ activityId, planId, claimedAmount, totalAmount, claimedAddresses, totalRecipients, claimLink }) => {
  return {
    method: 'post',
    url: '',
    data: {
      TdDistributionActivity: {
        activity_id: activityId,
        plan_id: planId,
        claimed_amount: claimedAmount,
        total_amount: totalAmount,
        claimed_addresses: claimedAddresses,
        total_recipients: totalRecipients,
        claim_link: claimLink
      }
    }
  }
}
// 修改活动统计
export const putDistributionActivity = ({ id, activityId, claimedAmount, totalAmount, claimedAddresses, totalRecipients, claimLink }) => {
  return {
    method: 'put',
    url: '',
    data: {
      TdDistributionActivity: {
        id: id,
        activity_id: activityId,
        claimed_amount: claimedAmount,
        total_amount: totalAmount,
        claimed_addresses: claimedAddresses,
        total_recipients: totalRecipients,
        claim_link: claimLink
      }
    }
  }
}

// 根据项目ID填写信息
export const postDistributionInfo = ({ planId, mchAssetAddress, step, email, distributionName, tokenNetwork, tokenContractAddress, tokenSymbol, distributionType, amount, website, xAccount, telegram, discord, kycProgramId, mchNo, blockchainId, displayTimezone, startDate, endDate, status }) => {
  return {
    method: 'post',
    url: '',
    data: {
      TdTokenDistributionPlan: {
        plan_id: planId,
        mch_asset_address: mchAssetAddress,
        step,
        email,
        distribution_name: distributionName,
        token_network: tokenNetwork,
        token_contract_address: tokenContractAddress,
        token_symbol: tokenSymbol,
        distribution_type: distributionType,
        amount,
        website,
        x_account: xAccount,
        telegram,
        discord,
        kyc_program_id: kycProgramId,
        mch_no: mchNo,
        blockchain_id: blockchainId,
        display_timezone: displayTimezone,
        // start_date: startDate,
        // end_date: endDate,
        status
      }
    }
  }
}
// 根据项目ID修改信息
export const putDistributionInfo = ({ id, planId, step, contractStatus, description, symbolUrl, claimPageUrl, distributionName, tokenNetwork, tokenContractAddress, tokenSymbol, distributionType, amount, website, xAccount, telegram, discord, kycProgramId, mchNo, blockchainId, displayTimezone, startDate, endDate, status }) => {
  return {
    method: 'put',
    url: '',
    data: {
      TdTokenDistributionPlan: {
        id: id,
        plan_id: planId,
        step,
        contract_status: contractStatus,
        description,
        claim_page_url: claimPageUrl,
        symbol_url: symbolUrl,
        distribution_name: distributionName,
        token_network: tokenNetwork,
        token_contract_address: tokenContractAddress,
        token_symbol: tokenSymbol,
        distribution_type: distributionType,
        amount,
        website,
        x_account: xAccount,
        telegram,
        discord,
        kyc_program_id: kycProgramId,
        mch_no: mchNo,
        blockchain_id: blockchainId,
        display_timezone: displayTimezone,
        start_date: startDate,
        end_date: endDate,
        status
      }
    }
  }
}

export const getTdRecipient = ({ planId, page, count }) => {
  return {
    method: 'get',
    url: '',
    data: {
      '[]': {
        page,
        count: 100,
        query: 2,
        TdRecipient: {
          plan_id: planId,
          '@order': 'created_at-'
        }
      }
    }
  }
}
export const deleteTdRecipient = ({ recipientId }) => {
  return {
    method: 'delete',
    url: '',
    data: {
      TdRecipient: {
        recipient_id: recipientId
      }
    }
  }
}

export const createProjectId = ({ planId }) => {
  return {
    method: 'get',
    url: '',
    data: {
      plan_id: planId,
      'func()': 'createProject(plan_id)',
      'txId()': 'projectMerkleRootOnChain(plan_id)',
      'depositTxId()': 'merkleDepositRootOnChain(plan_id)'
    }
  }
}
export const createTdRecipient = ({ length }) => {
  return {
    method: 'get',
    url: '',
    data: {
      '[]': {
        count: length,
        recipient_id: '11',
        'recipientId()': 'generateSeq(recipient_id)'
      }
    }
  }
}
export const TdMchWhiteList = ({ mchNo }) => {
  return {
    method: 'post',
    url: '',
    data: {
      TdMchWhiteList: {
        mch_no: mchNo
      }
    }
  }
}
export const cleanupWhitelist = ({ planId }) => {
  return {
    method: 'post',
    url: `/cleanup/${planId}`
  }
}
export const getWhitelistList = ({ planId }) => {
  return {
    method: 'get',
    url: `/cleanup/${planId}/count`
  }
}
export const postTdRecipient = ({ postTdRecipient }) => {
  return {
    method: 'post',
    url: '',
    data: postTdRecipient
  }
}

export const fileUpload = ({ file }) => {
  return {
    method: 'post',
    url: '/upload',
    data: file
  }
}

export const getFileUrl = ({ filePath, expireHours }) => {
  return {
    method: 'post',
    url: '/downloadUrl',
    data: {
      filePath,
      expireHours
    }
  }
}
export const postFile = ({ planId, dataList }) => {
  const {
    blob,
    boundary
  } = boundaryMount(JSON.stringify(dataList))
  return {
    method: 'post',
    url: `/recipient/import/${planId}`,
    data: {
      file: blob,
      boundary
    }
  }
}
export const cleanup = ({ planId }) => {
  return {
    method: 'post',
    url: `/recipient/cleanup/${planId}`,
    data: {
      plan_id: planId
    }
  }
}

export const deleteTdTokenDistributionPlan = ({ planId }) => {
  return {
    method: 'delete',
    url: '',
    data: {
      TdTokenDistributionPlan: {
        plan_id: planId
      }
    }
  }
}
export const postTdClaimPageUiConfig = ({ configId, planId, themeColor, secondaryColor, fontColor, buttonColor }) => {
  return {
    method: 'post',
    url: '',
    data: {
      TdClaimPageUiConfig: {
        config_id: configId,
        plan_id: planId,
        theme_color: themeColor,
        secondary_color: secondaryColor,
        font_color: fontColor,
        button_color: buttonColor
      }
    }
  }
}
export const putTdClaimPageUiConfig = ({ configId, id, themeColor, secondaryColor, fontColor, buttonColor, darkColor, lightColor }) => {
  return {
    method: 'put',
    url: '',
    data: {
      TdClaimPageUiConfig: {
        config_id: configId,
        id: id,
        // theme_color: themeColor,
        // secondary_color: secondaryColor,
        // font_color: fontColor,
        // button_color: buttonColor,
        dar_color: darkColor,
        light_color: lightColor
        // theme_color: '#1E40AF',
        // secondary_color: '#3B82F6',
        // font_color: '#1F2937',
        // button_color: '#10B981'
        // poster_img_oss_path: '/images/zkme-poster.jpg',
        // background_image_oss_path: '/images/zkme-bg.jpg',
        // description: 'Welcome to ZKME Token Airdrop! Complete KYC verification to claim your tokens.',
        // theme_order_url: 'https://zkme.org/custom-theme'
      }
    }
  }
}
