.diaTitle {
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
  color: #002E33;
  margin-bottom: 24px;
}
.diaStepTitle1 {
  color: #002E33;
  font-size: 16px;
  font-weight: 500;
  line-height: 22px;
  margin-bottom: 8px;
}
.diaStepTitle {
  color: #738C8F;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  margin-bottom: 16px;
}

.mico-step2, .mico-step1, .mcico-success2 {
  font-size: 20px;
  margin: 0 6px;
}

.mico-upload-file {
  margin-right: 8px;
}
.mcico-upload-success, .mcico-warn2 {
  margin-right: 4px;
}

.mcico-success-green, .mcico-warn1, .mcico-Processing  {
  font-size: 18px;
  margin-right: 2px;
}
.sol {
  margin: 6px 0;
  width: 1px;
  height: 24px;
  background-color: rgba(0, 46, 51, 0.1);
}

.uploadTip {
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  color: #B3C0C2;
  margin-top: 8px;
}
.mgt {
  margin-top: 32px;
}

.expiredDate {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.3);
  margin-bottom: 66px;
  img {
    width: 140px;
    height: 140px;
  }
}

.t1{
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #002E33;
  margin-bottom: 6px;
}
.t2 {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  color: #738C8F;
}

.st1, .st3 {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  margin: 16px 0 12px;
  color: #002E33;
}
.st2 {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin-bottom: 12px;
  color: #738C8F;
}
.st3 {
  margin: 24px 0 12px;
}
.stext{
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
}
.sty1-upload ::v-deep {
  .el-upload--text {
    width: 100%;
  }
  .el-upload-dragger {
    display: grid;
    align-items: center;
    width: 100%;
    overflow: scroll;
    i {
      font-size: 24px;
    }
  }
}
.mico-drawer-close{
  color: #738C8F;
}
.mico-upload {
  color: #000000;
}
.sty2-upload ::v-deep {
  .el-upload-dragger {
    border-radius: 12px;
    display: grid;
    align-items: start;
  }
}
.uploadList {
  padding: 16px;
}

.mico-downFile {
  font-size: 24px;
}
.uploadText {
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin-top: 6px;
  color: rgba(0, 0, 0, 0.3);
  width: 300px;
}
.jumpLink {
  color: #64ABFF;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  text-decoration: underline;
}
.download {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #FFFFFF;
  padding: 11px 32px;
  border-radius: 26px;
  background-color: #005563;
  display: inline-block;
}

// .loading {
//   margin: 10px 14px;
//   --d: 20px;
//   width: 4px;
//   height: 4px;
//   border-radius: 50%;
//   color: #005563;
//   transform: scale(0.35);
//   box-shadow: 
//     calc(1*var(--d))      calc(0*var(--d))     0 0,
//     calc(0.707*var(--d))  calc(0.707*var(--d)) 0 1px,
//     calc(0*var(--d))      calc(1*var(--d))     0 2px,
//     calc(-0.707*var(--d)) calc(0.707*var(--d)) 0 3px,
//     calc(-1*var(--d))     calc(0*var(--d))     0 4px,
//     calc(-0.707*var(--d)) calc(-0.707*var(--d))0 5px,
//     calc(0*var(--d))      calc(-1*var(--d))    0 6px;
//   animation: loading 1s infinite steps(8);
// }

// @keyframes loading {
//   100% {
//     transform: scale(0.35) rotate(1turn);
//   }
// }

.loading {
  width: 11px;
  padding: 2px;
  aspect-ratio: 1;
  border-radius: 50%;
  background: #005563;
  margin: 2px 9px 0;
  --_m: 
    conic-gradient(#0000 10%,#000),
    linear-gradient(#000 0 0) content-box;
  -webkit-mask: var(--_m);
          mask: var(--_m);
  -webkit-mask-composite: source-out;
          mask-composite: subtract;
  animation: loading 1s infinite linear;
}
@keyframes loading {
  to{transform: rotate(1turn)}
}
