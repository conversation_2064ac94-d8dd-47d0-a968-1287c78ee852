.t1 {
  color: #002E33;
  font-size: 28px;
  font-weight: 500;
}
.leftBlock {
  display: grid;
  grid-gap: 20px;
  margin: 24px 0;
  .t2 {
    margin-bottom: 4px;
  }
}
.t2 {
  color: #809799;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}
.t3 {
  color: #002E33;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.t4 {
  color: #002E33;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  margin: 20px 0 12px;
}
.t5 {
  color: #33585C;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin-right: 4px;
}
.t6 {
  color: #002E33;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  margin: 6px 0;
}
.t7 {
  color: #B3C0C2;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}
.t8 {
  color: #EE6969;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}
.t9, .t10 {
  color: #809799;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin-top: 24px;
}
.t10 {
  margin-top: 4px;
  color: #64ABFF;
}
.errorPageUrl {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #EE6969;
  margin-top: 4px;
}
.sty1-upload ::v-deep {
  border-radius: 16px;
  margin-top: 12px;
  margin-bottom: 24px;
  width: 60%;
  .el-upload--text {
    width: 100%;
  }
  .el-upload-dragger {
    width: 500px;
    height: 280px;
    background: rgba(132, 182, 184, 0.12);
    border: 1px dashed rgba(0, 46, 51, 0.10);
    display: grid;
    align-items: center;
    overflow: scroll;
    i {
      font-size: 24px;
    }
  }
  .uploadImg {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    overflow: hidden;
    img {
      object-fit: cover;
    }
  }
  &.errorUpload .el-upload-dragger {
    border-color: #EE6969;
  }
}
.pageUrl {
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(132, 182, 184, 0.04);
  color: #809799;
  font-size: 14px;
  font-weight: 400;
  padding: 12px 16px;
}
.errorText1 {
  color: #EE6969;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  margin-top: 4px;
}
.textareaBox {
  position: relative;
  margin-top: 4px;
  .ui_textarea {
    width: 820px;
    background-color: #FFF;
    border-radius: 8px;
    padding: 16px;
    resize: none;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    &.errorTextarea {
      border-color: #EE6969;
    }
    &:focus-visible {
      outline: none;
    }
    &::placeholder {
      color: #B3C0C2;
      font-size: 14px;
      font-weight: 400;
    }
  }
  .wordCount {
    position: absolute;
    bottom: 16px;
    right: 16px;
    color: #809799;
    text-align: right;
    font-size: 14px;
    font-weight: 400;
  }
  .errorWordCount {
    color: #EE6969;
  }
}
