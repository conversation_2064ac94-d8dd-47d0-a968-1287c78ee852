.e-chart-radar {
  width: 100%;
  height: 500px;
}
.echarts-block {
  border-radius: 15px;
  img {
    width: 16px;
    height: 16px;
  }
  
  .name {
    color: #002E33;
    font-size: 20px;
    font-weight: 700;
    line-height: 28px;
  }
  .title {
    color: #33585C;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    margin-bottom: 12px;
  }
  .block-time {
    padding: 16px 20px;
    border: 1px dashed rgba(0, 0, 26, 0.15);
  }
  .block-day {
    border-radius: 16px;
    background: rgba(31, 192, 170, 0.08);
    padding: 24px;
    cursor: pointer;
    .name {
      color: '#33585C';
      font-size: 14px;
      font-weight: 400;
    }
    .total {
      color: #002E33;
      font-size: 24px;
      font-weight: 700;
      margin-top: 4px;
    }
  }
  .isSelect {
    background: rgba(17, 121, 133, 0.08);
  }
}

.nameList {
  position: absolute;
  top: 60px;
  left: 12%;
  display: grid;
  grid-gap: 30px;
  color: #002E33;
  font-size: 14px;
  font-weight: 400;
  i {
    font-size: 18px;
  }
  li {
    line-height: 48px;
  }
}
.noData {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  img {
    width: 50%;
    height: 50%;
  }
}
.errorList {
  color: #002E33;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  margin-right: 36px;
  li {
    padding: 12px 28px;
    margin-top: 12px;
  }
}
.mgr12 {
  margin-right: 12px;
}
.mgr40 {
  margin-right: 40px;
}
.mgb45 {
  margin-bottom: 45px;
}