/**
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/decrypt/applyDecryptUsingPOST_1
 */

export const applyDecrypt = ({ allUser, detail, files, mchNo, purpose, typeId, zkmeIds, zkmeAccount, blockchain, zisId, boundWallet, userId, startTime, endTime, citizenship, blockchainId, programName, SelectApplyDecryptBoList }) => {
  return {
    method: 'post',
    url: '/decrypt/applyDecrypt',
    data: {
      allUser,
      detail,
      files,
      mchNo,
      purpose,
      typeId,
      // zkmeIds,
      zkmeAccount,
      blockchain,
      zisId,
      boundWallet,
      userId,
      startTime,
      endTime,
      citizenship,
      blockchainId,
      programName,
      SelectApplyDecryptBoList
    }
  }
}
/**
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/decrypt/generateUrlUsingPOST_1
 */

export const generateUrl = (fileName) => {
  return {
    method: 'post',
    url: '/decrypt/generateUrl',
    data: fileName
  }
}
/**
 * https://testzk-zkseradmin.bitkinetic.com/doc.html#/zkme-admin/decrypt/queryDecryptUsingPOST_1
 */

export const queryDecrypt = (mchNo) => {
  return {
    method: 'post',
    url: '/decrypt/queryDecrypt',
    data: {
      mchNo
    }
  }
}
