<template>
  <div class="pg-login mui-fl-central">
    <div class="toplogo mui-fl-vert">
      <i class="mcico-colorLogo"></i>
      <div class="logo" />
      <div class="top-logo-name mui-fl-vert">Business</div>
    </div>
    <div class="login-left mui-fl-vert">
      <div class="c mui-fl-col">
        <p class="t1 mui-shr-0">Partner Login</p>
        <m-form :model="form" ref="form" :rules="rules" class="sty1-form sty3-form cen-align-err mui-fl-1 mui-fl-col mui-fl-btw" :hide-required-asterisk="true" @submit.native.prevent>
          <div>
            <m-form-item class="mgb-26" prop="email" label="Email" :class="accountOrPwdErrFlag && 'uncheck'">
              <m-input class="pdl35" ref="email" v-model="form.email" clearable placeholder="Enter your email" @input="listPassWord('email')"></m-input>
            </m-form-item>
            <m-form-item class="mgb-26" prop="password" label="Password">
              <m-input
                :class="['passwords', typepassword ? 'closeeye' : 'openeye']"
                ref="focustg"
                @focus="fgpasswordasd"
                @input="listPassWord('password')"
                show-password
                @keyup.enter.native="onSubmit"
                v-model="form.password"
                clearable
                placeholder="Enter your password"
                >
              </m-input>
            </m-form-item>
            <div class="mui-fl-btw">
              <div @click="gtag">
                <router-link  :to="{ name: 'CreatAccount' }" class="retrieve mui-fl-hori">Sign up <span class="partner">as partner</span></router-link>
              </div>
              <router-link :to="{ name: 'ForgotPassword', params: { id: 'step1' } }" class="retrieve mui-fl-hori">Forgot password？</router-link>
            </div>
          </div>
          <m-form-item class="mgb-0">
            <m-button type="primary" :loading="onceClick" @click="onSubmit" :disabled="(!form.email || !form.password) && disabled">Log in</m-button>
          </m-form-item>
        </m-form>
      </div>
      <SignRightComponent></SignRightComponent>
    </div>
  </div>
</template>

<script>
import SignRightComponent from '@/components/sign/SignRightComponent.vue'
export default {
  name: 'Login',
  components: {
    SignRightComponent
  },
  data () {
    return {
      currentErrFlag: false,
      accountOrPwdErrFlag: false,
      typepassword: true,
      placeholder: '',
      emailValidator: /^[0-9a-zA-Z_+-./]+[@][0-9a-zA-Z_.-]+([.][a-zA-Z]+){1,2}$/,
      chineseValidator: /[\u4e00-\u9fa5\u3000-\u303F-\\，。、；’【】、（）·￥“”：《》？——！……\\]/g,
      form: {
        email: '',
        password: ''
      },
      intervallIndex: null,
      inputBgVal: null,
      timer: null,
      disabled: true,
      onceClick: false
    }
  },
  watch: {
    'form.password' (val) {
      if (val) {
        this.$refs.form.validate()
      }
      if (!val) {
        setTimeout(() => {
          this.form.email && this.$refs.form.clearValidate('password')
          !this.form.email && this.$refs.form.clearValidate()
        }, 0)
      }
    },
    'form.email' (val) {
      if (!val) {
        setTimeout(() => {
          this.$refs.form.clearValidate('email')
        }, 0)
      }
    },
    intervallIndex () {
      // 监听浏览器自动填充账号密码，背景颜色的值是rgb格式的，有232表示是自动填充的淡蓝色，此时登录按钮可以点击。
      if (this.inputBgVal.indexOf(232) > 0) {
        this.disabled = false
      } else {
        this.disabled = true
      }
      clearInterval(this.timer)
    }
  },
  computed: {
    rules () {
      return {
        email: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callbak) => {
              if (value) {
                callbak()
              } else {
                callbak('Please enter a valid email address.')
              }
            }
          },
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callbak) => {
              if (!this.emailValidator.test(value) && value) {
                this.$refs.form.clearValidate('password')
                callbak('Please enter a valid email address.')
              } else if (this.accountOrPwdErrFlag && value) {
                callbak(' ')
              } else {
                callbak()
              }
            }
          }
        ],
        password: [
          {
            required: true,
            trigger: 'change',
            validator: (rule, value, callbak) => {
              if (value) {
                callbak()
              } else {
                callbak('Wrong password. Try again.')
              }
            }
          },
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callbak) => {
              if (value && this.currentErrFlag) {
                callbak()
              } else if (value && this.accountOrPwdErrFlag && this.emailValidator.test(this.form.email)) {
                callbak('Wrong account or password. Try again.')
              } else {
                callbak()
              }
            }
          }
        ]
      }
    }
  },

  mounted () {
    this.timer = setInterval(() => {
      this.intervallIndex++
      // 获取伪类元素的背景颜色
      this.inputBgVal = getComputedStyle(document.querySelectorAll('input')[0], '-internal-autofill-selected').backgroundColor
    }, 1000)
  },
  created () {
    this.$message.closeAll()
  },
  methods: {
    gtag () {
      window.gtag('event', 'Sign_up', {
        app_name: 'zkMe Dashboard'
      })
    },
    listPassWord (target) {
      this.form[target] = this.form[target].replaceAll(this.chineseValidator, '')
    },
    fgpasswordasd () {
      this.typepassword = this.$refs.focustg.$el.children[0]?.type === 'text'
    },
    onSubmit () {
      window.gtag('event', 'Log_in', {
        app_name: 'zkMe Dashboard'
      })
      if (this.onceClick) return
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const form = {
            ia: btoa(this.form.email),
            ip: btoa(this.form.password),
            vc: btoa('rivs'),
            vt: btoa('78303059-c21a-47be-8531-a7529c0c8695')
          }
          this.onceClick = true
          const rp = await this.$store.dispatch('login', form)
          if (rp.code === ********) {
            this.emailflag = false
            this.passwordflag = false
            this.$store.dispatch('detail')
            this.$router.push('/')
          } else {
            this.onceClick = false
            if (rp.code === ********) {
              this.currentErrFlag = true
              this.$refs.form.validate()
              this.currentErrFlag = false
              return
            }
            this.accountOrPwdErrFlag = true
            this.$refs.form.validate()
            this.accountOrPwdErrFlag = false
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/views/sign/_login.scss" scoped></style>
