<template>
  <div>
    <m-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-class="sty1-dialog sty4-dialog"
      title="Submission background"
      @close="restState"
      :visible.sync="reqStates.form"
      width="610px">
      <div class="diaTitle">1. Purpose*</div>
      <div>
        <m-select popper-class="sty6-popper" class="sty1-select sty4-select" @change="pushLabel" v-model="selectValue" placeholder="Please select a purpose">
          <m-option
            v-for="item in options"
            :key="item.label"
            :label="item.value"
            :value="item.label">
          </m-option>
        </m-select>
      </div>

      <div class="diaTitle">2. Detailed explanation*</div>
      <div>
        <m-input
          resize="none"
          class="sty2-input sty1-input-textarea"
          type="textarea"
          placeholder="Please clearly give background information regarding the action submission."
          v-model="fetchDate.detail"
          maxlength="300"
          show-word-limit
        >
        </m-input>
      </div>

      <div class="diaTitle">3. Supplementary documents</div>
      <div>
        <m-upload
          :class="['sty1-upload', fileList.length && 'sty2-upload']"
          drag
          accept=".jpg,.png,.pdf,.jpeg"
          action="#"
          :file-list="fileList"
          :show-file-list="false"
          :auto-upload="false"
          :on-change="fileCheck"
          multiple>
          <div>
            <div v-if="fileList.length" >
              <div v-for="(data, index) of fileList" :key="index" class="mui-fl-btw uploadList mui-fl-vert">
                <div class="mui-fl-vert">
                  <i class="mico-upload-file"></i>
                  <span class="fileName">{{ data.name }}</span>
                  <span style="margin-left: 12px">{{ data.size | fileSizeCalculate }}</span>
                </div>
                <div class="mui-fl-vert">
                  <div v-if="data.loading === 'loading'" class="loading"></div>
                  <i v-else-if="data.loading === 'fail'" class="mcico-warn2"></i>
                  <i v-else class="mcico-upload-success"></i>
                  <i @click.stop="detelFileList(data, index)" class="mico-drawer-close"></i>
                </div>
              </div>
            </div>
            <div v-else class="mui-fl-vert mui-fl-col">
              <i class="mico-upload"></i>
              <div class="uploadText">Please upload relevant regulatory or audit documents as reference materials for this submission.</div>
            </div>
          </div>
        </m-upload>
        <div class="uploadTip" :style="{color: wrongFlag ? 'red' : '#B3C0C2'}">PDF/JPG/PNG only, max. 10MB</div>
      </div>
      <div class="mui-fl-hori">
        <m-button class="sty5-button mgt" :disabled="!fetchDate.purpose || !fetchDate.detail || wrongFlag" @click="updateAction()">Submit recovery</m-button>
      </div>
    </m-dialog>

    <m-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-class="sty1-dialog sty4-dialog"
      title="Submission records"
      @close="restState"
      :visible.sync="reqStates.list"
      width="764px">
      <m-table class="sty2-table sty3-table sty4-table" :data="tableList" max-height="466">
        <template slot="empty">
          <img src="@/assets/img/no-table-data.png" alt="">
          <div>No record</div>
        </template>
        <m-table-column property="submissionTime" label="Submission time" width="170"></m-table-column>
        <m-table-column property="userCount" label="Number of records" width="150">
          <template #default="{ row }">
            {{ new Intl.NumberFormat('en-US').format(row.userCount) }}
          </template>
        </m-table-column>
        <m-table-column property="status" label="Status" width="120">
          <template #default="{ row }">
            <div class="mui-fl-vert stext">
              <i v-if="getExpireDateTime(row.expireDateTime) && row.status === 2" class="mcico-warn1"></i>
              <i v-else-if="row.status < 2" class="mcico-Processing"></i>
              <i v-else class="mcico-success-green"></i>
              <div v-if="getExpireDateTime(row.expireDateTime) && row.status === 2">Expired</div>
              <div v-else-if="row.status < 2">In approval</div>
              <div v-else>Completed</div>
            </div>
          </template>
        </m-table-column>
        <m-table-column property="expireDateTime" label="Expiry date" width="170">
          <template #default="{ row }">
            <div class="mui-fl-vert">
              {{ row.expireDateTime || '-' }}
            </div>
          </template>
        </m-table-column>
        <m-table-column label="Operation" width="100">
          <template #default="{ row }">
            <m-button style="color: #64ABFF" type="text" @click="tableView(row)">View</m-button>
          </template>
        </m-table-column>
      </m-table>
      <div class="mui-fl-hori">
        <m-button class="sty5-button mgt" @click="restState('open')">Submit recovery</m-button>
      </div>
    </m-dialog>

    <m-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-class="sty1-dialog sty4-dialog"
      :title="flag !== 'Expired' ? 'Submission process' : 'Recovery'"
      @close="restState('time')"
      :visible.sync="reqStates.step"
      width="610px">
      <div v-if="flag !== 'Expired'">
        <!-- <div class="diaStepTitle">Submit and wait for the following 2 steps:</div> -->
        <div class="mui-flex">
          <div class="mui-fl-col mui-fl-vert">
            <!-- <i class="mico-step1" v-if="!textStep"></i> -->
            <div class="loading" v-if="!textStep"></div>
            <i class="mcico-success2" v-else></i>
            <div class="sol"></div>
          </div>
          <div>
            <div class="t1">Retrieving encrypted data from decentralized storage.</div>
            <!-- <div class="t2">Download the users' encrypted zkKYC data through IPFS.</div> -->
          </div>
        </div>
        <div class="mui-flex">
          <div>
            <i class="mico-step2" v-if="textStep < 2"></i>
            <div class="loading" v-else-if="textStep === 2"></div>
            <i class="mcico-success2" v-else></i>
          </div>
          <div>
            <div class="t1">Waiting for Issuer and Regulator recovery approval.</div>
            <!-- <div class="t2">Waiting for the regulatory to approve the request.</div> -->
          </div>
        </div>
        <div>
        </div>
        <div class="mui-fl-hori">
          <m-button class="sty5-button mgt" :disabled="textStep <= 2">Submit recovery</m-button>
        </div>
      </div>
      <div v-else class="mui-fl-col mui-fl-vert expiredDate">
        <img src="@/assets/img/no-table-data.png" alt="">
        <div>The decryption data you applied for has expired.</div>
        <div>Please submit your request again.</div>
      </div>
    </m-dialog>

    <m-dialog
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-class="sty1-dialog sty4-dialog"
      title="Recovery"
      @close="restState"
      :visible.sync="reqStates.decryption"
      width="610px">
      <div class="diaStepTitle1">Recovery submission approved!</div>
      <div class="diaStepTitle">Follow the 2 steps to access decrypted data:</div>
      <div class="st1">Step-1：Download partially decrypted data (1/2 threshold)</div>
      <!-- <div class="st2">Click the button to download the encrypt data:</div> -->
      <div>
        <a class="download" :href="downloadFile">Download data</a>
      </div>
      <div class="st3">Step-2：Use the tool to recover data</div>
      <!-- <div class="st2">You can download the tool and perform data recovery locally.</div> -->
      <div>
        <a :href=decryptLink class="download" target="_blank" >Open tool</a>
      </div>
    </m-dialog>
  </div>
</template>

<script>
import { fileCheck, calculateSize, getExpireDateTime } from '@/utils/filters'
export default {
  props: {
    type: {
      type: String,
      required: ''
    },
    keyword: {
      type: String,
      required: ''
    },
    state: {
      type: String,
      required: ''
    },
    selectall: {
      type: Boolean,
      required: false
    },
    selectAccount: {
      type: Object,
      default: () => {
        return {}
      }
    },
    startTime: {
      type: String,
      default: () => {
        return ''
      }
    },
    endTime: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data () {
    return {
      reqStates: {
        form: false,
        list: false,
        step: false,
        decryption: false
      },
      getExpireDateTime: getExpireDateTime,
      alertFlag: false,
      flag: '',
      fileList: [],
      tableList: [],
      description: '',
      selectValue: '',
      actionRequest: '',
      fileSize: 0,
      wrongFlag: false,
      fetchapplyDecrypt: false,
      decryptLink: process.env.VUE_APP_ZKME_DECRYPT_URL,
      time: '',
      options: [
        {
          label: 1,
          value: 'Regulatory Data Release'
        },
        {
          label: 2,
          value: 'Suspicious Activity Report'
        },
        {
          label: 3,
          value: 'Audit'
        }
      ],
      fetchDate: {
        detail: '',
        files: '',
        mchNo: '',
        purpose: '',
        typeId: '1',
        zkmeIds: ''
      },
      userView: {},
      textStep: 0,
      downloadFile: '',
      minWidthStyle: false
    }
  },
  watch: {
    state (val) {
      if (val) {
        this.reqStates[val] = true
      }
    },
    'reqStates.list' (val) {
      if (val) {
        this.decryptList()
      }
    }
  },
  created () {
    this.fetchDate.mchNo = this.$store.state.auth.user.mchNo
  },
  methods: {
    pushLabel (val) {
      this.fetchDate.purpose = val.toString()
    },
    detelFileList (val, index) {
      this.fileList.splice(index, 1)
      this.fileSize = calculateSize(this.fileSize, val.size)
      if (this.fileList.length) {
        if (this.fileList.some(v => v.loading !== 'fail') && this.fileSize <= 10) {
          this.wrongFlag = false
        }
      } else {
        this.wrongFlag = false
      }
    },
    async fileCheck (file) {
      const fc = fileCheck(file, this.fileSize)
      this.fileSize = fc.fileSize
      if (file.name.length > 30) {
        file.name = file.name.slice(0, 30) + '...'
      }
      if (!fc.fileCheck || this.fileSize > 10) {
        this.$set(file, 'loading', 'fail')
        this.fileList.push(file)
        this.wrongFlag = true
        return
      }
      const fileType = file.raw.name.substring(0, file.raw.name.indexOf('.'))
      let newFileType
      if (fileType.length > 20) {
        newFileType = fileType.replace(fileType, fileType.slice(0, 10) + '...' + fileType.slice(-7)) + file.raw.name.substring(file.raw.name.indexOf('.'))
      } else {
        newFileType = file.raw.name
      }
      const time = new Date().getTime()
      const generateUrl = await this.$api.request('decrypt.generateUrl', { fileName: time + '-' + newFileType })
      if (generateUrl.code === ********) {
        this.$set(file, 'loading', 'loading')
        this.$set(file, 'fileOssUrl', generateUrl.data.url)
        this.fileList.push(file)
        const formdata = new FormData()
        formdata.append('OSSAccessKeyId', generateUrl.data.ossAccessKeyId)
        formdata.append('signature', generateUrl.data.postSignature)
        formdata.append('expire', generateUrl.data.expire)
        formdata.append('key', generateUrl.data.dir + time + '-' + newFileType)
        formdata.append('policy', generateUrl.data.encodedPolicy)
        formdata.append('file', file.raw)

        const requestOptions = {
          method: 'POST',
          body: formdata
        }

        fetch(generateUrl.data.host, requestOptions)
          .then((result) => {
            if (result.status === 204) {
              this.$set(file, 'loading', false)
              this.fileList.reverse().reverse()
            } else {
              this.$set(file, 'loading', 'fail')
              this.fileList.reverse().reverse()
            }
          })
          .catch(() => {
            this.$set(file, 'loading', 'fail')
            this.fileList.reverse().reverse()
          })
      } else {
        this.$message({
          message: 'upload fail',
          type: 'error'
        })
      }
    },
    tableView (val) {
      if (getExpireDateTime(val.expireDateTime) && val.status === 2) {
        this.flag = 'Expired'
      } else {
        this.flag = ''
        if (val.status < 1) {
          this.textStep = val.status
          this.time = setTimeout(() => {
            this.decryptList(val.applicationId)
          }, 2000)
        } else {
          if (val.status === 2) {
            this.textStep = 3
            this.downloadFile = val.downloadLink
            this.reqStates.list = false
            this.reqStates.step = false
            setTimeout(() => {
              this.reqStates.decryption = true
            }, 500)
            return
          } else {
            this.time = setTimeout(() => {
              if (this.reqStates.step) {
                this.decryptList(val.applicationId)
              }
            }, 3000)
            this.textStep = 2
          }
        }
      }
      this.reqStates.list = false
      this.reqStates.step = true
    },
    restState (val) {
      this.fileList = []
      this.selectValue = ''
      this.fetchDate.detail = ''
      this.fileSize = 0
      this.fetchDate.purpose = ''
      this.$emit('restState')
      if (val === 'time') {
        clearTimeout(this.time)
      }
      if (val === 'open') {
        this.reqStates.list = false
        this.$emit('selStateChange')
      }
    },
    async updateAction () {
      const form = {
        allUser: this.selectall,
        detail: this.fetchDate.detail,
        files: this.fileList.map(x => { return x.fileOssUrl }).toString(),
        mchNo: this.fetchDate.mchNo,
        purpose: Number(this.fetchDate.purpose),
        typeId: 1,
        // zkmeIds: Object.values(this.selectAccount).flat().map(x => { return x.zkmeId }),
        zkmeAccount: (this.type === '' || this.type === '0') ? this.keyword : '',
        boundWallet: this.type === '1' ? this.keyword : '',
        zisId: this.type === '2' ? this.keyword : '',
        blockchain: this.type === '3' ? this.keyword : '',
        userId: this.type === '4' ? this.keyword : '',
        citizenship: this.type === '5' ? this.keyword : '',
        programName: this.type === '6' ? this.keyword : '',
        startTime: this.selectall ? this.$options.filters.timestampDate(this.startTime) : '',
        endTime: this.selectall ? this.$options.filters.timestampDate(this.endTime) : '',
        blockchainId: this.$route.query.blockchainId || '',
        SelectApplyDecryptBoList: Object.values(this.selectAccount).flat().map(x => {
          return {
            programId: x.kycProgramId,
            zkmeId: x.zkmeId
          }
        })
      }
      if (this.fetchapplyDecrypt) return
      this.fetchapplyDecrypt = true
      const rp = await this.$api.request('decrypt.applyDecrypt', form)
      this.fetchapplyDecrypt = false
      if (rp.code === ********) {
        this.reqStates.form = false
        this.reqStates.step = true
        this.$emit('closeSelect')
        this.fileSize = ''
        this.selectValue = ''
        this.fetchDate.detail = ''
        this.fileList = []
        setTimeout(() => {
          this.decryptList(rp.data.applicationId)
        }, 1000)
      }
    },
    async decryptList (appId) {
      const rp = await this.$api.request('decrypt.queryDecrypt', this.fetchDate.mchNo)
      if (rp.code === ********) {
        this.reqStates.step = false
        return
      }
      if (rp.code === ********) {
        this.minWidthStyle = rp.data.some(x => !getExpireDateTime(x.expireDateTime) && x.status === 2)
        this.tableList = rp.data
        if (appId) {
          this.tableView(this.tableList.find(x => x.applicationId === appId))
        }
      }
    }
  }
}
</script>

<style lang="scss" src="@/assets/css/components/_request_information.scss" scoped></style>
