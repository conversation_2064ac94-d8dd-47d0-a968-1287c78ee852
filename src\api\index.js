import axios from 'axios'
// import store from '@/store'
// import router from '@/router'

import * as dashboard from './dashboard'
import * as kyc from './kyc'
import * as common from './common'
import * as auth from './auth'
import * as apikey from './apikey'
import * as kyt from './kyt'
import * as Uidesign from './Uidesign'
import * as decrypt from './decrypt'
import * as statistics from './statistics'
import * as disctrbution from './disctrbution'
import { assertResponse, calcSignature } from './crypto'

const apis = {
  dashboard,
  kyc,
  common,
  auth,
  apikey,
  kyt,
  Uidesign,
  decrypt,
  statistics,
  disctrbution
}

// 对外扩展配置
const defaults = {
  headers: {},
  successResponseHandler: () => {},
  errorResponseHandler: () => {}
}

/**
 * http 统一请求方法
 * @param {string} event 请求的接口类型
 * @param {object} data 提交的接口数据
 * @param {object} options 发出请求的可用配置选项
 * @param {boolean} changeApi 更改api地址
 */
const request = async function (event, data = {}, options = {}, changeApi = false) {
  const path = event.split('.')
  let changeUrl = process.env.VUE_APP_ZKME_API_URL
  const { method, data: payload } = apis[path[0]][path[1]](data)
  if (changeApi === 'distribution') {
    changeUrl = process.env.VUE_APP_ZKME_DISTRIBUTION_URL + `/${method}`
  } else if (changeApi === 'distributionImg' || changeApi === 'distributionImgUrl' || changeApi === 'distributionFile') {
    changeUrl = process.env.VUE_APP_ZKME_DISTRIBUTION_IMG_URL
  } else if (changeApi === 'cleanupWhitelist') {
    changeUrl = process.env.VUE_APP_ZKME_RECIPIENT_URL
  } else if (changeApi) {
    changeUrl = process.env.VUE_APP_ZKME_NEW_API_URL
  }
  const cfg = Object.assign({
    baseURL: changeUrl // 设置 axios 的 http请求 baseURL
  }, apis[path[0]][path[1]](data))
  if (changeApi === 'distribution') {
    cfg.method = 'post'
  }
  const {
    autoHandleSuccess = true,
    autoHandleError = true
  } = options || {}
  const timestamp = String(Date.now())
  let signature

  if (changeApi === 'distributionFile') {
    console.log(payload.file)
    signature = await calcSignature(payload.file || '', timestamp)
  } else {
    signature = await calcSignature(JSON.stringify(cfg.data) || '', timestamp)
  }

  cfg.headers = Object.assign({
    timestamp: Date.now(),
    'X-AppVersion': 'zkserapi_1.0.0',
    'X-Timestamp': timestamp,
    'X-Signature': signature
    // sign: cfg.method.toLowerCase() === 'post' || cfg.method.toLowerCase() === 'put' ? cfg.data : cfg.params
  }, defaults.headers, cfg.headers || {})
  // cfg.responseType = 'arraybuffer'
  if (changeApi === 'distributionImg') {
    cfg.data = data
    cfg.headers = Object.assign({}, cfg.headers, {
      'x-test': true
    })
  } else if (changeApi === 'distributionFile') {
    cfg.headers = Object.assign({}, cfg.headers, {
      'Content-Type': 'multipart/form-data; boundary=' + payload.boundary
    })
    cfg.data = payload.file
  }

  cfg.signal = options?.signal
  return new Promise((resolve, reject) => {
    axios.request({ ...cfg, responseType: 'arraybuffer' })
      .then(async ({ data, headers }) => {
        const r = await assertResponse(data, headers, cfg)
        resolve(r)
        if (r.code === 80000000 || Object.prototype.toString.call(r) === '[object Blob]') {
          autoHandleSuccess && defaults.successResponseHandler(r)
        } else if (r.msg !== 'Current network is insecure. Please switch networks and try again.') {
          autoHandleError && defaults.errorResponseHandler(r)
        }
      })
      .catch(e => {
        try {
          const rp = e.response && JSON.parse(Buffer.from(e.response.data).toString())
          resolve(rp)
        } catch (error) {
          console.log(error)
        }
        if (!axios.isCancel(e)) { // 不是【取消请求】类型的错误才进行错误处理
          defaults.errorResponseHandler(e)
        }
      })
  })
}

export default {
  defaults,
  request
}
