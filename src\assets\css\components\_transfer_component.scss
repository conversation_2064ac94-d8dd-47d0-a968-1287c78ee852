.expand-box {
  padding: 16px 0 24px 0;
  color: #002E33;
  &>li:first-child {
    margin-bottom: 16px;
  }
}

.expand-p-mar {
  margin-right: 12px;
}

.t1 {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

.t2 {
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
}

.transfer-content {
  margin: 24px 0 0 0;
}

.iconi {
  font-size: 20px;
  vertical-align: middle;
  margin-right: 4px;
}

.left-transfer {
  width: 100%;
}

.transfer-box {
  width: 100%;
  margin-top: 12px;
  border-radius: 12px;
  border: 1px solid #F0F0F0;
  overflow: hidden;
  box-sizing: border-box;
}

.top-select {
  width: 100%;
  height: 40px;
  background-color: #F2F7F7;
  padding: 10px 12px;
  box-sizing: border-box;
  cursor: pointer;
  i {
    color: #33585C;
  }
}

.select-p-color {
  color: #33585C;
  margin: auto 4px auto 6px;
}

.top-select-option {
  color: #002E33;
  font-size: 14px;
  font-weight: 400;
  line-height: 32px;
  height: 32px;
  padding-left: 8px;
  background-color: #fff;
  cursor: pointer;
}

.top-select-option:hover {
  background-color: #F7F7F7;
}

.top-select-option.active {
  background-color: #D9F6EF33;
  color: #005563;
}

.search-box {
  width: 100%;
  padding: 12px 0;
  box-sizing: border-box;
}

.hori-mar {
  width: calc(100% - 24px);
  margin: auto 12px;
}
.network-tag {
  background: #F7F7F7;
  border-radius: 4px;
  color: #002E33;
  font-size: 12px;
  line-height: 16px;
  font-weight: 500;
  padding: 0;
  border: none;
  cursor: pointer;
  position: relative;
  p {
    padding: 0px 16px 0px 8px !important;
  }
}
.tag-checked {
  background: #84B6B81F;
  overflow: hidden;
  &::after {
    content: '';
    width: 12px;
    height: 12px;
    background: url('~@/assets/img/tag-checked.png') 0 0 no-repeat;
    background-size: 100%;
    position: absolute;
    top: 0;
    right: 0;
  }
}
.tag-checked-hover {
  &:hover {
    position: relative;
    overflow: visible;
    &::after {
      content: '';
      width: 20px;
      height: 20px;
      background: url('~@/assets/img/close.png') 0 0 no-repeat;
      background-size: 100%;
      position: absolute;
      top: -6px;
      right: -6px;
    }
  }
}
.nations {
  height: 456px;
  position: relative;
  margin-top: 12px;
  &>ul {
    height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .nation-item {
    color: #002E33;
    p {
      height: 28px;
      line-height: 28px;
      padding: 0 12px;
    }
    li {
      height: 40px;
      padding: 0 12px;
      cursor: pointer;
      &:hover {
        background: #F7F7F7;
        .network-tag {
          background-color: #FFFFFF;
        }
      }
    }
    .img {
      width: 24px;
      height: 24px;
      margin: auto 8px auto 12px;
    }
    .item-checked {
      background: #F2F7F7;
      &:hover {
        background: #E6EDED;
      }
    }
    .country-name {
      font-size: 14px;
      padding: 0;
      color: #002E33;
      // max-width: 248px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  .letter-box {
    position: absolute;
    top: 21px;
    right: 12px;
    li {
      height: 14px;
      line-height: 14px;
      font-size: 10px;
      font-weight: 400;
      cursor: pointer;
    }
  }
}
.no-content {
  width: 100%;
  height: 456px;
  margin-top: 12px;
  img {
    width: 128px;
  }
  p {
    font-size: 14px;
    font-weight: 450;
    line-height: 18px;
    color: #B3C0C2;
  }
}
.transfer-btns {
  margin: auto 29px;
  p {
    cursor: pointer;
    width: 32px;
    height: 32px;
    border: 1px solid #F0F0F0;
    background: #005563;
    color: #fff;
    border-radius: 12px;
    box-sizing: border-box;
  }
  .to-right i {
    transform: rotate(180deg);
  }
  .to-left {
    margin-top: 12px;
  }
  .to-disabled {
    background: #fff;
    color: #002E33;
    cursor: not-allowed;
  }
}
.mico-Fallback {
  padding: 16px 0;
  // color: #002E33;
  line-height: 24px;
  font-size: 30px;
}

.nation-sprites {
  width: 24px;
  height: 24px;
  background: url('~@/assets/img/nation_sprites2.png') no-repeat;
  background-size: 8466px;
  transform-origin: 0 50%;
  margin-right: 12px;
}