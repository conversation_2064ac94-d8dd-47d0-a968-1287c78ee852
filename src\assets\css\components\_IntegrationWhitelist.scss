.inputBox {
  padding: 24px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

.auth-b {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: none;
  background: #f0f0f0;
  outline: none;
  line-height: 25px;
  text-align: center;
  font-weight: 700;
  font-size: 18px;
  margin-right: 15px;
  color: #002E33;
  border: 1px solid #f0f0f0;
}

.text {
  font-size: 36px;
  font-weight: bold;
  margin-top: 100px;
  text-align: left;
  margin-bottom: 50px;
}

.igcomp {
  padding: 0 125px;
}

.borb::after {
  content: '';
  height: 2px;
  position: absolute;
  width: 88px;
  border-radius: 5px;
  background-color: #002E33;
  bottom: -6px;
  left: 50%;
  transform: translate(-50%, 0);
}

.igTabs {
  padding: 12px 60px;
  margin: 40px 0;
  position: sticky;
  top: 72px;
  width: 100%;
  background-color: #FFFFFF;
  display: flex;
  justify-content: center;
  z-index: 1;
}

.igcur {
  cursor: pointer;
}

.igTexT {
  font-weight: 700;
  font-size: 22px;
  line-height: 32px;
  color: #002E33;
  position: relative;
}

.igTexts {
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #738C8F;
}

.lockDialog {
  font-weight: 500;
  color: #005563;
  .mico-question {
    font-size: 16px;
    margin-left: 2px;
  }
}

.lock {
  padding: 2px 8px;
  background: rgba(132, 182, 184, 0.12);
  color: #33585C;
  border-radius: 40px;
  margin-left: 8px;
  font-weight: 500;
  i {
    margin-right: 2px;
  }
}

.mico-lock {
  font-size: 16px;
  font-weight: 500;
}
.cfLock {
  font-size: 28px;
  margin-right: 10px;
}

.creatURL {
  width: 100%;
  padding-right: 30px;
}
.creatinp {
  margin: 8px 0;
}

.delete {
  color: #EE6969;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  position: absolute;
  right: -50px;
  transform: translate(0, -50%);
}

.saveDe {
  opacity: 0.5;
  cursor: not-allowed;
}

.unDelete {
  color: #B3C0C2;
}

.igtip {
  margin: 8px 0 28px;
}

.url {
  margin-top: 20px;
  // padding: 8px 0;

  .urlText {
    margin-right: 10px;
  }

  input {
    border-radius: 26px;
    background-color: #F7F7F7;
    border: none;
    color: #002E33;
  }
}

.mcico-celar {
  margin-right: 8px;
  cursor: pointer;

  &::before {
    font-size: 20px;
  }
}

.el-input__suffix-inner {
  line-height: 42px;
}

.url+.url:has([data-input]) {
  margin-top: 0px;
}
.url+.url:has([data-a]) {
  margin-top: 8px;
}

.openUrl {
  color: #002E33;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  overflow: hidden;
  -webkit-box-orient: vertical;
  line-height: 20px;
  padding: 8px 0;
  &:hover {
    text-decoration: revert;
  }
}

.addurl {
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  margin: 16px 0 12px;
  color: #002E33;
  .mico-creat {
    font-size: 20px;
    margin-right: 8px;
  }
}

.save {
  margin-top: 28px;
  border-radius: 26px;
  background-color: #005563;
  color: #FFFFFF;
  padding: 12px 55px;
  line-height: 22px;
  font-weight: 700;
  font-size: 16px;
  &:hover,
  &:focus {
    background-color: #005563;
    color: #FFFFFF;
  }
}

.configuration {
  max-width: 820px;
  border: 1px solid rgba(0, 46, 51, 0.1);
  padding: 24px;
  margin-bottom: 40px;
  border-radius: 16px;
}

.con-title {
  font-weight: 700;
  font-size: 16px;
  line-height: 22px;
  color: #005563;
  width: 100%;
  background-color: rgba(132, 182, 184, 0.12);
  border-radius: 8px;
  padding: 8px 0;
  margin-bottom: 40px;

  .documentations {
    color: #005563;

    &:hover {
      text-decoration: revert;
    }
  }

  .mico-file {
    margin-right: 2px;
    font-size: 24px;
  }
}

.con-text {
  font-weight: 700;
  font-size: 18px;
  line-height: 25px;
  color: #002E33;
}

.mb {
  margin-bottom: 28px;
}

.mbs {
  margin-bottom: 16px;
}

.addressName {
  margin-right: 16px;

  div+div {
    margin-top: 12px;
  }
}

.addressWl {
  div+div {
    margin-top: 12px;
  }
}

.addresscor {
  color: #002E33;
}

.dwJson {
  padding: 24px 20px 20px;
  border-radius: 12px;
  background-color: #F7F7F7;
}

.dwTip {
  background: rgba(132, 182, 184, 0.12);
  border-radius: 8px;
  padding: 8px 12px;
}

.dwrad {
  margin: 20px 0;

  span+span {
    padding-left: 8px;
  }
}

.dwbut {
  padding: 7px 30px;
  margin-top: 0;
  font-weight: 500;
  font-size: 16px;
  line-height: 22px;
  border: none;
}

.edAddress {
  padding: 24px 20px;
  border-radius: 12px;
  background-color: #F7F7F7;
}

.private {
  width: 28px;
  height: 28px;
  margin-right: 10px;
}

.radio {
  border-radius: 50%;
  width: 13px;
  height: 13px;
  border: 1px solid#B3C0C2;
  margin: 0 11px 0 2px;
  cursor: pointer;
}

.mcico-success2 {
  font-size: 16px;
  margin: 0px 8px 0px 0px;
  font-weight: 500;
}

.diaIcon {
  font-size: 20px;
}

.diaPls {
  padding: 4px 12px;
  background: rgba(132, 182, 184, 0.12);
  border-radius: 8px;
  color: #005563;
  margin-bottom: 12px;
}

.diaTip {
  color: #002E33;
}

.diaLockText {
  color: #005563;
}
.jsonTipText {
  display: inline-block;
}

.diaLockBut span {
  cursor: pointer;
  border-radius: 26px;
  background-color: #005563;
  padding: 11px 32px;
  color: #FFFFFF;
  margin: 24px 0 12px 0;
  font-weight: 500;
}

.mico-copy {
  font-size: 16px;
  cursor: pointer;
}
.jsonSs {
  color: #005563;
  font-weight: 400;
  font-size: 16px;
  line-height: 22px;
  background-color: rgba(0, 85, 99, 0.1);
  display: inline-block;
  padding: 7px 32px;
  border-radius: 45px;
  i {
    font-size: 16px;
    margin: 0 4px 0 0 ;
  }
}
.mico-warning {
  color: #EE6969;
}
.leaveTipTitle {
  font-weight: 500;
  font-size: 18px;
  line-height: 22px;
  color: #002E33;
}
.leaveTipText {
  line-height: 18px;
  color: #002E33;
}
.dialog-footer {
  .c, .l {
    border-radius: 26px;
    border: none;
    padding: 8px 32px;
    background-color:#F7F7F7;
    color:#33585C;
    font-weight: 500;
    &:hover {
      background-color:#F7F7F7;
    }
  }
  .l {
    margin-left: 12px;
    background: rgba(238, 105, 105, 0.06);
    color: #EE6969;
    &:hover {
      background: rgba(238, 105, 105, 0.06);
    }
  }
}