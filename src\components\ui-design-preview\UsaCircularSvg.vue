<template>
    <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style="margin-right: 8px;"
  >
    <mask
      id="mask0_6205_3654"
      style="mask-type: alpha"
      maskUnits="userSpaceOnUse"
      x="2"
      y="2"
      width="20"
      height="20"
    >
      <circle cx="12" cy="12" r="10" fill="#64ABFF" fill-opacity="0.2" />
    </mask>
    <g mask="url(#mask0_6205_3654)">
      <path
        d="M12 24.5C16.4183 24.5 20 22.7091 20 20.5C20 18.2909 16.4183 16.5 12 16.5C7.58172 16.5 4 18.2909 4 20.5C4 22.7091 7.58172 24.5 12 24.5Z"
        :fill="themeColor1"
        fill-opacity="0.5"
      />
    </g>
    <circle cx="12" cy="12" r="10" :fill="themeColor1" fill-opacity="0.1" />
    <path
      d="M12 20C13.6569 20 15 19.5523 15 19C15 18.4477 13.6569 18 12 18C10.3431 18 9 18.4477 9 19C9 19.5523 10.3431 20 12 20Z"
      :fill="themeColor1"
      fill-opacity="0.1"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.9104 15.1201C13.2327 15.7134 12.5543 16.3072 12 17C11.4457 16.3072 10.7673 15.7134 10.0895 15.1201C9.24481 14.3808 8.40109 13.6423 7.80002 12.7141C7.29387 11.9324 7 11.0005 7 10C7 7.23858 9.23858 5 12 5C14.7614 5 17 7.23858 17 10C17 11.0005 16.7061 11.9324 16.2 12.7141C15.5989 13.6423 14.7552 14.3808 13.9104 15.1201ZM12 12C13.1046 12 14 11.1046 14 10C14 8.89543 13.1046 8 12 8C10.8954 8 10 8.89543 10 10C10 11.1046 10.8954 12 12 12Z"
      :fill="themeColor1"
      fill-opacity="0.5"
    />
  </svg>
</template>
<script>
export default {
  name: 'UsaCircularSvg',
  props: {
    themeColor1: {
      type: String,
      required: true,
      default: () => {
        return '#005563'
      }
    }
  }
}
</script>
