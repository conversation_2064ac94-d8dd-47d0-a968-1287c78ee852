<template>
  <m-dialog
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    title="AML screening"
    :visible="visible"
    custom-class="sty1-dialog sty3-dialog"
    @close="close">
      <div class="content">
        <p class="c1">
          In this level we will conduct the AML analysis on users through the following databases.
        </p>
        <ul>
          <li v-for="item of list" :key="item.id" class="top-mar-1">
            <h4 class="item-title">{{ item.title }}</h4>
            <ul class="c-item-ul">
              <li v-for="cItem of item.contents" :key="cItem.cId" class="c-item-li top-mar-2">
                <h4 class="mui-fl-vert"><span />{{ cItem.title }}</h4>
                <p v-if="cItem.content" class="c2">{{ cItem.content }}</p>
              </li>
            </ul>
          </li>
        </ul>
        <div class="mui-fl-end kycbut">
          <m-button class="kycCancel" @click="close">OK</m-button>
        </div>
      </div>
    </m-dialog>
</template>
<script>

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      list: [
        {
          id: 1,
          title: '1.Sanctions, Warnings, and Fitness & Probity',
          contents: [
            {
              cId: 11,
              title: 'Warnings',
              content: 'Heads of State, National Parliaments, National governments'
            },
            {
              cId: 12,
              title: 'Wanted fitness & Probity',
              content: 'e.g. US System for Award management exclusions'
            }
          ]
        },
        {
          id: 2,
          title: '2.Politically Exposed Persons (PEP)',
          contents: [
            {
              cId: 21,
              title: 'Heads of State, National Parliaments, National governments (PEP Class 1)'
            },
            {
              cId: 22,
              title: 'Regional governments, Regional parliaments (PEP Class 2)'
            },
            {
              cId: 23,
              title: 'Senior management & Board of SOEs (PEP Class 3)'
            },
            {
              cId: 24,
              title: 'Mayors and members of local city councils (PEP Class 4)'
            },
            {
              cId: 25,
              title: 'Unclassified PEP (Also initiates search for all PEP classes above)'
            }
          ]
        },
        {
          id: 3,
          title: '3.Adverse Media General',
          contents: [
            {
              cId: 31,
              title: 'Adverse Media'
            },
            {
              cId: 32,
              title: 'Adverse Media General'
            },
            {
              cId: 33,
              title: 'v2 - Adverse Media General AML/CFT'
            },
            {
              cId: 34,
              title: 'v2 - Adverse Media Financial Difficulty'
            },
            {
              cId: 35,
              title: 'v2 - Adverse Meida Financial AML/CFT'
            },
            {
              cId: 36,
              title: 'Adverse Media Violent Crime'
            },
            {
              cId: 37,
              title: 'v2 - Adverse Media Violence NON AML/CFT'
            },
            {
              cId: 38,
              title: 'v2 - Adverse Media Violence NON AML/CFT'
            },
            {
              cId: 39,
              title: 'Adverse Media Sexual Crime'
            },
            {
              cId: 40,
              title: 'Adverse Media Terrorism'
            },
            {
              cId: 42,
              title: 'Adverse Media Fraud'
            },
            {
              cId: 43,
              title: 'Adverse Media Fraud - Linked'
            },
            {
              cId: 44,
              title: 'Adverse Media Narcotics'
            },
            {
              cId: 45,
              title: 'v2 - Adverse Media Narcotics AML/CFT'
            },
            {
              cId: 46,
              title: 'v2 - Adverse Media Cybercrime'
            },
            {
              cId: 47,
              title: 'v2 - Adverse Media Other Financial'
            },
            {
              cId: 48,
              title: 'v2 - Adverse Media Other Serious'
            },
            {
              cId: 49,
              title: 'v2 - Adverse Media Other Minor'
            },
            {
              cId: 50,
              title: 'v2 - Adverse Media Property'
            },
            {
              cId: 51,
              title: 'v2 - Adverse Media Regulatory'
            }
          ]
        }
      ]
    }
  },
  methods: {
    close () {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  height: 100%;
  overflow-y: scroll;
  // padding-right: 10px;
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
}
.c1 {
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}
.c2 {
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  color: #738C8F;
}
h4 {
  font-size: 14px;
  line-height: 20px;
  color: #002E33;
}
.item-title {
  height: 26px;
  line-height: 26px;
  background: #84B6B81F;
  font-weight: 500;
  padding: 0 12px;
  box-sizing: border-box;
  // margin-bottom: 12px;
}

.top-mar-1 {
  margin-top: 24px;
}

.top-mar-2 {
  margin-top: 12px;
}

.c-item-ul {
  padding-left: 12px;
}

.c-item-li h4 {
  margin-bottom: 2px;
  color: #33585C;
  span {
    display: block;
    width: 6px;
    height: 6px;
    background: #33585C;
    border-radius: 50%;
    margin-right: 4px;
  }
}

.kycbut {
  width: 590px;
  position: fixed;
  right: calc((100% - 610px) / 2);
  bottom: 15vh;
  // transform: translateX(-50%);
  background: #fff;
  border-radius: 16px;
  box-sizing: border-box;
  padding-right: 14px;
  padding-bottom: 24px;
  margin-right: 20px;
}

.kycCancel {
  width: 109px;
  height: 36px;
  background-color: #F7F7F7;
  color: #002E33;
  border-radius: 26px;
  border: none;
  margin-top: 24px;
  font-weight: 500;
}
</style>
