import Vue from 'vue'
import * as types from './../mutation-types'

export default {
  state: {
    verifyStatusList: [],
    blockChainList: null
  },
  mutations: {
    // [types.SET_VERIFYSTATUS_LIST] (state, payload) {
    //   state.verifyStatusList = payload
    // },
    [types.SET_BLOCK_CHAIN_LIST] (state, payload) {
      state.blockChainList = payload
    }
  },
  actions: {
    async getBlockChainList ({ commit }) {
      const rp = await Vue.prototype.$api.request('common.getBlockChain')
      if (rp.code === 80000000) {
        commit(types.SET_BLOCK_CHAIN_LIST, rp.data)
      }
    }
  }
}
