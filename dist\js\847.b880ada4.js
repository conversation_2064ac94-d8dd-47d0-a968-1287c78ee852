"use strict";(self["webpackChunkzkme_web"]=self["webpackChunkzkme_web"]||[]).push([[847],{76847:function(t,e,a){a.r(e),a.d(e,{default:function(){return u}});var i=function(){var t=this,e=t._self._c;return e("div",[t._m(0),e("div",{class:["7day"!==t.userDay&&t.MoonData.data.length||t.colStyle?"mui-fl-col":"mui-fl-btw"]},[e("div",{ref:"leftBlock",class:["mui-fl-1","echarts-block","7day"!==t.userDay&&t.MoonData.data.length||t.colStyle?"mgb45":"mgr40"]},[e("div",{staticClass:"name"},[t._v("User Number")]),e("div",{staticClass:"title"},[t._v("The number of users who have passed KYC. ")]),e("div",{staticClass:"mui-fl-btw"},[e("div",{class:["block-day","mui-fl-1","mgr12","7day"!==t.userDay&&"isSelect"],on:{click:function(e){return t.changeDay("7day")}}},[e("div",{staticClass:"name"},[t._v("Users (7 days)")]),e("div",{staticClass:"total"},[t._v(t._s(t._f("thousandth")(t.weekData.total||0)))])]),e("div",{class:["block-day","mui-fl-1","30day"!==t.userDay&&"isSelect"],on:{click:function(e){return t.changeDay("30day")}}},[e("div",{staticClass:"name"},[t._v("Users (30 days)")]),e("div",{staticClass:"total"},[t._v(t._s(t._f("thousandth")(t.MoonData.total||0)))])])]),e("div",{staticStyle:{position:"relative"}},[e("div",{style:{opacity:("7day"===t.userDay?t.weekData.data?.length:t.MoonData.data?.length)||0}},[e("div",{staticClass:"e-chart-radar",attrs:{id:"polyline"}})]),("7day"===t.userDay?t.weekData.data?.length:t.MoonData.data?.length)?t._e():e("div",{staticClass:"noData mui-fl-central"},[e("img",{attrs:{src:a(78790)}})])]),e("div",{staticClass:"mui-fl-hori"},[e("div",{staticClass:"mui-fl-vert"},[e("img",{staticStyle:{"margin-right":"4px"},attrs:{src:a(43423)}}),t._v(" "+t._s((new Date).getFullYear())+" ")])])]),e("div",{ref:"rightBlock",staticClass:"echarts-block",staticStyle:{flex:"1"}},[e("div",{staticClass:"name"},[t._v("KYC Process Funnel")]),e("div",{staticClass:"title"},[t._v("This funnel chart illustrates the total number of users who successfully passed each stage of the KYC process, along with the corresponding pass rate.")]),e("div",{staticClass:"block-time"},[e("div",{staticClass:"mui-fl-btw mui-fl-vert"},[e("div",{staticClass:"name"},[t._v("Total conversion rate："+t._s(t.totalConversionRate)+"%")]),e("m-date-picker",{staticClass:"sty1-date-editor sty3-date-editor mui-shr-0",attrs:{"popper-class":"sty1-date-popper","prefix-icon":"mico-date","value-format":"timestamp",format:"yyyy.MM.dd",type:"daterange",align:"right","range-separator":"-","start-placeholder":"start","end-placeholder":"end","unlink-panels":"",clearable:!1,editable:!1,"default-time":["00:00:00","23:59:59"]},on:{change:t.userEventCount},model:{value:t.picker,callback:function(e){t.picker=e},expression:"picker"}})],1),e("div",{staticStyle:{position:"relative"}},[e("ul",{staticClass:"nameList",style:{left:t.colStyle?"0%":"7day"!==t.userDay&&t.MoonData.data.length?"5%":"7%"}},t._l(t.titleName,(function(a,i){return e("li",{key:i},[e("i",{class:a.icon}),t._v(" "+t._s(a.name)+" ")])})),0),e("div",{staticClass:"e-chart-radar",staticStyle:{height:"600px"},attrs:{id:"funnel"}})])])])]),e("m-dialog",{attrs:{"close-on-click-modal":!1,"close-on-press-escape":!1,"custom-class":"sty1-dialog sty4-dialog sty5-dialog",title:"Datails",visible:t.dialogVisible,width:"760px"},on:{"update:visible":function(e){t.dialogVisible=e}}},[e("div",{staticClass:"datailsTitle"},[t._v(" This table shows the number of users who passed the previous step, the current step, and those who failed at the current step with detailed failure reasons. ")]),e("div",{staticClass:"mui-fl-btw"},[e("div",[e("span",{staticClass:"title"},[t._v("Type")]),e("div",[e("div",{staticClass:"kycDetailsList"},[t._v(t._s(t.totalDetail.first))]),e("div",{staticClass:"kycDetailsList"},[t._v(t._s(t.totalDetail.last))])])]),e("div",{staticClass:"mui-fl-vert"},[e("div",{staticClass:"mgr12 sty1"},[e("span",{staticClass:"title"},[t._v("Users")]),e("div",{staticClass:"kycDetailsList"},[t._v(t._s(t._f("thousandth")(t.totalDetail.firstRate)))]),e("div",{staticClass:"kycDetailsList"},[t._v(t._s(t._f("thousandth")(t.totalDetail.lastRate)))])])])]),e("m-collapse",{staticClass:"igCollapse sty3-collapse sty4-collapse",on:{change:t.handleChange}},[e("m-collapse-item",{attrs:{name:"1"}},[e("template",{slot:"title"},[e("div",{staticClass:"mui-fl-btw"},[e("div",{staticClass:"mui-fl-vert error"},[t.totalDetail.error.errorTotal?e("i",{staticClass:"mico-fold",style:{transform:t.rotateError?"rotate(0deg)":"rotate(270deg)"}}):t._e(),t._v(" Failure Reasons ")]),e("div",{staticClass:"mui-fl-vert",staticStyle:{"margin-right":"12px"}},[e("div",{staticStyle:{"min-width":"123px"}},[t._v(t._s(t._f("thousandth")(t.totalDetail.error.errorTotal)))])])])]),t.totalDetail.error.errorList?e("ul",{staticClass:"errorList"},t._l(t.totalDetail.error.errorList,(function(a,i){return e("li",{key:i},[e("div",{staticClass:"mui-fl-btw"},[e("div",[t._v(t._s(i))]),e("div",{staticClass:"mui-fl-vert"},[e("div",{staticStyle:{"margin-right":"-52px","min-width":"123px"}},[t._v(t._s(t._f("thousandth")(t.totalDetail.error.errorList[i])))])])])])})),0):t._e()],2)],1)],1)],1)},s=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"pg-title1 mui-fl-vert mui-shr-0"},[e("i",{staticClass:"mcico-active-dashboard"}),t._v(" Statistics (Beta) ")])}],l=(a(44114),a(98992),a(54520),a(81454),a(8872),a(9435)),r=a(5626),n=a(93518),o={data(){return{userDay:"7day",polylineChart:null,funnelChart:null,dialogVisible:!1,rotateError:0,kycDetailsList:{verification:{name:"Verification Started",users:"",rate:""},OCR:{name:"OCR Passed",users:"",rate:""},errorTotal:0,errorRate:0,error:{name:"error",errorList:[{name:"Unable to fill in required fields",users:0,rate:0},{name:"Issuing institution format error",users:0,rate:0},{name:"Document does not match",users:0,rate:0},{name:"Nationality does not match",users:0,rate:0}]}},query:{startTime:new Date-6048e5,endTime:(new Date).setHours(23,59,59,0)},colStyle:!1,weekData:{data:[],total:0},MoonData:{data:[],total:0},totalConversionRate:0,totalDetail:{first:"",firstRate:0,last:"",lastRate:0,error:{errorTotal:0,errorList:null}}}},computed:{...(0,n.aH)({level:({auth:t})=>t.user&&t.user.level,isNoMint:({auth:t})=>t.user&&t.user.isNoMint}),picker:{get(){return this.query.startTime?[this.query.startTime,this.query.endTime]:""},set(t){this.query.startTime=t?new Date(t[0]).getTime():"",this.query.endTime=t?new Date(t[1]).getTime():""}},titleName(){let t=[{name:"Verification Started",icon:"mcico-verification"},{name:"OCR Passed",icon:"mcico-ocr"},{name:"Liveness Checked",icon:"mcico-liveness"},{name:"ZKP Generated",icon:"mcico-zkp"},{name:"SBT Minted",icon:"mcico-sbtMinted"},{name:"OnChain Minted",icon:"mcico-Authorized"},{name:"KYC Passed",icon:"mcico-Passed"}];return this.isNoMint&&(t=t.filter((t=>"SBT Minted"!==t.name&&"OnChain Minted"!==t.name))),0!==this.level&&(t=t.filter((t=>"OnChain Minted"!==t.name))),t},funnelList(){let t=[{value:0,name:"",itemStyle:{color:"#A9E1D3"},titileName:"Verification Started",flag:1},{value:0,label:{color:"#738C8F"},itemStyle:{color:"transparent",height:"30px"},titileName:"Verification Started"},{value:0,name:"",itemStyle:{color:"#64ABFF"},titileName:"OCR Passed",flag:1},{value:0,label:{color:"#738C8F"},itemStyle:{color:"transparent",height:"30px"},titileName:"OCR Passed"},{value:0,name:"",itemStyle:{color:"#FFE28E"},titileName:"Liveness Checked",flag:1},{value:0,label:{color:"#738C8F"},itemStyle:{color:"transparent",height:"30px"},titileName:"Liveness Checked"},{value:0,name:"",itemStyle:{color:"#8EDEE3"},titileName:"ZKP Generated",flag:1},{value:0,label:{color:"#738C8F"},itemStyle:{color:"transparent",height:"30px"},titileName:"ZKP Generated"},{value:0,name:"",itemStyle:{color:"#88F0DA"},titileName:"SBT minted",flag:1},{value:0,label:{color:"#738C8F"},itemStyle:{color:"transparent",height:"30px"},titileName:"SBT minted"},{value:0,name:"",itemStyle:{color:"#B2D5FF"},titileName:"OnChain Minted",flag:1},{value:0,label:{color:"#738C8F"},itemStyle:{color:"transparent",height:"30px"},titileName:"OnChain Minted"},{value:0,name:"",itemStyle:{color:"#77D980"},titileName:"KYC Passed",flag:1}];return this.isNoMint&&(t=t.filter((t=>"SBT minted"!==t.titileName&&"OnChain Minted"!==t.titileName))),0!==this.level&&(t=t.filter((t=>"OnChain Minted"!==t.titileName))),t}},async mounted(){this.colStyle=window.innerWidth<=1550,await this.handleChangeDate(),this.userEventCount(),this.$nextTick((()=>{this.changeDay("7day","mounted");const t=document.getElementById("polyline"),e=document.getElementById("funnel");this.blockWidth=this.$refs.leftBlock.clientWidth,this.polylineChart=l.Ts(t),this.funnelChart=l.Ts(e)}))},methods:{handleChange(t){this.rotateError=t.length},formatDate(t){return new Date(t).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).replace(/\//g,"-")},async handleChangeDate(){const t=(new Date).setHours(23,59,59,0),e=new Date-6048e5,a=new Date-2592e6,i=await this.$api.request("statistics.authorizedUserCount",{startTime:e,endTime:t}),s=await this.$api.request("statistics.authorizedUserCount",{startTime:a,endTime:t});8e7===i.code&&(this.weekData={data:i.data.sort(((t,e)=>new Date(t[0]).getTime>new Date(e[0]).getTime?1:-1)),total:i.data.reduce(((t,e)=>t+e.count),0)}),8e7===s.code&&(this.MoonData={data:s.data.sort(((t,e)=>new Date(t[0]).getTime>new Date(e[0]).getTime?1:-1)),total:s.data.reduce(((t,e)=>t+e.count),0)}),this.polylineChartFun(this.weekData.data)},calculateTotalCount(t){return this.isNoMint&&(t["SBT minted"]=0,t["OnChain Minted"]=0),0!==this.level&&(t["OnChain Minted"]=0),Object.values(t).reduce(((t,e)=>t+e),0)},async userEventCount(){const t=await this.$api.request("statistics.userEventCount",{startTime:this.query.startTime,endTime:this.query.endTime}),e=await this.$api.request("statistics.failReasonCount",{startTime:this.query.startTime,endTime:this.query.endTime});if(8e7===t.code&&8e7===e.code){const a=t.data["Verification Started"],i=t.data["KYC Passed"];let s=i/a*100;s>0&&(s=s.toFixed(2)),this.totalConversionRate=s||0,this.funnelList.map(((a,i)=>{if(i%2===0)a.name=isNaN((0,r.Hk)(t.data[a.titileName]))?0:(0,r.Hk)(t.data[a.titileName]);else{const s=Number((t.data[this.funnelList[i+1].titileName]/t.data[a.titileName]*100).toFixed(2))||0;a.name=s===1/0?"0%":s+"%",this.funnelList[i-1].otherDatas=a.name;let l=e.data[this.funnelList[i+1].titileName];if(l){const t=Object.entries(l).sort(((t,e)=>"other"===t[0]?1:"other"===e[0]?-1:e[1]-t[1]));l=Object.fromEntries(t)}a.totalDetail={first:a.titileName,firstRate:t.data[a.titileName],last:this.funnelList[i+1].titileName,lastRate:t.data[this.funnelList[i+1].titileName],error:{errorTotal:e.data[this.funnelList[i+1].titileName]?Object.values(e.data[this.funnelList[i+1].titileName]).reduce(((t,e)=>t+e),0):0,errorList:l}}}a.value=Number((t.data[a.titileName]/this.calculateTotalCount(t.data)*100).toFixed(2))||0})),this.kycDetailsList.verification.users=t.data["Verification Started"],this.kycDetailsList.verification.rate=Number((100*(t.data["Verification Started"]/this.calculateTotalCount(t.data)||0)).toFixed(2)),this.kycDetailsList.OCR.users=t.data["OCR Passed"],this.kycDetailsList.OCR.rate=Number((100*(t.data["OCR Passed"]/this.calculateTotalCount(t.data)||0)).toFixed(2))}return this.funnelChartFun(t),t},async changeDay(t,e){this.userDay!==t&&(this.userDay=t,this.$nextTick((()=>{this.MoonData.data.length&&(this.polylineChart.resize({width:"7day"!==t?"auto":this.blockWidth}),this.colStyle||this.funnelChart.resize({width:"7day"!==t?"auto":this.blockWidth}),this.polylineChartFun("7day"!==t?this.MoonData.data:this.weekData.data))})))},transformData(t){const e=t.map((t=>[t.date,t.count])),a=e.map((t=>t[1])),i=Math.max(...a),s=Math.min(...a);return{transformedData:e,maxDay:i,minDay:s}},async polylineChartFun(t){const e=document.getElementById("polyline"),a=l.Ts(e),i=this.transformData(t);console.log(i);const s={tooltip:{trigger:"axis",formatter:function(t){return t[0].data[0]+"<br />"+`<div style="background: #005563;width: 8px;height:8px;line-height: 8px;border-radius: 50%;"><div>\n              <div style="margin: 10px 0px 10px 20px">${t[0].data[1]}</div>`},textStyle:{align:"left"}},grid:{left:"10%",right:"10%",bottom:"10%",containLabel:!0},xAxis:{type:"category",axisLabel:{formatter:0,color:"rgba(0, 0, 0, 0.7)"},axisLine:{lineStyle:{type:"solid",color:"background: rgba(0, 0, 26);"}},axisTick:{lineStyle:{color:"none"}},splitLine:{lineStyle:{type:"dashed"}}},yAxis:{type:"value",min:0,max:i.maxDay<=5?5:i.maxDay,axisLabel:{formatter:0,color:"rgba(0, 0, 0, 0.7)"},axisLine:{lineStyle:{type:"dashed",color:"none"}},splitLine:{lineStyle:{type:"dashed"}},axisTick:{lineStyle:{color:"none"}}},series:[{type:"line",symbol:"circle",symbolSize:7.5,showSymbol:!0,smooth:!0,data:i.transformedData,width:"100%",lineStyle:{width:2,color:"#005563",shadowColor:"rgba(0, 85, 99, 0.40)",shadowBlur:8,shadowOffsetY:7},itemStyle:{color:"#005563",borderColor:"#FFF",borderWidth:1},areaStyle:{color:new l.fA.W4(0,0,0,1,[{offset:0,color:"rgba(0, 85, 99, 0.6)"},{offset:1,color:"rgba(0, 85, 99, 0.05)"}])}}]};a.setOption(s)},funnelChartFun(){const t=document.getElementById("funnel"),e=l.Ts(t),a=[];for(const l in this.funnelList)if(1&l)if(this.funnelList[l].emphasis={label:{fontWeight:600}},"1"===l){const t=JSON.parse(JSON.stringify(this.funnelList[l])),e=JSON.parse(JSON.stringify(this.funnelList[l])),i=JSON.parse(JSON.stringify(this.funnelList[Number(l)+2]));t.itemStyle.height="48px",e.itemStyle.color="rgba(0, 85, 99, 0.10)",i.itemStyle.color="#F2F7F7",i.value=(.76*i.value).toFixed(2)||0,a.push([{...t},{...e},{...i}])}else{const t=(l-3)/2;if(a[t]){const e=Number(l)===this.funnelList.length-2,i=JSON.parse(JSON.stringify(a[t])),s=JSON.parse(JSON.stringify(this.funnelList[l])),r=JSON.parse(JSON.stringify(this.funnelList[l]));r.itemStyle.color="#F2F7F7",i[t].itemStyle.height="78px",i[t+1]=s,i[t+1].itemStyle.height="48px",i.slice(-1)[0].value=Number((e?this.funnelList[this.funnelList.length-1].value:.76*JSON.parse(JSON.stringify(this.funnelList[Number(l)+1])).value).toFixed(2))||0,i.push(r),a.push(i)}}else this.funnelList[l].emphasis={disabled:!0};const i=[{name:"Actual",type:"funnel",left:this.colStyle?"25%":"7day"===this.userDay?"35%":"25%",width:this.colStyle?"70%":"7day"===this.userDay?"65%":"75%",minSize:"25%",label:{position:"inside",formatter:"{b}",color:"#002E33",fontWeight:500,fontSize:"16px"},itemStyle:{borderColor:"#fff",borderWidth:0,height:"48px",borderJoin:"miter"},emphasis:{},data:this.funnelList,z:100}];a.forEach((t=>{i.push({type:"funnel",left:this.colStyle?"25%":"7day"===this.userDay?"35%":"25%",width:this.colStyle?"70%":"7day"===this.userDay?"65%":"75%",top:60,minSize:"22%",emphasis:{disabled:!0},label:{position:"inside",formatter:"",color:"#738C8F",fontWeight:400,fontSize:"14px"},itemStyle:{borderWidth:0},labelLine:{show:!1},data:t})}));const s={tooltip:{trigger:"item",formatter:t=>t.data.otherDatas||t.data.name},series:i};e.setOption(s),e.on("click",(({data:t})=>{t.titileName&&!t.flag&&(this.totalDetail=t.totalDetail,this.dialogVisible=!0)}))}}},d=o,c=a(81656),h=(0,c.A)(d,i,s,!1,null,"25176faf",null),u=h.exports},78790:function(t,e,a){t.exports=a.p+"img/norecord.78002e32.png"},43423:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAY1BMVEUAAAAAVGQAUWEAUmMAQE4AU2MAU2L///8AU2MAU2MAUGL///////////////8AUGD///////////8AVWP////v9fWfv8RAgIowdYAga3cganff6uzf6uuAqrHP4OLP3+J/qrGCAhFGAAAAE3RSTlMAQAk5BCgakCQwNBDPQCAQn9+g1lOJmAAAASVJREFUOMuVU1lygzAMRXjFhCVFbEloe/9TVmMsFEgmTN+XlifJ1pIJtDVBAahgrM5ekXsFApMf3NqTtbC5JrGxBSle78LVPig3VOpZV6weTKKYl2fpYmNoBUX2BgbUGuZZEHCgjwUAuFjlroiXtl419hgwq146THAlF/GUiBOUF+yXseumgbKUKQUVt/xCh49bF3G7o1ttBVhKY6NcI5I/MXqsotFS+QBNlFscug0LulQjZGz7xkkIIz5Y3AiILLH2X8IdR/FP+LMRQmqDw0UIA7bR2EDYvllhP7N/Rqz5m7tGzcm/a5RW0mocJvriby+tBk3T/jQssx93/UVZrq56GvfZwrBg3hEKCvy0tIaX9nztzw9HTq/RJOaH05MggfISfnb+f2rpGOoY5dVjAAAAAElFTkSuQmCC"}}]);