<template>
  <div class="mui-fl-vert" style="position: relative; width: 100%;">
    <DistributionStep3 class="mui-fl-1" :onlyRead=true></DistributionStep3>
    <DistributionStep2 class="mui-fl-1" :onlyRead=true></DistributionStep2>
  </div>
</template>

<script>
import DistributionStep2 from './distributionWhitelistStep2.vue'
import DistributionStep3 from './distributionPageSettingStep3.vue'
export default {
  components: {
    DistributionStep2,
    DistributionStep3
  },
  data () {
    return {

    }
  },
  computed: {
  },
  watch: {
  },
  created () {
  },
  methods: {}
}
</script>

<style lang="scss" src="@/assets/css/views/tokenDistribution/_distributionWhitelistStep2.scss" scoped></style>
