<template>
  <div>
    <div :class="{'mui-input-wrap-sty1': 1, 'err': error, 'search': searchflg}">
      <div class="mui-fl-vert mui-fl-btw">

        <input
          :placeholder="placeholder"
          v-model="value"
          class="mui-input--input"
          @input="input"
          @focus="focus"
          @blur="blur"
          @change="change"
          @keyup.enter="enter"
          ref="ndInput"
        />

        <p class="mui-fl-vert mui-input--icons">
          <i v-if="value && closeflg" class="mcico-celar" @mousedown="handleClear"/>
          <span v-if="searchflg" class="search" @mousedown="search"><i class="mico-search"></i></span>
        </p>

      </div>
    </div>
    <p v-if="error && errorMsg" class="mui-input-sty1--err">{{ errorMsg }}</p>
  </div>
</template>

<script>
export default {
  name: 'MuiInput',
  model: {
    prop: '_value',
    event: 'input'
  },
  props: {
    _value: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'text'
    },
    placeholder: {
      type: String,
      default: 'Please enter...'
    },
    error: {
      type: Boolean,
      default: false
    },
    errorMsg: {
      type: String,
      default: ''
    },
    searchflg: {
      type: Boolean,
      default: false
    },
    closeflg: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      realType: this.type
    }
  },
  computed: {
    value: {
      get () {
        return this._value
      },
      set (val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    input () {},
    focus () {
      this.$emit('focus')
    },
    blur () {
      this.$emit('blur')
    },
    change () {
      this.$emit('change')
    },
    enter () {
      this.$emit('enter')
    },
    handleClear () {
      this.value = ''
      this.$emit('clear')
      setTimeout(() => {
        this.$refs.ndInput.focus()
      }, 0)
    },
    search () {
      this.$emit('search')
    }
  }
}
</script>

<style lang="scss" scoped>
.mui-input-wrap-sty1 {
  background-color: #F7F7F7;
  padding: 9px 12px;
  border-radius: 10px;
  border: 1px solid #F7F7F7;
  &.err {
    border-color:red;
  }
  &.search {
    padding: 0 0 0 12px;
  }
}

.mui-input--input{
  display: block;
  width: 100%;
  background-color: #F7F7F7;
  color: #002E33;
  font-size: 14px;
  line-height: 18px !important;
  &::placeholder{
    font-size: 14px !important;
    color: #B3C0C2;
    font-weight: 400 !important;
    letter-spacing: normal;
  }
  &:focus {
    outline: none;
  }
  caret-color:#005563;
  border: none;
}

.mui-input--icons {
  .mcico-celar {
    cursor: pointer;
    font-size: 20px;
  }
  .search {
    padding: 8px 10px 8px 0;
    background: #E6EDED;
    border-radius: 0 10px 10px 0;
    margin-left: 10px;
    border-left: 1px solid #FFFFFF;
    cursor: pointer;
  }
  .mico-search {
    color: #B3C0C2;
  }
  i {
    font-size: 20px;
    margin-left: 8px;
  }
}

.mui-input-sty1--err {
  font-size: 12px;
  line-height: 16px;
  color: #EE6969;
  margin-top: 8px;
}

</style>
